package main

import (
	"code.byted.org/infcs/dbw-mgr/biz/actors/abnormal_detection"
	"code.byted.org/infcs/dbw-mgr/biz/actors/auto_create_index"
	"code.byted.org/infcs/dbw-mgr/biz/actors/cclrule"
	"code.byted.org/infcs/dbw-mgr/biz/actors/command"
	data_archive_actor "code.byted.org/infcs/dbw-mgr/biz/actors/data_archive"
	"code.byted.org/infcs/dbw-mgr/biz/actors/data_migration"
	dbw_ticket_actor "code.byted.org/infcs/dbw-mgr/biz/actors/dbw_ticket"
	online_ddl_actor "code.byted.org/infcs/dbw-mgr/biz/actors/dbw_ticket/online_ddl"
	inspectionActor "code.byted.org/infcs/dbw-mgr/biz/actors/inspection"
	"code.byted.org/infcs/dbw-mgr/biz/actors/scale"
	"code.byted.org/infcs/dbw-mgr/biz/actors/scale/bandwidth"
	shuttleActor "code.byted.org/infcs/dbw-mgr/biz/actors/shuttle"
	sla_actor "code.byted.org/infcs/dbw-mgr/biz/actors/sla"
	"code.byted.org/infcs/dbw-mgr/biz/actors/sql_console"
	sql_review_actor "code.byted.org/infcs/dbw-mgr/biz/actors/sql_review"
	sqltask_actor "code.byted.org/infcs/dbw-mgr/biz/actors/sqltask"
	"code.byted.org/infcs/dbw-mgr/biz/actors/tenant"
	"code.byted.org/infcs/dbw-mgr/biz/actors/ticket"
	"code.byted.org/infcs/dbw-mgr/biz/actors/ticket/sharding_free_lock_dml"
	"code.byted.org/infcs/dbw-mgr/biz/actors/user_mgmt"
	"code.byted.org/infcs/dbw-mgr/biz/apigateway"
	"code.byted.org/infcs/dbw-mgr/biz/bootstrap"
	"code.byted.org/infcs/dbw-mgr/biz/com"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	abnormal_detection_handler "code.byted.org/infcs/dbw-mgr/biz/handler/abnormal_detection"
	abnormal_diagnosis "code.byted.org/infcs/dbw-mgr/biz/handler/abnormal_diagnosis"
	"code.byted.org/infcs/dbw-mgr/biz/handler/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/handler/autoscale"
	"code.byted.org/infcs/dbw-mgr/biz/handler/collector"
	"code.byted.org/infcs/dbw-mgr/biz/handler/console/sql"
	data_archive_handler "code.byted.org/infcs/dbw-mgr/biz/handler/data_archive"
	"code.byted.org/infcs/dbw-mgr/biz/handler/data_copilot"
	"code.byted.org/infcs/dbw-mgr/biz/handler/dbgpt"
	"code.byted.org/infcs/dbw-mgr/biz/handler/dbw_instance"
	"code.byted.org/infcs/dbw-mgr/biz/handler/diagnosis"
	"code.byted.org/infcs/dbw-mgr/biz/handler/full_sql"
	"code.byted.org/infcs/dbw-mgr/biz/handler/full_sql/collection_sqlfingerprint"
	"code.byted.org/infcs/dbw-mgr/biz/handler/full_sql/export_file"
	inner_full_sql "code.byted.org/infcs/dbw-mgr/biz/handler/full_sql/inner"
	full_sql_tls "code.byted.org/infcs/dbw-mgr/biz/handler/full_sql/tls"
	"code.byted.org/infcs/dbw-mgr/biz/handler/inspection"
	"code.byted.org/infcs/dbw-mgr/biz/handler/mongo_console"
	"code.byted.org/infcs/dbw-mgr/biz/handler/multicloud"
	"code.byted.org/infcs/dbw-mgr/biz/handler/openapi"
	"code.byted.org/infcs/dbw-mgr/biz/handler/openapi/auditlog"
	sqltask_api "code.byted.org/infcs/dbw-mgr/biz/handler/openapi/sqltask"
	"code.byted.org/infcs/dbw-mgr/biz/handler/operate_record"
	"code.byted.org/infcs/dbw-mgr/biz/handler/ops"
	"code.byted.org/infcs/dbw-mgr/biz/handler/pg_console"
	"code.byted.org/infcs/dbw-mgr/biz/handler/securityrule"
	"code.byted.org/infcs/dbw-mgr/biz/handler/sql_advisor"
	"code.byted.org/infcs/dbw-mgr/biz/handler/sql_review"
	"code.byted.org/infcs/dbw-mgr/biz/handler/sql_statistic"
	tag_handler "code.byted.org/infcs/dbw-mgr/biz/handler/tag"
	"code.byted.org/infcs/dbw-mgr/biz/handler/taskflow"
	trx "code.byted.org/infcs/dbw-mgr/biz/handler/trx_lock"
	upgradeHandler "code.byted.org/infcs/dbw-mgr/biz/handler/upgrade"
	userMgmt "code.byted.org/infcs/dbw-mgr/biz/handler/user_mgmt"
	"code.byted.org/infcs/dbw-mgr/biz/handler/workflow"
	infactor "code.byted.org/infcs/dbw-mgr/biz/infrastructure/actors"
	configImpl "code.byted.org/infcs/dbw-mgr/biz/infrastructure/config"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure/db"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure/deploy"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure/deploy/resource"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure/deploy/resource/bytebrain"
	collectors "code.byted.org/infcs/dbw-mgr/biz/infrastructure/deploy/resource/collector"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure/deploy/resource/connections"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure/deploy/resource/monitor"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure/deploy/resource/syncer"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure/deploy/resource/transit"
	uimpl "code.byted.org/infcs/dbw-mgr/biz/infrastructure/deploy/resource/upgradable/impl"
	mgrInfra "code.byted.org/infcs/dbw-mgr/biz/infrastructure/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure/server"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure/server/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/instance/control"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/privilege/database"
	"code.byted.org/infcs/dbw-mgr/biz/privilege/user"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/repository/chat"
	"code.byted.org/infcs/dbw-mgr/biz/repository/instance"
	"code.byted.org/infcs/dbw-mgr/biz/repository/privilege"
	abnormal_detection_service "code.byted.org/infcs/dbw-mgr/biz/service/abnormal_detection"
	adsvc "code.byted.org/infcs/dbw-mgr/biz/service/abnormal_diagnosis"
	approval_flow_service "code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/service/audit/k8s"
	mongok8s "code.byted.org/infcs/dbw-mgr/biz/service/audit/mongo"
	pgsvc "code.byted.org/infcs/dbw-mgr/biz/service/audit/pg"
	redisk8s "code.byted.org/infcs/dbw-mgr/biz/service/audit/redis"
	"code.byted.org/infcs/dbw-mgr/biz/service/audit/statistic"
	bw_impl "code.byted.org/infcs/dbw-mgr/biz/service/autoscale/impl"
	"code.byted.org/infcs/dbw-mgr/biz/service/billing"
	fake_bill "code.byted.org/infcs/dbw-mgr/biz/service/billing/fake_bill"
	"code.byted.org/infcs/dbw-mgr/biz/service/billing_msg"
	"code.byted.org/infcs/dbw-mgr/biz/service/bytebrain/impl"
	"code.byted.org/infcs/dbw-mgr/biz/service/cloudmonitor"
	"code.byted.org/infcs/dbw-mgr/biz/service/cmdset"
	"code.byted.org/infcs/dbw-mgr/biz/service/common"
	iam "code.byted.org/infcs/dbw-mgr/biz/service/cross_service_authorization"
	"code.byted.org/infcs/dbw-mgr/biz/service/data_archive"
	"code.byted.org/infcs/dbw-mgr/biz/service/data_archive/data_backup"
	tos_service "code.byted.org/infcs/dbw-mgr/biz/service/data_archive/data_backup/tos"
	data_copilot_service "code.byted.org/infcs/dbw-mgr/biz/service/data_copilot"
	"code.byted.org/infcs/dbw-mgr/biz/service/data_copilot/function_call"
	data_migration_svc "code.byted.org/infcs/dbw-mgr/biz/service/data_migration"
	migration_job "code.byted.org/infcs/dbw-mgr/biz/service/data_migration_job"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/dair"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/mongo"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/mssql"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/mysql"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/nosql"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/pg"
	agent_service2 "code.byted.org/infcs/dbw-mgr/biz/service/db_copilot/agent_service"
	"code.byted.org/infcs/dbw-mgr/biz/service/db_copilot/agent_utils"
	dbgptsvc "code.byted.org/infcs/dbw-mgr/biz/service/dbgpt"
	dbw_syncer_svc "code.byted.org/infcs/dbw-mgr/biz/service/dbw_syncer"
	dbw_ticket_service "code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket"
	online_ddl_service "code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket/online_ddl"
	dbw_runtime "code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket/online_ddl/runtime"
	runtime_k8s "code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket/online_ddl/runtime/k8s"
	"code.byted.org/infcs/dbw-mgr/biz/service/dbwerror"
	"code.byted.org/infcs/dbw-mgr/biz/service/dbwinstance"
	diagnosisSvc "code.byted.org/infcs/dbw-mgr/biz/service/diagnosis"
	full_sql_service "code.byted.org/infcs/dbw-mgr/biz/service/full_sql"
	"code.byted.org/infcs/dbw-mgr/biz/service/full_sql/zkconfig"
	"code.byted.org/infcs/dbw-mgr/biz/service/i18n"
	iam_svc "code.byted.org/infcs/dbw-mgr/biz/service/iam"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/infra"
	"code.byted.org/infcs/dbw-mgr/biz/service/inspect_config"
	inspectionSvc "code.byted.org/infcs/dbw-mgr/biz/service/inspection"
	bizLoc "code.byted.org/infcs/dbw-mgr/biz/service/location"
	"code.byted.org/infcs/dbw-mgr/biz/service/message"
	"code.byted.org/infcs/dbw-mgr/biz/service/metric_data"
	"code.byted.org/infcs/dbw-mgr/biz/service/metrics"
	"code.byted.org/infcs/dbw-mgr/biz/service/monitor/influxdb"
	operate_record_svc "code.byted.org/infcs/dbw-mgr/biz/service/operate_record"
	"code.byted.org/infcs/dbw-mgr/biz/service/project"
	"code.byted.org/infcs/dbw-mgr/biz/service/publish_event"
	secruleSvc "code.byted.org/infcs/dbw-mgr/biz/service/securityrule"
	"code.byted.org/infcs/dbw-mgr/biz/service/securityrule/action"
	"code.byted.org/infcs/dbw-mgr/biz/service/securityrule/custom_rule"
	"code.byted.org/infcs/dbw-mgr/biz/service/securityrule/custom_rule_evaluate"
	"code.byted.org/infcs/dbw-mgr/biz/service/securityrule/factor"
	"code.byted.org/infcs/dbw-mgr/biz/service/securityrule/group"
	"code.byted.org/infcs/dbw-mgr/biz/service/securityrule/system_rule"
	shuttle "code.byted.org/infcs/dbw-mgr/biz/service/shuttle/impl"
	"code.byted.org/infcs/dbw-mgr/biz/service/sla"
	"code.byted.org/infcs/dbw-mgr/biz/service/slow_advice"
	slowlog_svc "code.byted.org/infcs/dbw-mgr/biz/service/slowquery"
	"code.byted.org/infcs/dbw-mgr/biz/service/space_analysis"
	sql_advisor_svc "code.byted.org/infcs/dbw-mgr/biz/service/sql_advisor"
	sql_review_svc "code.byted.org/infcs/dbw-mgr/biz/service/sql_review"
	sqltask_svc "code.byted.org/infcs/dbw-mgr/biz/service/sqltask"
	"code.byted.org/infcs/dbw-mgr/biz/service/tag"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls/index"
	tls_tenant "code.byted.org/infcs/dbw-mgr/biz/service/tls/tenant"
	"code.byted.org/infcs/dbw-mgr/biz/service/troubleshooting"
	"code.byted.org/infcs/dbw-mgr/biz/service/upgrade"
	userSvc "code.byted.org/infcs/dbw-mgr/biz/service/usermgmt"
	workflowSvc "code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/migration"
	"code.byted.org/infcs/ds-lib/common/http"
	"code.byted.org/infcs/ds-lib/framework/actorsystem"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/persistence"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/di"
	common_log "code.byted.org/infcs/lib-log"
	"code.byted.org/infcs/lib-mgr-common/volc"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"

	"code.byted.org/infcs/dbw-mgr/biz/actors"
	audit_actor "code.byted.org/infcs/dbw-mgr/biz/actors/audit"
	billActor "code.byted.org/infcs/dbw-mgr/biz/actors/billing"
	c3Actor "code.byted.org/infcs/dbw-mgr/biz/actors/c3"
	cleanActor "code.byted.org/infcs/dbw-mgr/biz/actors/clean_data"
	dbgptactor "code.byted.org/infcs/dbw-mgr/biz/actors/dbgpt"
	dialogActor "code.byted.org/infcs/dbw-mgr/biz/actors/dialog"
	tfactor "code.byted.org/infcs/dbw-mgr/biz/actors/taskflow"
	"code.byted.org/infcs/dbw-mgr/biz/handler/console"
	DataMigration "code.byted.org/infcs/dbw-mgr/biz/handler/data_migration"
	"code.byted.org/infcs/dbw-mgr/biz/handler/dialog"
	"code.byted.org/infcs/dbw-mgr/biz/handler/errlog"
	"code.byted.org/infcs/dbw-mgr/biz/handler/loc"
	logAnalysis "code.byted.org/infcs/dbw-mgr/biz/handler/log_analysis"
	logAudit "code.byted.org/infcs/dbw-mgr/biz/handler/log_audit"
	opsHandler "code.byted.org/infcs/dbw-mgr/biz/handler/ops"
	SqlCcl "code.byted.org/infcs/dbw-mgr/biz/handler/sql_ccl"
	SqlKill "code.byted.org/infcs/dbw-mgr/biz/handler/sqlkill"
	"code.byted.org/infcs/dbw-mgr/biz/service/audit"
	instance_cache "code.byted.org/infcs/dbw-mgr/biz/service/audit/cache/instance"
	auto_create_index_event "code.byted.org/infcs/dbw-mgr/biz/service/auto_create_index_event/impl"
	"code.byted.org/infcs/dbw-mgr/biz/service/parser"
)

func Bootstrap() {
	registerMultiCloud()
	bootstrap.InitC3Config()
	bootstrap.InitDALAndRepo()
	// TODO init according env
	if utils.IsByteCloud() {
		bootstrap.RegisterMW()
	}
	volc.SetUseIndependentEndpointRegion(utils.GetRegion())

	common_log.Init(common_log.Ops{
		Path:            "/opt/tiger/dbwmgr/log/lib-common.log",
		Prefixs:         []string{"dbwmgr"},
		LogLevel:        common_log.DebugLevel,
		Provider:        common_log.LocalDir,
		KeepFiles:       20,
		FilePerDuration: common_log.HourDur,
		LoggerProvider:  common_log.Logrus,
		Format:          common_log.Text,
		Package:         false,
		TraceConfig: common_log.TraceOps{
			EnableTracing:            true,
			EnableTraceLogLevels:     common_log.AllLevels,
			ErrorTraceLogToSpanLevel: common_log.ErrorLevel,
		},
	})
	di.GetAppContainer().MustRegister(configImpl.New)
	if utils.IsByteCloud() {
		di.GetAppContainer().MustRegister(fake_bill.NewFakeBillingService)
		di.GetAppContainer().MustRegister(fake_bill.NewFakeBillingMsgService)
		di.GetAppContainer().MustRegister(fake_bill.NewFakeProjectService)
		di.GetAppContainer().MustRegister(fake_bill.NewFakeTagService)
		di.GetAppContainer().MustRegister(inner_full_sql.NewCreateInnerFullSqlHandler)
		di.GetAppContainer().MustRegister(inner_full_sql.NewDeleteInnerFullSqlHandler)
		di.GetAppContainer().MustRegister(inner_full_sql.NewDescribeInnerRdsInstanceHandler)

		di.GetAppContainer().MustRegister(apigateway.NewGatewayService)
	} else {
		di.GetAppContainer().MustRegister(upgradeHandler.NewUpgradeResourceHandler)

		di.GetAppContainer().MustRegister(transit.NewFullSqlLogTransitResourceProvider)

		di.GetAppContainer().MustRegister(opsHandler.NewModifyLogCollectorImageHandler)
		di.GetAppContainer().MustRegister(opsHandler.NewDescribeLogCollectorClustersHandler)
		di.GetAppContainer().MustRegister(openapi.NewDescribeAuditLogDetailHandler)
		di.GetAppContainer().MustRegister(auditlog.NewModifyAuditLogConfigHandler)
		di.GetAppContainer().MustRegister(auditlog.NewDescribeAuditLogConfigHandler)

		// sql audit log handler
		di.GetAppContainer().MustRegister(logAudit.NewDescribeSqlAuditCandidateHandler)
		di.GetAppContainer().MustRegister(logAudit.NewDescribeSqlAuditDetailHandler)
		di.GetAppContainer().MustRegister(logAudit.NewDescribeSqlAuditExportTasksHandler)
		di.GetAppContainer().MustRegister(logAudit.NewDescribeSqlAuditStatusHandler)
		di.GetAppContainer().MustRegister(logAudit.NewDescribeSqlAuditTlsHandler)
		di.GetAppContainer().MustRegister(logAudit.NewDescribeInstancePriceHandler)
		di.GetAppContainer().MustRegister(logAudit.NewDescribeDownloadUrlHandler)
		di.GetAppContainer().MustRegister(logAudit.NewModifyInstanceClustersHandler)
		di.GetAppContainer().MustRegister(sql_statistic.NewDescribeSQLStatisticMetricsHandler)
		di.GetAppContainer().MustRegister(sql_statistic.NewDescribeSQLTemplateStatisticHandler)
		di.GetAppContainer().MustRegister(sql_statistic.NewDescribeSQLTimeElapseDistributionHandler)
		di.GetAppContainer().MustRegister(sql_statistic.NewDescribeSQLTimeElapseTotalHandler)
		di.GetAppContainer().MustRegister(tls_tenant.NewTenantTlsClient)
		di.GetAppContainer().MustRegister(billing.NewBillingService)
		di.GetAppContainer().MustRegister(billing_msg.NewBillingMsgService)
		di.GetAppContainer().MustRegister(billActor.NewPushBilling)
		di.GetAppContainer().MustRegister(logAudit.NewOrderCreateAuditHandler)
		di.GetAppContainer().MustRegister(logAudit.NewOrderCreateAuditPrecheckHandler)
		di.GetAppContainer().MustRegister(logAudit.NewOrderTerminateAuditHandler)
		di.GetAppContainer().MustRegister(logAudit.NewOrderNotifyAuditHandler)
		di.GetAppContainer().MustRegister(logAudit.NewOrderSupplementaryAuditHandler)
		di.GetAppContainer().MustRegister(logAudit.NewModifyAuditProjectFollowHandler)
		di.GetAppContainer().MustRegister(logAudit.NewIlmfHandleTradeMessageHandler)

		registerTag()
	}

	di.GetAppContainer().MustRegister(dal.NewMigrationTaskDAL)
	di.GetAppContainer().MustRegister(dal.NewMigrationProgressDAL)

	di.GetAppContainer().MustRegister(dal.NewEventDal)

	di.GetAppContainer().MustRegister(dal.NewWorkflowDAL)
	di.GetAppContainer().MustRegister(dal.NewPreCheckDetailDAL)
	di.GetAppContainer().MustRegister(dal.NewDbwUserDAL)
	di.GetAppContainer().MustRegister(dal.NewDbwUserGroupDAL)
	di.GetAppContainer().MustRegister(dal.NewProjectGroupRelation)
	di.GetAppContainer().MustRegister(dal.NewUserGroupRelation)
	di.GetAppContainer().MustRegister(dal.NewInstancePrivilegeDAL)
	di.GetAppContainer().MustRegister(dal.NewDatabasePrivilegeDAL)
	di.GetAppContainer().MustRegister(dal.NewTablePrivilegeDAL)
	di.GetAppContainer().MustRegister(dal.NewColumnPrivilegeDAL)
	di.GetAppContainer().MustRegister(dal.NewSecGroupDAL)
	di.GetAppContainer().MustRegister(dal.NewSecRuleDAL)
	di.GetAppContainer().MustRegister(dal.NewOpsDAL)
	di.GetAppContainer().MustRegister(dal.NewSqlCCLEventDAL)
	di.GetAppContainer().MustRegister(dal.NewSqlCCLRulesDAL)
	di.GetAppContainer().MustRegister(dal.NewSqlKillEventDAL)
	di.GetAppContainer().MustRegister(dal.NewSqlKillRulesDAL)
	di.GetAppContainer().MustRegister(dal.NewInspectionTaskDAL)
	di.GetAppContainer().MustRegister(dal.NewInspectionConfigDAL)
	di.GetAppContainer().MustRegister(dal.NewInspectionParamConfigDAL)
	di.GetAppContainer().MustRegister(dal.NewInspectionReportDAL)
	di.GetAppContainer().MustRegister(dal.NewInspectionSlowLogDAL)
	di.GetAppContainer().MustRegister(dal.NewScaleEventDAL)
	di.GetAppContainer().MustRegister(dal.NewScaleRuleDAL)
	di.GetAppContainer().MustRegister(dal.NewDbwLogCollectorDAL)
	di.GetAppContainer().MustRegister(dal.NewAbnormalDetectionEventDAL)
	di.GetAppContainer().MustRegister(dal.NewAbnormalDetectionConfigDAL)
	di.GetAppContainer().MustRegister(dal.NewSecFactorDAL)
	di.GetAppContainer().MustRegister(dal.NewSecActionDAL)
	di.GetAppContainer().MustRegister(dal.NewSlowQueryAdviceConfigDAL)
	di.GetAppContainer().MustRegister(dal.NewDbwAutoCreateIndexEventDAL)
	di.GetAppContainer().MustRegister(dal.NewSecRuleExecuteRecordDAL)
	di.GetAppContainer().MustRegister(dal.NewStatisticSqlTaskDal)
	di.GetAppContainer().MustRegister(dal.NewStatisticSqlTlsDal)
	di.GetAppContainer().MustRegister(dal.NewObInstDAL)
	di.GetAppContainer().MustRegister(dal.NewFullSqlDAL)
	di.GetAppContainer().MustRegister(dal.NewAccountantDAL)
	di.GetAppContainer().MustRegister(dal.NewInstanceExtraTlsDAL)
	di.GetAppContainer().MustRegister(dal.NewCollectionDAL)
	di.GetAppContainer().MustRegister(dal.NewActorDAL)
	di.GetAppContainer().MustRegister(dal.NewPrivilegeTicketDAL)
	di.GetAppContainer().MustRegister(dal.NewMysqlInformationSchemaTableDAL)
	di.GetAppContainer().MustRegister(dal.NewTableIoWaitsSummaryByTableDAL)
	di.GetAppContainer().MustRegister(dal.NewTableIoWaitsSummaryByIndexUsageDAL)
	di.GetAppContainer().MustRegister(dal.NewMysqlInnodbIndexStatsDAL)

	di.GetAppContainer().MustRegister(dal.NewUserFolderTokenDAL)
	di.GetAppContainer().MustRegister(dal.NewSQLAdvisorDAL)
	di.GetAppContainer().MustRegister(dal.NewInstanceExtraNodeDAL)
	di.GetAppContainer().MustRegister(dal.NewInstanceHistoryDAL)

	di.GetAppContainer().MustRegister(db.NewDBProvider)
	di.GetAppContainer().MustRegister(dal.NewSqlTaskDAL)
	di.GetAppContainer().MustRegister(migration.New)
	di.GetAppContainer().MustRegister(location.New)
	di.GetAppContainer().MustRegister(datasource.NewRootDataSource)
	di.GetAppContainer().MustRegister(parser.New)
	di.GetAppContainer().MustRegister(idgen.New)
	di.GetAppContainer().MustRegister(repository.NewCommandResultRepo)
	di.GetAppContainer().MustRegister(repository.NewCommandRepo)
	di.GetAppContainer().MustRegister(repository.NewShuttleRepo)
	di.GetAppContainer().MustRegister(repository.NewEventRepo)
	di.GetAppContainer().MustRegister(repository.NewMigrationTaskRepo)
	di.GetAppContainer().MustRegister(repository.NewInspectionRepo)
	di.GetAppContainer().MustRegister(repository.NewAutoScaleRepo)
	di.GetAppContainer().MustRegister(repository.NewAbnormalDetectionRepo)
	di.GetAppContainer().MustRegister(repository.NewSlowAdviceConfigRepo)
	di.GetAppContainer().MustRegister(repository.NewAutoCreateIndexEventRepo)
	di.GetAppContainer().MustRegister(mysql.NewMySQLDataSource)
	di.GetAppContainer().MustRegister(mysql.NewVeDBMySQLDataSource)
	di.GetAppContainer().MustRegister(nosql.NewRedisNoSQLDataSource)
	di.GetAppContainer().MustRegister(mongo.NewMongoNoSQLDataSource)
	di.GetAppContainer().MustRegister(mysql.NewMetaRDSDataSource)
	di.GetAppContainer().MustRegister(mysql.NewMetaMySQLDataSource)
	di.GetAppContainer().MustRegister(mssql.NewMSSQLDataSource)
	di.GetAppContainer().MustRegister(pg.NewPostgreSQLDataSource)
	di.GetAppContainer().MustRegister(dair.NewDAIRDataSource)

	di.GetAppContainer().MustRegister(mysql.NewMySQLShardingDataSource)
	di.GetAppContainer().MustRegister(shuttle.NewPGWShuttleService)
	di.GetAppContainer().MustRegister(shuttle.NewPGWShuttleV2ProviderWithRetry)
	di.GetAppContainer().MustRegister(infra.New)
	di.GetAppContainer().MustRegister(http.NewClient)
	di.GetAppContainer().MustRegister(cloudmonitor.NewClient)
	di.GetAppContainer().MustRegister(com.NewK8sClientsetProvider)
	di.GetAppContainer().MustRegister(com.NewK8sSecretProvider)
	di.GetAppContainer().MustRegister(com.NewClusterControl)
	di.GetAppContainer().MustRegister(iam.NewServiceImpl)
	di.GetAppContainer().MustRegister(k8s.NewAuditResClientProvider)
	di.GetAppContainer().MustRegister(pgsvc.NewPGAuditResClientProvider)
	di.GetAppContainer().MustRegister(redisk8s.NewRedisAuditResClientProvider)
	di.GetAppContainer().MustRegister(mongok8s.NewMongoAuditResClientProvider)
	di.GetAppContainer().MustRegister(audit.NewSqlAuditService)
	di.GetAppContainer().MustRegister(statistic.NewSqlStatisticService)
	di.GetAppContainer().MustRegister(publish_event.NewPublishEventService)
	di.GetAppContainer().MustRegister(migration_job.NewK8sAPIServerClientProvider)
	di.GetAppContainer().MustRegister(influxdb.NewRdsMonitor)
	di.GetAppContainer().MustRegister(system_rule.NewMySQLSecurityEngine)
	di.GetAppContainer().MustRegister(system_rule.NewVedbMySQLSecurityEngine)
	di.GetAppContainer().MustRegister(system_rule.NewPostgresSecurityEngine)
	di.GetAppContainer().MustRegister(system_rule.NewShardingSecurityEngine)
	di.GetAppContainer().MustRegister(system_rule.NewMssqlSecurityEngine)
	di.GetAppContainer().MustRegister(system_rule.NewByteRdsSecurityEngine)
	di.GetAppContainer().MustRegister(secruleSvc.NewSecRulesValidationService)
	di.GetAppContainer().MustRegister(custom_rule_evaluate.NewEvaluateHandler)
	di.GetAppContainer().MustRegister(custom_rule.NewMysqlCustomRuleServiceHandler)
	di.GetAppContainer().MustRegister(system_rule.NewSecurityEngineService)
	di.GetAppContainer().MustRegister(dbwerror.NewErrorMapper)
	di.GetAppContainer().MustRegister(instance_cache.NewAuditInstanceManager)
	di.GetAppContainer().MustRegister(abnormal_detection_service.NewAbnormalDetectionService)
	di.GetAppContainer().MustRegister(common.NewAbnormalDetectionService)

	/* deploy */
	di.GetAppContainer().MustRegister(deploy.New)
	di.GetAppContainer().MustRegister(deploy.NewBrainDeploy)
	di.GetAppContainer().MustRegister(deploy.NewFullSqlDeploy)
	di.GetAppContainer().MustRegister(deploy.NewDetectionBrainDeploy)
	di.GetAppContainer().MustRegister(resource.NewDynamicK8sResourceProvider)
	di.GetAppContainer().MustRegister(connections.NewConnectionsResourceProvider)
	di.GetAppContainer().MustRegister(monitor.NewMonitorResourceProvider)
	di.GetAppContainer().MustRegister(collectors.NewCollectorResourceProvider)
	di.GetAppContainer().MustRegister(syncer.NewSyncerResourceProvider)
	di.GetAppContainer().MustRegister(bytebrain.NewByteBrainResourceProvider)
	di.GetAppContainer().MustRegister(bytebrain.NewDetectionByteBrainResourceProvider)
	di.GetAppContainer().MustRegister(impl.NewByteBrainService)
	di.GetAppContainer().MustRegister(resource.NewK8sClientsetProvider)
	di.GetAppContainer().MustRegister(resource.NewFileReader)
	di.GetAppContainer().MustRegister(uimpl.NewUpgradableResourcePool)
	di.GetAppContainer().MustRegister(upgrade.New)

	/* mgr */
	di.GetAppContainer().MustRegister(mgrInfra.NewRDSMgr)
	di.GetAppContainer().MustRegister(mgrInfra.NewVeDBMgr)
	di.GetAppContainer().MustRegister(mgrInfra.NewRedisMgr)
	di.GetAppContainer().MustRegister(mgrInfra.NewMongoMgr)
	di.GetAppContainer().MustRegister(mgrInfra.NewPgMgr)
	di.GetAppContainer().MustRegister(mgrInfra.NewInfraMgr)
	di.GetAppContainer().MustRegister(mgrInfra.NewUpgradeMgr)
	di.GetAppContainer().MustRegister(mgrInfra.NewMetaRDSMgr)
	di.GetAppContainer().MustRegister(mgrInfra.NewMSSQLMgr)
	di.GetAppContainer().MustRegister(mgrInfra.NewRDSShardingMgr)
	di.GetAppContainer().MustRegister(mgrInfra.NewMetaMySQLMgr)
	di.GetAppContainer().MustRegister(mgrInfra.NewSyncer)
	di.GetAppContainer().MustRegister(mgrInfra.NewDtsMgr)
	di.GetAppContainer().MustRegister(mgrInfra.NewDairMgr)

	setupActorSystem()

	di.GetAppContainer().MustRegister(mgr.New)
	di.GetAppContainer().MustRegister(metrics.NewConnectionMetrics)

	/* register handler */
	di.GetAppContainer().MustRegister(handler.NewDescribeMgrParamHandler)
	di.GetAppContainer().MustRegister(handler.NewUpdateMgrParamHnadler)
	// session handlers
	di.GetAppContainer().MustRegister(handler.NewCreateSessionHandler)
	di.GetAppContainer().MustRegister(handler.NewCloseSessionHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeSessionHandler)
	di.GetAppContainer().MustRegister(handler.NewSessionKeepAliveHandler)

	// connection handlers
	di.GetAppContainer().MustRegister(handler.NewCreateConnectionHandler)
	di.GetAppContainer().MustRegister(handler.NewCloseConnectionHandler)
	di.GetAppContainer().MustRegister(handler.NewCheckConnHandler)

	// command handlers
	di.GetAppContainer().MustRegister(handler.NewExecuteCommandSetHandler)
	di.GetAppContainer().MustRegister(handler.NewCancelCommandSetHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeCommandSetHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeCommandHandler)

	// common handlers
	di.GetAppContainer().MustRegister(handler.NewDescribeDataSourceTypesHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeLinkTypesHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeRegionsHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeInstancesHandler)
	di.GetAppContainer().MustRegister(handler.NewAgreeUserProtocolHandler)
	di.GetAppContainer().MustRegister(handler.NewGetUserProtocolStateHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeInstanceNodesHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeInstanceShardsHandler)
	// database handlers
	di.GetAppContainer().MustRegister(handler.NewDescribeDatabasesHandler)

	di.GetAppContainer().MustRegister(handler.NewChangeDBHandler)
	di.GetAppContainer().MustRegister(handler.NewDeployHandler)

	// pg handlers
	di.GetAppContainer().MustRegister(pg_console.NewDescribePgTableHandler)
	di.GetAppContainer().MustRegister(pg_console.NewDescribeSchemasHandler)
	di.GetAppContainer().MustRegister(pg_console.NewDescribeSequencesHandler)
	di.GetAppContainer().MustRegister(pg_console.NewDescribePgCollationsHandler)
	di.GetAppContainer().MustRegister(pg_console.NewDescribePgUsersHandler)
	di.GetAppContainer().MustRegister(pg_console.NewDescribeTableSpacesHandler)

	// view handlers
	di.GetAppContainer().MustRegister(handler.NewDescribeViewHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeViewsHandler)
	di.GetAppContainer().MustRegister(handler.NewSearchViewHandler)

	// function handlers
	di.GetAppContainer().MustRegister(handler.NewDescribeFunctionsHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeDataTypesHandler)
	di.GetAppContainer().MustRegister(handler.NewCreateFunctionHandler)
	di.GetAppContainer().MustRegister(handler.NewExecuteFunctionHandler)
	di.GetAppContainer().MustRegister(handler.NewDropFunctionHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeFunctionHandler)

	// procedure handlers
	di.GetAppContainer().MustRegister(handler.NewCreateProcedureHandler)
	di.GetAppContainer().MustRegister(handler.NewExecuteProcedureHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeProcedureHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeProceduresHandler)

	// trigger handlers
	di.GetAppContainer().MustRegister(handler.NewDescribeTriggersHandler)
	di.GetAppContainer().MustRegister(handler.NewSearchTriggerHandler)
	di.GetAppContainer().MustRegister(handler.NewAgreeUserProtocolHandler)
	di.GetAppContainer().MustRegister(handler.NewGetUserProtocolStateHandler)
	di.GetAppContainer().MustRegister(handler.NewGetTotalKeyNumberHandler)
	di.GetAppContainer().MustRegister(handler.NewGetKeyMembersHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeKeysHandler)
	di.GetAppContainer().MustRegister(handler.NewAlterKVsHandler)

	// table handlers
	di.GetAppContainer().MustRegister(handler.NewRenameTableHandler)
	di.GetAppContainer().MustRegister(handler.NewCreateTableHandler)
	di.GetAppContainer().MustRegister(handler.NewDropTableHandler)
	di.GetAppContainer().MustRegister(handler.NewCopyTableSchemaOnlyHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeCharsetsHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeCollationsHandler)
	di.GetAppContainer().MustRegister(handler.NewAlterTableHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeTablesHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeTableHandler)
	di.GetAppContainer().MustRegister(handler.NewSearchTableHandler)

	//event handlers
	di.GetAppContainer().MustRegister(console.NewCreateEventHandler)
	di.GetAppContainer().MustRegister(console.NewDescribeEventsHandler)
	di.GetAppContainer().MustRegister(console.NewDescribeEventHandler)
	di.GetAppContainer().MustRegister(console.NewDropEventHandler)

	// database handlers
	di.GetAppContainer().MustRegister(handler.NewCreateDatabaseHandler)
	di.GetAppContainer().MustRegister(handler.NewAlterDatabaseHandler)
	di.GetAppContainer().MustRegister(handler.NewDropDatabaseHandler)

	//view handlers
	di.GetAppContainer().MustRegister(handler.NewCreateViewHandler)
	di.GetAppContainer().MustRegister(handler.NewDropViewHandler)
	di.GetAppContainer().MustRegister(handler.NewCreateTriggerHandler)

	//trigger handlers
	di.GetAppContainer().MustRegister(handler.NewDescribeTriggerHandler)
	di.GetAppContainer().MustRegister(handler.NewDropTriggerHandler)
	di.GetAppContainer().MustRegister(handler.NewAlterRowsHandler)

	//SqlAdvice handlers
	di.GetAppContainer().MustRegister(handler.NewGetSqlAdviceHandler)

	// ops handlers
	di.GetAppContainer().MustRegister(upgradeHandler.NewCheckMgrSelfHealthHandler)
	di.GetAppContainer().MustRegister(upgradeHandler.NewCheckResHealthHandler)
	di.GetAppContainer().MustRegister(upgradeHandler.NewGetUpgradeProgressHandler)

	di.GetAppContainer().MustRegister(opsHandler.NewStopDataMigrationTaskHandler)
	di.GetAppContainer().MustRegister(opsHandler.NewDescribeDmTasksHandler)
	di.GetAppContainer().MustRegister(opsHandler.NewCreateManualCCLInspectionTaskHandler)
	di.GetAppContainer().MustRegister(opsHandler.NewModifyLogCollectorImageHandler)
	di.GetAppContainer().MustRegister(opsHandler.NewDescribeLogCollectorClustersHandler)
	di.GetAppContainer().MustRegister(opsHandler.NewDescribeOpsListHandler)

	// slowlog handler
	di.GetAppContainer().MustRegister(logAnalysis.NewDescribeAvailableTLSTopicHandler)
	di.GetAppContainer().MustRegister(logAnalysis.NewDescribeSlowLogTimeSeriesStatsHandler)
	di.GetAppContainer().MustRegister(logAnalysis.NewDescribeAggregateSlowLogsHandler)
	di.GetAppContainer().MustRegister(logAnalysis.NewDescribeSlowLogsHandler)
	di.GetAppContainer().MustRegister(logAnalysis.NewDescribeUsersHandler)
	di.GetAppContainer().MustRegister(logAnalysis.NewDescribeSourceIPsHandler)
	di.GetAppContainer().MustRegister(logAnalysis.NewDescribeDBsHandler)
	di.GetAppContainer().MustRegister(logAnalysis.NewDescribeExampleSQLHandler)
	di.GetAppContainer().MustRegister(logAnalysis.NewCreateSlowLogsExportTaskHandler)
	di.GetAppContainer().MustRegister(logAnalysis.NewDescribeSlowLogsExportTasksHandler)
	di.GetAppContainer().MustRegister(logAnalysis.NewDescribeLogsDownloadUrlHandler)
	di.GetAppContainer().MustRegister(logAnalysis.NewDescribeSlowLogsDetailHandler)
	di.GetAppContainer().MustRegister(slowlog_svc.NewSlowLogService)

	// dialog handlers
	di.GetAppContainer().MustRegister(dialog.NewDescribeDialogDetailSnapshotHandler)
	di.GetAppContainer().MustRegister(dialog.NewDescribeEngineStatusSnapShotHandler)
	di.GetAppContainer().MustRegister(dialog.NewDescribeDialogSnapshotsHandler)
	di.GetAppContainer().MustRegister(dialog.NewDescribeDialogStatisticsHandler)
	di.GetAppContainer().MustRegister(dialog.NewKillProcessHandler)
	di.GetAppContainer().MustRegister(dialog.NewDescribeDialogInfosHandler)
	di.GetAppContainer().MustRegister(dialog.NewDescribeAggregateDialogsHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeDBProxyStatusHandler)
	di.GetAppContainer().MustRegister(handler.NewDescribeInstanceFeaturesHandler)
	di.GetAppContainer().MustRegister(dialog.NewDescribeAggregateDialogsHandler)
	di.GetAppContainer().MustRegister(dialog.NewDescribeDialogHotspotsHandler)
	di.GetAppContainer().MustRegister(dialog.NewDescribeCurrentConnInfosHandler)

	// tenant
	di.GetAppContainer().MustRegister(tenant.NewTenantMgrActor)
	di.GetAppContainer().MustRegister(handler.NewAgreeDataCollectionHandler)
	di.GetAppContainer().MustRegister(dal.NewTenantDAL)
	di.GetAppContainer().MustRegister(repository.NewTenantRepo)

	di.GetAppContainer().MustRegister(sqltask_api.NewCreateSqlTaskHandler)
	di.GetAppContainer().MustRegister(sqltask_api.NewDescribeSqlTasksHandler)
	di.GetAppContainer().MustRegister(sqltask_api.NewDescribeSqlTaskHandler)

	// slow advice
	di.GetAppContainer().MustRegister(sql_advisor.NewListSlowQueryAdviceConfigHandler)
	di.GetAppContainer().MustRegister(sql_advisor.NewListSlowQueryAdviceHandler)
	di.GetAppContainer().MustRegister(sql_advisor.NewUpdateSlowQueryAnalysisHandler)
	di.GetAppContainer().MustRegister(sql_advisor.NewSlowQueryAdviceTaskHistoryHandler)

	// OpenAPI handler
	di.GetAppContainer().MustRegister(openapi.NewIDCodec)
	di.GetAppContainer().MustRegister(openapi.NewDataConnectInstanceHandler)
	di.GetAppContainer().MustRegister(openapi.NewDataCancelExecHandler)
	di.GetAppContainer().MustRegister(openapi.NewDataCloseSessionHandler)
	di.GetAppContainer().MustRegister(openapi.NewDataExecCommandsHandler)
	di.GetAppContainer().MustRegister(openapi.NewDataSessionKeepAliveHandler)
	di.GetAppContainer().MustRegister(openapi.NewDataExecCommandSetAsyncHandler)
	di.GetAppContainer().MustRegister(openapi.NewDataDescribeCommandSetHandler)
	di.GetAppContainer().MustRegister(openapi.NewDataGetCommandSetResultHandler)
	di.GetAppContainer().MustRegister(openapi.NewListSlowQueryAdviceApiHandler)
	di.GetAppContainer().MustRegister(openapi.NewSlowQueryAdviceTaskHistoryApiHandler)
	di.GetAppContainer().MustRegister(openapi.NewGenerateSQLFromNLHandler)
	//di.GetAppContainer().MustRegister(openapi.NewDescribeCurrentDialogsHandler)

	// Trx and lock handler
	di.GetAppContainer().MustRegister(trx.NewDescribeTrxAndLocksHandler)
	di.GetAppContainer().MustRegister(trx.NewDescribeDeadlockDetectHandler)
	di.GetAppContainer().MustRegister(trx.NewDescribeDeadlockHandler)
	di.GetAppContainer().MustRegister(trx.NewDescribeTrxSnapshotsHandler)
	di.GetAppContainer().MustRegister(trx.NewDescribeTrxDetailSnapshotHandler)
	di.GetAppContainer().MustRegister(trx.NewDescribeLockCurrentWaitsHandler)
	di.GetAppContainer().MustRegister(trx.NewDescribeLockWaitsDetailSnapshotHandler)

	// mongo console
	di.GetAppContainer().MustRegister(mongo_console.NewDescribeCollectionsHandler)
	di.GetAppContainer().MustRegister(mongo_console.NewDescribeIndexsHandler)
	di.GetAppContainer().MustRegister(mongo_console.NewDescribeMongoDBsHandler)

	// sql audit log handler
	// di.GetAppContainer().MustRegister(logAudit.NewCreateSqlAuditHandler)
	di.GetAppContainer().MustRegister(logAudit.NewCreateSqlAuditExportTaskHandler)
	// di.GetAppContainer().MustRegister(logAudit.NewDeleteSqlAuditHandler)
	di.GetAppContainer().MustRegister(logAudit.NewDescribeAccountTlsStatusHandler)
	di.GetAppContainer().MustRegister(logAudit.NewDescribeInstanceChargeItemUsageHandler)

	di.GetAppContainer().MustRegister(ops.NewDescribeAuditInstanceHandler)
	di.GetAppContainer().MustRegister(ops.NewDescribeClusterHandler)
	di.GetAppContainer().MustRegister(ops.NewDescribeAuditInstancesHandler)
	di.GetAppContainer().MustRegister(ops.NewModifyAuditPodImageHandler)
	di.GetAppContainer().MustRegister(logAudit.NewDescribeLogInstancesHandler)

	// diagnosis
	di.GetAppContainer().MustRegister(diagnosisSvc.NewMemUsage)
	di.GetAppContainer().MustRegister(diagnosisSvc.NewCpuUsage)
	di.GetAppContainer().MustRegister(diagnosisSvc.NewConnectionUsage)
	di.GetAppContainer().MustRegister(diagnosisSvc.NewSlowlogUsage)
	di.GetAppContainer().MustRegister(diagnosisSvc.NewDiskUsage)
	di.GetAppContainer().MustRegister(diagnosisSvc.NewRootDiagItem)

	//di.GetAppContainer().MustRegister(diagnosis.NewDescribeDeadlockHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeDBDiagnosisHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeTableSpaceHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeTableColumnHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeTableIndexHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeDBHealthScoreHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeHealthSummaryHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeDiagItemDetailHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeStorageCapacityHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeDataBaseTablesHandler)
	di.GetAppContainer().MustRegister(space_analysis.NewSpaceAnalysisService)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeTableSpaceAutoIncrHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeDiagRootCauseHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeDiagTypeHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeMonitorByMetricHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewCollectTableSpaceDataHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeSpaceTopHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeTableSpaceDetailHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeIndexSpaceHandler)
	di.GetAppContainer().MustRegister(diagnosis.NewDescribeIndexSpaceDetailHandler)

	di.GetAppContainer().MustRegister(diagnosis.NewDescribeAggregateDiagSlowQueryHandler)
	di.GetAppContainer().MustRegister(troubleshooting.NewRootcauseAnalysisService)

	// metrics
	di.GetAppContainer().MustRegister(metric_data.NewRootMetricDataItem)
	di.GetAppContainer().MustRegister(metric_data.NewConnectionRatioUsage)

	// data migration handler
	di.GetAppContainer().MustRegister(DataMigration.NewCreateDbExportTaskHandler)
	di.GetAppContainer().MustRegister(DataMigration.NewCreateDbImportTaskHandler)
	di.GetAppContainer().MustRegister(DataMigration.NewDescribeDbMigrationTasksHandler)
	di.GetAppContainer().MustRegister(DataMigration.NewDescribeDbMigrationTaskDetailHandler)
	di.GetAppContainer().MustRegister(DataMigration.NewDeleteDbTasksHandler)
	di.GetAppContainer().MustRegister(DataMigration.NewDescribeDbExportDownloadUrlHandler)
	di.GetAppContainer().MustRegister(DataMigration.NewDescribeTempCredentialsHandler)

	di.GetAppContainer().MustRegister(DataMigration.NewCreateMigrationTicketHandler)
	di.GetAppContainer().MustRegister(DataMigration.NewPreCheckMigrationTicketHandler)
	di.GetAppContainer().MustRegister(DataMigration.NewSubmitMigrationTicketHandler)
	di.GetAppContainer().MustRegister(DataMigration.NewExecuteMigrationTicketHandler)
	di.GetAppContainer().MustRegister(DataMigration.NewDescribeMigrationTicketsHandler)
	di.GetAppContainer().MustRegister(DataMigration.NewDescribeMigrationTicketDetailHandler)
	di.GetAppContainer().MustRegister(DataMigration.NewDescribeMigrationPreCheckDetailHandler)
	di.GetAppContainer().MustRegister(DataMigration.NewDescribeDbMigrationTableCharsetHandler)
	di.GetAppContainer().MustRegister(DataMigration.NewCancelMigrationTicketHandler)
	//di.GetAppContainer().MustRegister(DataMigration.NewStopMigrationTicketHandler)
	di.GetAppContainer().MustRegister(data_migration_svc.NewDataMigrationService)

	//online ddl
	di.GetAppContainer().MustRegister(sqltask_svc.NewSqlTaskService)

	// sql ccl handler
	di.GetAppContainer().MustRegister(SqlCcl.NewGetSqlConcurrencyControlStatusHandler)
	di.GetAppContainer().MustRegister(SqlCcl.NewEnableSqlConcurrencyControlHandler)
	di.GetAppContainer().MustRegister(SqlCcl.NewDisableSqlConcurrencyControlHandler)
	di.GetAppContainer().MustRegister(SqlCcl.NewCreateSqlConcurrencyControlRuleHandler)
	di.GetAppContainer().MustRegister(SqlCcl.NewDescribeSqlKeywordsHandler)
	di.GetAppContainer().MustRegister(SqlCcl.NewDeleteSqlConcurrencyControlRuleHandler)
	di.GetAppContainer().MustRegister(SqlCcl.NewDescribeSqlConcurrencyControlRulesHandler)
	di.GetAppContainer().MustRegister(SqlCcl.NewRestartSqlConcurrencyControlRuleHandler)
	di.GetAppContainer().MustRegister(SqlCcl.NewStopSqlConcurrencyControlRuleHandler)
	di.GetAppContainer().MustRegister(SqlCcl.NewDescribeSqlConcurrencyControlRuleDetailHandler)
	di.GetAppContainer().MustRegister(SqlCcl.NewDescribeSqlFingerPrintHandler)
	di.GetAppContainer().MustRegister(SqlCcl.NewDescribeSqlTypeHandler)
	di.GetAppContainer().MustRegister(SqlCcl.NewModifySqlConcurrencyControlRuleHandler)
	// workflow handler
	di.GetAppContainer().MustRegister(workflow.NewDescribeTicketsHandler)
	di.GetAppContainer().MustRegister(workflow.NewDescribeTicketDetailHandler)
	di.GetAppContainer().MustRegister(workflow.NewDescribeTicketLogDetailHandler)
	di.GetAppContainer().MustRegister(workflow.NewCreateTicketHandler)
	di.GetAppContainer().MustRegister(workflow.NewDescribePreCheckDetailHandler)
	di.GetAppContainer().MustRegister(workflow.NewCancelTicketHandler)
	di.GetAppContainer().MustRegister(workflow.NewModifyTicketHandler)
	di.GetAppContainer().MustRegister(workflow.NewExecuteTicketHandler)
	di.GetAppContainer().MustRegister(workflow.NewStopTicketHandler)
	di.GetAppContainer().MustRegister(workflow.NewSubmitTicketHandler)
	di.GetAppContainer().MustRegister(workflow.NewDescribeWorkflowHandler)
	di.GetAppContainer().MustRegister(workflow.NewPreCheckTicketHandler)
	di.GetAppContainer().MustRegister(workflow.NewWorkflowActionHandler)
	di.GetAppContainer().MustRegister(workflow.NewGetArchiveNextSqlHandler)
	di.GetAppContainer().MustRegister(workflowSvc.NewTicketService)
	di.GetAppContainer().MustRegister(workflowSvc.NewTicketWorkflowService)

	// api for bpm callback
	di.GetAppContainer().MustRegister(workflow.NewDescribeTicketDetailProgressHandler)
	di.GetAppContainer().MustRegister(workflow.NewWorkflowActionProgressHandler)

	// user management Handler
	di.GetAppContainer().MustRegister(userMgmt.NewAddUserHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewDescribeUserHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewDescribeUserGroupHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewDescribeUserGroupsHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewDeleteUserHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewCreateUserGroupHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewDeleteUserGroupHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewUpdateUserGroupHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewUserGroupSyncHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewDescribeManagedUsersHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewInitUserManagementAdminHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewUpdateUserHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewGrantUserPrivilegeHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewGrantUserGroupPrivilegeHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewRevokeUserPrivilegeHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewRevokeUserGroupPrivilegeHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewDescribeUserPrivilegesHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewDescribeManagedInstancesHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewDescribeManagedInstanceDatabasesHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewDescribeManagedInstanceTablesHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewDescribeManagedInstanceColumnsHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewDescribeRoleHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewCreatePrivilegeTicketHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewDescribePrivilegeTicketDetailHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewDescribePrivilegeTicketsHandler)
	di.GetAppContainer().MustRegister(userMgmt.NewPrivilegeTicketActionHandler)

	di.GetAppContainer().MustRegister(userSvc.NewUserMgmtService)
	di.GetAppContainer().MustRegister(userSvc.NewPrivilegeService)
	di.GetAppContainer().MustRegister(userSvc.NewMysqlPrivilegeService)
	di.GetAppContainer().MustRegister(userSvc.NewPostgresPrivilegeService)
	di.GetAppContainer().MustRegister(userSvc.NewMssqlPrivilegeService)
	di.GetAppContainer().MustRegister(userSvc.NewPrivilegeTicketService)

	di.GetAppContainer().MustRegister(factor.NewFactor)
	di.GetAppContainer().MustRegister(action.NewAction)

	// dbwinstance
	di.GetAppContainer().MustRegister(dbw_instance.NewDescribeInstanceManagementHandler)
	di.GetAppContainer().MustRegister(dbw_instance.NewEnableInstanceManagementHandler)
	di.GetAppContainer().MustRegister(dbw_instance.NewUpdateInstanceManagementConfigHandler)
	di.GetAppContainer().MustRegister(dbw_instance.NewDisableInstanceManagementHandler)
	di.GetAppContainer().MustRegister(dbw_instance.NewSyncDBInstancesHandler)
	di.GetAppContainer().MustRegister(dbw_instance.NewSyncDBInstanceHandler)
	di.GetAppContainer().MustRegister(dbw_instance.NewDescribeRealTimeInstancesHandler)
	di.GetAppContainer().MustRegister(dbw_instance.NewDeleteDBInstanceHandler)
	di.GetAppContainer().MustRegister(dbw_instance.NewDescribeRecentlyUsedDBInstancesHandler)
	di.GetAppContainer().MustRegister(dbw_instance.NewDeleteRecentlyUsedDBInstancesHandler)
	di.GetAppContainer().MustRegister(dbw_instance.NewAddRecentlyUsedDBInstanceHandler)
	di.GetAppContainer().MustRegister(dbw_instance.NewCheckInstanceExistHandler)
	di.GetAppContainer().MustRegister(dbwinstance.NewDbwInstanceService)
	di.GetAppContainer().MustRegister(dbw_syncer_svc.NewDbwSyncerService)

	// dbw_inspection
	di.GetAppContainer().MustRegister(inspectionSvc.NewInspectionService)
	di.GetAppContainer().MustRegister(inspect_config.NewInspectionConfigService)
	di.GetAppContainer().MustRegister(inspection.NewCreateAutomaticInspectionHandler)
	di.GetAppContainer().MustRegister(inspection.NewCreateManualInspectionHandler)
	di.GetAppContainer().MustRegister(inspection.NewDescribeDBInspectionReportHandler)
	di.GetAppContainer().MustRegister(inspection.NewDescribeDBInspectionScoreHandler)
	di.GetAppContainer().MustRegister(inspection.NewDescribeDBInspectionsHandler)
	di.GetAppContainer().MustRegister(inspection.NewModifyInspectionConfigHandler)
	di.GetAppContainer().MustRegister(inspection.NewQueryInspectionConfigHandler)

	// autoscale_bandwidth
	di.GetAppContainer().MustRegister(bw_impl.NewAutoScaleService)
	di.GetAppContainer().MustRegister(autoscale.NewAutoScaleHandler)
	di.GetAppContainer().MustRegister(autoscale.NewDescribeAutoScaleEventsHandler)
	di.GetAppContainer().MustRegister(autoscale.NewDescribeAutoScaleRulesHandler)
	di.GetAppContainer().MustRegister(autoscale.NewDescribeAutoScaleInstanceSpecHandler)

	// autoscale_disk
	di.GetAppContainer().MustRegister(bw_impl.NewDiskAutoScaleService)
	di.GetAppContainer().MustRegister(autoscale.NewDescribeAutoScaleDiskEventsHandler)
	di.GetAppContainer().MustRegister(autoscale.NewDescribeAutoScaleDiskHandler)
	di.GetAppContainer().MustRegister(autoscale.NewModifyAutoScaleDiskHandler)
	di.GetAppContainer().MustRegister(autoscale.NewModifyDBAutoStorageScalingHandler)

	// autoscale_cpu
	di.GetAppContainer().MustRegister(autoscale.NewDescribeDBAutoScaleConfigHandler)
	di.GetAppContainer().MustRegister(autoscale.NewModifyDBAutoScaleConfigHandler)
	di.GetAppContainer().MustRegister(autoscale.NewModifyDBLocalSpecManuallyHandler)
	di.GetAppContainer().MustRegister(autoscale.NewDescribeDBAutoScaleEventsHandler)

	// security rules
	di.GetAppContainer().MustRegister(securityrule.NewDescribeSecurityGroupDetailHandler)
	di.GetAppContainer().MustRegister(securityrule.NewDescribeSecurityGroupsHandler)
	di.GetAppContainer().MustRegister(securityrule.NewDescribeSecurityRuleHandler)
	di.GetAppContainer().MustRegister(securityrule.NewEnableSecurityRuleHandler)
	di.GetAppContainer().MustRegister(securityrule.NewDisableSecurityRuleHandler)
	di.GetAppContainer().MustRegister(securityrule.NewAddSecurityRuleGroupHandler)
	di.GetAppContainer().MustRegister(securityrule.NewEditSecurityRuleGroupHandler)
	di.GetAppContainer().MustRegister(securityrule.NewDeleteSecurityRuleGroupHandler)
	di.GetAppContainer().MustRegister(securityrule.NewAddCustomSecurityRuleHandler)
	di.GetAppContainer().MustRegister(securityrule.NewDescribeCustomRuleHandler)
	di.GetAppContainer().MustRegister(securityrule.NewDeleteCustomRuleHandler)
	di.GetAppContainer().MustRegister(securityrule.NewDescribeActionsHandler)
	di.GetAppContainer().MustRegister(securityrule.NewDescribeFactorsHandler)
	di.GetAppContainer().MustRegister(securityrule.NewEditCustomSecurityRuleHandler)
	di.GetAppContainer().MustRegister(securityrule.NewDescribeRuleConfigurationHandler)
	di.GetAppContainer().MustRegister(securityrule.NewUpdateSecurityRuleHandler)
	di.GetAppContainer().MustRegister(securityrule.NewDescribeRuleExecuteRecordDetailHandler)
	di.GetAppContainer().MustRegister(securityrule.NewDescribeRuleExecuteRecordSummaryResultHandler)
	di.GetAppContainer().MustRegister(securityrule.NewDescribeOnlineDDLSecurityRuleHandler)
	di.GetAppContainer().MustRegister(securityrule.NewUpdateOnlineDDLSecurityRuleHandler)
	di.GetAppContainer().MustRegister(group.NewMysqlMultiCloudGroupService)
	di.GetAppContainer().MustRegister(group.NewVedbMultiCloudGroupService)
	di.GetAppContainer().MustRegister(group.NewShardingMultiCloudGroupService)
	di.GetAppContainer().MustRegister(group.NewByteRDSGroupService)
	di.GetAppContainer().MustRegister(group.NewRootGroupService)
	di.GetAppContainer().MustRegister(group.NewInitTemplateService)
	di.GetAppContainer().MustRegister(audit.NewLogCollectorService)

	// abnormalDetection
	di.GetAppContainer().MustRegister(abnormal_detection_handler.NewDescribeAbnormalDetectionConfigHandler)
	di.GetAppContainer().MustRegister(abnormal_detection_handler.NewDescribeAbnormalDetectionDetailHandler)
	di.GetAppContainer().MustRegister(abnormal_detection_handler.NewModifyAbnormalDetectionConfigHandler)
	di.GetAppContainer().MustRegister(abnormal_detection_handler.NewDescribeAbnormalDetectionInfoHandler)

	//dbw-collector
	di.GetAppContainer().MustRegister(collector.NewDescribeInstanceListHandler)
	di.GetAppContainer().MustRegister(collector.NewDescribeDataSourceHandlerHandler)

	// sqlAdvisor handler
	di.GetAppContainer().MustRegister(sql_advisor.NewDescribeInstanceVariablesHandler)
	di.GetAppContainer().MustRegister(sql_advisor.NewDescribePrimaryKeyRangeHandler)
	di.GetAppContainer().MustRegister(sql_advisor.NewDescribeSampleDataHandler)
	di.GetAppContainer().MustRegister(sql_advisor.NewDescribeSQLAdvisorTableMetaHandler)
	di.GetAppContainer().MustRegister(sql_advisor.NewCreateSQLAdvisorTaskHandler)
	di.GetAppContainer().MustRegister(sql_advisor.NewDescribeSQLAdvisorTaskHandler)
	di.GetAppContainer().MustRegister(sql_advisor.NewGetSQLAdvisorProtocolHandler)
	di.GetAppContainer().MustRegister(sql_advisor.NewAgreeSQLAdvisorProtocolHandler)

	// slow advice
	di.GetAppContainer().MustRegister(slow_advice.NewSlowAdviceConfigService)
	di.GetAppContainer().MustRegister(slow_advice.NewListSlowQueryAdvice)
	di.GetAppContainer().MustRegister(sql_advisor_svc.NewSqlAdvisorService)
	di.GetAppContainer().MustRegister(auto_create_index_event.NewAutoCreateIndexEventServiceService)
	di.GetAppContainer().MustRegister(sql_advisor.NewDescribeAutoCreateIndexEventsHandler)
	di.GetAppContainer().MustRegister(auto_create_index.NewCronActor, dig.Group("singleton-actor"))
	di.GetAppContainer().MustRegister(sql_advisor.NewDescribeMaintenanceWindowHandler)

	//sqlReview handler + DAL
	di.GetAppContainer().MustRegister(dal.NewSqlReviewDAL)
	di.GetAppContainer().MustRegister(dal.NewSqlReviewDetailDAL)
	di.GetAppContainer().MustRegister(sql_review.NewDescribeSqlReviewListHandler)
	di.GetAppContainer().MustRegister(sql_review.NewAddSqlReviewHandler)
	di.GetAppContainer().MustRegister(sql_review.NewDescribeSqlReviewDetailListHandler)
	di.GetAppContainer().MustRegister(sql_review.NewDescribeSqlReviewDetailSummaryResultHandler)
	di.GetAppContainer().MustRegister(sql_review.NewDescribeReviewDetailActionListHandler)
	di.GetAppContainer().MustRegister(sql_review.NewSubmitApproveSqlReviewHandler)
	di.GetAppContainer().MustRegister(sql_review.NewSqlReviewBySingleHandler)
	di.GetAppContainer().MustRegister(sql_review_svc.NewSqlReviewService)

	//operateRecord handler + DAL
	di.GetAppContainer().MustRegister(dal.NewConsoleOperateRecordDAL)
	di.GetAppContainer().MustRegister(dal.NewDasOperateRecordDAL)
	di.GetAppContainer().MustRegister(operate_record_svc.NewOperateRecordService)
	di.GetAppContainer().MustRegister(operate_record.NewDescribeConsoleRecordListHandler)
	di.GetAppContainer().MustRegister(operate_record.NewDescribeTicketRecordListHandler)
	di.GetAppContainer().MustRegister(operate_record.NewDescribeDasOperationTaskListHandler)

	//data copilot
	di.GetAppContainer().MustRegister(dal.NewCopilotChatMessageDAL)
	di.GetAppContainer().MustRegister(dal.NewCopilotChatTaskDAL)
	di.GetAppContainer().MustRegister(dal.NewCopilotChatDAL)
	di.GetAppContainer().MustRegister(data_copilot_service.NewFornaxService)
	di.GetAppContainer().MustRegister(data_copilot.NewExecutePromptHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewExecuteAssistantPromptHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewCopilotButtonGenerationHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewDescribeChatMessageHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewDeleteCopilotChatHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewCreateCopilotChatHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewDescribeCopilotChatListHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewDescribeCopilotTaskListHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewUpdateCopilotChatNameHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewRateModelReplyHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewCreateOrderContentFromMessagesHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewExecuteProxyAgentHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewExecuteSlaveAgentHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewExecuteMonitorAgentHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewTriggerButtonHandler)
	di.GetAppContainer().MustRegister(data_copilot_service.NewCopilotFunctionServiceHandler)
	di.GetAppContainer().MustRegister(function_call.NewRootFunctionCallService)
	di.GetAppContainer().MustRegister(function_call.NewMySQLFunctionCallService)
	di.GetAppContainer().MustRegister(function_call.NewVedbFunctionCallService)
	di.GetAppContainer().MustRegister(function_call.NewPostgresFunctionCallService)
	di.GetAppContainer().MustRegister(function_call.NewRedisFunctionCallService)
	di.GetAppContainer().MustRegister(function_call.NewMongoFunctionCallService)
	di.GetAppContainer().MustRegister(function_call.NewByteRDSFunctionCallService)
	di.GetAppContainer().MustRegister(agent_service2.NewMonitorAgentService)
	di.GetAppContainer().MustRegister(agent_service2.NewProxyAgentService)
	di.GetAppContainer().MustRegister(agent_service2.NewDiagnosisAgentService)
	di.GetAppContainer().MustRegister(agent_utils.NewAgentToolServiceHandler)

	// err logs
	di.GetAppContainer().MustRegister(errlog.NewDescribeErrLogsHandler)
	di.GetAppContainer().MustRegister(errlog.NewCreateLogExportTaskHandler)
	di.GetAppContainer().MustRegister(errlog.NewCreateErrLogsExportTaskHandler)
	di.GetAppContainer().MustRegister(errlog.NewDescribeErrLogsExportTasksHandler)

	di.GetAppContainer().MustRegister(errlog.NewGetLogFilesHandler)

	// location
	di.GetAppContainer().MustRegister(bizLoc.NewLocProvider)
	di.GetAppContainer().MustRegister(loc.NewDescribeAZsHandler)

	registerDBGPTDalAndRepo()
	registerDBGPTHandler()

	// fullSql
	registerFullSql()
	di.GetAppContainer().MustRegister(index.NewGenTlsService)

	registerApprovalFlow()
	registerDataArchive()

	//sql kill
	di.GetAppContainer().MustRegister(SqlKill.NewDescribeAutoKillSessionConfigHandler)
	di.GetAppContainer().MustRegister(SqlKill.NewModifyAutoKillSessionConfigHandler)
	di.GetAppContainer().MustRegister(SqlKill.NewCreateSqlKillRuleHandler)
	di.GetAppContainer().MustRegister(SqlKill.NewDeleteSqlKillRuleHandler)
	di.GetAppContainer().MustRegister(SqlKill.NewStopSqlKillRuleHandler)
	di.GetAppContainer().MustRegister(SqlKill.NewDescribeSqlKillRulesHandler)

	// conn-pool
	di.GetAppContainer().MustRegister(datasource.NewPool)
	//framework.Init(&config.GlobalConf, framework.DisableComponent(fwcfg.ComponentGin))
	//di.GetAppContainer().MustRegister(config.New)
	di.GetAppContainer().MustRegister(server.NewHttpServer)
	di.GetAppContainer().MustRegister(NewApp)

	// sla svc
	di.GetAppContainer().MustRegister(sla.NewSlaSvc)
	registerTaskFlow()

	// monitor
	di.GetAppContainer().MustRegister(adsvc.NewMetricDataService)
	di.GetAppContainer().MustRegister(abnormal_diagnosis.NewGetMetricDataHandler)
	di.GetAppContainer().MustRegister(abnormal_diagnosis.NewGetMetricItemsHandler)

	// international
	di.GetAppContainer().MustRegister(i18n.NewI18nService)
	registerConsoleConnEnv()
	registerFavouriteSQL()
	registerMetaStore()

	registerDbwTicket()
	registerDair()
}

func registerTag() {
	di.GetAppContainer().MustRegister(tag_handler.NewAddTagsToResourceHandler)
	di.GetAppContainer().MustRegister(tag_handler.NewRemoveTagsFromResourceHandler)
	di.GetAppContainer().MustRegister(tag_handler.NewDescribeResourceTagsHandler)
}

func registerDbwTicket() {
	di.GetAppContainer().MustRegister(online_ddl_service.NewOnlineDDlService)
	di.GetAppContainer().MustRegister(dbw_ticket_service.NewDbwTicketService)
	di.GetAppContainer().MustRegister(dbw_ticket_service.NewTicketCommonService)

	di.GetAppContainer().MustRegister(data_backup.NewBackupService)
	di.GetAppContainer().MustRegister(tos_service.NewTosBackupImpl)

	di.GetAppContainer().MustRegister(runtime_k8s.NewK8sRuntime)
	di.GetAppContainer().MustRegister(dbw_runtime.NewRuntimeService)

	di.GetAppContainer().MustRegister(repository.NewTicketRepo)
}

func registerDBGPTDalAndRepo() {
	di.GetAppContainer().MustRegister(chat.NewChatRepo)
	di.GetAppContainer().MustRegister(chat.NewMessageRepo)
	di.GetAppContainer().MustRegister(repository.NewUserProtocolRepo)
	di.GetAppContainer().MustRegister(dal.NewChatDAL)
	di.GetAppContainer().MustRegister(dal.NewMessageDAL)
	di.GetAppContainer().MustRegister(dal.NewReportDAL)
	di.GetAppContainer().MustRegister(dal.NewUserProtocolDAL)
}

func registerDBGPTHandler() {
	di.GetAppContainer().MustRegister(dbgptsvc.NewByteBrainChatService)
	di.GetAppContainer().MustRegister(dbgptsvc.NewRiskControlService)
	di.GetAppContainer().MustRegister(dbgptsvc.NewByteBrainNL2SQLService)
	di.GetAppContainer().MustRegister(dbgpt.NewChatHandler)
	di.GetAppContainer().MustRegister(dbgpt.NewCreateChatHandler)
	di.GetAppContainer().MustRegister(dbgpt.NewDeleteChatHandler)
	di.GetAppContainer().MustRegister(dbgpt.NewListChatHistoryHandler)
	di.GetAppContainer().MustRegister(dbgpt.NewListChatsHandler)
	di.GetAppContainer().MustRegister(dbgpt.NewReportHandler)
	di.GetAppContainer().MustRegister(dbgpt.NewSqlAssistantHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewAgreeSqlAssistantProtocolHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewGetSqlAssistantProtocolHandler)
	di.GetAppContainer().MustRegister(dbgpt.NewSqlCorrectHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewAgreeSqlAssistantProtocolHandler)
	di.GetAppContainer().MustRegister(data_copilot.NewGetSqlAssistantProtocolHandler)
}

func registerMultiCloud() {
	di.GetAppContainer().MustRegister(multicloud.NewDescribeSensitiveDatabasesHandler)
	di.GetAppContainer().MustRegister(multicloud.NewAddSensitiveDatabaseHandler)
	di.GetAppContainer().MustRegister(dal.NewSensitiveDatabaseDAL)
	di.GetAppContainer().MustRegister(repository.NewSensitiveDatabaseRepo)
	di.GetAppContainer().MustRegister(iam_svc.NewIAMService)
	di.GetAppContainer().MustRegister(control.NewInstanceManager)
	di.GetAppContainer().MustRegister(dal.NewDbwInstanceDAL)
	di.GetAppContainer().MustRegister(instance.NewCtrlInstanceRepo)
	di.GetAppContainer().MustRegister(user.NewUserManager)
	di.GetAppContainer().MustRegister(database.NewDatabaseManager)
	di.GetAppContainer().MustRegister(privilege.NewUserRepo)
	di.GetAppContainer().MustRegister(privilege.NewDatabasePrivRepo)
	di.GetAppContainer().MustRegister(multicloud.NewAddControlInstanceHandler)
	di.GetAppContainer().MustRegister(multicloud.NewDescribeDbTreeMountInfoInHandler)
	di.GetAppContainer().MustRegister(multicloud.NewDescribeInstanceInfoHandler)
}

func registerTaskFlow() {
	di.GetAppContainer().MustRegister(dal.NewTaskFlowDal)
	di.GetAppContainer().MustRegister(dal.NewTaskFlowJobDal)
	di.GetAppContainer().MustRegister(repository.NewTaskFlowRepo)
	di.GetAppContainer().MustRegister(repository.NewTaskFlowJobRepo)
	di.GetAppContainer().MustRegister(taskflow.NewCreateTaskFlowHandler)
	di.GetAppContainer().MustRegister(taskflow.NewDeleteTaskFlowHandler)
	di.GetAppContainer().MustRegister(taskflow.NewDescribeTaskFlowHandler)
	di.GetAppContainer().MustRegister(taskflow.NewDescribeTaskExecuteRecordsHandler)
	di.GetAppContainer().MustRegister(taskflow.NewDescribeTaskFlowsHandler)
	di.GetAppContainer().MustRegister(taskflow.NewDescribeInstanceDatabasesHandler)
	di.GetAppContainer().MustRegister(taskflow.NewUpdateTaskFlowHandler)
	di.GetAppContainer().MustRegister(taskflow.NewDescribeExecuteLogsHandler)
	di.GetAppContainer().MustRegister(taskflow.NewTriggerTaskFlowHandler)

	di.GetAppContainer().MustRegister(tfactor.NewTaskFlowScheduleActor, dig.Group(PersistActor))
	di.GetAppContainer().MustRegister(tfactor.NewJobActor, dig.Group(PersistActor))
	di.GetAppContainer().MustRegister(cmdset.NewCommandSetExecutorService)
}

func registerFullSql() {
	di.GetAppContainer().MustRegister(full_sql.NewCreateFullSqlOrderHandler)
	di.GetAppContainer().MustRegister(full_sql.NewCreateTerminateFullSqlOrderHandler)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeAggregationSQLTemplatesHandler)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeFullSQLDetailHandler)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeSQLExecItemMetricHandler)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeSQLExecNumHandler)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeSQLExecTimeDetailHandler)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeSQLExecTimeDistributionHandler)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeSqlTemplatesContrastHandler)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeTopNFullSQLDetailHandler)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeAggregationSQLTableHandler)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeTableMetricHandler)
	di.GetAppContainer().MustRegister(full_sql_service.NewAllSqlService)
	di.GetAppContainer().MustRegister(zkconfig.NewFullSqlConfigService)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeFullSqlStatusHandler)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeFullSqlCandidateHandler)
	di.GetAppContainer().MustRegister(full_sql.NewModifyFullSqlConfigHandler)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeFullSqlConfigHandler)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeFullSQLFingerprintExampleHandler)
	di.GetAppContainer().MustRegister(ops.NewDeleteFullsqlResourceHandler)
	di.GetAppContainer().MustRegister(ops.NewUpgradeFullsqlTableAggrHandler)
	di.GetAppContainer().MustRegister(full_sql.NewPreCheckCreateFullSqlHandler)
	di.GetAppContainer().MustRegister(ops.NewBatchOpenInstanceFunctionHandler)
	di.GetAppContainer().MustRegister(ops.NewUpgradeFullsqlHandler)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeInstanceLogNodesHandler)
	di.GetAppContainer().MustRegister(full_sql.NewDescribeFullSQLFingerprintExampleHandler)
	di.GetAppContainer().MustRegister(collection_sqlfingerprint.NewAddFullSqlCollectionFingerprintHandler)
	di.GetAppContainer().MustRegister(collection_sqlfingerprint.NewDeleteFullSqlCollectionFingerprintHandler)
	di.GetAppContainer().MustRegister(export_file.NewCreateFullSqlExportTaskHandler)
	di.GetAppContainer().MustRegister(export_file.NewDescribeFullSqlExportTasksHandler)

	di.GetAppContainer().MustRegister(full_sql_tls.NewDescribeHistogramV1DBWHandler)
	di.GetAppContainer().MustRegister(full_sql_tls.NewDescribeIndexDBWHandler)
	di.GetAppContainer().MustRegister(full_sql_tls.NewDescribeLogContextDBWHandler)
	di.GetAppContainer().MustRegister(full_sql_tls.NewDescribeTopicDBWHandler)
	di.GetAppContainer().MustRegister(full_sql_tls.NewSearchLogDBWHandler)
	di.GetAppContainer().MustRegister(ops.NewUpgradeFullsqlHandler)
}

func registerApprovalFlow() {
	di.GetAppContainer().MustRegister(dal.NewApprovalFlowConfigDAL)
	di.GetAppContainer().MustRegister(dal.NewApprovalFlowHistoryDAL)
	di.GetAppContainer().MustRegister(dal.NewApprovalFlowNodeDAL)
	di.GetAppContainer().MustRegister(dal.NewApprovalFlowDAL)
	di.GetAppContainer().MustRegister(dal.NewApprovalFlowTemplateDAL)

	di.GetAppContainer().MustRegister(repository.NewApprovalFlowRepo)

	di.GetAppContainer().MustRegister(approval_flow.NewCreateApprovalFlowConfigHandler)
	di.GetAppContainer().MustRegister(approval_flow.NewCreateApprovalNodeHandler)
	di.GetAppContainer().MustRegister(approval_flow.NewDeleteApprovalFlowConfigHandler)
	di.GetAppContainer().MustRegister(approval_flow.NewDeleteApprovalNodeHandler)
	di.GetAppContainer().MustRegister(approval_flow.NewDescribeApprovalFlowConfigHandler)
	di.GetAppContainer().MustRegister(approval_flow.NewListApprovalFlowConfigHandler)
	di.GetAppContainer().MustRegister(approval_flow.NewListApprovalAssociatedInstanceHandler)
	di.GetAppContainer().MustRegister(approval_flow.NewListApprovalNodeHandler)
	di.GetAppContainer().MustRegister(approval_flow.NewModifyApprovalFlowConfigHandler)
	di.GetAppContainer().MustRegister(approval_flow.NewModifyApprovalNodeHandler)
	di.GetAppContainer().MustRegister(approval_flow.NewDescribeApprovalFlowLogsHandler)

	di.GetAppContainer().MustRegister(approval_flow_service.NewApprovalFlowService)
}

func registerDataArchive() {
	di.GetAppContainer().MustRegister(dal.NewDataArchiveConfigDAL)
	di.GetAppContainer().MustRegister(dal.NewDataArchiveTaskDAL)

	di.GetAppContainer().MustRegister(repository.NewDataArchiveRepo)

	di.GetAppContainer().MustRegister(data_archive_handler.NewDescribeArchiveConfigsHandler)
	di.GetAppContainer().MustRegister(data_archive_handler.NewDescribeArchiveTaskLogDetailHandler)
	di.GetAppContainer().MustRegister(data_archive_handler.NewDescribeArchiveTasksHandler)

	// di.GetAppContainer().MustRegister(cx.NewTestApiHandler)
	di.GetAppContainer().MustRegister(data_archive.NewDataArchiveService)
}

func registerConsoleConnEnv() {
	di.GetAppContainer().MustRegister(repository.NewConsoleConnEnvRepo)
	di.GetAppContainer().MustRegister(console.NewSaveConsolePasswordHandler)
	di.GetAppContainer().MustRegister(console.NewUpdateConsoleConnEnvHandler)
	di.GetAppContainer().MustRegister(console.NewDescribeConsoleConnEnvsHandler)
	di.GetAppContainer().MustRegister(dal.NewConsoleConnEnvDAL)
	di.GetAppContainer().MustRegister(console.NewForgetConsolePasswordHandler)
}

func registerFavouriteSQL() {
	di.GetAppContainer().MustRegister(sql.NewAddMyFavouriteSQLHandler)
	di.GetAppContainer().MustRegister(sql.NewDescribeMyFavouriteSQLHandler)
	di.GetAppContainer().MustRegister(sql.NewUpdateMyFavouriteSQLHandler)
	di.GetAppContainer().MustRegister(sql.NewDeleteMyFavouriteSQLHandler)
	di.GetAppContainer().MustRegister(repository.NewFavouriteSQLRepo)
	di.GetAppContainer().MustRegister(dal.NewFavouriteSQLDal)
}

func registerMetaStore() {
	di.GetAppContainer().MustRegister(console.NewRegisterDBInstanceHandler)
}

func registerDair() {
	di.GetAppContainer().MustRegister(console.NewDescribeBranchesHandler)
	di.GetAppContainer().MustRegister(console.NewGetRestoreWindowHandler)
}

const (
	PersistActor = "persist-actor"
)

func setupActorSystem() {
	di.GetAppContainer().MustRegister(func(p struct {
		dig.In
		PersistActors   []types.VirtualPersistenceProducer `group:"persist-actor"`
		VirtualActors   []types.VirtualProducer            `group:"virtual-actor"`
		LocalActors     []types.SingletonProducer          `group:"local-actor"`
		SingletonActors []types.SingletonProducer          `group:"singleton-actor"`
	}) actorsystem.ActorSystem {
		system := actorsystem.Use(`default`)
		fp.StreamOf(p.PersistActors).
			Foreach(func(vp types.VirtualPersistenceProducer) {
				system.RegisterPersistActor(vp.Kind, vp.Producer)
			}).
			Run()
		fp.StreamOf(p.VirtualActors).
			Foreach(func(vp types.VirtualProducer) {
				system.RegisterVirtualActor(vp.Kind, vp.Producer)
			}).
			Run()
		fp.StreamOf(p.LocalActors).
			Foreach(func(vp types.SingletonProducer) {
				system.RegisterLocalActor(vp.Name, vp.Producer)
			}).
			Run()
		fp.StreamOf(p.SingletonActors).
			Foreach(func(vp types.SingletonProducer) {
				system.RegisterSingletonActor(vp.Name, vp.Producer)
			}).
			Run()
		return system
	})

	di.GetAppContainer().MustRegister(func(p struct {
		dig.In
		Sys actorsystem.ActorSystem
	}) persistence.ActorStoragePeeker {
		return actorsystem.Use(`default`).Peeker()
	})

	if utils.IsByteCloud() {
		di.GetAppContainer().MustRegister(audit_actor.NewInnerFullSqlCreateLifecycleActor, dig.Group(PersistActor))
		di.GetAppContainer().MustRegister(audit_actor.NewInnerFullSqlDeleteLifecycleActor, dig.Group(PersistActor))
	} else {
		di.GetAppContainer().MustRegister(actors.NewDeployActor, dig.Group(`persist-actor`))
		di.GetAppContainer().MustRegister(tag.NewTagService)
		di.GetAppContainer().MustRegister(project.NewProjectService)
		di.GetAppContainer().MustRegister(billActor.NewBillingScheduleCheckActor, dig.Group(PersistActor))
		di.GetAppContainer().MustRegister(billActor.NewScheduleInstanceCheckActor, dig.Group(PersistActor))

		di.GetAppContainer().MustRegister(audit_actor.NewFullSqlCreateLifecycleActor, dig.Group(PersistActor))
		di.GetAppContainer().MustRegister(audit_actor.NewFullSqlDeleteLifecycleActor, dig.Group(PersistActor))
		di.GetAppContainer().MustRegister(audit_actor.NewRedisCreateLifecycleActor, dig.Group(PersistActor))
		di.GetAppContainer().MustRegister(audit_actor.NewAuditCreateLifecycleActor, dig.Group(PersistActor))
		di.GetAppContainer().MustRegister(audit_actor.NewAuditDeleteLifecycleActor, dig.Group(PersistActor))
		di.GetAppContainer().MustRegister(audit_actor.NewAuditMysqlCreateLifecycleActor, dig.Group(PersistActor))
		di.GetAppContainer().MustRegister(audit_actor.NewMongoCreateLifecycleActor, dig.Group(PersistActor))

		di.GetAppContainer().MustRegister(sla_actor.NewSLAAuditMetricReporterActor, dig.Group(PersistActor))
	}
	di.GetAppContainer().MustRegister(infactor.NewActorClient)
	di.GetAppContainer().MustRegister(actors.NewSessionActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(sql_console.NewQueryResultActor, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(command.NewCommandSetResultBufferActor, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(actors.NewUserProtocolActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(shuttleActor.NewShuttleActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(shuttleActor.NewShuttleMgrActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(actors.NewIDGenerator, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(actors.NewCounter, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(actors.NewDiscoActor, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(actors.NewSessionMgrActor, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(c3Actor.NewApolloActor, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(actors.NewMonitorVirtualActor, dig.Group("local-actor"))
	di.GetAppContainer().MustRegister(dialogActor.NewDialogMgrActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(dialogActor.NewDialogActor, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(tenant.NewTenantMgrActor, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(cleanActor.NewCleanActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(actors.NewLogAnalysisActor, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(inspectionActor.NewTaskActor, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(data_migration.NewTaskActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(data_migration.NewTaskInspectionActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(ticket.NewExecTicketActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(ticket.NewOnlineDDLTicketActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(ticket.NewVeDBDDLTicketActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(ticket.NewFreeLockDMLActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(ticket.NewTicketInspectionActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(ticket.NewDataCleanTicketActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(ticket.NewShardingDDLTicketActor, dig.Group(`persist-actor`))

	di.GetAppContainer().MustRegister(sharding_free_lock_dml.NewShardingFreeLockDMLActor, dig.Group(`persist-actor`))

	di.GetAppContainer().MustRegister(actors.NewCCLRuleActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(sqltask_actor.NewSqlTaskActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(cclrule.NewCCLRuleInspectionActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(inspectionActor.NewCronActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(inspectionActor.NewCheckInspectionActor, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(bandwidth.NewBandWidthScaleActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(user_mgmt.NewUserManagementActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(scale.NewAutoScaleActor, dig.Group(`persist-actor`))

	di.GetAppContainer().MustRegister(dbgptactor.NewSqlAssistantActor, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(dbgptactor.NewNL2SQLSessionActor, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(abnormal_detection.NewDetectionConsumerActor, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(abnormal_detection.NewDetectionProducerActor, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(abnormal_detection.NewCloudMonitorActor, dig.Group(`virtual-actor`))
	di.GetAppContainer().MustRegister(audit_actor.NewMysqlFullSqlUpgradeTableAggrLifecycleActor, dig.Group(PersistActor))
	di.GetAppContainer().MustRegister(audit_actor.NewFullSqlDeleteResourceLifecycleActor, dig.Group(PersistActor))
	di.GetAppContainer().MustRegister(audit_actor.NewMysqlFullSqlUpgradeIndexLifecycleActor, dig.Group(PersistActor))
	di.GetAppContainer().MustRegister(sql_review_actor.NewSqlReviewActor, dig.Group("local-actor"))

	di.GetAppContainer().MustRegister(data_archive_actor.NewArchiveConfigActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(data_archive_actor.NewDataArchiveActor, dig.Group(`persist-actor`))

	di.GetAppContainer().MustRegister(online_ddl_actor.NewOnlineDDLActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(dbw_ticket_actor.NewDbwTicketActor, dig.Group(`persist-actor`))
	di.GetAppContainer().MustRegister(dbw_ticket_actor.NewDbwInstanceTicketActor, dig.Group(`persist-actor`))

	// message
	di.GetAppContainer().MustRegister(message.NewMsgService)

	// lark
	di.GetAppContainer().MustRegister(message.NewLarkService)
}
