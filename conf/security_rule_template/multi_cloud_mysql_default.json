{"instance_type": "MySQL", "template_name": "多云管理平台默认规则", "rules": [{"name": "禁止CREATE_TABLE在数据库交互台直接执行，请提交DDL工单执行", "func": "IsAllowCreateTableExecDirect", "dsl_flow": {"factor_name": "sql_type", "factor_value": "CREATE_TABLE", "action_yes": "reject_execute", "action_no": ""}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "categoryList": ["SqlQuery"], "description": "禁止CREATE_TABLE在数据库交互台直接执行，请提交DDL工单执行", "enabled": 1, "rule_type": 0}, {"name": "禁止DROP_TABLE在数据库交互台直接执行，请提交DDL工单执行", "func": "IsAllowDropTableExecDirect", "dsl_flow": {"factor_name": "sql_type", "factor_value": "DROP_TABLE", "action_yes": "reject_execute", "action_no": ""}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "categoryList": ["SqlQuery"], "description": "禁止DROP_TABLE在数据库交互台直接执行，请提交DDL工单执行", "enabled": 1, "rule_type": 0}, {"name": "禁止TRUNCATE在数据库交互台直接执行，请提交DDL工单执行", "func": "IsAllowTruncateTableExecDirect", "dsl_flow": {"factor_name": "sql_type", "factor_value": "TRUNCATE", "action_yes": "reject_execute", "action_no": ""}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "categoryList": ["SqlQuery"], "description": "禁止TRUNCATE在数据库交互台直接执行，请提交DDL工单执行", "enabled": 1, "rule_type": 0}, {"name": "禁止INSERT在数据库交互台直接执行，请提交DML工单执行", "func": "IsAllowInsertExecDirect", "dsl_flow": {"factor_name": "sql_type", "factor_value": "INSERT", "action_yes": "reject_execute", "action_no": ""}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "categoryList": ["SqlQuery"], "description": "禁止INSERT在数据库交互台直接执行，请提交DML工单执行", "enabled": 1, "rule_type": 0}, {"name": "禁止UPDATE在SQL控制台直接执行，请提交DML工单执行", "func": "IsAllowUpdateExecDirect", "dsl_flow": {"factor_name": "sql_type", "factor_value": "UPDATE", "action_yes": "reject_execute", "action_no": ""}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "categoryList": ["SqlQuery"], "description": "禁止UPDATE在SQL控制台直接执行，请提交DML工单执行", "enabled": 1, "rule_type": 0}, {"name": "禁止DELETE在SQL控制台直接执行，请提交DML工单执行", "func": "IsAllowDeleteExecDirect", "dsl_flow": {"factor_name": "sql_type", "factor_value": "DELETE", "action_yes": "reject_execute", "action_no": ""}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "categoryList": ["SqlQuery"], "description": "禁止DELETE在SQL控制台直接执行，请提交DML工单执行", "enabled": 1, "rule_type": 0}, {"name": "禁止ALTER_TABLE在数据库交互台直接执行，请提交DDL工单执行", "func": "IsAllowAlterTableExecDirect", "dsl_flow": {"factor_name": "sql_type", "factor_value": "ALTER_TABLE", "action_yes": "reject_execute", "action_no": ""}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "categoryList": ["SqlQuery"], "description": "禁止ALTER_TABLE在数据库交互台直接执行，请提交DDL工单执行", "enabled": 1, "rule_type": 0}, {"name": "禁止KILL在数据库交互台直接执行，请提交DML工单执行", "func": "IsAllowKillExecDirect", "dsl_flow": {"factor_name": "sql_type", "factor_value": "KILL", "action_yes": "reject_execute", "action_no": ""}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "categoryList": ["SqlQuery"], "description": "禁止KILL在数据库交互台直接执行，请提交DML工单执行", "enabled": 1, "rule_type": 0}, {"name": "禁止存储过程在数据库交互台直接创建，请提交DDL工单执行", "func": "IsAllowCreateProcessExecDirect", "dsl_flow": {"factor_name": "sql_type", "factor_value": "CREATE_PROCEDURE", "action_yes": "reject_execute", "action_no": ""}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "categoryList": ["SqlQuery"], "description": "禁止存储过程在数据库交互台直接创建，请提交DDL工单执行", "enabled": 1, "rule_type": 0}, {"name": "禁止无法识别SQL解析异常继续执行【继续执行会导致安全规则失效】", "func": "IsAllowUnknownSqlExec", "dsl_flow": {"factor_name": "sql_type", "factor_value": "UNKNOWN", "action_yes": "reject_execute", "action_no": "allow_execute"}, "detect_point": "SqlParseExceptionSpecs", "instanceTypeList": ["MySQL"], "value": "true", "categoryList": ["SqlQuery"], "description": "禁止无法识别SQL解析异常继续执行【继续执行会导致安全规则失效】", "enabled": 1, "rule_type": 0}, {"name": "控制SQL库表权限校验", "func": "IsCheckDbOrTablePrivilege", "dsl_flow": {"factor_name": "user_role", "factor_value": ["user_is_inst_dba", "user_is_inst_owner", "user_is_root"], "action_yes": "uncheck_sql_access_permission", "action_no": "check_sql_access_permission"}, "detect_point": "SqlPermissionSpecs", "instanceTypeList": ["MySQL"], "value": "true", "categoryList": ["SqlQuery"], "description": "控制SQL库表权限校验", "enabled": 1, "rule_type": 0}, {"name": "仅支持SQL扫描行数小于10万行的语句执行，请优化SQL语句", "func": "", "dsl_flow": {"RuleList": [{"ConditionList": [{"ValueType": "int", "LeftValue": "@fac.GetScanRowsFromExplain", "RightValue": "100000", "OperatorType": ">=", "Combination": null}], "Connector": "or", "Action": "@act.RejectSubmitByConsole"}], "defaultAction": ""}, "detect_point": "", "instanceTypeList": ["MySQL"], "value": "true", "categoryList": ["SqlQuery"], "description": "仅支持SQL扫描行数小于10万行的语句执行，请优化SQL语句", "enabled": 1, "rule_type": 1}, {"name": "仅支持select_type在范围内的SQL执行：SIMPLE、PRIMARY、DERIVED、UNION、UNION RESULT、SUBQUERY、DEPENDENT SUBQUERY、MATERIALIZED、DEPENDENT UNION", "func": "", "dsl_flow": {"RuleList": [{"Action": "@act.RejectSubmitByConsole", "ConditionList": [{"Combination": null, "LeftValue": "@fac.GetSelectTypeFromExplain", "OperatorType": "not contains", "RightValue": "SIMPLE、PRIMARY、DERIVED、UNION、UNION RESULT、SUBQUERY、DEPENDENT SUBQUERY、MATERIALIZED、DEPENDENT UNION", "ValueType": "string"}, {"Combination": null, "LeftValue": "@fac.GetScanRowsFromExplain", "OperatorType": ">=", "RightValue": "100000", "ValueType": "int"}], "Connector": "and"}], "defaultAction": ""}, "detect_point": "", "instanceTypeList": ["MySQL"], "value": "true", "categoryList": ["SqlQuery"], "description": "仅支持select_type在范围内的SQL执行", "enabled": 1, "rule_type": 1}, {"name": "仅支持特定类型SQL执行：SHOW TABLES、SHOW CREATE TABLE、EXPLAIN、SELECT、DBATMAN HELP、DBATMAN SHOW", "func": "", "dsl_flow": {"RuleList": [{"ConditionList": [{"ValueType": "string", "LeftValue": "@fac.GetSqlType", "RightValue": "SHOW TABLES、SHOW CREATE TABLE、EXPLAIN、SELECT、DBATMAN HELP、DBATMAN SHOW", "OperatorType": "not contains", "Combination": null}], "Connector": "or", "Action": "@act.RejectSubmitByConsole"}], "defaultAction": ""}, "detect_point": "", "instanceTypeList": ["MySQL"], "value": "true", "categoryList": ["SqlQuery"], "description": "仅支持特定类型SQL执行", "enabled": 1, "rule_type": 1}, {"name": "仅支持index_type在范围内SQL执行：index、range、ref、eq_ref、const、system、NULL", "func": "", "dsl_flow": {"RuleList": [{"ConditionList": [{"ValueType": "string", "LeftValue": "@fac.GetJoinTypeFromExplain", "RightValue": "index、range、ref、eq_ref、const、system、NULL", "OperatorType": "not contains", "Combination": null}, {"Combination": null, "LeftValue": "@fac.GetScanRowsFromExplain", "OperatorType": ">=", "RightValue": "100000", "ValueType": "int"}], "Connector": "and", "Action": "@act.RejectSubmitByConsole"}], "defaultAction": ""}, "detect_point": "", "instanceTypeList": ["MySQL"], "value": "true", "categoryList": ["SqlQuery"], "description": "仅支持index_type在范围内SQL执行", "enabled": 1, "rule_type": 1}, {"name": "SQL语法解析异常，建议检查SQL语句是否合法", "func": "IsAllowUnknownSqlExec", "dsl_flow": {"factor_name": "sql_type", "factor_value": "UNKNOWN", "action_yes": "reject_execute", "action_no": "allow_execute"}, "detect_point": "SqlParseExceptionSpecs", "instanceTypeList": ["MySQL"], "value": "true", "level": "High", "categoryList": ["SqlReview"], "description": "SQL语法解析异常，建议检查SQL语句是否合法", "enabled": 1, "rule_type": 0}, {"name": "限制 UPDATE/DELETE 语句多表关联的数量", "func": "RestrictMultipleTableUpdateAndDeleteNumber", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "3", "level": "Medium", "categoryList": ["SqlReview"], "description": "限制 UPDATE/DELETE 语句多表关联的数量", "enabled": 1, "rule_type": 0}, {"name": "UPDATE/DELETE 语句检测多表关联语法是否完整（JOIN 遗漏 ON 子句）", "func": "IsCheckMultipleTableUpdateAndDeleteJoinHaveOn", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "UPDATE/DELETE 语句检测多表关联语法是否完整（JOIN 遗漏 ON 子句）", "enabled": 1, "rule_type": 0}, {"name": "UPDATE/DELETE 语句建议指定 WHERE 条件", "func": "IsCheckUpdateAndDeleteHaveWhere", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "UPDATE/DELETE 语句建议指定 WHERE 条件", "enabled": 1, "rule_type": 0}, {"name": "INSERT 语句字段名不能重复", "func": "IsCheckInsertCannotBeDuplicated", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "INSERT 语句字段名不能重复", "enabled": 1, "rule_type": 0}, {"name": "INSERT 语句字段列表和值列表要匹配", "func": "IsCheckInsertColAndValueMatch", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "INSERT 语句字段列表和值列表要匹配", "enabled": 1, "rule_type": 0}, {"name": "INSERT 语句建议指定 INSERT 字段列表", "func": "IsCheckInsertNeedColumnList", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "INSERT 语句建议指定 INSERT 字段列表", "enabled": 1, "rule_type": 0}, {"name": "INSERT 语句不能为 NOT NULL 列插入 NULL 值", "func": "RestrictInsertNotNullColumnCannotBeNull", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "INSERT 语句不能为 NOT NULL 列插入 NULL 值", "enabled": 1, "rule_type": 0}, {"name": "INSERT 语句不建议使用 SYSDATE() 函数", "func": "IsAllowFuncSysdate", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "INSERT 语句不建议使用 SYSDATE() 函数", "enabled": 1, "rule_type": 0}, {"name": "限制 INSERT 语句一条 INSERT VALUES 的总行数", "func": "RestrictInsertValuesNumber", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "1000", "level": "Medium", "categoryList": ["SqlReview"], "description": "限制 INSERT 语句一条 INSERT VALUES 的总行数", "enabled": 1, "rule_type": 0}, {"name": "INSERT 语句检测 INSERT 的表/字段是否存在", "func": "IsCheckInsertTableAndColumnExist", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "INSERT 语句检测 INSERT 的表/字段是否存在", "enabled": 1, "rule_type": 0}, {"name": "UPDATE 多表时检测 SET 的列是否指定表前缀", "func": "IsCheckUpdateSetColumnTablePrefix", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "UPDATE 多表时检测 SET 的列是否指定表前缀", "enabled": 1, "rule_type": 0}, {"name": "UPDATE 语句不建议更新表上的“创建时间”列", "func": "IsCheckUpdateTableCreateTimeColumn", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "create_time,create_at", "level": "Medium", "categoryList": ["SqlReview"], "description": "UPDATE 语句不建议更新表上的“创建时间”列", "enabled": 1, "rule_type": 0}, {"name": "UPDATE 语句建议更新表上的“修改时间”列", "func": "IsCheckUpdateTableModifyTimeColumn", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "update_time,modify_time,update_at,modify_at", "level": "Medium", "categoryList": ["SqlReview"], "description": "UPDATE 语句建议更新表上的“修改时间”列", "enabled": 1, "rule_type": 0}, {"name": "UPDATE 语句检测 SET 多个列之间的分隔符（ AND 非法）", "func": "IsCheckUpdateSetColumnSeparator", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "UPDATE 语句检测 SET 多个列之间的分隔符（ AND 非法）", "enabled": 1, "rule_type": 0}, {"name": "UPDATE/DELETE 语句检测 WHERE 条件是否包含子查询", "func": "IsCheckUpdateAndDeleteWhereSubQuery", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "UPDATE/DELETE 语句检测 WHERE 条件是否包含子查询", "enabled": 1, "rule_type": 0}, {"name": "限制 WHERE 条件中 IN 子句包含元素个数", "func": "RestrictWhereInNumber", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "1000", "level": "Medium", "categoryList": ["SqlReview"], "description": "限制 WHERE 条件中 IN 子句包含元素个数", "enabled": 1, "rule_type": 0}, {"name": "UPDATE 语句检测是否更新了主键", "func": "IsCheckUpdatePrimaryKeyValue", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "UPDATE 语句检测是否更新了主键", "enabled": 1, "rule_type": 0}, {"name": "UPDATE 语句检测UPDATE的表/字段是否存在", "func": "IsCheckUpdateTableAndColumnExist", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "UPDATE 语句检测UPDATE的表/字段是否存在", "enabled": 1, "rule_type": 0}, {"name": "UPDATE 语句检测是否更新了唯一键", "func": "IsCheckUpdateUniqueKeyValue", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "UPDATE 语句检测是否更新了唯一键", "enabled": 1, "rule_type": 0}, {"name": "限制 SELECT 语句 LIMIT 的 OFFSET 大小", "func": "RestrictOffsetSizeInSelect", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "2000", "level": "Medium", "categoryList": ["SqlReview"], "description": "限制 SELECT 语句 LIMIT 的 OFFSET 大小", "enabled": 1, "rule_type": 0}, {"name": "SELECT 语句建议包含 WHERE 条件", "func": "RestrictNeedUseWhereInSelect", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "SELECT 语句建议包含 WHERE 条件", "enabled": 1, "rule_type": 0}, {"name": "SELECT 时不建议 GROUP BY 常量", "func": "RestrictGroupByConstantStatementsInSelect", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "SELECT 时不建议 GROUP BY 常量", "enabled": 1, "rule_type": 0}, {"name": "限制 SELECT 语句多表关联的数量", "func": "RestrictTableAssociationsCountInSelect", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "3", "level": "Medium", "categoryList": ["SqlReview"], "description": "限制 SELECT 语句多表关联的数量", "enabled": 1, "rule_type": 0}, {"name": "SELECT 语句不建议使用 *", "func": "RestrictAsteriskStatementInSelect", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "SELECT 语句不建议使用 *", "enabled": 1, "rule_type": 0}, {"name": "SELECT 语句不建议对不同的表 GROUP BY 或 ORDER BY", "func": "RestrictOrderByDifferentTablesInSelect", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "SELECT 语句不建议对不同的表 GROUP BY 或 ORDER BY", "enabled": 1, "rule_type": 0}, {"name": "SELECT 语句不建议对常量进行 ORDER BY", "func": "RestrictOrderByConstantInSelect", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "SELECT 语句不建议对常量进行 ORDER BY", "enabled": 1, "rule_type": 0}, {"name": "SELECT 语句不建议 ORDER BY 多个字段使用不同方向排序", "func": "RestrictOrderByDifferentDirectionsInSelect", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "SELECT 语句不建议 ORDER BY 多个字段使用不同方向排序", "enabled": 1, "rule_type": 0}, {"name": "SELECT 语句不建议 GROUP BY 表达式或函数", "func": "RestrictGroupByFuncStatementsInSelect", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "SELECT 语句不建议 GROUP BY 表达式或函数", "enabled": 1, "rule_type": 0}, {"name": "SELECT 语句不建议 ORDER BY 表达式或函数", "func": "RestrictOrderByFuncStatementsInSelect", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "SELECT 语句不建议 ORDER BY 表达式或函数", "enabled": 1, "rule_type": 0}, {"name": "SELECT 语句不建议使用 ORDER BY RAND()", "func": "RestrictOrderByRandStatementsInSelect", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "SELECT 语句不建议使用 ORDER BY RAND()", "enabled": 1, "rule_type": 0}, {"name": "SELECT 语句不建议使用 HAVING 子句", "func": "RestrictHavingStatementsInSelect", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "SELECT 语句不建议使用 HAVING 子句", "enabled": 1, "rule_type": 0}, {"name": "SELECT 语句不建议使用 UNION", "func": "RestrictUnionStatementsInSelect", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "SELECT 语句不建议使用 UNION", "enabled": 1, "rule_type": 0}, {"name": "WHERE 条件中不建议使用前通配符查找", "func": "RestrictLikeStatementsUsePreWildcards", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "WHERE 条件中不建议使用前通配符查找", "enabled": 1, "rule_type": 0}, {"name": "WHERE 条件中不建议使用反向查询（NOT IN / NOT LIKE）", "func": "RestrictReverseQueryInWhere", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "WHERE 条件中不建议使用反向查询（NOT IN / NOT LIKE）", "enabled": 1, "rule_type": 0}, {"name": "WHERE 条件中检测是否通过 OR 操作符连接过滤条件", "func": "RestrictFilterConnectWithOr", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "WHERE 条件中检测是否通过 OR 操作符连接过滤条件", "enabled": 1, "rule_type": 0}, {"name": "WHERE 条件中检测没有通配符的 LIKE 语句", "func": "RestrictLikeStatementsWithoutWildcards", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "Medium", "categoryList": ["SqlReview"], "description": "WHERE 条件中检测没有通配符的 LIKE 语句", "enabled": 1, "rule_type": 0}, {"name": "限制SQL语句长度(默认8MB)", "func": "RestrictSQLLength", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "High", "categoryList": ["SqlReview"], "description": "限制SQL语句长度(默认8MB)", "enabled": 1, "rule_type": 0}, {"name": "不允许跨 DB 提交 SQL", "func": "RestrictSQLCrossDB", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "High", "categoryList": ["SqlReview"], "description": "不允许跨 DB 提交 SQL", "enabled": 1, "rule_type": 0}, {"name": "SQL语法中必须包含表名", "func": "RestrictSQLWithOutTableName", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "High", "categoryList": ["SqlReview"], "description": "SQL语法中必须包含表名", "enabled": 1, "rule_type": 0}, {"name": "仅支持 INSERT、UPDATE 和 DELETE 语句", "func": "RestrictSQLStatement", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "High", "categoryList": ["SqlReview"], "description": "仅支持 INSERT、UPDATE 和 DELETE 语句", "enabled": 1, "rule_type": 0}, {"name": "Insert 语句中必须包含列信息", "func": "RestrictInsertWithOutColumnInfo", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "High", "categoryList": ["SqlReview"], "description": "Insert 语句中必须包含列信息", "enabled": 1, "rule_type": 0}, {"name": "Insert 语句禁止On Duplicate Key语法", "func": "RestrictInsertOnDuplicateKey", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "High", "categoryList": ["SqlReview"], "description": "Insert 语句禁止On Duplicate Key语法", "enabled": 1, "rule_type": 0}, {"name": "Insert 语句禁止 Replace 语法", "func": "RestrictInsertByReplace", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "High", "categoryList": ["SqlReview"], "description": "Insert 语句禁止 Replace 语法", "enabled": 1, "rule_type": 0}, {"name": "UPDATE 语句不支持 UPDATE t SET age = age + 1 句式", "func": "RestrictUpdateWithOnlyValue", "dsl_flow": {}, "detect_point": "SqlExecutionRule", "instanceTypeList": ["MySQL"], "value": "true", "level": "High", "categoryList": ["SqlReview"], "description": "UPDATE 语句不支持 UPDATE t SET age = age + 1 句式", "enabled": 1, "rule_type": 0}]}