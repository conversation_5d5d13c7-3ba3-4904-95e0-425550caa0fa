#!/usr/bin/env bash

set -o errexit
set -o nounset
set -o pipefail

BIN="dbw-mgr"
set -e

os=$(uname -s)

rm -rf output/
mkdir -p output/bin/
cp -r conf output/
cp scripts/conn.sh output/
cp scripts/gauge.py output/
VERSION=`git rev-parse HEAD`
GOVERSION=`go version`


if [ $os == "Linux" ]; then
    GOOS=linux GOARCH=amd64  CGO_ENABLED=1 go build -gcflags '-N -l' -ldflags "-X 'code.byted.org/infcs/ds-lib/framework/version.commitHash=$VERSION' -X 'code.byted.org/infcs/ds-lib/framework/version.goVersion=$GOVERSION'" -o output/bin/${BIN}
else
    CGO_ENABLED=1 CC="x86_64-linux-musl-gcc" CXX="x86_64-linux-musl-g++" GOOS=linux GOARCH=amd64 go build -gcflags '-N -l' -ldflags "-X 'code.byted.org/infcs/ds-lib/framework/version.commitHash=$VERSION' -X 'code.byted.org/infcs/ds-lib/framework/version.goVersion=$GOVERSION'" -o output/bin/${BIN}
fi

chmod +x output/bin/${BIN}



