package dal

import (
	"code.byted.org/gopkg/gorm"
	"context"
	"fmt"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"github.com/qjpcpu/fp"
	. "gorm.io/gorm"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	//"code.byted.org/infcs/ds-lib/common/log"
)

const DefaultTicketSource = "ticket"

type WorkflowDAL interface {
	DescribeByTicketID(ctx context.Context, ticketId int64) (*dao.Ticket, error)
	DescribeTicketWithoutTenant(ctx context.Context, ticketId int64) (*dao.Ticket, error)
	DescribeTicketsByCreateTime(ctx context.Context, createTime int64) ([]*dao.Ticket, error)
	DescribeTickets(ctx context.Context, req *model.DescribeTicketsReq, userRole string) ([]*dao.Ticket, int, error)
	UpdateWorkStatus(ctx context.Context, ticket *dao.Ticket) error
	UpdateWorkStatusAndProgress(ctx context.Context, ticketId int64, status int32, desc string, progress int32) error
	UpdateProgress(ctx context.Context, ticketId int64, desc string, progress int32) error
	UpdateWorkStatusAndOperator(ctx context.Context, ticketId int64, status int, userRole *dao.UserRole) error
	CreateTicket(ctx context.Context, ticket *dao.Ticket) error
	UpdateById(ctx context.Context, ticket *dao.Ticket) error
	Save(ctx context.Context, ticket *dao.Ticket) error
	ModifyTicketFlow(ctx context.Context, flowInfo *dao.FlowInfo) error
	GetBpmWorkflow(ctx context.Context, ticketId int64) (*dao.BpmFlowInfo, error)
	IsAutoExecute(ctx context.Context, ticketId int64) (bool, error)
	GetInstanceOwnerIds(ctx context.Context, tenantId string, instanceId string) (string, error)
	GetInstanceDbaIds(ctx context.Context, tenantId string, instanceId string) (string, error)
	GetInstanceTickets(ctx context.Context, instanceId string, tenantId string) ([]*dao.Ticket, error)
	GetTenantAdminIds(ctx context.Context, tenantId string) (*[]string, error)
	GetUserNameByIds(ctx context.Context, userIds *[]string, tenantId string) (*[]*dao.TicketUserInfo, error)
	GetUserInstancePrivilege(ctx context.Context, userId string, instanceId string, tenantId string, privilegeType string) (*[]*dao.UserInstancePrivilege, error)
	GetUserDatabasePrivilege(ctx context.Context, userId string, instanceId string, tenantId string, privilegeType string) (*[]*dao.UserDatabasePrivilege, error)
	GetUserTablePrivilege(ctx context.Context, userId string, instanceId string, tenantId string, privilegeType string) (*[]*dao.UserTablePrivilege, error)
	GetUserColumnPrivilege(ctx context.Context, userId string, instanceId string, tenantId string, privilegeType string) (*[]*dao.UserColumnPrivilege, error)
	GetUserRoles(ctx context.Context, userId string, tenantId string, instanceId string) (*[]string, error)
	IsUserExists(ctx context.Context, userId string, tenantId string) (bool, error)
	GetTicketStatus(ctx context.Context, ticketId int64) (int64, error)
	ReplaceIntoPreCheckResult(ctx context.Context, ticketId int64, items []*model.CheckItem) error
	GetPreCheckResult(ctx context.Context, ticketId int64) ([]*dao.TicketPreCheckResult, error)
	IsUpperAccount(ctx context.Context, tenantId string, userId string, instanceId string) (bool, error)
	IsInstanceAvailable(ctx context.Context, tenantId string, instanceId string) (bool, error)
	IsUserInTenant(ctx context.Context, tenantId string, ticketId int64) (bool, error)
	IsRootAccount(ctx context.Context, tenantId string, userId string) (bool, error)
	DescribeTicketsForOperateRecord(ctx context.Context, req *model.DescribeTicketRecordListReq) ([]*dao.Ticket, int, error)
	UpdateTicketCurrentUser(ctx context.Context, approvalNodeId int64, approvalUserIds string, approvalNodeName string) error
	IsTemplateInTicket(ctx context.Context, tenantId string, approvalTemplateId int64) (bool, error)

	// 根据无锁DML OpenAPI的TaskID来查询工单信息
	GetTicketByTaskID(ctx context.Context, tenantId string, taskId int64) (*dao.Ticket, error)
	// 根据InstanceID来查询是否有运行中的工单信息
	GetTicketByInstanceID(ctx context.Context, InstanceId string) ([]*dao.Ticket, error)

	UpdateTicketSubmitFlag(ctx context.Context, ticketId string, submitFlag int8) error
	UpdateTicketConfig(ctx context.Context, ticketId string, ticketConfig string) error

	UpdateRunningInfo(ctx context.Context, runningInfo *dao.TicketRunningInfo) error
	HasTicketRunning(ctx context.Context, instanceId string, ticketType int) (bool, error)
	GetByTicketStatus(ctx context.Context, instanceId string, ticketStatus int, ticketType int) ([]*dao.Ticket, error)

	CreateItemPreCheck(ctx context.Context, preCheckDetail *dao.PreCheckDetail) error
	GetDbwPreCheckResult(ctx context.Context, ticketId string, tenantId string) ([]*dao.ItemDetail, error)
	GetDbwPreCheckSqlResult(ctx context.Context, preCheckSqlDetailId int64) ([]*dao.ItemSqlDetail, error)

	GetByID(ctx context.Context, ticketId int64) (*dao.Ticket, error)
	UpdateTicketTaskId(ctx context.Context, ticket *dao.Ticket) error
	UpdateTicketAffectedRows(ctx context.Context, ticket *dao.Ticket) error

	UpdateTicketRunningDetail(ctx context.Context, ticketId string, runningDetail string) error
}

func NewWorkflowDAL(provider DBProvider) WorkflowDAL {
	return &Workflow{dbProvider: provider}
}

type Workflow struct {
	dbProvider DBProvider
}

func (selfWorkflow *Workflow) GetTicketByInstanceID(ctx context.Context, InstanceId string) ([]*dao.Ticket, error) {
	var rets []*dao.Ticket
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	err := db.Where("instance_id=? and ticket_type=2 and ticket_status=7 and created_from != 'data_archive' and created_from != 'data_archive_inner' and created_from != 'InnerShardingFreeLockDml' ", InstanceId).Find(&rets).Error
	if err != nil {
		return nil, err
	}
	if err = fp.StreamOf(rets).Map(func(db *dao.Ticket) *dao.Ticket {
		return &dao.Ticket{
			TicketId:     db.TicketId,
			TicketStatus: db.TicketStatus,
			InstanceId:   db.InstanceId,
		}
	}).ToSlice(&rets); err != nil {
		return nil, err
	}
	return rets, err
}
func (selfWorkflow *Workflow) DescribeByTicketID(ctx context.Context, ticketId int64) (*dao.Ticket, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	var result dao.Ticket
	tenantId := fwctx.GetTenantID(ctx)
	var sqlStr string
	if tenantId == "" {
		sqlStr = " ticket_id=? "
		if err := db.Model(&dao.Ticket{}).Where(sqlStr, ticketId).Scan(&result).Error; err != nil {
			return nil, fmt.Errorf("describe ticket %d by ticketId error: %s ", ticketId, err.Error())
		}
	} else {
		sqlStr = " ticket_id=? and tenant_id=? "
		if err := db.Model(&dao.Ticket{}).Where(sqlStr, ticketId, tenantId).Scan(&result).Error; err != nil {
			return nil, fmt.Errorf("describe ticket %d by ticketId error: %s ", ticketId, err.Error())
		}
	}
	if &result == nil || result.TicketId != ticketId {
		return nil, fmt.Errorf("ticket record not found,ticketId: %d ", ticketId)
	}
	return &result, nil
}

func (selfWorkflow *Workflow) GetByID(ctx context.Context, ticketId int64) (*dao.Ticket, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	var result dao.Ticket
	sqlStr := " ticket_id=? "
	if err := db.Model(&dao.Ticket{}).
		Where(sqlStr, ticketId).Scan(&result).Error; err != nil {
		return nil, fmt.Errorf("describe ticket %d by ticketId error: %s ", ticketId, err.Error())
	}
	if result.TicketId != ticketId {
		return nil, fmt.Errorf("ticket record not found,ticketId: %d ", ticketId)
	}
	return &result, nil
}

func (selfWorkflow *Workflow) DescribeTicketWithoutTenant(ctx context.Context, ticketId int64) (*dao.Ticket, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	var result dao.Ticket
	whereCase := " ticket_id=?  "
	if err := db.Model(&dao.Ticket{}).Where(whereCase, ticketId).Scan(&result).Error; err != nil {
		return nil, fmt.Errorf("describe ticket %d by ticketId error: %s ", ticketId, err.Error())
	}
	if result.TicketId != ticketId {
		return nil, fmt.Errorf("ticket record not found,ticketId: %d ", ticketId)
	}
	return &result, nil
}

func (selfWorkflow *Workflow) DescribeTicketsByCreateTime(ctx context.Context, createTime int64) ([]*dao.Ticket, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	sqlStr := "select * from " + dao.TicketTableName + " where create_time > ? and  ticket_status in (0,6)" // ignore_security_alert
	var result []*dao.Ticket
	err := db.Raw(sqlStr, createTime).Scan(&result).Error // ignore_security_alert
	if err != nil {
		log.Info(ctx, "error is :%s", err.Error())
		return nil, err
	}
	return result, err
}

func (selfWorkflow *Workflow) DescribeTickets(ctx context.Context, req *model.DescribeTicketsReq, userRole string) ([]*dao.Ticket, int, error) {
	createFrom := DefaultTicketSource
	if req.CreateFrom != nil {
		createFrom = req.GetCreateFrom()
	}
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	var (
		result         []*dao.Ticket
		total          int64
		baseQuery      string
		baseConditions []interface{}
		orderBy        = orderByForTicketModelToString(req.GetOrderBy())
		sortBy         = sortByModelToString(req.GetSortBy())
		limit          = int(*req.PageSize)
		offset         = int(*req.PageSize * (*req.PageNumber - 1))
	)
	baseQuery = " tenant_id = ? "
	baseConditions = append(baseConditions, fwctx.GetTenantID(ctx))
	// 1、我创建的
	if req.ListType != nil && req.GetListType() == model.TicketListType_CreatedByMe {
		// 判断是子账号还是主账号
		userId := fwctx.GetTenantID(ctx)
		if fwctx.GetUserID(ctx) != "" { // 子账号,且不为管理员
			userId = fwctx.GetUserID(ctx)
		}
		baseQuery += " and submitted = 1 "
		// 管理员可以看到所有,不是管理员,只能看到自己创建的
		baseQuery += " and create_user_id = ? "
		baseConditions = append(baseConditions, userId)
		if req.SearchParam != nil {
			// 按照ticketId查询
			if req.SearchParam.IsSetTicketId() {
				ticketId := *req.SearchParam.TicketId
				baseQuery += " and ticket_id like ? "
				baseConditions = append(baseConditions, "%"+ticketId+"%")
			}
			// 如果只传入了CreateUser,此时CreateUserId会填入
			if req.SearchParam.IsSetCreateUserId() && !req.SearchParam.IsSetCreateUser() { // 只填入了CreateUserId
				createUserId := *req.SearchParam.CreateUserId
				baseQuery += " and create_user_id like ? "
				baseConditions = append(baseConditions, "%"+createUserId+"%")
			}
			if req.SearchParam.IsSetCreateUser() { // 如果填入了CreateUser,此时只有精准匹配
				createUserId := *req.SearchParam.CreateUserId
				baseQuery += " and create_user_id = ? "
				baseConditions = append(baseConditions, createUserId)
			}
			if req.SearchParam.IsSetCreateUserName() { // 如果填入了CreateUserName,此时只有精准匹配
				createUserName := *req.SearchParam.CreateUserName
				baseQuery += " and create_user_name = ? "
				baseConditions = append(baseConditions, createUserName)
			}
			// 按照TicketType查询
			if req.SearchParam.IsSetTicketType() {
				ticketType := *req.SearchParam.TicketType
				baseQuery += " and ticket_type = ? "
				baseConditions = append(baseConditions, int64(ticketType))
			}
			// 按照TicketStatus查询
			if req.SearchParam.IsSetTicketStatus() {
				ticketStatus := *req.SearchParam.TicketStatus
				baseQuery += " and ticket_status = ? "
				baseConditions = append(baseConditions, int64(ticketStatus))
			}
			// 按照TicketStatus多个筛选
			if req.SearchParam.IsSetTicketStatusList() {
				ticketStatusList := req.SearchParam.GetTicketStatusList()
				baseQuery += " and ticket_status in (?) "
				baseConditions = append(baseConditions, ticketStatusList)
			}
			// 按照InstanceId查询
			if req.SearchParam.IsSetInstanceId() {
				instanceId := *req.SearchParam.InstanceId
				baseQuery += " and instance_id = ? "
				baseConditions = append(baseConditions, instanceId)
			}
			// 按照InstanceType查询
			if req.SearchParam.IsSetInstanceType() {
				instanceType := req.SearchParam.InstanceType.String()
				baseQuery += " and instance_type = ? "
				baseConditions = append(baseConditions, instanceType)
			}
			// 按照Memo查询
			if req.SearchParam.IsSetMemo() {
				memo := req.SearchParam.GetMemo()
				baseQuery += " and memo like ? "
				baseConditions = append(baseConditions, "%"+memo+"%")
			}
			// 按照title查询
			if req.SearchParam.IsSetTitle() {
				title := req.SearchParam.GetTitle()
				baseQuery += " and title like ? "
				baseConditions = append(baseConditions, "%"+title+"%")
			}
		}
		baseQuery += " and created_from = ? "
		baseConditions = append(baseConditions, createFrom)

		if err := db.Model(result).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
			log.Info(ctx, "error is :%s", err.Error())
			return nil, 0, err
		}
		log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
			return db.Where(baseQuery, baseConditions...).Order(orderBy).Offset(offset).Limit(limit).Find(&result) // ignore_security_alert
		}))
		err := db.Where(baseQuery, baseConditions...).Order(orderBy + " " + sortBy).Offset(offset).Limit(limit).Find(&result).Error // ignore_security_alert;
		if err != nil {
			log.Info(ctx, "error is :%s", err.Error())
			return nil, 0, err
		}
		return result, int(total), nil
	}
	// 2、我审批的
	if req.ListType != nil && req.GetListType() == model.TicketListType_ApprovedByMe {
		// 判断是子账号还是主账号
		userId := fwctx.GetTenantID(ctx)
		if fwctx.GetUserID(ctx) != "" { // 子账号
			userId = fwctx.GetUserID(ctx)
		}
		baseQuery += " and submitted = 1 "
		baseQuery += " and ticket_status = 3  "
		baseQuery += " and current_user_ids like ? "
		baseConditions = append(baseConditions, "%"+userId+"%")
		if req.SearchParam != nil {
			// 按照ticketId查询
			if req.SearchParam.IsSetTicketId() {
				ticketId := *req.SearchParam.TicketId
				baseQuery += " and ticket_id like ? "
				baseConditions = append(baseConditions, "%"+ticketId+"%")
			}
			// 如果只传入了CreateUser,此时CreateUserId会填入
			if req.SearchParam.IsSetCreateUserId() && !req.SearchParam.IsSetCreateUser() { // 只填入了CreateUserId
				createUserId := *req.SearchParam.CreateUserId
				baseQuery += " and create_user_id like ? "
				baseConditions = append(baseConditions, "%"+createUserId+"%")
			}
			if req.SearchParam.IsSetCreateUser() { // 如果填入了CreateUser,此时只有精准匹配
				createUserId := *req.SearchParam.CreateUserId
				baseQuery += " and create_user_id = ? "
				baseConditions = append(baseConditions, createUserId)
			}
			if req.SearchParam.IsSetCreateUserName() { // 如果填入了CreateUserName,此时只有精准匹配
				createUserName := *req.SearchParam.CreateUserName
				baseQuery += " and create_user_name = ? "
				baseConditions = append(baseConditions, createUserName)
			}
			if req.SearchParam.IsSetTicketType() {
				ticketType := *req.SearchParam.TicketType
				baseQuery += " and ticket_type = ? "
				baseConditions = append(baseConditions, int64(ticketType))
			}
			if req.SearchParam.IsSetTicketStatus() {
				ticketStatus := *req.SearchParam.TicketStatus
				baseQuery += " and ticket_status = ? "
				baseConditions = append(baseConditions, int64(ticketStatus))
			}
			// 按照TicketStatus多个筛选
			if req.SearchParam.IsSetTicketStatusList() {
				ticketStatusList := req.SearchParam.GetTicketStatusList()
				baseQuery += " and ticket_status in (?) "
				baseConditions = append(baseConditions, ticketStatusList)
			}
			// 按照InstanceId查询
			if req.SearchParam.IsSetInstanceId() {
				instanceId := *req.SearchParam.InstanceId
				baseQuery += " and instance_id = ? "
				baseConditions = append(baseConditions, instanceId)
			}
			// 按照InstanceType查询
			if req.SearchParam.IsSetInstanceType() {
				instanceType := req.SearchParam.InstanceType.String()
				baseQuery += " and instance_type = ? "
				baseConditions = append(baseConditions, instanceType)
			}
			// 按照Memo查询
			if req.SearchParam.IsSetMemo() {
				memo := req.SearchParam.GetMemo()
				baseQuery += " and memo like ? "
				baseConditions = append(baseConditions, "%"+memo+"%")
			}
			// 按照title查询
			if req.SearchParam.IsSetTitle() {
				title := req.SearchParam.GetTitle()
				baseQuery += " and title like ? "
				baseConditions = append(baseConditions, "%"+title+"%")
			}
		}
		baseQuery += " and created_from = ? "
		baseConditions = append(baseConditions, createFrom)

		if err := db.Model(result).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
			log.Info(ctx, "error is :%s", err.Error())
			return nil, 0, err
		}
		log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
			return db.Where(baseQuery, baseConditions...).Order(orderBy).Offset(offset).Limit(limit).Find(&result) // ignore_security_alert
		}))
		err := db.Where(baseQuery, baseConditions...).Order(orderBy + " " + sortBy).Offset(offset).Limit(limit).Find(&result).Error // ignore_security_alert;
		if err != nil {
			log.Info(ctx, "error is :%s", err.Error())
			return nil, 0, err
		}
		return result, int(total), nil
	}
	// 3、所有工单
	if req.ListType != nil && req.GetListType() == model.TicketListType_All {
		// 判断是子账号还是主账号
		userId := fwctx.GetTenantID(ctx)
		if fwctx.GetUserID(ctx) != "" { // 子账号
			userId = fwctx.GetUserID(ctx)
			if userRole != model.DbwRoleType_ADMIN.String() {
				baseQuery += " and (all_operator_id like ? or current_user_ids like ? )"
				baseConditions = append(baseConditions, "%"+userId+"%", "%"+userId+"%")
			}
		}
		// 按照ticketId查询
		baseQuery += " and submitted = 1 "
		if req.SearchParam != nil {
			if req.SearchParam.IsSetTicketId() {
				ticketId := *req.SearchParam.TicketId
				baseQuery += " and ticket_id like ? "
				baseConditions = append(baseConditions, "%"+ticketId+"%")
			}
			// 如果只传入了CreateUser,此时CreateUserId会填入
			if req.SearchParam.IsSetCreateUserId() && !req.SearchParam.IsSetCreateUser() { // 只填入了CreateUserId
				createUserId := *req.SearchParam.CreateUserId
				baseQuery += " and create_user_id like ? "
				baseConditions = append(baseConditions, "%"+createUserId+"%")
			}
			if req.SearchParam.IsSetCreateUser() { // 如果填入了CreateUser,此时只有精准匹配
				createUserId := *req.SearchParam.CreateUserId
				baseQuery += " and create_user_id = ? "
				baseConditions = append(baseConditions, createUserId)
			}
			if req.SearchParam.IsSetCreateUserName() { // 如果填入了CreateUserName,此时只有精准匹配
				createUserName := *req.SearchParam.CreateUserName
				baseQuery += " and create_user_name = ? "
				baseConditions = append(baseConditions, createUserName)
			}
			if req.SearchParam.IsSetTicketType() {
				ticketType := *req.SearchParam.TicketType
				baseQuery += " and ticket_type = ? "
				baseConditions = append(baseConditions, int64(ticketType))
			}
			if req.SearchParam.IsSetTicketStatus() {
				ticketStatus := *req.SearchParam.TicketStatus
				baseQuery += " and ticket_status = ? "
				baseConditions = append(baseConditions, int64(ticketStatus))
			}
			// 按照TicketStatus多个筛选
			if req.SearchParam.IsSetTicketStatusList() {
				ticketStatusList := req.SearchParam.GetTicketStatusList()
				baseQuery += " and ticket_status in (?) "
				baseConditions = append(baseConditions, ticketStatusList)
			}
			// 按照InstanceId查询
			if req.SearchParam.IsSetInstanceId() {
				instanceId := *req.SearchParam.InstanceId
				baseQuery += " and instance_id = ? "
				baseConditions = append(baseConditions, instanceId)
			}
			// 按照InstanceType查询
			if req.SearchParam.IsSetInstanceType() {
				instanceType := req.SearchParam.InstanceType.String()
				baseQuery += " and instance_type = ? "
				baseConditions = append(baseConditions, instanceType)
			}
			// 按照Memo查询
			if req.SearchParam.IsSetMemo() {
				memo := req.SearchParam.GetMemo()
				baseQuery += " and memo like ? "
				baseConditions = append(baseConditions, "%"+memo+"%")
			}
			// 按照title查询
			if req.SearchParam.IsSetTitle() {
				title := req.SearchParam.GetTitle()
				baseQuery += " and title like ? "
				baseConditions = append(baseConditions, "%"+title+"%")
			}
		}
		baseQuery += " and created_from = ? "
		baseConditions = append(baseConditions, createFrom)

		if err := db.Model(result).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
			log.Info(ctx, "error is :%s", err.Error())
			return nil, 0, err
		}
		log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
			return db.Where(baseQuery, baseConditions...).Order(orderBy).Offset(offset).Limit(limit).Find(&result) // ignore_security_alert
		}))
		err := db.Where(baseQuery, baseConditions...).Order(orderBy + " " + sortBy).Offset(offset).Limit(limit).Find(&result).Error // ignore_security_alert;
		if err != nil {
			log.Info(ctx, "error is :%s", err.Error())
			return nil, 0, err
		}
		return result, int(total), nil
	}
	return nil, 0, consts.ErrorWithParam(model.ErrorCode_InputParamError, "Ticket ListType错误,请检查")
}

func (selfWorkflow *Workflow) UpdateWorkStatus(ctx context.Context, ticket *dao.Ticket) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	if ticket.TicketStatus == int8(model.TicketStatus_TicketError) ||
		ticket.TicketStatus == int8(model.TicketStatus_TicketPreCheckError) { // 如果执行报错,更新原因
		dbUp := db.Model(&dao.Ticket{}).Where("ticket_id = ? ", ticket.TicketId).Updates(map[string]interface{}{
			"TicketStatus": ticket.TicketStatus,
			"UpdateTime":   time.Now().UnixMilli(),
			"Description":  ticket.Description,
		})
		err := dbUp.Error
		if err != nil {
			log.Warn(ctx, "UpdateWorkStatus error: %s", err.Error())
		}
		log.Info(ctx, "ticketId:%d, status:%d, UpdateWorkStatus, affect rows: %d", ticket.TicketId, ticket.TicketStatus, dbUp.RowsAffected)
		return err
	}
	dbUp := db.Model(&dao.Ticket{}).Where("ticket_id = ? ", ticket.TicketId).Updates(map[string]interface{}{
		"TicketStatus": ticket.TicketStatus,
		"UpdateTime":   time.Now().UnixMilli(),
	})
	err := dbUp.Error
	if err != nil {
		log.Warn(ctx, "UpdateWorkStatus error: %s", err.Error())
	}
	log.Info(ctx, "ticketId:%d, status:%d, UpdateWorkStatus, affect rows: %d", ticket.TicketId, ticket.TicketStatus, dbUp.RowsAffected)
	return err
}

func (selfWorkflow *Workflow) UpdateWorkStatusAndProgress(ctx context.Context, ticketId int64, status int32, desc string, progress int32) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	if err := db.Model(&dao.Ticket{}).Where("ticket_id = ? ", ticketId).Updates(map[string]interface{}{
		"TicketStatus": status,
		"UpdateTime":   time.Now().UnixMilli(),
		"Description":  desc,
		"Progress":     progress,
	}).Error; err != nil {
		return fmt.Errorf("update ticket_record error %s", err)
	}
	return nil
}

func (selfWorkflow *Workflow) UpdateProgress(ctx context.Context, ticketId int64, desc string, progress int32) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	if err := db.Model(&dao.Ticket{}).Where("ticket_id = ? ", ticketId).Updates(map[string]interface{}{
		"UpdateTime":  time.Now().UnixMilli(),
		"Description": desc,
		"Progress":    progress,
	}).Error; err != nil {
		return fmt.Errorf("update ticket_record error %s", err)
	}
	return nil
}

func (selfWorkflow *Workflow) CreateTicket(ctx context.Context, ticket *dao.Ticket) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	err := db.Create(ticket).Error
	return err
}

func (selfWorkflow *Workflow) UpdateById(ctx context.Context, ticket *dao.Ticket) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	log.Info(ctx, "ticket:%s dal modify", ticket.TicketId)

	sqlStr := "update " + dao.TicketTableName + " set sql_text = ?, update_time = ?, data_archive_config = ?,ticket_status = 0 where ticket_id = ? and tenant_id = ? ;" // ignore_security_alert
	err := db.Exec(sqlStr, ticket.SqlText, time.Now().UnixMilli(), ticket.DataArchiveConfig, ticket.TicketId, ticket.TenantId).Error
	log.Info(ctx, "ticket:%s dal modify end", ticket.TicketId)
	return err
}

func (selfWorkflow *Workflow) Save(ctx context.Context, ticket *dao.Ticket) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	log.Info(ctx, "ticket:%v update task_id", ticket.TicketId)
	if err := db.Model(&dao.Ticket{}).Where("ticket_id = ? ", ticket.TicketId).Updates(map[string]interface{}{
		"TaskId":     ticket.TaskId,
		"UpdateTime": time.Now().UnixMilli(),
	}).Error; err != nil {
		return fmt.Errorf("update ticket_record error %s", err)
	}
	return nil
}

func (selfWorkflow *Workflow) ModifyTicketFlow(ctx context.Context, flowInfo *dao.FlowInfo) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	appendUserId := "," + flowInfo.PreOperatorId
	sqlStr := "update ticket_record set all_operator_id = CONCAT( all_operator_id, ?), ticket_status = ?, current_user_ids = ?, current_user_role = ?, update_time = ?, flow_step= ? , description = ?" +
		" where ticket_id = ?" // ignore_security_alert
	err := db.Exec(sqlStr, appendUserId, flowInfo.Status, flowInfo.UserIds, flowInfo.UserRole, time.Now().UnixMilli(), flowInfo.FlowStep, flowInfo.Comment, flowInfo.TicketId).Error
	return err
}

func (selfWorkflow *Workflow) GetBpmWorkflow(ctx context.Context, ticketId int64) (*dao.BpmFlowInfo, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	var bpmFlowInfo dao.BpmFlowInfo
	err := db.Raw("select ticket_id, workflow_id,  current_user_ids, create_user_id, flow_step, instance_id, tenant_id from  "+dao.TicketTableName+
		" where "+dao.TicketPrimaryWhereCase, ticketId).Scan(&bpmFlowInfo).Error
	if err != nil {
		return nil, err
	}
	//if bpmFlowInfo.WorkflowId == 0 {
	//	return nil, fmt.Errorf("ticket record not found,ticketId:%d", ticketId)
	//}
	return &bpmFlowInfo, nil
}

func (selfWorkflow *Workflow) IsAutoExecute(ctx context.Context, ticketId int64) (bool, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	autoExecute := -1
	err := db.Raw("select execute_type from  "+dao.TicketTableName+
		" where "+dao.TicketPrimaryWhereCase, ticketId).Scan(&autoExecute).Error
	if err != nil {
		return false, err
	}
	if autoExecute == -1 {
		return false, fmt.Errorf("ticket record not found,ticketId:%d", ticketId)
	}
	// 自动执行和定时执行,严格来讲,都属于自动执行
	return model.ExecuteType(autoExecute) == model.ExecuteType_Auto || model.ExecuteType(autoExecute) == model.ExecuteType_Cron, nil
}

func (selfWorkflow *Workflow) GetInstanceOwnerIds(ctx context.Context, tenantId string, instanceId string) (string, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	var userIds string
	sql := "select user_id from " + dao.UserTableName +
		" where user_id = (" + " select owner_uid from  " + dao.InstanceTableName +
		" where " + dao.InstanceIdWhereCase + " and tenant_id = ? and deleted = 0) and tenant_id = ? and state = ?" // ignore_security_alert;
	err := db.Raw(sql, instanceId, tenantId, tenantId, model.UserState_NORMAL.String()).Scan(&userIds).Error
	if err != nil {
		return "", err
	}
	return userIds, nil
}

func (selfWorkflow *Workflow) GetInstanceDbaIds(ctx context.Context, tenantId string, instanceId string) (string, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	var userIds string
	err := db.Raw("select user_id from "+dao.UserTableName+
		" where user_id = ("+" select dba_uid from  "+dao.InstanceTableName+
		" where "+dao.InstanceIdWhereCase+" and tenant_id = ? and deleted = 0 ) and tenant_id = ? and state = ? ",
		instanceId, tenantId, tenantId, model.UserState_NORMAL.String()).Scan(&userIds).Error
	if err != nil {
		return "", err
	}
	return userIds, nil
}

func (selfWorkflow *Workflow) GetInstanceTickets(ctx context.Context, instanceId string, tenantId string) ([]*dao.Ticket, error) {
	var rets []*dao.Ticket
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	err := db.Where("instance_id=? and tenant_id=? and submitted=1", instanceId, tenantId).Find(&rets).Error
	if err != nil {
		return nil, err
	}
	if err = fp.StreamOf(rets).Map(func(db *dao.Ticket) *dao.Ticket {
		return &dao.Ticket{
			TicketStatus: db.TicketStatus,
		}
	}).ToSlice(&rets); err != nil {
		return nil, err
	}
	return rets, err
}

func (selfWorkflow *Workflow) GetTenantAdminIds(ctx context.Context, tenantId string) (*[]string, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	var userIds []string
	err := db.Raw("select user_id from  "+dao.UserTableName+
		" where "+dao.TenantIdWhereCase+" and state = ? and role = ? and deleted = 0", tenantId, model.UserState_NORMAL.String(), model.DbwRoleType_ADMIN.String()).Scan(&userIds).Error
	if err != nil {
		return nil, err
	}
	if len(userIds) == 0 {
		return nil, fmt.Errorf("租户ID：%s，未找到对应的实例的管理员", tenantId)
	}
	return &userIds, nil
}

func (selfWorkflow *Workflow) GetUserNameByIds(ctx context.Context, userIds *[]string, tenantId string) (*[]*dao.TicketUserInfo, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	var userInfos []*dao.TicketUserInfo
	err := db.Raw("select user_id, user_name from  "+dao.UserTableName+
		" where "+selfWorkflow.formatUserNamesWhereCase(*userIds), tenantId).Scan(&userInfos).Error // ignore_security_alert
	return &userInfos, err
}

func (selfWorkflow *Workflow) GetUserInstancePrivilege(ctx context.Context, userId string, instanceId string, tenantId string, privilegeType string) (*[]*dao.UserInstancePrivilege, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	var instancePrivilege []*dao.UserInstancePrivilege
	err := db.Raw("select instance_id from  "+dao.InstancePrivilegeTableName+
		" where user_id = ? and instance_id = ?  and tenant_id = ? and privilege_type= ? and state = 'NORMAL' and deleted = 0  ", userId, instanceId, tenantId, privilegeType).Scan(&instancePrivilege).Error
	return &instancePrivilege, err
}

func (selfWorkflow *Workflow) GetUserDatabasePrivilege(ctx context.Context, userId string, instanceId string, tenantId string, privilegeType string) (*[]*dao.UserDatabasePrivilege, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	var databasePrivilege []*dao.UserDatabasePrivilege
	err := db.Raw("select db_name from  "+dao.DatabasePrivilegeTableName+
		" where user_id = ? and instance_id = ?  and tenant_id = ? and privilege_type= ? and state = 'NORMAL' and deleted = 0", userId, instanceId, tenantId, privilegeType).Scan(&databasePrivilege).Error
	return &databasePrivilege, err
}

func (selfWorkflow *Workflow) GetUserTablePrivilege(ctx context.Context, userId string, instanceId string, tenantId string, privilegeType string) (*[]*dao.UserTablePrivilege, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	var tablePrivilege []*dao.UserTablePrivilege
	err := db.Raw("select db_name, table_name from  "+dao.TablePrivilegeTableName+
		" where user_id = ? and instance_id = ?  and tenant_id = ? and privilege_type= ? and state = 'NORMAL' and deleted = 0", userId, instanceId, tenantId, privilegeType).Scan(&tablePrivilege).Error
	return &tablePrivilege, err
}

func (selfWorkflow *Workflow) GetUserColumnPrivilege(ctx context.Context, userId string, instanceId string, tenantId string, privilegeType string) (*[]*dao.UserColumnPrivilege, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	var columnPrivilege []*dao.UserColumnPrivilege
	err := db.Raw("select db_name, table_name, column_name from  "+dao.ColumnPrivilegeTableName+
		" where user_id = ? and instance_id = ?  and tenant_id = ? and privilege_type= ? and state = 'NORMAL' and deleted = 0", userId, instanceId, tenantId, privilegeType).Scan(&columnPrivilege).Error
	return &columnPrivilege, err
}

func (selfWorkflow *Workflow) GetUserRoles(ctx context.Context, userId string, tenantId string, instanceId string) (*[]string, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	// 1.查是不是管理员
	var res []string
	var role string
	err := db.Raw("select role from   "+dao.UserTableName+
		"  where user_id = ? and tenant_id = ?", userId, tenantId).Scan(&role).Error
	if err != nil {
		return nil, err
	}
	if role == model.DbwRoleType_ADMIN.String() {
		res = append(res, dao.AdminUser)
	}
	var searchUserId string
	// 2.查是不是dba
	err = db.Raw("select user_id from "+dao.UserTableName+" where user_id = ("+
		" select dba_uid from "+dao.InstanceTableName+
		" where instance_id = ? and tenant_id = ? and deleted = 0)", instanceId, tenantId).Scan(&searchUserId).Error
	if err != nil {
		return nil, err
	}
	if userId == searchUserId {
		res = append(res, dao.DbaUser)
	}
	// 3.查是不是实例owner
	err = db.Raw("select user_id from "+dao.UserTableName+" where user_id = ("+
		" select owner_uid from "+dao.InstanceTableName+
		" where instance_id = ? and tenant_id = ? and deleted = 0)", instanceId, tenantId).Scan(&searchUserId).Error
	if err != nil {
		return nil, err
	}
	if userId == searchUserId {
		res = append(res, dao.OwnerUser)
	}
	res = append(res, dao.NormalUser)
	return &res, nil
}

func (selfWorkflow *Workflow) IsUserExists(ctx context.Context, userId string, tenantId string) (bool, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	var userCount int
	err := db.Raw("select count(*) from  "+dao.UserTableName+
		" where user_id = ? and tenant_id = ? and state = ? and deleted = 0", userId, tenantId, model.UserState_NORMAL.String()).Scan(&userCount).Error
	if err != nil {
		return false, err
	}
	return userCount != 0, nil
}

func (selfWorkflow *Workflow) GetTicketStatus(ctx context.Context, ticketId int64) (int64, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	var status int64
	err := db.Raw("select ticket_status from "+dao.TicketTableName+
		" where ticket_id = ? ", ticketId).Scan(&status).Error
	return status, err
}

func (selfWorkflow *Workflow) UpdateWorkStatusAndOperator(ctx context.Context, ticketId int64, status int, userRole *dao.UserRole) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	sqlStr := "update " + dao.TicketTableName + " set ticket_status = ?, update_time = ?, current_user_ids = ?,current_user_role = ?,flow_step = ?  " +
		" where ticket_id = ? ;" // ignore_security_alert

	err := db.Exec(sqlStr, status, time.Now().UnixMilli(), userRole.Id, userRole.Role, 1, ticketId).Error

	//err := db.Model(&dao.Ticket{}).Where(dao.TicketPrimaryWhereCase, ticketId).Updates(map[string]interface{}{
	//	"TicketStatus":    status,
	//	"UpdateTime":      time.Now().UnixMilli(),
	//	"CurrentUserIds":  userRole.Id,
	//	"CurrentUserRole": userRole.Role,
	//	"FlowStep":        1,
	//}).Error
	return err
}

func (selfWorkflow *Workflow) ReplaceIntoPreCheckResult(ctx context.Context, ticketId int64, items []*model.CheckItem) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	sqlStr := "replace into `ticket_pre_check_result` (`ticket_id`, `item`, `status`, `memo`) " +
		" values (?, ?, ?, ?);"
	// 因为item本身也不多，所以我们循环执行，避免拼接sql
	for _, item := range items {
		err := db.Exec(sqlStr, ticketId, item.Item, item.Status, selfWorkflow.interceptStr1k(item.Memo)).Error
		if err != nil {
			return err
		}
	}
	return nil
}

func (selfWorkflow *Workflow) interceptStr1k(str string) string {
	if len(str) > 1023 {
		return str[0:1023]
	} else {
		return str
	}
}

func (selfWorkflow *Workflow) GetPreCheckResult(ctx context.Context, ticketId int64) ([]*dao.TicketPreCheckResult, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	var result []*dao.TicketPreCheckResult
	err := db.Model(&dao.TicketPreCheckResult{}).Where(dao.TicketPrimaryWhereCase, ticketId).Order("item DESC").Scan(&result).Error

	return result, err
}

func (selfWorkflow *Workflow) IsUpperAccount(ctx context.Context, tenantId string, userId string, instanceId string) (bool, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	// 先查主账户
	var userCount int
	err := db.Raw("select count(*) from  "+dao.UserTableName+
		" where user_id = ? and tenant_id = ? and state = ? and account_type = ? and deleted = 0", userId, tenantId, model.UserState_NORMAL.String(), model.AccountType_ROOT.String()).Scan(&userCount).Error
	if err != nil {
		return false, err
	}
	// 如果是主账户就直接返回
	if userCount != 0 {
		return true, nil
	}
	// 然后查是不是dbw_bot
	err = db.Raw("select count(*) from  "+dao.UserTableName+
		" where user_id = ? and tenant_id = ? and state = ? and account_type = ? and deleted = 0", userId, tenantId, model.UserState_NORMAL.String(), model.AccountType_DBWBOT.String()).Scan(&userCount).Error
	if err != nil {
		return false, err
	}
	// 如果是dbw_bot账户就直接返回
	if userCount != 0 {
		return true, nil
	}
	// 然后查是不是dba或者实例owner
	err = db.Raw("select count(*) from  "+dao.InstanceTableName+
		" where tenant_id = ? and instance_id = ? and (owner_uid = ? or dba_uid = ?) and deleted = 0 ", tenantId, instanceId, userId, userId).Scan(&userCount).Error
	if err != nil {
		return false, err
	}
	return userCount != 0, nil
}

func (selfWorkflow *Workflow) IsInstanceAvailable(ctx context.Context, tenantId string, instanceId string) (bool, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	// 查这个实例是不是这个租户&是否开启管控
	var instanceCount int
	err := db.Raw("select count(*) from  "+dao.InstanceTableName+
		" where deleted = 0 and instance_id = ? and tenant_id = ?", instanceId, tenantId).Scan(&instanceCount).Error
	if err != nil {
		return false, err
	}
	return instanceCount != 0, nil
}

func (selfWorkflow *Workflow) IsUserInTenant(ctx context.Context, tenantId string, ticketId int64) (bool, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	// 查这个用户的租户id和工单中的租户id是不是一致的
	var count int
	err := db.Raw("select count(*) from  "+dao.TicketTableName+
		" where ticket_id = ? and tenant_id = ?", ticketId, tenantId).Scan(&count).Error
	if err != nil {
		return false, err
	}
	return count != 0, nil
}

func (selfWorkflow *Workflow) IsRootAccount(ctx context.Context, tenantId string, userId string) (bool, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	// 先查主账户
	var userCount int
	err := db.Raw("select count(*) from  "+dao.UserTableName+
		" where user_id = ? and tenant_id = ? and state = ? and account_type = ? and deleted = 0", userId, tenantId, model.UserState_NORMAL.String(), model.AccountType_ROOT.String()).Scan(&userCount).Error
	if err != nil {
		return false, err
	}
	// 如果是主账户就直接返回
	if userCount != 0 {
		return true, nil
	}
	return false, nil
}

func (selfWorkflow *Workflow) UpdateTicketCurrentUser(ctx context.Context, approvalNodeId int64, approvalUserIds string, approvalNodeName string) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	sqlStr := "update ticket_record, dbw_approval_flow  set ticket_record.current_user_ids = ?, current_user_role = ?, update_time = ? where ticket_record.ticket_status = 3 AND ticket_record.approval_flow_id = dbw_approval_flow.flow_id  AND approver_node_id = ?;"
	err := db.Exec(sqlStr, approvalUserIds, approvalNodeName, time.Now().UnixMilli(), approvalNodeId).Error
	return err
}

func (selfWorkflow *Workflow) IsTemplateInTicket(ctx context.Context, tenantId string, approvalTemplateId int64) (bool, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	var total int64
	whereCase := " ticket_status < 4 AND ticket_status !=2 AND tenant_id = ? AND flow_config_id = ? AND submitted = 1 "
	err := db.Model(&dao.Ticket{}).Where(whereCase, tenantId, approvalTemplateId).Count(&total).Error
	if err != nil {
		log.Warn(ctx, "IsTemplateInTicket error :%s", err.Error())
		return false, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return total != 0, nil
}

func (selfWorkflow *Workflow) GetTicketByTaskID(ctx context.Context, tenantId string, taskId int64) (*dao.Ticket, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	var result dao.Ticket
	var sqlStr = " ticket_id=? and tenant_id=? "
	if err := db.Model(&dao.Ticket{}).Where(sqlStr, taskId, tenantId).Scan(&result).Error; err != nil {
		return nil, fmt.Errorf("find ticket %v 错误: %v ", taskId, err)
	}
	if result.TicketId != taskId {
		return nil, fmt.Errorf("ticket record not found,ticketId: %v ", taskId)
	}
	return &result, nil
}

func (selfWorkflow *Workflow) formatUserNamesWhereCase(userIds []string) string {
	return "user_id in (" + fp.StreamOf(userIds).Map(func(userId string) string {
		return "'" + strings.Replace(userId, "'", "\\'", -1) + "'"
	}).JoinStrings(",") + ") and tenant_id = ?"
}

func orderByForTicketModelToString(orderBy model.OrderByForTicket) string {
	switch orderBy {
	case model.OrderByForTicket_CreateTime:
		return "create_time"
	case model.OrderByForTicket_UpdateTime:
		return "update_time"
	default:
		return "create_time"
	}
}

func sortByModelToString(sortBy model.SortBy) string {
	switch sortBy {
	case model.SortBy_ASC:
		return "ASC"
	case model.SortBy_DESC:
		return "DESC"
	default:
		return "DESC" // 默认倒排
	}
}

func (selfWorkflow *Workflow) DescribeTicketsForOperateRecord(ctx context.Context, req *model.DescribeTicketRecordListReq) ([]*dao.Ticket, int, error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	var (
		result         []*dao.Ticket
		total          int64
		baseQuery      string
		baseConditions []interface{}
		orderBy        = orderByForTicketModelToString(req.GetOrderBy())
		sortBy         = sortByModelToString(req.GetSortBy())
		limit          = int(*req.PageSize)
		offset         = int(*req.PageSize * (*req.PageNumber - 1))
	)
	baseQuery = " tenant_id = ? and created_from not in ('data_archive','data_archive_inner','migration_ticket') and submitted = 1 "
	baseConditions = append(baseConditions, fwctx.GetTenantID(ctx))
	if req.CreateFrom != nil {
		baseQuery = " tenant_id = ? and created_from = ? and submitted = 1 "
		baseConditions = append(baseConditions, req.GetCreateFrom())
	}

	// 判断是子账号还是主账号
	userId := fwctx.GetTenantID(ctx)
	if fwctx.GetUserID(ctx) != "" { // 子账号
		userId = fwctx.GetUserID(ctx)
		baseQuery += " and (all_operator_id like ? or create_user_id like ?)"
		baseConditions = append(baseConditions, "%"+userId+"%", "%"+userId+"%")
	}
	if req.IsSetTicketRecordSearchParam() {
		if req.GetTicketRecordSearchParam().IsSetTicketID() {
			ticketId := *req.TicketRecordSearchParam.TicketID
			baseQuery += " and ticket_id like ? "
			baseConditions = append(baseConditions, "%"+ticketId+"%")
		}
		// 如果只传入了CreateUser,此时CreateUserId会填入
		if req.GetTicketRecordSearchParam().IsSetCreateUserId() && !req.GetTicketRecordSearchParam().IsSetCreateUserName() { // 只填入了CreateUserId
			createUserId := *req.TicketRecordSearchParam.CreateUserId
			baseQuery += " and create_user_id like ? "
			baseConditions = append(baseConditions, "%"+createUserId+"%")
		}
		if req.GetTicketRecordSearchParam().IsSetCreateUserName() { // 如果填入了CreateUser,此时只有精准匹配
			createUserId := *req.TicketRecordSearchParam.CreateUserId
			baseQuery += " and create_user_id = ? "
			baseConditions = append(baseConditions, createUserId)
		}
		if req.GetTicketRecordSearchParam().IsSetTicketType() {
			ticketType := *req.TicketRecordSearchParam.TicketType
			baseQuery += " and ticket_type = ? "
			baseConditions = append(baseConditions, int64(ticketType))
		}
		if req.GetTicketRecordSearchParam().IsSetTicketStatus() {
			ticketStatus := *req.TicketRecordSearchParam.TicketStatus
			baseQuery += " and ticket_status = ? "
			baseConditions = append(baseConditions, int64(ticketStatus))
		}
		// 按照InstanceId查询
		if req.GetTicketRecordSearchParam().IsSetInstanceID() {
			instanceId := *req.TicketRecordSearchParam.InstanceID
			baseQuery += " and instance_id = ? "
			baseConditions = append(baseConditions, instanceId)
		}
		if req.GetTicketRecordSearchParam().IsSetDbName() {
			BbName := *req.TicketRecordSearchParam.DbName
			baseQuery += " and db_name = ? "
			baseConditions = append(baseConditions, BbName)
		}
		if req.GetTicketRecordSearchParam().IsSetUptdateStartTime() && req.GetTicketRecordSearchParam().IsSetUptdateEndTime() {
			UpdateTimeStart := *req.TicketRecordSearchParam.UptdateStartTime
			UpdateTimeEnd := *req.TicketRecordSearchParam.UptdateEndTime
			baseQuery += " and update_time >= ? and update_time <= ? "
			baseConditions = append(baseConditions, UpdateTimeStart, UpdateTimeEnd)
		}
	}

	if err := db.Model(result).Where(baseQuery, baseConditions...).Count(&total).Error; err != nil {
		log.Info(ctx, "error is :%s", err.Error())
		return nil, 0, err
	}
	log.Info(ctx, "sql is %s", db.ToSQL(func(db *DB) *DB {
		return db.Where(baseQuery, baseConditions...).Order(orderBy).Offset(offset).Limit(limit).Find(&result) // ignore_security_alert
	}))
	err := db.Where(baseQuery, baseConditions...).Order(orderBy + " " + sortBy).Offset(offset).Limit(limit).Find(&result).Error // ignore_security_alert;
	if err != nil {
		log.Info(ctx, "error is :%s", err.Error())
		return nil, 0, err
	}

	return result, int(total), nil
}

func (selfWorkflow *Workflow) UpdateTicketSubmitFlag(ctx context.Context, ticketId string, submitFlag int8) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.Ticket{}).Where("ticket_id = ? ", ticketId).Updates(map[string]interface{}{
		"Submitted": submitFlag,
	}).Error
	return err
}

func (selfWorkflow *Workflow) UpdateTicketConfig(ctx context.Context, ticketId string, ticketConfig string) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.Ticket{}).Where("ticket_id = ? ", ticketId).Updates(map[string]interface{}{
		"TicketConfig": ticketConfig,
	}).Error
	return err
}

func (selfWorkflow *Workflow) UpdateRunningInfo(ctx context.Context, runningInfo *dao.TicketRunningInfo) error {
	params := map[string]interface{}{
		"Description":  runningInfo.Description,
		"TicketStatus": runningInfo.TicketStatus,
		"UpdateTime":   time.Now().UnixMilli(),
	}
	if runningInfo.Process != 0 {
		params["Progress"] = runningInfo.Process
	}
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.Ticket{}).Where("ticket_id = ? ", runningInfo.TicketId).Updates(params).Error
	return err
}

func (selfWorkflow *Workflow) HasTicketRunning(ctx context.Context, instanceId string, ticketType int) (hasTicket bool, err error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	var total int64
	// ticket_status = 7 表示执行中的状态
	whereCase := " instance_id=? AND ticket_status = 7 AND created_from not in ('data_archive', 'data_archive_inner','InnerShardingFreeLockDml')  "
	if ticketType >= 0 {
		whereCase += " AND  ticket_type = ? "
		err = db.Model(&dao.Ticket{}).Where(whereCase, instanceId, ticketType).Count(&total).Error
	} else {
		err = db.Model(&dao.Ticket{}).Where(whereCase, instanceId).Count(&total).Error
	}
	if err != nil {
		log.Warn(ctx, "HasTicketRunning error :%s", err.Error())
		return false, err
	}
	return total != 0, nil
}

func (selfWorkflow *Workflow) GetByTicketStatus(ctx context.Context, instanceId string, ticketStatus int, ticketType int) (tickets []*dao.Ticket, err error) {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	whereCase := "instance_id=? and ticket_status=? and created_from not in ('data_archive', 'data_archive_inner','InnerShardingFreeLockDml') "
	if ticketType >= 0 {
		whereCase += " AND  ticket_type = ? "
		err = db.Where(whereCase, instanceId, ticketStatus, ticketType).Find(&tickets).Error
	} else {
		err = db.Where(whereCase, instanceId, ticketStatus).Find(&tickets).Error
	}

	if err != nil {
		log.Warn(ctx, "GetByTicketStatus error:%v", err)
		return nil, err
	}
	return tickets, nil
}

func (selfWorkflow *Workflow) CreateItemPreCheck(ctx context.Context, preCheckDetail *dao.PreCheckDetail) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)

	preCheckDetail.ItemDetail.CreateAt = time.Now().UnixMilli()
	err := db.Create(preCheckDetail.ItemDetail).Error
	if err != nil {
		log.Warn(ctx, "insert pre check item detail error:%s", err.Error())
		return err
	}

	for _, value := range preCheckDetail.ItemSqlDetails {
		value.CreateAt = time.Now().UnixMilli()
		err = db.Create(value).Error
		if err != nil {
			log.Warn(ctx, "insert pre check sql item detail error:%s", err.Error())
			return err
		}
	}
	return nil
}

func (selfWorkflow *Workflow) GetDbwPreCheckResult(ctx context.Context, ticketId string, tenantId string) ([]*dao.ItemDetail, error) {
	var rets []*dao.ItemDetail
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	err := db.Where("ticket_id=? and tenant_id=?", ticketId, tenantId).Order("state desc").Find(&rets).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return rets, nil
	}
	if err != nil {
		return nil, err
	}
	return rets, nil
}

func (selfWorkflow *Workflow) GetDbwPreCheckSqlResult(ctx context.Context, preCheckSqlDetailId int64) ([]*dao.ItemSqlDetail, error) {
	var rets []*dao.ItemSqlDetail
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	err := db.Where("pre_check_detail_id=?", preCheckSqlDetailId).Find(&rets).Error
	if err != nil && err == gorm.ErrRecordNotFound {
		return rets, nil
	}
	if err != nil {
		return nil, err
	}
	return rets, nil
}

func (selfWorkflow *Workflow) UpdateTicketTaskId(ctx context.Context, ticket *dao.Ticket) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.Ticket{}).Where("ticket_id = ? ", ticket.TicketId).Updates(map[string]interface{}{
		"TaskId": ticket.TaskId,
	}).Error
	return err
}

func (selfWorkflow *Workflow) UpdateTicketAffectedRows(ctx context.Context, ticket *dao.Ticket) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.Ticket{}).Where("ticket_id = ? ", ticket.TicketId).Updates(map[string]interface{}{
		"AffectedRows": ticket.AffectedRows,
		"UpdateTime":   time.Now().UnixMilli(),
	}).Error
	return err
}

func (selfWorkflow *Workflow) UpdateTicketRunningDetail(ctx context.Context, ticketId string, runningDetail string) error {
	db := selfWorkflow.dbProvider.GetMetaDB(ctx)
	err := db.Model(&dao.Ticket{}).Where("ticket_id = ? ", ticketId).Updates(map[string]interface{}{
		"RunningDetail": runningDetail,
		"UpdateTime":    time.Now().UnixMilli(),
	}).Error
	return err
}
