package dal

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/ds-lib/framework/db"
	"code.byted.org/lang/gg/gptr"
	"context"
	"errors"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"gorm.io/gorm"
	"testing"
)

func initWorkFlowDal() *Workflow {
	return &Workflow{
		dbProvider: &mockDBProvider{},
	}
}

func TestIsInstanceAvailable(t *testing.T) {
	dal := initWorkFlowDal()
	mock1 := mockey.Mock((*db.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Scan).Return(&gorm.DB{Error: fmt.Errorf("test")}).Build()
	defer mock3.UnPatch()

	_, err := dal.IsInstanceAvailable(context.Background(), "1", "instanceId")
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestIsUpperAccount(t *testing.T) {
	dal := initWorkFlowDal()
	mock1 := mockey.Mock((*db.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Scan).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()

	_, err := dal.IsUpperAccount(context.Background(), "1", "1", "instanceId")
	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestReplaceIntoPreCheckResult(t *testing.T) {
	dal := initWorkFlowDal()
	mock1 := mockey.Mock((*db.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Exec).Return(&gorm.DB{Error: fmt.Errorf("test")}).Build()
	defer mock3.UnPatch()

	err := dal.ReplaceIntoPreCheckResult(context.Background(), 1, []*model.CheckItem{{Item: "a"}})
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestUpdateWorkStatusAndOperator(t *testing.T) {
	dal := initWorkFlowDal()
	mock1 := mockey.Mock((*db.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Exec).Return(&gorm.DB{Error: fmt.Errorf("test")}).Build()
	defer mock3.UnPatch()

	err := dal.UpdateWorkStatusAndOperator(context.Background(), 1, 1, &dao.UserRole{})
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestIsUserInTenant(t *testing.T) {
	dal := initWorkFlowDal()
	mock1 := mockey.Mock((*db.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Scan).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()

	_, err := dal.IsUserInTenant(context.Background(), "1", 1)
	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestGetInstanceDbaIds(t *testing.T) {
	dal := initWorkFlowDal()
	mock1 := mockey.Mock((*db.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Scan).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()

	_, err := dal.GetInstanceDbaIds(context.Background(), "1", "1")

	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestGetInstanceOwnerIds(t *testing.T) {
	dal := initWorkFlowDal()
	mock1 := mockey.Mock((*db.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Raw).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Scan).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()

	_, err := dal.GetInstanceOwnerIds(context.Background(), "1", "1")

	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestDescribeTicketsForOperateRecord(t *testing.T) {
	dal := initWorkFlowDal()
	mock04 := mockey.Mock(fwctx.GetTenantID).Return("").Build()
	defer mock04.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()

	number := int32(1)
	size := int32(10)
	UptdateStartTime := "1243532"
	UptdateEndTime := "132456432"
	InstanceID := "12323sdfsdf"
	CreateUserId := "123sdfsdfsd"
	CreateUserName := "asfsfsdf"
	DbName := "fisher"
	TicketID := "12342345234"
	TicketStatus := model.TicketStatus_TicketCancel
	TicketType := model.TicketType_NormalSqlChange
	TicketRecordSearchParam := model.TicketRecordSearchParam{
		UptdateStartTime: &UptdateStartTime,
		UptdateEndTime:   &UptdateEndTime,
		InstanceID:       &InstanceID,
		CreateUserId:     &CreateUserId,
		CreateUserName:   &CreateUserName,
		DbName:           &DbName,
		TicketID:         &TicketID,
		TicketStatus:     &TicketStatus,
		TicketType:       &TicketType,
	}

	SortBy := model.SortBy_ASC
	OrderBy := model.OrderByForTicket_CreateTime
	req := model.DescribeTicketRecordListReq{
		PageNumber:              &number,
		PageSize:                &size,
		TicketRecordSearchParam: &TicketRecordSearchParam,
		SortBy:                  &SortBy,
		OrderBy:                 &OrderBy,
	}

	_, _, err := dal.DescribeTicketsForOperateRecord(context.Background(), &req)
	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestDescribeByTicketID(t *testing.T) {
	dal := initWorkFlowDal()
	mock0 := mockey.Mock((*mockDBProvider).GetMetaDB).Return(&db.DB{DB: &gorm.DB{}}).Build()
	defer mock0.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Scan).Return(&gorm.DB{Error: fmt.Errorf("test")}).Build()
	defer mock3.UnPatch()
	mock04 := mockey.Mock(fwctx.GetTenantID).Return("").Build()
	defer mock04.UnPatch()
	_, err := dal.DescribeByTicketID(context.Background(), 1)
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestGetTicketByInstanceID(t *testing.T) {
	dal := initWorkFlowDal()
	mock0 := mockey.Mock((*mockDBProvider).GetMetaDB).Return(&db.DB{DB: &gorm.DB{}}).Build()
	defer mock0.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Scan).Return(&gorm.DB{Error: fmt.Errorf("test")}).Build()
	defer mock3.UnPatch()
	mock04 := mockey.Mock(fwctx.GetTenantID).Return("").Build()
	defer mock04.UnPatch()
	dal.GetTicketByInstanceID(context.Background(), "xx")
}

func TestDescribeTickets(t *testing.T) {
	dal := initWorkFlowDal()
	mock04 := mockey.Mock(fwctx.GetTenantID).Return("").Build()
	defer mock04.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Count).Return(&gorm.DB{}).Build()
	defer mock3.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Offset).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock31 := mockey.Mock((*gorm.DB).Limit).Return(&gorm.DB{}).Build()
	defer mock31.UnPatch()
	mock41 := mockey.Mock((*gorm.DB).Order).Return(&gorm.DB{}).Build()
	defer mock41.UnPatch()
	mock51 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock51.UnPatch()
	mock61 := mockey.Mock((*gorm.DB).ToSQL).Return("test").Build()
	defer mock61.UnPatch()

	number := int32(1)
	size := int32(10)
	TicketID := "12342345234"
	TicketRecordSearchParam := &model.TicketSearchParam{
		TicketId: utils.StringRef(TicketID),
		Memo:     utils.StringRef("111"),
		Title:    utils.StringRef("xxx"),
	}

	SortBy := model.SortBy_ASC
	OrderBy := model.OrderByForTicket_CreateTime
	req := model.DescribeTicketsReq{
		ListType:    model.TicketListTypePtr(model.TicketListType_CreatedByMe),
		PageNumber:  &number,
		PageSize:    &size,
		SortBy:      &SortBy,
		OrderBy:     &OrderBy,
		SearchParam: TicketRecordSearchParam,
	}

	dal.DescribeTickets(context.Background(), &req, model.DbwRoleType_ADMIN.String())

	req.ListType = model.TicketListTypePtr(model.TicketListType_All)
	dal.DescribeTickets(context.Background(), &req, model.DbwRoleType_ADMIN.String())

	req.ListType = model.TicketListTypePtr(model.TicketListType_ApprovedByMe)
	dal.DescribeTickets(context.Background(), &req, model.DbwRoleType_ADMIN.String())

}

func TestUpdateWorkStatus(t *testing.T) {
	dal := initWorkFlowDal()

	mock0 := mockey.Mock((*mockDBProvider).GetMetaDB).Return(&db.DB{DB: &gorm.DB{}}).Build()
	defer mock0.UnPatch()
	mock1 := mockey.Mock((*gorm.DB).Model).Return(&gorm.DB{}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*gorm.DB).Where).Return(&gorm.DB{}).Build()
	defer mock2.UnPatch()
	mock21 := mockey.Mock((*gorm.DB).Find).Return(&gorm.DB{}).Build()
	defer mock21.UnPatch()
	mock3 := mockey.Mock((*gorm.DB).Updates).Return(&gorm.DB{Error: fmt.Errorf("test")}).Build()
	defer mock3.UnPatch()
	mock04 := mockey.Mock(fwctx.GetTenantID).Return("").Build()
	defer mock04.UnPatch()

	_ = dal.UpdateWorkStatus(context.Background(), &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError)})
	_ = dal.UpdateWorkStatus(context.Background(), &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketFinished)})

}

// mockStandardError is a mock for the consts.StandardError interface.
type mockStandardError struct {
	msg string
}

func (e *mockStandardError) Error() string {
	return e.msg
}
func (e *mockStandardError) GetCode() int32 {
	return int32(model.ErrorCode_InputParamError)
}
func (e *mockStandardError) GetMsg() string {
	return e.msg
}
func (e *mockStandardError) GetMessage() string {
	return e.msg
}
func (e *mockStandardError) GetStatus() string {
	return "InputParamError"
}
func (e *mockStandardError) GetHTTPCode() int64 {
	return 400
}
func (e *mockStandardError) GetDetail() string {
	return ""
}
func (e *mockStandardError) GetReason() string {
	return ""
}

// Mock_DBProvider_Workflow_DescribeTickets is a mock for the DBProvider interface.
type Mock_DBProvider_Workflow_DescribeTickets struct {
	DBProvider
}

// GetMetaDB is the mocked method.
func (m *Mock_DBProvider_Workflow_DescribeTickets) GetMetaDB(ctx context.Context) *db.DB {
	return &db.DB{DB: &gorm.DB{}}
}
func Test_Workflow_DescribeTickets_BitsUTGen(t *testing.T) {
	// Common test data
	testTenantID := "test-tenant-id"
	testUserID := "test-user-id"
	mockError := errors.New("db error")

	// Setup a context with tenant and user info
	bizCtx := fwctx.NewBizContext()
	bizCtx.TenantID = testTenantID
	bizCtx.UserID = testUserID
	ctx := context.WithValue(context.Background(), fwctx.BIZ_CONTEXT_KEY, bizCtx)

	// Shared GORM DB mock instance
	mockGormDB := &gorm.DB{}
	mockDb := &db.DB{DB: mockGormDB}

	mockey.PatchConvey("Test_Workflow_DescribeTickets", t, func() {
		selfWorkflow := &Workflow{
			dbProvider: &Mock_DBProvider_Workflow_DescribeTickets{},
		}
		// Mock common dependencies
		mockey.Mock(fwctx.GetTenantID).Return(testTenantID).Build()
		mockey.Mock(fwctx.GetUserID).Return(testUserID).Build()
		mockey.Mock((*Mock_DBProvider_Workflow_DescribeTickets).GetMetaDB).Return(mockDb).Build()
		mockey.Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()

		// Mock the GORM chain methods to return the same mock DB instance
		mockey.Mock((*gorm.DB).Model).Return(mockGormDB).Build()
		mockey.Mock((*gorm.DB).Where).Return(mockGormDB).Build()
		mockey.Mock((*gorm.DB).Order).Return(mockGormDB).Build()
		mockey.Mock((*gorm.DB).Offset).Return(mockGormDB).Build()
		mockey.Mock((*gorm.DB).Limit).Return(mockGormDB).Build()
		mockey.Mock((*gorm.DB).ToSQL).Return("SELECT * FROM tickets").Build()

		mockey.PatchConvey("成功场景 - 我创建的工单(CreatedByMe) - 包含所有搜索条件", func() {
			// 场景描述：
			// 用户查询自己创建的工单列表，并提供了所有可能的搜索参数，数据库操作均成功。
			// 数据构造：
			// - req.ListType: TicketListType_CreatedByMe
			// - req.SearchParam: 包含TicketId, CreateUserId, CreateUser, CreateUserName, TicketType, TicketStatus, TicketStatusList, InstanceId, InstanceType, Memo, Title
			// 逻辑链路：
			// 1. Mock fwctx.GetTenantID 和 fwctx.GetUserID 返回测试用户ID。
			// 2. Mock dbProvider.GetMetaDB 返回 mock GORM DB。
			// 3. Mock GORM 的 Count 方法成功返回总数。
			// 4. Mock GORM 的 Find 方法成功返回工单列表。
			// 5. 验证函数返回正确的工单列表、总数，并且没有错误。

			// 数据构造
			req := &model.DescribeTicketsReq{
				PageNumber: gptr.Of(int32(1)),
				PageSize:   gptr.Of(int32(10)),
				ListType:   gptr.Of(model.TicketListType_CreatedByMe),
				CreateFrom: gptr.Of("test-source"),
				SearchParam: &model.TicketSearchParam{
					TicketId:         gptr.Of("ticket-123"),
					TicketType:       gptr.Of(model.TicketType(1)),
					TicketStatus:     gptr.Of(model.TicketStatus(1)),
					TicketStatusList: []model.TicketStatus{model.TicketStatus(2)},
					CreateUserId:     gptr.Of("creator-id"),
					CreateUser:       gptr.Of("creator-user"),
					CreateUserName:   gptr.Of("creator-name"),
					InstanceId:       gptr.Of("instance-1"),
					InstanceType:     gptr.Of(model.InstanceType_MySQL),
					Memo:             gptr.Of("test memo"),
					Title:            gptr.Of("test title"),
				},
			}

			// Mock
			mockey.Mock((*gorm.DB).Count).To(func(db1 *gorm.DB, count *int64) *gorm.DB {
				*count = 1
				db1.Error = nil
				return db1
			}).Build()
			mockey.Mock((*gorm.DB).Find).To(func(db1 *gorm.DB, dest interface{}, conds ...interface{}) *gorm.DB {
				tickets, ok := dest.(*[]*dao.Ticket)
				if ok {
					*tickets = []*dao.Ticket{{TicketId: 1, Title: "test title"}}
				}
				db1.Error = nil
				return db1
			}).Build()

			// 调用
			tickets, total, err := selfWorkflow.DescribeTickets(ctx, req, model.DbwRoleType_USER.String())

			// 断言
			convey.So(err, convey.ShouldBeNil)
			convey.So(total, convey.ShouldEqual, 1)
			convey.So(len(tickets), convey.ShouldEqual, 1)
			convey.So(tickets[0].Title, convey.ShouldEqual, "test title")
		})

		mockey.PatchConvey("成功场景 - 我审批的工单(ApprovedByMe)", func() {
			// 场景描述：
			// 用户查询等待自己审批的工单列表，数据库操作均成功。
			// 数据构造：
			// - req.ListType: TicketListType_ApprovedByMe
			// 逻辑链路：
			// 1. Mock 依赖以支持查询。
			// 2. Mock Count 和 Find 方法成功返回数据。
			// 3. 验证函数返回正确的工单列表、总数，并且没有错误。

			// 数据构造
			req := &model.DescribeTicketsReq{
				PageNumber: gptr.Of(int32(1)),
				PageSize:   gptr.Of(int32(10)),
				ListType:   gptr.Of(model.TicketListType_ApprovedByMe),
			}

			// Mock
			mockey.Mock((*gorm.DB).Count).To(func(db1 *gorm.DB, count *int64) *gorm.DB {
				*count = 1
				db1.Error = nil
				return db1
			}).Build()
			mockey.Mock((*gorm.DB).Find).To(func(db1 *gorm.DB, dest interface{}, conds ...interface{}) *gorm.DB {
				tickets, ok := dest.(*[]*dao.Ticket)
				if ok {
					*tickets = []*dao.Ticket{{TicketId: 2, CurrentUserIds: "test-user-id"}}
				}
				db1.Error = nil
				return db1
			}).Build()

			// 调用
			tickets, total, err := selfWorkflow.DescribeTickets(ctx, req, model.DbwRoleType_USER.String())

			// 断言
			convey.So(err, convey.ShouldBeNil)
			convey.So(total, convey.ShouldEqual, 1)
			convey.So(len(tickets), convey.ShouldEqual, 1)
			convey.So(tickets[0].TicketId, convey.ShouldEqual, 2)
		})

		mockey.PatchConvey("成功场景 - 所有工单(All) - 非管理员", func() {
			// 场景描述：
			// 非管理员用户查询所有与其相关的工单列表，数据库操作均成功。
			// 数据构造：
			// - req.ListType: TicketListType_All
			// - userRole: USER
			// 逻辑链路：
			// 1. Mock 依赖以支持查询。
			// 2. 验证SQL查询中包含对 `all_operator_id` 和 `current_user_ids` 的过滤。
			// 3. Mock Count 和 Find 方法成功返回数据。
			// 4. 验证函数返回正确的工单列表、总数，并且没有错误。

			// 数据构造
			req := &model.DescribeTicketsReq{
				PageNumber: gptr.Of(int32(1)),
				PageSize:   gptr.Of(int32(10)),
				ListType:   gptr.Of(model.TicketListType_All),
			}

			// Mock
			mockey.Mock((*gorm.DB).Count).To(func(db1 *gorm.DB, count *int64) *gorm.DB {
				*count = 1
				db1.Error = nil
				return db1
			}).Build()
			mockey.Mock((*gorm.DB).Find).To(func(db1 *gorm.DB, dest interface{}, conds ...interface{}) *gorm.DB {
				tickets, ok := dest.(*[]*dao.Ticket)
				if ok {
					*tickets = []*dao.Ticket{{TicketId: 3}}
				}
				db1.Error = nil
				return db1
			}).Build()

			// 调用
			tickets, total, err := selfWorkflow.DescribeTickets(ctx, req, model.DbwRoleType_USER.String())

			// 断言
			convey.So(err, convey.ShouldBeNil)
			convey.So(total, convey.ShouldEqual, 1)
			convey.So(len(tickets), convey.ShouldEqual, 1)
			convey.So(tickets[0].TicketId, convey.ShouldEqual, 3)
		})

		mockey.PatchConvey("成功场景 - 所有工单(All) - 管理员", func() {
			// 场景描述：
			// 管理员用户查询所有工单列表，数据库操作均成功。
			// 数据构造：
			// - req.ListType: TicketListType_All
			// - userRole: ADMIN
			// 逻辑链路：
			// 1. Mock 依赖以支持查询。
			// 2. 验证SQL查询中不包含对 `all_operator_id` 和 `current_user_ids` 的过滤。
			// 3. Mock Count 和 Find 方法成功返回数据。
			// 4. 验证函数返回正确的工单列表、总数，并且没有错误。

			// 数据构造
			req := &model.DescribeTicketsReq{
				PageNumber: gptr.Of(int32(1)),
				PageSize:   gptr.Of(int32(10)),
				ListType:   gptr.Of(model.TicketListType_All),
			}

			// Mock
			mockey.Mock((*gorm.DB).Count).To(func(db1 *gorm.DB, count *int64) *gorm.DB {
				*count = 5
				db1.Error = nil
				return db1
			}).Build()
			mockey.Mock((*gorm.DB).Find).To(func(db1 *gorm.DB, dest interface{}, conds ...interface{}) *gorm.DB {
				tickets, ok := dest.(*[]*dao.Ticket)
				if ok {
					*tickets = []*dao.Ticket{{TicketId: 101}, {TicketId: 102}}
				}
				db1.Error = nil
				return db1
			}).Build()

			// 调用
			tickets, total, err := selfWorkflow.DescribeTickets(ctx, req, model.DbwRoleType_ADMIN.String())

			// 断言
			convey.So(err, convey.ShouldBeNil)
			convey.So(total, convey.ShouldEqual, 5)
			convey.So(len(tickets), convey.ShouldEqual, 2)
		})

		mockey.PatchConvey("失败场景 - 数据库Count查询失败", func() {
			// 场景描述：
			// 在查询工单总数时，数据库返回错误。
			// 数据构造：
			// - req.ListType: TicketListType_CreatedByMe
			// 逻辑链路：
			// 1. Mock GORM 的 Count 方法返回一个非nil的 error。
			// 2. 验证函数返回的 error 与 mock 的 error 一致。
			// 3. 验证返回的工单列表为nil，总数为0。

			// 数据构造
			req := &model.DescribeTicketsReq{
				PageNumber: gptr.Of(int32(1)),
				PageSize:   gptr.Of(int32(10)),
				ListType:   gptr.Of(model.TicketListType_CreatedByMe),
			}

			// Mock
			mockey.Mock((*gorm.DB).Count).To(func(db1 *gorm.DB, count *int64) *gorm.DB {
				db1.Error = mockError
				return db1
			}).Build()

			// 调用
			tickets, total, err := selfWorkflow.DescribeTickets(ctx, req, model.DbwRoleType_USER.String())

			// 断言
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "db error")
			convey.So(total, convey.ShouldEqual, 0)
			convey.So(tickets, convey.ShouldBeNil)
		})

		mockey.PatchConvey("失败场景 - 数据库Find查询失败", func() {
			// 场景描述：
			// 在查询工单列表时，数据库返回错误。
			// 数据构造：
			// - req.ListType: TicketListType_CreatedByMe
			// 逻辑链路：
			// 1. Mock GORM 的 Count 方法成功。
			// 2. Mock GORM 的 Find 方法返回一个非nil的 error。
			// 3. 验证函数返回的 error 与 mock 的 error 一致。
			// 4. 验证返回的工单列表为nil，总数为0。

			// 数据构造
			req := &model.DescribeTicketsReq{
				PageNumber: gptr.Of(int32(1)),
				PageSize:   gptr.Of(int32(10)),
				ListType:   gptr.Of(model.TicketListType_CreatedByMe),
			}

			// Mock
			mockey.Mock((*gorm.DB).Count).To(func(db1 *gorm.DB, count *int64) *gorm.DB {
				*count = 1
				db1.Error = nil
				return db1
			}).Build()
			mockey.Mock((*gorm.DB).Find).To(func(db1 *gorm.DB, dest interface{}, conds ...interface{}) *gorm.DB {
				db1.Error = mockError
				return db1
			}).Build()

			// 调用
			tickets, total, err := selfWorkflow.DescribeTickets(ctx, req, model.DbwRoleType_USER.String())

			// 断言
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "db error")
			convey.So(total, convey.ShouldEqual, 0)
			convey.So(tickets, convey.ShouldBeNil)
		})

		mockey.PatchConvey("失败场景 - 无效的ListType", func() {
			// 场景描述：
			// 请求中提供了无效的 ListType。
			// 数据构造：
			// - req.ListType: nil
			// 逻辑链路：
			// 1. 函数应直接返回参数错误。
			// 2. Mock consts.ErrorWithParam 验证其被正确调用。
			// 3. 验证函数返回预期的错误信息。

			// 数据构造
			req := &model.DescribeTicketsReq{
				PageNumber: gptr.Of(int32(1)),
				PageSize:   gptr.Of(int32(10)),
				ListType:   nil, // Invalid ListType
			}

			// Mock
			mockey.Mock(consts.ErrorWithParam).To(func(e model.ErrorCode, params ...interface{}) consts.StandardError {
				if len(params) > 0 {
					return &mockStandardError{msg: fmt.Sprintf("%v", params[0])}
				}
				return &mockStandardError{msg: "mocked error"}
			}).Build()

			// 调用
			tickets, total, err := selfWorkflow.DescribeTickets(ctx, req, model.DbwRoleType_USER.String())

			// 断言
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "Ticket ListType错误,请检查")
			convey.So(total, convey.ShouldEqual, 0)
			convey.So(tickets, convey.ShouldBeNil)
		})
	})
}
