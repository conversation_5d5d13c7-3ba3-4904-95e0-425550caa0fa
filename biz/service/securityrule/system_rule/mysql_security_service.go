package system_rule

import (
	"code.byted.org/gopkg/lang/conv"
	parser "code.byted.org/infcs/ds-sql-parser"
	"context"
	"errors"
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"

	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/securityrule/sql_parser_visitor"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	dbwutils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/ds-sql-parser/ast"
	"code.byted.org/infcs/ds-sql-parser/mysql"
	"code.byted.org/infcs/ds-sql-parser/opcode"
	"code.byted.org/infcs/ds-sql-parser/test_driver"
	_ "github.com/pingcap/tidb/types/parser_driver"
	"go.uber.org/dig"
	"k8s.io/utils/strings/slices"
)

type MySQLSecurityEngine interface {
	IsAllowCreateTableExecDirect(ctx context.Context, input *InputRawData) *CheckResult
	IsAllowDropTableExecDirect(ctx context.Context, input *InputRawData) *CheckResult
	IsAllowTruncateTableExecDirect(ctx context.Context, input *InputRawData) *CheckResult
	IsAllowInsertExecDirect(ctx context.Context, input *InputRawData) *CheckResult
	IsAllowUpdateExecDirect(ctx context.Context, input *InputRawData) *CheckResult
	IsAllowDeleteExecDirect(ctx context.Context, input *InputRawData) *CheckResult
	IsAllowAlterTableExecDirect(ctx context.Context, input *InputRawData) *CheckResult
	IsAllowKillExecDirect(ctx context.Context, input *InputRawData) *CheckResult
	IsAllowCreateProcessExecDirect(ctx context.Context, input *InputRawData) *CheckResult
	IsAllowUnknownSqlExec(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckDbOrTablePrivilege(ctx context.Context, input *InputRawData) *CheckResult
	IsAllowUnknownSqlPermissionsExec(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckTableNeedHavePrimaryKey(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckTableNeedHaveCommand(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckTableHaveForeignKey(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckTableNameCase(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckTableEngine(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckTablePartitionSettings(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckTableIncludeColumns(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckColumnCharset(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckTableCharacter(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckTableCollation(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckTableNameKeyword(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckTableIndexCount(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckTableColumnCount(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckTableAutoIncrement(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckTablePrimaryKeyAutoIncrement(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckColumnNameKeyword(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckColumnNameCase(ctx context.Context, input *InputRawData) *CheckResult
	RestrictColumnNeedComment(ctx context.Context, input *InputRawData) *CheckResult
	RestrictCharColumnLength(ctx context.Context, input *InputRawData) *CheckResult
	RestrictVarCharColumnLength(ctx context.Context, input *InputRawData) *CheckResult
	RestrictColumnNotNull(ctx context.Context, input *InputRawData) *CheckResult
	RestrictIncrementColumnName(ctx context.Context, input *InputRawData) *CheckResult
	RestrictIncrementColumnIsUnsigned(ctx context.Context, input *InputRawData) *CheckResult
	RestrictColumnUseFloatType(ctx context.Context, input *InputRawData) *CheckResult
	RestrictColumnHasDefaultValue(ctx context.Context, input *InputRawData) *CheckResult
	RestrictColumnCheck(ctx context.Context, input *InputRawData) *CheckResult
	RestrictColumnUseEnumType(ctx context.Context, input *InputRawData) *CheckResult
	RestrictColumnUseZeroFill(ctx context.Context, input *InputRawData) *CheckResult
	RestrictIndexNeedName(ctx context.Context, input *InputRawData) *CheckResult
	RestrictUniqueIndexKeyFormat(ctx context.Context, input *InputRawData) *CheckResult
	RestrictNormalIndexKeyFormat(ctx context.Context, input *InputRawData) *CheckResult
	RestrictIndexKeyInColumnCounts(ctx context.Context, input *InputRawData) *CheckResult
	RestrictPrimaryKeyCounts(ctx context.Context, input *InputRawData) *CheckResult
	RestrictPrimaryKeyColumnTypes(ctx context.Context, input *InputRawData) *CheckResult
	RestrictColumnDataType(ctx context.Context, input *InputRawData) *CheckResult
	RestrictIndexColumnIsNotNull(ctx context.Context, input *InputRawData) *CheckResult
	IsCheckSelectNeedWhere(ctx context.Context, input *InputRawData) *CheckResult
	RestrictOffsetSizeInSelect(ctx context.Context, input *InputRawData) *CheckResult
	RestrictGroupByConstantStatementsInSelect(ctx context.Context, input *InputRawData) *CheckResult
	RestrictTableAssociationsCountInSelect(ctx context.Context, input *InputRawData) *CheckResult
	RestrictAsteriskStatementInSelect(ctx context.Context, input *InputRawData) *CheckResult
	RestrictOrderByConstantInSelect(ctx context.Context, input *InputRawData) *CheckResult
	RestrictOrderByDifferentDirectionsInSelect(ctx context.Context, input *InputRawData) *CheckResult
	RestrictGroupByFuncStatementsInSelect(ctx context.Context, input *InputRawData) *CheckResult
	RestrictOrderByFuncStatementsInSelect(ctx context.Context, input *InputRawData) *CheckResult
	RestrictOrderByRandStatementsInSelect(ctx context.Context, input *InputRawData) *CheckResult
	RestrictHavingStatementsInSelect(ctx context.Context, input *InputRawData) *CheckResult
	RestrictUnionStatementsInSelect(ctx context.Context, input *InputRawData) *CheckResult
	RestrictLikeStatementsUsePreWildcards(ctx context.Context, input *InputRawData) *CheckResult
	RestrictLikeStatementsWithoutWildcards(ctx context.Context, input *InputRawData) *CheckResult
	RestrictReverseQueryInWhere(ctx context.Context, input *InputRawData) *CheckResult
	RestrictFilterConnectWithOr(ctx context.Context, input *InputRawData) *CheckResult

	// 新增多云的DML规则
	RestrictSQLLength(ctx context.Context, input *InputRawData) *CheckResult
	RestrictSQLCrossDB(ctx context.Context, input *InputRawData) *CheckResult // 不允许跨db
	RestrictSQLWithOutTableName(ctx context.Context, input *InputRawData) *CheckResult
	RestrictSQLStatement(ctx context.Context, input *InputRawData) *CheckResult
	RestrictInsertWithOutColumnInfo(ctx context.Context, input *InputRawData) *CheckResult
	RestrictInsertOnDuplicateKey(ctx context.Context, input *InputRawData) *CheckResult
	RestrictInsertByReplace(ctx context.Context, input *InputRawData) *CheckResult
	RestrictUpdateWithOnlyValue(ctx context.Context, input *InputRawData) *CheckResult
}

type MysqlCheckerImpl struct {
	SecurityEngineService
	WorkflowDal dal.WorkflowDAL
	Ds          datasource.DataSourceService
}

type NewMySQLSecurityEngineIn struct {
	dig.In
	WorkflowDal dal.WorkflowDAL
	Ds          datasource.DataSourceService
}

type NewMySQLSecurityEngineOut struct {
	dig.Out
	Engine SecurityEngineService `group:"securityengine"`
}

func NewMySQLSecurityEngine(in NewMySQLSecurityEngineIn) NewMySQLSecurityEngineOut {
	return NewMySQLSecurityEngineOut{
		Engine: NewSecurityEngineServiceDecorator(
			&MysqlCheckerImpl{
				SecurityEngineService: NewSecurityEngineServiceDecorator(nil).Export(),
				WorkflowDal:           in.WorkflowDal,
				Ds:                    in.Ds,
			}).Export(),
	}
}

func (self *MysqlCheckerImpl) IsAllowCreateTableExecDirect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmtNode.(type) {
		case *ast.CreateTableStmt:
			msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
			return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
		case *ast.CreateIndexStmt:
			msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
			return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsAllowDropTableExecDirect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmtNode.(type) {
		case *ast.DropTableStmt:
			msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
			return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
		case *ast.DropIndexStmt:
			msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
			return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsAllowTruncateTableExecDirect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0. 解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1. 解析DSL
	dsl, err := self.checkDSL(ctx, input)
	if err != nil {
		log.Info(ctx, "checkDSL failed:%+v", err)
		return &CheckResult{Result: false, ErrorMsg: err}
	}
	// 2.执行规则检查
	if len(stmts) == 0 {
		return &CheckResult{Result: false, ErrorMsg: fmt.Errorf("sql parser failed")}
	} else {
		msg := fmt.Sprintf("Do not execute on the database interaction console, please jump to the work order to execute, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: checkStatementNodeType(stmts[0], dsl), ErrorMsg: fmt.Errorf(msg)}
	}
}

func (self *MysqlCheckerImpl) IsAllowInsertExecDirect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0. 解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1. 解析DSL
	dsl, err := self.checkDSL(ctx, input)
	if err != nil {
		log.Info(ctx, "checkDSL failed:%+v", err)
		return &CheckResult{Result: false, ErrorMsg: err}
	}
	// 2.执行规则检查
	if len(stmts) == 0 {
		return &CheckResult{Result: false, ErrorMsg: fmt.Errorf("sql parser failed")}
	} else {
		msg := fmt.Sprintf("Do not execute on the database interaction console, please jump to the work order to execute, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: checkStatementNodeType(stmts[0], dsl), ErrorMsg: fmt.Errorf(msg)}
	}
}

func (self *MysqlCheckerImpl) IsAllowUpdateExecDirect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0. 解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1. 解析DSL
	dsl, err := self.checkDSL(ctx, input)
	if err != nil {
		log.Info(ctx, "checkDSL failed:%+v", err)
		return &CheckResult{Result: false, ErrorMsg: err}
	}
	// 2.执行规则检查
	if len(stmts) == 0 {
		return &CheckResult{Result: false, ErrorMsg: fmt.Errorf("sql parser failed")}
	} else {
		msg := fmt.Sprintf("Do not execute on the database interaction console, please jump to the work order to execute, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: checkStatementNodeType(stmts[0], dsl), ErrorMsg: fmt.Errorf(msg)}
	}
}

func (self *MysqlCheckerImpl) IsAllowDeleteExecDirect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0. 解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1. 解析DSL
	dsl, err := self.checkDSL(ctx, input)
	if err != nil {
		log.Info(ctx, "checkDSL failed:%+v", err)
		return &CheckResult{Result: false, ErrorMsg: err}
	}
	// 2.执行规则检查
	if len(stmts) == 0 {
		return &CheckResult{Result: false, ErrorMsg: fmt.Errorf("sql parser failed")}
	} else {
		msg := fmt.Sprintf("Do not execute on the database interaction console, please jump to the work order to execute, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: checkStatementNodeType(stmts[0], dsl), ErrorMsg: fmt.Errorf(msg)}
	}
}

func (self *MysqlCheckerImpl) IsAllowAlterTableExecDirect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0. 解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1. 解析DSL
	dsl, err := self.checkDSL(ctx, input)
	if err != nil {
		log.Info(ctx, "checkDSL failed:%+v", err)
		return &CheckResult{Result: false, ErrorMsg: err}
	}
	// 2.执行规则检查
	if len(stmts) == 0 {
		return &CheckResult{Result: false, ErrorMsg: fmt.Errorf("sql parser failed")}
	} else {
		msg := fmt.Sprintf("Do not execute on the database interaction console, please jump to the work order to execute, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: checkStatementNodeType(stmts[0], dsl), ErrorMsg: fmt.Errorf(msg)}
	}
}

func (self *MysqlCheckerImpl) IsAllowKillExecDirect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0. 解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1. 解析DSL
	dsl, err := self.checkDSL(ctx, input)
	if err != nil {
		log.Info(ctx, "checkDSL failed:%+v", err)
		return &CheckResult{Result: false, ErrorMsg: err}
	}
	// 2.执行规则检查
	if len(stmts) == 0 {
		return &CheckResult{Result: false, ErrorMsg: fmt.Errorf("sql parser failed")}
	} else {
		msg := fmt.Sprintf("Do not execute on the database interaction console, please jump to the work order to execute, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: checkStatementNodeType(stmts[0], dsl), ErrorMsg: fmt.Errorf(msg)}
	}
}

func (self *MysqlCheckerImpl) IsAllowCreateProcessExecDirect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0. 解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1. 解析DSL
	dsl, err := self.checkDSL(ctx, input)
	if err != nil {
		log.Info(ctx, "checkDSL failed:%+v", err)
		return &CheckResult{Result: false, ErrorMsg: err}
	}
	// 2.执行规则检查
	if len(stmts) == 0 {
		return &CheckResult{Result: false, ErrorMsg: fmt.Errorf("sql parser failed")}
	} else {
		msg := fmt.Sprintf("Do not execute on the database interaction console, please jump to the work order to execute, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: checkStatementNodeType(stmts[0], dsl), ErrorMsg: fmt.Errorf(msg)}
	}
}

func (self *MysqlCheckerImpl) IsAllowUnknownSqlExec(ctx context.Context, input *InputRawData) *CheckResult {

	// 1. 解析SQL
	if _, err := sqlParser(input.Sql); err != nil && !strings.HasPrefix(strings.ToUpper(input.Sql), "VEPROXY") {
		msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	} else {
		msg := fmt.Sprintf("Do not execute on the database interaction console, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: true, ErrorMsg: fmt.Errorf(msg)}
	}
}

func (self *MysqlCheckerImpl) IsCheckDbOrTablePrivilege(ctx context.Context, input *InputRawData) *CheckResult {
	// 0. 解析DSL
	dsl, err := self.checkDSL(ctx, input)
	if err != nil {
		log.Info(ctx, "checkDSL failed:%+v", err)
		return &CheckResult{Result: false, ErrorMsg: err}
	}
	// 2.执行规则检查
	val, ok := dsl.Value.([]string)
	if !ok {
		return &CheckResult{Result: false, ErrorMsg: fmt.Errorf("value type is not []string")}
	}
	userId := fwctx.GetUserID(ctx)
	if userId == "" {
		if fwctx.GetTenantID(ctx) != "" {
			userId = fwctx.GetTenantID(ctx)
		} else {
			return &CheckResult{Result: false, ErrorMsg: fmt.Errorf("tenantID is null, reject")}
		}
	}
	// 高权限账号跳过权限校验
	isUpperAccount, err := self.WorkflowDal.IsUpperAccount(ctx, fwctx.GetTenantID(ctx), userId, input.InstanceId)
	if err != nil {
		log.Warn(ctx, "Failed to obtain and create user role, err：%v", err)
		return &CheckResult{Result: false, ErrorMsg: err}
	}
	msg := fmt.Sprintf("Do not execute on the database interaction console, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
	if isUpperAccount {
		return &CheckResult{Result: true, ErrorMsg: fmt.Errorf(msg), Action: "uncheck_sql_access_permission"}
	}
	if dsl.Factor == "user_role" && len(val) > 0 {
		for _, item := range val {
			if userId == item {
				return &CheckResult{Result: true, ErrorMsg: fmt.Errorf(msg), Action: "uncheck_sql_access_permission"}
			}
		}
		return &CheckResult{Result: true, ErrorMsg: fmt.Errorf(msg), Action: "check_sql_access_permission"}
	} else {
		return &CheckResult{Result: true, ErrorMsg: fmt.Errorf(msg), Action: "check_sql_access_permission"}
	}
}

func (self *MysqlCheckerImpl) IsAllowUnknownSqlPermissionsExec(ctx context.Context, input *InputRawData) *CheckResult {
	// 0. 解析DSL
	dsl, err := self.checkDSL(ctx, input)
	if err != nil {
		log.Info(ctx, "checkDSL failed:%+v", err)
		return &CheckResult{Result: false, ErrorMsg: err}
	}
	// 1. 解析SQL
	if _, err = sqlParser(input.Sql); err != nil {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err, Action: dsl.ActionYes}
	}
	// 2.执行规则检查
	msg := fmt.Sprintf("Do not execute on the database interaction console, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
	return &CheckResult{Result: true, ErrorMsg: fmt.Errorf(msg), Action: dsl.ActionYes}
}

func (self *MysqlCheckerImpl) IsCheckTableNeedHavePrimaryKey(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			for _, constraint := range stmt.Constraints {
				if constraint.Tp == ast.ConstraintPrimaryKey {
					return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
				}
			}
			for _, constraint := range stmt.Cols {
				for _, opt := range constraint.Options {
					if opt.Tp == ast.ColumnOptionPrimaryKey {
						return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
					}
				}
			}
			msg := fmt.Sprintf("Triggered a security rule, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
			return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckTableNeedHaveCommand(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			if stmt.Options != nil {
				for _, option := range stmt.Options {
					if option.Tp == ast.TableOptionComment {
						return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
					}
				}
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckTableHaveForeignKey(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			if stmt.Constraints != nil && len(stmt.Constraints) > 0 {
				for _, constraint := range stmt.Constraints {
					if constraint.Tp == ast.ConstraintForeignKey {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}
		case *ast.AlterTableStmt:
			for _, spa := range stmt.Specs {
				if spa.Tp == ast.AlterTableAddConstraint {
					if spa.Constraint.Tp == ast.ConstraintForeignKey {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckTableNameCase(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	if input.CheckRule.Value == "" {
		return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
	}
	check := input.CheckRule.Value
	var name string
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			name = stmt.Table.Name.O
		case *ast.AlterTableStmt:
			for _, spec := range stmt.Specs {
				if spec.Tp == ast.AlterTableRenameTable {
					name = spec.NewTable.Name.O
				}
			}
		case *ast.RenameTableStmt:
			name = stmt.NewTable.Name.O
		}
		switch check {
		case "lower":
			if name != strings.ToLower(name) {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		case "upper":
			if name != strings.ToUpper(name) {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckTableEngine(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	if input.CheckRule.Value == "" {
		return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
	}
	check := input.CheckRule.Value
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			if stmt.Options != nil {
				for _, option := range stmt.Options {
					if option.Tp == ast.TableOptionEngine {
						if option.StrValue == check {
							return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
						} else {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckTablePartitionSettings(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			if stmt.Partition == nil {
				return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
			} else {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		case *ast.AlterTableStmt:
			for _, spec := range stmt.Specs {
				if spec.Partition == nil {
					return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
				} else {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		}

	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckTableIncludeColumns(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	if input.CheckRule.Value == "" {
		return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
	}
	check := strings.Split(input.CheckRule.Value, ",")
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			var colList []string
			for _, colDef := range stmt.Cols {
				colName := colDef.Name.Name.String()
				colList = append(colList, colName)
			}
			for _, res := range check {
				if !isInList(res, colList) {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckColumnCharset(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "sql格式不合法，错误原因：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			for _, colDef := range stmt.Cols {
				if colDef.Tp.Charset != "" {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}

		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddColumns || spe.Tp == ast.AlterTableModifyColumn || spe.Tp == ast.AlterTableChangeColumn {
					for _, newCol := range spe.NewColumns {
						if newCol.Tp.Charset != "" {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckTableCharacter(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	if input.CheckRule.Value == "" {
		return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
	}
	check := strings.Split(input.CheckRule.Value, ",")
	for i, val := range check {
		check[i] = strings.ToLower(val)
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			for _, option := range stmt.Options {
				if option.Tp == ast.TableOptionCharset && option.StrValue != "" {
					for _, char := range check {
						if strings.ToLower(option.StrValue) == (char) {
							return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
						}
					}
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		}

	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckTableCollation(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "sql格式不合法，错误原因：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	if input.CheckRule.Value == "" {
		return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
	}
	check := strings.Split(input.CheckRule.Value, ",")
	for i, val := range check {
		check[i] = strings.ToLower(val)
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			for _, colDef := range stmt.Cols {
				if colDef.Options != nil {
					for _, option := range colDef.Options {
						if option.Tp == ast.ColumnOptionCollate {
							if !isInList(strings.ToLower(option.StrValue), check) {
								msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
								return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
							}
							return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
						}
					}
				}
			}
		}

	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckTableNameKeyword(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "sql格式不合法，错误原因：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	if input.CheckRule.Value == "" {
		return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
	}
	check := strings.Split(input.CheckRule.Value, ",")
	for i, val := range check {
		check[i] = strings.ToLower(val)
	}
	var name string
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			name = stmt.Table.Name.String()
		case *ast.AlterTableStmt:
			for _, spec := range stmt.Specs {
				if spec.Tp == ast.AlterTableRenameTable {
					name = spec.NewTable.Name.String()
				}
			}
		case *ast.RenameTableStmt:
			name = stmt.NewTable.Name.String()
		}
		if isInList(strings.ToLower(name), check) {
			msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
			return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckTableIndexCount(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "sql格式不合法，错误原因：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	check, err := strconv.Atoi(input.CheckRule.Value)
	if err != nil {
		msg := fmt.Sprintf("The rule configuration fails to be resolved. Please check the rule configuration value, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}
	// 1.执行规则检查
	indexCount := 0
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			if stmt.Constraints != nil {
				for _, constraint := range stmt.Constraints {
					if constraint.Tp == ast.ConstraintIndex {
						indexCount++
					}
				}
			}

		}
	}
	if indexCount > check {
		msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckTableColumnCount(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "sql格式不合法，错误原因：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	check, err := strconv.Atoi(input.CheckRule.Value)
	if err != nil {
		msg := fmt.Sprintf("The rule configuration fails to be resolved. Please check the rule configuration value, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			if len(stmt.Cols) > check {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckTableAutoIncrement(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "sql格式不合法，错误原因：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	check, err := strconv.Atoi(input.CheckRule.Value)
	if err != nil {
		msg := fmt.Sprintf("The rule configuration fails to be resolved. Please check the rule configuration value, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			for _, colDef := range stmt.Cols {
				if colDef.Options != nil {
					for _, option := range colDef.Options {
						if option.Tp == ast.ColumnOptionAutoIncrement {
							for _, opt := range colDef.Options {
								if opt.Tp == ast.ColumnOptionDefaultValue {
									if opt.Expr.(ast.ValueExpr).GetValue().(int64) != int64(check) || opt.Expr != nil {
										msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
										return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
									}
								}
							}
						}
					}
				}
			}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckTablePrimaryKeyAutoIncrement(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "sql格式不合法，错误原因：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			for _, col := range stmt.Cols {
				pri := false
				inc := false
				for _, opt := range col.Options {
					if opt.Tp == ast.ColumnOptionAutoIncrement {
						inc = true
					}
					if opt.Tp == ast.ColumnOptionPrimaryKey {
						pri = true
					}
				}
				if pri && inc {
					return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
				}
				for _, con := range stmt.Constraints {
					if con.Tp == ast.ConstraintPrimaryKey {
						for _, k := range con.Keys {
							if k.Column.Name.O == col.Name.Name.O {
								pri = true
							}
						}
					}
					if pri {
						if inc {
							return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
						} else {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		case *ast.AlterTableStmt:
			for _, spec := range stmt.Specs {
				for _, col := range spec.NewColumns {
					pri := false
					inc := false
					for _, opt := range col.Options {
						if opt.Tp == ast.ColumnOptionAutoIncrement {
							inc = true
						}
						if opt.Tp == ast.ColumnOptionPrimaryKey {
							pri = true
						}
					}
					if pri {
						if inc {
							return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
						} else {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}

			var priKey string
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddConstraint && spe.Constraint.Tp == ast.ConstraintPrimaryKey {
					for _, col := range spe.Constraint.Keys {
						priKey = col.Column.Name.O
					}
				}
			}
			for _, spec := range stmt.Specs {
				if spec.Tp == ast.AlterTableAddColumns {
					for _, col := range spec.NewColumns {
						if col.Name.Name.O == priKey {
							for _, opt := range col.Options {
								if opt.Tp == ast.ColumnOptionAutoIncrement {
									return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}

								}
							}
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckColumnNameKeyword(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "sql格式不合法，错误原因：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	if input.CheckRule.Value == "" {
		return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
	}
	check := strings.Split(input.CheckRule.Value, ",")
	for i, val := range check {
		check[i] = strings.ToLower(val)
	}
	var nameList []string
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			for _, col := range stmt.Cols {
				nameList = append(nameList, col.Name.String())
			}
		case *ast.AlterTableStmt:
			for _, col := range stmt.Specs {
				if col.Tp == ast.AlterTableAddColumns || col.Tp == ast.AlterTableChangeColumn || col.Tp == ast.AlterTableRenameColumn {
					for _, newCol := range col.NewColumns {
						nameList = append(nameList, newCol.Name.String())
					}
				}
			}
		}
		for _, name := range nameList {
			if isInList(strings.ToLower(name), check) {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) IsCheckColumnNameCase(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "sql格式不合法，错误原因：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	if input.CheckRule.Value == "" {
		return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
	}
	check := input.CheckRule.Value
	var nameList []string
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			for _, col := range stmt.Cols {
				nameList = append(nameList, col.Name.Name.O)
			}
		case *ast.AlterTableStmt:
			for _, col := range stmt.Specs {
				if col.Tp == ast.AlterTableAddColumns || col.Tp == ast.AlterTableChangeColumn || col.Tp == ast.AlterTableRenameColumn {
					for _, newCol := range col.NewColumns {
						nameList = append(nameList, newCol.Name.Name.O)
					}
				}
			}
		}
		for _, name := range nameList {
			switch check {
			case "lower":
				if name != strings.ToLower(name) {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			case "upper":
				if name != strings.ToUpper(name) {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictPrimaryKeyColumnTypes 限制主键列类型
func (self *MysqlCheckerImpl) RestrictPrimaryKeyColumnTypes(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			primaryKeyNameList := make([]string, 0)
			//首先要从语句最后面找到所有的主键列，然后将主键列的名字保存下来
			for _, constraint := range stmt.Constraints {
				if constraint.Tp != ast.ConstraintPrimaryKey {
					continue
				}
				for _, key := range constraint.Keys {
					primaryKeyNameList = append(primaryKeyNameList, key.Column.Name.String())
				}
			}

			//其次再遍历没一列，判断列名是否已经在主键列的名字里，或者判断该列是否是主键列
			for _, col := range stmt.Cols {
				if col.Options == nil {
					continue
				}

				//找到对应的是主键的列
				for _, option := range col.Options {
					if option.Tp == ast.ColumnOptionPrimaryKey {
						//判断当前主键列的类型，是否符合传入的类型
						//由于col.Tp.String()的返回结果是int(11)、bigint(20)这种，所以需要用包含来判断
						result := false
						for _, char := range strings.Split(input.CheckRule.Value, ",") {
							if strings.Contains(col.Tp.String(), char) {
								result = true
							}
						}
						if !result {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}

				//从主键列集合中判断当前列是不是主键
				for _, name := range primaryKeyNameList {
					//如果是主键，那么就判断是否符合传入的类型
					if col.Name.Name.String() == name {
						result := false
						for _, char := range strings.Split(input.CheckRule.Value, ",") {
							if strings.Contains(col.Tp.String(), char) {
								result = true
							}
						}
						if !result {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		case *ast.AlterTableStmt:
			//
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddColumns || spe.Tp == ast.AlterTableModifyColumn || spe.Tp == ast.AlterTableChangeColumn {
					for _, col := range spe.NewColumns {
						if col.Options == nil {
							continue
						}

						//找到对应的是主键的列
						for _, option := range col.Options {
							if option.Tp == ast.ColumnOptionPrimaryKey {
								//判断当前主键列的类型，是否符合传入的类型
								//由于col.Tp.String()的返回结果是int(11)、bigint(20)这种，所以需要用包含来判断
								result := false
								for _, char := range strings.Split(input.CheckRule.Value, ",") {
									if strings.Contains(col.Tp.String(), char) {
										result = true
									}
								}
								if !result {
									msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
									return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
								}
							}
						}
					}
				}
			}

		}

	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictPrimaryKeyCounts 限制主键列的个数
func (self *MysqlCheckerImpl) RestrictPrimaryKeyCounts(ctx context.Context, input *InputRawData) *CheckResult {
	// 解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "RestrictPrimaryKeyCounts SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 如果是主键则计算总数
	var primaryKeyCount int64 = 0
	for _, stmtNode := range stmts {
		createTableStmt, ok := stmtNode.(*ast.CreateTableStmt)
		if !ok {
			continue
		}

		for _, constraint := range createTableStmt.Constraints {
			if constraint.Tp == ast.ConstraintPrimaryKey {
				primaryKeyCount++
			}
		}
	}

	//判断当前主键列的个数与配置值的关系
	value, err := strconv.ParseInt(input.CheckRule.Value, 10, 64)
	if err != nil {
		log.Error(ctx, "RestrictPrimaryKeyCounts ParseInt err, input.CheckRule.Value is:%v", input.CheckRule.Value)
		msg := fmt.Sprintf("The rule configuration fails to be resolved. Please check the rule configuration value, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}
	if primaryKeyCount > value {
		msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictIndexKeyInColumnCounts 限制单个索引包含列的个数
func (self *MysqlCheckerImpl) RestrictIndexKeyInColumnCounts(ctx context.Context, input *InputRawData) *CheckResult {
	// 解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "RestrictPrimaryKeyCounts SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	//将配置值解析出来
	value, err := strconv.ParseInt(input.CheckRule.Value, 10, 64)
	if err != nil {
		log.Error(ctx, "RestrictPrimaryKeyCounts ParseInt err, input.CheckRule.Value is:%v", input.CheckRule.Value)
		msg := fmt.Sprintf("The rule configuration fails to be resolved. Please check the rule configuration value, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}

	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:

			//计算每个索引中所包含的列的个数
			for _, constraint := range stmt.Constraints {
				var indexKeyCount = int64(len(constraint.Keys))
				//判断当前主键列的个数与配置值的关系
				if indexKeyCount > value {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddConstraint {
					var indexKeyCount = int64(len(spe.Constraint.Keys))
					//判断当前主键列的个数与配置值的关系
					if indexKeyCount > value {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}

		}

	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictNormalIndexKeyFormat 限制普通索引名的格式
func (self *MysqlCheckerImpl) RestrictNormalIndexKeyFormat(ctx context.Context, input *InputRawData) *CheckResult {
	// 解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "RestrictPrimaryKeyCounts SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	//首先解析配置值的正则，如果校验失败，则直接返回报错
	regex, err := regexp.Compile(input.CheckRule.Value)
	if err != nil {
		log.Error(ctx, "RestrictNormalIndexKeyFormat return false, regexp.Compile failed, input.CheckRule.Value is:%v", input.CheckRule.Value)
		msg := fmt.Sprintf("The rule configuration fails to be resolved. Please check the rule configuration value, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}

	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			//如果是普通索引的情况下，则根据配置值校验正则的结果
			for _, constraint := range stmt.Constraints {
				if constraint.Tp == ast.ConstraintIndex {
					result := regex.MatchString(constraint.Name)
					if !result {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddConstraint && spe.Constraint.Tp == ast.ConstraintIndex {
					result := regex.MatchString(spe.Constraint.Name)
					if !result {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictUniqueIndexKeyFormat 限制唯一索引名的格式
func (self *MysqlCheckerImpl) RestrictUniqueIndexKeyFormat(ctx context.Context, input *InputRawData) *CheckResult {
	// 解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "RestrictPrimaryKeyCounts SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	//首先解析配置值的正则，如果校验失败，则直接返回报错
	regex, err := regexp.Compile(input.CheckRule.Value)
	if err != nil {
		log.Error(ctx, "RestrictNormalIndexKeyFormat return false, regexp.Compile failed, input.CheckRule.Value is:%v", input.CheckRule.Value)
		msg := fmt.Sprintf("The rule configuration fails to be resolved. Please check the rule configuration value, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}

	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:

			//如果是普通索引的情况下，则根据配置值校验正则的结果
			for _, constraint := range stmt.Constraints {
				if constraint.Tp == ast.ConstraintUniq {
					result := regex.MatchString(constraint.Name)
					if !result {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddConstraint && spe.Constraint.Tp == ast.ConstraintUniq {
					result := regex.MatchString(spe.Constraint.Name)
					if !result {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}
		}

	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictIndexNeedName 限制索引必须要有名字
func (self *MysqlCheckerImpl) RestrictIndexNeedName(ctx context.Context, input *InputRawData) *CheckResult {
	// 解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "RestrictPrimaryKeyCounts SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			//校验索引列，必须要有名字
			for _, constraint := range stmt.Constraints {
				if constraint.Name == "" || len(constraint.Name) <= 0 {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddConstraint {
					//校验索引列，必须要有名字
					if spe.Constraint.Name == "" || len(spe.Constraint.Name) <= 0 {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictColumnDataType 限制列不能使用部分数据类型
func (self *MysqlCheckerImpl) RestrictColumnDataType(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	//校验如果配置值为空，则该规则就不需要限制，直接返回true
	valueList := strings.Split(input.CheckRule.Value, ",")
	if input.CheckRule.Value == "" || valueList == nil || len(valueList) == 0 {
		return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
	}

	//执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			//其次再遍历每一列
			for _, col := range stmt.Cols {
				if col.Options == nil {
					continue
				}

				//判断当前列的类型和配置值的关系
				//由于col.Tp.String()的返回结果是int(11)、bigint(20)这种，所以需要用包含来判断
				for _, char := range valueList {
					if strings.Contains(col.Tp.String(), char) {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddColumns || spe.Tp == ast.AlterTableModifyColumn || spe.Tp == ast.AlterTableChangeColumn {
					//判断当前列的类型和配置值的关系
					//由于col.Tp.String()的返回结果是int(11)、bigint(20)这种，所以需要用包含来判断
					for _, col := range spe.NewColumns {
						if col.Options == nil {
							continue
						}

						for _, char := range valueList {
							if strings.Contains(col.Tp.String(), char) {
								msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
								return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
							}
						}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictColumnUseEnumType 不能使用enum类型（建议用tinyint/char代替）
func (self *MysqlCheckerImpl) RestrictColumnUseEnumType(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			//其次再遍历每一列
			for _, col := range stmt.Cols {
				//判断当前列的类型是否是枚举类型
				if col.Tp.Tp == mysql.TypeEnum {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddColumns || spe.Tp == ast.AlterTableModifyColumn || spe.Tp == ast.AlterTableChangeColumn {
					for _, col := range spe.NewColumns {
						//判断当前列的类型是否是枚举类型
						if col.Tp.Tp == mysql.TypeEnum {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		}

	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictColumnCheck 不能设置列的校验集
func (self *MysqlCheckerImpl) RestrictColumnCheck(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			//其次再遍历每一列
			for _, col := range stmt.Cols {
				if col.Options == nil {
					continue
				}

				//如果某一列的constraint内包含校验集，则返回报错
				for _, option := range col.Options {
					if option.Tp == ast.ColumnOptionCheck {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddColumns || spe.Tp == ast.AlterTableModifyColumn || spe.Tp == ast.AlterTableChangeColumn {
					for _, col := range spe.NewColumns {
						if col.Options == nil {
							continue
						}

						//如果某一列的constraint内包含校验集，则返回报错
						for _, option := range col.Options {
							if option.Tp == ast.ColumnOptionCheck {
								msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
								return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
							}
						}
					}
				}
				if spe.Tp == ast.AlterTableAddConstraint {
					if spe.Constraint != nil && spe.Constraint.Tp == ast.ConstraintCheck {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}
		}

	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictColumnUseFloatType 不能使用float/double类型
func (self *MysqlCheckerImpl) RestrictColumnUseFloatType(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			//其次再遍历每一列
			for _, col := range stmt.Cols {
				//判断当前列的类型是否是double/float类型
				if col.Tp.Tp == mysql.TypeDouble || col.Tp.Tp == mysql.TypeFloat {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddColumns || spe.Tp == ast.AlterTableModifyColumn || spe.Tp == ast.AlterTableChangeColumn {
					for _, col := range spe.NewColumns {
						if col.Tp == nil {
							continue
						}

						if col.Tp.Tp == mysql.TypeDouble || col.Tp.Tp == mysql.TypeFloat {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictColumnHasDefaultValue 每个列都要有默认值
func (self *MysqlCheckerImpl) RestrictColumnHasDefaultValue(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			//其次再遍历每一列
			for _, col := range stmt.Cols {
				if col.Options == nil {
					continue
				}

				//校验每一列是否存在默认值
				result := false
				for _, option := range col.Options {
					//如果是自增属性的字段则直接跳过，自增字段不需要默认值
					if option.Tp == ast.ColumnOptionAutoIncrement || col.Tp.Tp == mysql.TypeLongBlob || col.Tp.Tp == mysql.TypeBlob {
						result = true
					}

					if option.Tp == ast.ColumnOptionDefaultValue {
						result = true
					}
				}

				//如果不存在则返回报错
				if !result {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddColumns || spe.Tp == ast.AlterTableModifyColumn || spe.Tp == ast.AlterTableChangeColumn {
					for _, col := range spe.NewColumns {
						if col.Options == nil {
							continue
						}

						//校验每一列是否存在默认值
						result := false
						for _, option := range col.Options {
							//如果是自增属性的字段则直接跳过，自增字段不需要默认值
							if option.Tp == ast.ColumnOptionAutoIncrement {
								result = true
							}

							if option.Tp == ast.ColumnOptionDefaultValue {
								result = true
							}
						}

						//如果不存在则返回报错
						if !result {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictColumnNotNull 限制列都不可空(not null)
func (self *MysqlCheckerImpl) RestrictColumnNotNull(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			//其次再遍历每一列
			for _, col := range stmt.Cols {
				if col.Options == nil {
					continue
				}

				//校验每一列是否存在不为null的判断
				result := false
				for _, option := range col.Options {
					if option.Tp == ast.ColumnOptionNotNull {
						result = true
					}
				}

				//如果不存在则返回报错
				if !result {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddColumns || spe.Tp == ast.AlterTableModifyColumn || spe.Tp == ast.AlterTableChangeColumn {
					for _, col := range spe.NewColumns {
						if col.Options == nil {
							continue
						}

						//校验每一列是否存在不为null的判断
						result := false
						for _, option := range col.Options {
							if option.Tp == ast.ColumnOptionNotNull {
								result = true
							}
						}

						//如果不存在则返回报错
						if !result {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		}

	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictIncrementColumnName 限制自增列名字为id
func (self *MysqlCheckerImpl) RestrictIncrementColumnName(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			//其次再遍历每一列
			for _, col := range stmt.Cols {
				if col.Options == nil {
					continue
				}

				//当前列的类型是否为自增类型，如果是自增，则判断列名是否为ID
				for _, option := range col.Options {
					if option.Tp == ast.ColumnOptionAutoIncrement {
						if strings.ToLower(col.Name.String()) != "id" {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddColumns || spe.Tp == ast.AlterTableModifyColumn || spe.Tp == ast.AlterTableChangeColumn {
					for _, col := range spe.NewColumns {
						if col.Options == nil {
							continue
						}

						//当前列的类型是否为自增类型，如果是自增，则判断列名是否为ID
						for _, option := range col.Options {
							if option.Tp == ast.ColumnOptionAutoIncrement {
								if strings.ToLower(col.Name.String()) != "id" {
									msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
									return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
								}
							}
						}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictIncrementColumnIsUnsigned 限制自增列为无符号
func (self *MysqlCheckerImpl) RestrictIncrementColumnIsUnsigned(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			//其次再遍历每一列
			for _, col := range stmt.Cols {
				if col.Options == nil {
					continue
				}

				//当前列的类型是否为自增类型，如果是自增，则判断当前列是否为无符号型
				for _, option := range col.Options {
					if option.Tp == ast.ColumnOptionAutoIncrement {
						if col.Tp.Flag != mysql.UnsignedFlag {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddColumns || spe.Tp == ast.AlterTableModifyColumn || spe.Tp == ast.AlterTableChangeColumn {
					for _, col := range spe.NewColumns {
						if col.Options == nil {
							continue
						}

						//当前列的类型是否为自增类型，如果是自增，则判断当前列是否为无符号型
						for _, option := range col.Options {
							if option.Tp == ast.ColumnOptionAutoIncrement {
								if col.Tp.Flag != mysql.UnsignedFlag {
									msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
									return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
								}
							}
						}
					}
				}
			}
		}

	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictIndexColumnIsNotNull 索引中的字段要求设置为not null
func (self *MysqlCheckerImpl) RestrictIndexColumnIsNotNull(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	for _, stmtNode := range stmts {
		createTableStmt, ok := stmtNode.(*ast.CreateTableStmt)
		if !ok {
			continue
		}

		// 首先先遍历Constraint，获取到所有的索引下的列的名称
		indexColName := make([]string, 0)
		for _, constraint := range createTableStmt.Constraints {
			if constraint.Tp == ast.ConstraintIndex || constraint.Tp == ast.ConstraintUniq || constraint.Tp == ast.ConstraintPrimaryKey {
				for _, key := range constraint.Keys {
					indexColName = append(indexColName, key.Column.Name.String())
				}
			}
		}

		//其次再遍历每一列，
		for _, col := range createTableStmt.Cols {
			if col.Options == nil {
				continue
			}

			//如果这个列在在索引列中，那就判断他是否是not null，如果不是则报错
			if !slices.Contains(indexColName, col.Name.String()) {
				continue
			}
			result := false
			for _, option := range col.Options {
				if option.Tp == ast.ColumnOptionNotNull {
					result = true
				}
			}
			if !result {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictCharColumnLength 限制char类型字段长度
func (self *MysqlCheckerImpl) RestrictCharColumnLength(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	//将配置值解析出来
	value, err := strconv.Atoi(input.CheckRule.Value)
	if err != nil {
		log.Error(ctx, "RestrictCharColumnLength ParseInt err, input.CheckRule.Value is:%v", input.CheckRule.Value)
		msg := fmt.Sprintf("The rule configuration fails to be resolved. Please check the rule configuration value, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			//其次再遍历每一列
			for _, col := range stmt.Cols {
				//判断当前列的类型是否是char类型，如果是则判断与配置值的大小关系
				if col.Tp.Tp == mysql.TypeString && col.Tp.Flen > value {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddColumns || spe.Tp == ast.AlterTableModifyColumn || spe.Tp == ast.AlterTableChangeColumn {
					for _, newCol := range spe.NewColumns {
						//判断当前列的类型是否是char类型，如果是则判断与配置值的大小关系
						if newCol.Tp.Tp == mysql.TypeString && newCol.Tp.Flen > value {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictVarCharColumnLength 限制varchar类型字段长度
func (self *MysqlCheckerImpl) RestrictVarCharColumnLength(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	//将配置值解析出来
	value, err := strconv.Atoi(input.CheckRule.Value)
	if err != nil {
		log.Error(ctx, "RestrictCharColumnLength ParseInt err, input.CheckRule.Value is:%v", input.CheckRule.Value)
		msg := fmt.Sprintf("The rule configuration fails to be resolved. Please check the rule configuration value, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			//其次再遍历每一列
			for _, col := range stmt.Cols {
				//判断当前列的类型是否是char类型，如果是则判断与配置值的大小关系
				if col.Tp.Tp == mysql.TypeVarchar && col.Tp.Flen > value {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddColumns || spe.Tp == ast.AlterTableModifyColumn || spe.Tp == ast.AlterTableChangeColumn {
					for _, newCol := range spe.NewColumns {
						//判断当前列的类型是否是char类型，如果是则判断与配置值的大小关系
						if newCol.Tp.Tp == mysql.TypeVarchar && newCol.Tp.Flen > value {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictColumnNeedComment 限制列需要有注释
func (self *MysqlCheckerImpl) RestrictColumnNeedComment(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			//其次再遍历每一列，判断当前列是否包含注释，如果不包含则报错
			for _, col := range stmt.Cols {
				if col.Options == nil {
					continue
				}

				result := false
				for _, option := range col.Options {
					if option.Tp == ast.ColumnOptionComment {
						result = true
					}
				}

				if !result {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddColumns || spe.Tp == ast.AlterTableModifyColumn || spe.Tp == ast.AlterTableChangeColumn {
					for _, col := range spe.NewColumns {
						result := false
						for _, option := range col.Options {
							if option.Tp == ast.ColumnOptionComment {
								result = true
							}
						}

						if !result {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsCheckSelectNeedWhere  select语句建议指定where条件
func (self *MysqlCheckerImpl) IsCheckSelectNeedWhere(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.SelectStmt:
			if stmt.Where == nil {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictColumnUseZeroFill 限制列不能使用ZEROFILL
func (self *MysqlCheckerImpl) RestrictColumnUseZeroFill(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.CreateTableStmt:
			for _, col := range stmt.Cols {
				if col.Tp == nil {
					continue
				}
				if mysql.HasZerofillFlag(col.Tp.Flag) {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		case *ast.AlterTableStmt:
			for _, spe := range stmt.Specs {
				if spe.Tp == ast.AlterTableAddColumns || spe.Tp == ast.AlterTableModifyColumn || spe.Tp == ast.AlterTableChangeColumn {
					for _, col := range spe.NewColumns {
						if col.Tp == nil {
							continue
						}
						if mysql.HasZerofillFlag(col.Tp.Flag) {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictOffsetSizeInSelect 限制 SELECT 语句 LIMIT 的 OFFSET 大小
func (self *MysqlCheckerImpl) RestrictOffsetSizeInSelect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	//将配置值解析出来
	value, err := strconv.ParseInt(input.CheckRule.Value, 10, 64)
	if err != nil {
		log.Error(ctx, "RestrictCharColumnLength ParseInt err, input.CheckRule.Value is:%v", input.CheckRule.Value)
		msg := fmt.Sprintf("The rule configuration fails to be resolved. Please check the rule configuration value, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}

	var offsetCount int64
	switch stmt := stmts[0].(type) {
	case *ast.SelectStmt:
		limitResult := stmt.Limit
		if limitResult != nil {
			if offest, ok := stmt.Limit.Offset.(*test_driver.ValueExpr); ok {
				switch v := offest.Datum.GetValue().(type) {
				case int:
					offsetCount = int64(v)
				case int64:
					offsetCount = v
				case float64:
					offsetCount = int64(v)
				case uint64:
					offsetCount = int64(v)
				}
			}
		}
	case *ast.UpdateStmt:
		limitResult := stmt.Limit
		if limitResult != nil {
			if offest, ok := stmt.Limit.Offset.(*test_driver.ValueExpr); ok {
				switch v := offest.Datum.GetValue().(type) {
				case int:
					offsetCount = int64(v)
				case int64:
					offsetCount = v
				case float64:
					offsetCount = int64(v)
				case uint64:
					offsetCount = int64(v)
				}
			}
		}
	case *ast.DeleteStmt:
		limitResult := stmt.Limit
		if limitResult != nil {

			if offest, ok := stmt.Limit.Offset.(*test_driver.ValueExpr); ok {
				switch v := offest.Datum.GetValue().(type) {
				case int:
					offsetCount = int64(v)
				case int64:
					offsetCount = v
				case float64:
					offsetCount = int64(v)
				case uint64:
					offsetCount = int64(v)
				}
			}
		}
	}

	if offsetCount > value {
		msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictGroupByConstantStatementsInSelect SELECT 时不建议 GROUP BY 常量
func (self *MysqlCheckerImpl) RestrictGroupByConstantStatementsInSelect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.SelectStmt:
			if stmt.GroupBy != nil && len(stmt.GroupBy.Items) > 0 {
				for _, item := range stmt.GroupBy.Items {
					switch item.Expr.(type) {
					case *ast.PositionExpr:
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}

				}
			}
		case *ast.UnionStmt:
			if stmt.SelectList.Selects != nil && len(stmt.SelectList.Selects) > 0 {
				for _, childStmt := range stmt.SelectList.Selects {
					if childStmt.GroupBy != nil && len(childStmt.GroupBy.Items) > 0 {
						for _, item := range childStmt.GroupBy.Items {
							switch item.Expr.(type) {
							case *ast.PositionExpr:
								msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
								return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
							}

						}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictTableAssociationsCountInSelect 限制 SELECT 语句多表关联的数量
func (self *MysqlCheckerImpl) RestrictTableAssociationsCountInSelect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	//将配置值解析出来
	value, err := strconv.Atoi(input.CheckRule.Value)
	if err != nil {
		msg := fmt.Sprintf("The rule configuration fails to be resolved. Please check the rule configuration value, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}

	tables, err := dbwutils.GetTables(input.Sql, shared.MySQL)
	if err != nil {
		log.Error(ctx, "GetTables err, input.Sql is:%v", input.Sql)
		msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}

	//判断表数量和配置值的关系
	if len(tables) > value {
		msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictAsteriskStatementInSelect SELECT 语句不建议使用 *
func (self *MysqlCheckerImpl) RestrictAsteriskStatementInSelect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.SelectStmt:
			for _, field := range stmt.Fields.Fields {
				if field.WildCard != nil {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		case *ast.UnionStmt:
			if stmt.SelectList.Selects != nil && len(stmt.SelectList.Selects) > 0 {
				for _, childStmt := range stmt.SelectList.Selects {
					for _, field := range childStmt.Fields.Fields {
						if field.WildCard != nil {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictOrderByConstantInSelect SELECT 语句不建议对常量进行 ORDER BY
func (self *MysqlCheckerImpl) RestrictOrderByConstantInSelect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.SelectStmt:
			if stmt.OrderBy != nil && len(stmt.OrderBy.Items) > 0 {
				for _, item := range stmt.OrderBy.Items {
					switch item.Expr.(type) {
					case *ast.PositionExpr:
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}

				}
			}
		case *ast.UnionStmt:
			if stmt.SelectList.Selects != nil && len(stmt.SelectList.Selects) > 0 {
				for _, childStmt := range stmt.SelectList.Selects {
					if childStmt.OrderBy != nil && len(childStmt.OrderBy.Items) > 0 {
						for _, item := range childStmt.OrderBy.Items {
							switch item.Expr.(type) {
							case *ast.PositionExpr:
								msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
								return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
							}

						}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictOrderByDifferentDirectionsInSelect SELECT 语句不建议 ORDER BY 多个字段使用不同方向排序
func (self *MysqlCheckerImpl) RestrictOrderByDifferentDirectionsInSelect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	var sortType *bool
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.SelectStmt:
			if stmt.OrderBy != nil && len(stmt.OrderBy.Items) > 1 {
				for _, item := range stmt.OrderBy.Items {
					//首先，如果sortType为空，先赋初始值
					if sortType == nil {
						sortType = &item.Desc
						continue
					}

					//接下来，再判断下一次的SortType是否和上一次的一致
					if *sortType != item.Desc {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}
		case *ast.UnionStmt:
			//首先，先判断Union外部的SortBy的排序情况
			if stmt.OrderBy != nil && len(stmt.OrderBy.Items) > 1 {
				for _, item := range stmt.OrderBy.Items {
					//首先，如果sortType为空，先赋初始值
					if sortType == nil {
						sortType = &item.Desc
						continue
					}

					//接下来，再判断下一次的SortType是否和上一次的一致
					if *sortType != item.Desc {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}

			//其次，再判断Union内部selectList的SortBy的排序情况
			if stmt.SelectList.Selects != nil && len(stmt.SelectList.Selects) > 0 {
				for _, childStmt := range stmt.SelectList.Selects {
					if childStmt.OrderBy != nil && len(childStmt.OrderBy.Items) > 1 {
						for _, item := range childStmt.OrderBy.Items {
							//首先，如果sortType为空，先赋初始值
							if sortType == nil {
								sortType = &item.Desc
								continue
							}

							//接下来，再判断下一次的SortType是否和上一次的一致
							if *sortType != item.Desc {
								msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
								return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
							}
						}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictGroupByFuncStatementsInSelect SELECT 语句不建议 GROUP BY 表达式或函数
func (self *MysqlCheckerImpl) RestrictGroupByFuncStatementsInSelect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		var vister = &sql_parser_visitor.FieldVisitor{}
		stmtNode.Accept(vister)

		switch stmt := stmtNode.(type) {
		case *ast.SelectStmt:
			if stmt.GroupBy != nil && len(stmt.GroupBy.Items) > 0 {
				for _, item := range stmt.GroupBy.Items {
					switch node := item.Expr.(type) {
					case *ast.FuncCallExpr, *ast.AggregateFuncExpr:
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					case *ast.ColumnNameExpr:
						if dbwutils.StringInArray(node.Name.Name.String(), vister.FuncFieldAsNameList) {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		case *ast.UnionStmt:
			if stmt.SelectList.Selects != nil && len(stmt.SelectList.Selects) > 0 {
				for _, childStmt := range stmt.SelectList.Selects {
					if childStmt.GroupBy != nil && len(childStmt.GroupBy.Items) > 0 {
						for _, item := range childStmt.GroupBy.Items {
							switch node := item.Expr.(type) {
							case *ast.FuncCallExpr, *ast.AggregateFuncExpr:
								msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
								return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
							case *ast.ColumnNameExpr:
								if dbwutils.StringInArray(node.Name.Name.String(), vister.FuncFieldAsNameList) {
									msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
									return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
								}
							}
						}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictOrderByFuncStatementsInSelect SELECT 语句不建议 ORDER BY 表达式或函数
func (self *MysqlCheckerImpl) RestrictOrderByFuncStatementsInSelect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		var vister = &sql_parser_visitor.FieldVisitor{}
		stmtNode.Accept(vister)

		switch stmt := stmtNode.(type) {
		case *ast.SelectStmt:
			if stmt.OrderBy != nil && len(stmt.OrderBy.Items) > 0 {
				for _, item := range stmt.OrderBy.Items {
					switch node := item.Expr.(type) {
					case *ast.FuncCallExpr, *ast.AggregateFuncExpr:
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					case *ast.ColumnNameExpr:
						if dbwutils.StringInArray(node.Name.Name.String(), vister.FuncFieldAsNameList) {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}

				}
			}
		case *ast.UnionStmt:
			//首先，先判断Union外部的SortBy的情况
			if stmt.OrderBy != nil && len(stmt.OrderBy.Items) > 0 {
				for _, item := range stmt.OrderBy.Items {
					switch node := item.Expr.(type) {
					case *ast.FuncCallExpr, *ast.AggregateFuncExpr:
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					case *ast.ColumnNameExpr:
						if dbwutils.StringInArray(node.Name.Name.String(), vister.FuncFieldAsNameList) {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}

			if stmt.SelectList.Selects != nil && len(stmt.SelectList.Selects) > 0 {
				for _, childStmt := range stmt.SelectList.Selects {
					if childStmt.OrderBy != nil && len(childStmt.OrderBy.Items) > 0 {
						for _, item := range childStmt.OrderBy.Items {
							switch node := item.Expr.(type) {
							case *ast.FuncCallExpr, *ast.AggregateFuncExpr:
								msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
								return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
							case *ast.ColumnNameExpr:
								if dbwutils.StringInArray(node.Name.Name.String(), vister.FuncFieldAsNameList) {
									msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
									return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
								}
							}
						}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictOrderByRandStatementsInSelect SELECT 语句不建议使用 ORDER BY RAND()
func (self *MysqlCheckerImpl) RestrictOrderByRandStatementsInSelect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.SelectStmt:
			if stmt.OrderBy != nil && len(stmt.OrderBy.Items) > 0 {
				for _, item := range stmt.OrderBy.Items {
					switch expr := item.Expr.(type) {
					case *ast.FuncCallExpr:
						name := strings.ToLower(expr.FnName.String())
						if name == "random" || name == "rand" {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}
		case *ast.UnionStmt:
			//首先，先判断Union外部的SortBy的情况
			if stmt.OrderBy != nil && len(stmt.OrderBy.Items) > 0 {
				for _, item := range stmt.OrderBy.Items {
					switch expr := item.Expr.(type) {
					case *ast.FuncCallExpr:
						name := strings.ToLower(expr.FnName.String())
						if name == "random" || name == "rand" {
							msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
							return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
						}
					}
				}
			}

			if stmt.SelectList.Selects != nil && len(stmt.SelectList.Selects) > 0 {
				for _, childStmt := range stmt.SelectList.Selects {
					if childStmt.OrderBy != nil && len(childStmt.OrderBy.Items) > 0 {
						for _, item := range childStmt.OrderBy.Items {
							switch expr := item.Expr.(type) {
							case *ast.FuncCallExpr:
								name := strings.ToLower(expr.FnName.String())
								if name == "random" || name == "rand" {
									msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
									return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
								}
							}

						}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictHavingStatementsInSelect SELECT 语句不建议使用 HAVING 子句
func (self *MysqlCheckerImpl) RestrictHavingStatementsInSelect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.SelectStmt:
			if stmt.Having != nil {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		case *ast.UnionStmt:
			if stmt.SelectList.Selects != nil && len(stmt.SelectList.Selects) > 0 {
				for _, childStmt := range stmt.SelectList.Selects {
					if childStmt.Having != nil {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictUnionStatementsInSelect SELECT 语句不建议使用 UNION
func (self *MysqlCheckerImpl) RestrictUnionStatementsInSelect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmtNode.(type) {
		case *ast.UnionStmt:
			msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
			return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictLikeStatementsUsePreWildcards WHERE 条件中不建议使用前通配符查找
func (self *MysqlCheckerImpl) RestrictLikeStatementsUsePreWildcards(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		var vister = &sql_parser_visitor.MysqlLikeWildcardVisitor{}
		stmtNode.Accept(vister)
		if vister.ExistPreWildcard {
			msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
			return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictLikeStatementsWithoutWildcards WHERE 条件中检测没有通配符的 LIKE 语句
func (self *MysqlCheckerImpl) RestrictLikeStatementsWithoutWildcards(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		var vister = &sql_parser_visitor.MysqlLikeWildcardVisitor{}
		stmtNode.Accept(vister)
		if vister.NotExistWildcard {
			msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
			return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictReverseQueryInWhere WHERE 条件中不建议使用反向查询（NOT IN / NOT LIKE）
func (self *MysqlCheckerImpl) RestrictReverseQueryInWhere(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		var vister = &sql_parser_visitor.ReverserQueryVisitor{}
		stmtNode.Accept(vister)
		if vister.ExistReverserQuery {
			msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
			return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictFilterConnectWithOr WHERE 条件中检测是否通过 OR 操作符连接过滤条件
func (self *MysqlCheckerImpl) RestrictFilterConnectWithOr(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		var vister = &sql_parser_visitor.ConnectWithOr{}
		stmtNode.Accept(vister)
		if vister.IsConnectWithOr {
			msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
			return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictMultipleTableUpdateAndDeleteNumber 限制 UPDATE/DELETE 语句多表关联的数量
func (self *MysqlCheckerImpl) RestrictMultipleTableUpdateAndDeleteNumber(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	//将配置值解析出来
	value, err := strconv.Atoi(input.CheckRule.Value)
	if err != nil {
		msg := fmt.Sprintf("The rule configuration fails to be resolved. Please check the rule configuration value, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		var vister = &sql_parser_visitor.TableNameVisitor{}
		stmtNode.Accept(vister)

		if len(vister.TableNameList) > value {
			msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
			return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictOrderByDifferentTablesInSelect SELECT 语句不建议对不同的表 GROUP BY 或 ORDER BY
func (self *MysqlCheckerImpl) RestrictOrderByDifferentTablesInSelect(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		var vister = &sql_parser_visitor.TableNameVisitor{}
		stmtNode.Accept(vister)
		switch stmt := stmtNode.(type) {
		case *ast.SelectStmt:
			if stmt.GroupBy != nil && len(vister.TableNameList) > 1 {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
			if stmt.OrderBy != nil && len(vister.TableNameList) > 1 {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}

		case *ast.UnionStmt:
			if stmt.SelectList.Selects != nil && len(stmt.SelectList.Selects) > 0 {
				for _, childStmt := range stmt.SelectList.Selects {
					if childStmt.GroupBy != nil && len(vister.TableNameList) > 1 {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
					if childStmt.OrderBy != nil && len(vister.TableNameList) > 1 {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsCheckMultipleTableUpdateAndDeleteJoinHaveOn UPDATE/DELETE 语句检测多表关联语法是否完整（JOIN 遗漏 ON 子句）
func (self *MysqlCheckerImpl) IsCheckMultipleTableUpdateAndDeleteJoinHaveOn(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmtNode.(type) {
		case *ast.UpdateStmt, *ast.DeleteStmt:
			var vister = &sql_parser_visitor.JoinVisitor{}
			stmtNode.Accept(vister)
			if vister.IsHaveOnCondition {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsCheckUpdateAndDeleteHaveWhere UPDATE/DELETE 语句建议指定 WHERE 条件
func (self *MysqlCheckerImpl) IsCheckUpdateAndDeleteHaveWhere(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}
	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.UpdateStmt:
			if stmt.Where == nil {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		case *ast.DeleteStmt:
			if stmt.Where == nil {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsCheckInsertCannotBeDuplicated INSERT 语句字段名不能重复
func (self *MysqlCheckerImpl) IsCheckInsertCannotBeDuplicated(ctx context.Context, input *InputRawData) *CheckResult {

	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmtNode.(type) {
		case *ast.InsertStmt:
			//首先，先获取到Insert中所有的列名信息
			var vister = &sql_parser_visitor.FieldVisitor{}
			stmtNode.Accept(vister)

			//接下来判断所有的列名集合中，是否有重复项
			if dbwutils.HasDuplicates(vister.InsertColumnNameList) {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsCheckInsertColAndValueMatch INSERT 语句字段列表和值列表要匹配
func (self *MysqlCheckerImpl) IsCheckInsertColAndValueMatch(ctx context.Context, input *InputRawData) *CheckResult {

	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.InsertStmt:
			//首先，先获取到Insert中所有的列名信息
			var vister = &sql_parser_visitor.FieldVisitor{}
			stmtNode.Accept(vister)

			InsertValueMinCount := -1
			//接下来判断所有插入的值，每一个值对应的数量，取最小的那一组
			for _, value := range stmt.Lists {
				if InsertValueMinCount == -1 || InsertValueMinCount > len(value) {
					InsertValueMinCount = len(value)
				}
			}

			//如果此时查询到的列为空，那么就跳过判断
			if len(vister.InsertColumnNameList) == 0 {
				return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
			}

			//最后，判断插入的列的数量，和最小的插入的值的数量是不是一致的
			if len(vister.InsertColumnNameList) != InsertValueMinCount {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsCheckInsertNeedColumnList INSERT 语句建议指定 INSERT 字段列表
func (self *MysqlCheckerImpl) IsCheckInsertNeedColumnList(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.InsertStmt:
			if stmt.Columns == nil {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsAllowFuncSysdate INSERT 语句不建议使用 SYSDATE() 函数
func (self *MysqlCheckerImpl) IsAllowFuncSysdate(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmtNode.(type) {
		case *ast.InsertStmt:
			var vister = &sql_parser_visitor.InsertValueVisitor{}
			stmtNode.Accept(vister)
			if vister.IsHaveSysDate {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictInsertValuesNumber 限制 INSERT 语句一条 INSERT VALUES 的总行数
func (self *MysqlCheckerImpl) RestrictInsertValuesNumber(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	//将配置值解析出来
	value, err := strconv.Atoi(input.CheckRule.Value)
	if err != nil {
		msg := fmt.Sprintf("The rule configuration fails to be resolved. Please check the rule configuration value, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.InsertStmt:
			if len(stmt.Lists) > value {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsCheckUpdateSetColumnTablePrefix UPDATE 多表时检测 SET 的列是否指定表前缀
func (self *MysqlCheckerImpl) IsCheckUpdateSetColumnTablePrefix(ctx context.Context, input *InputRawData) *CheckResult {

	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch stmtNode.(type) {
		case *ast.UpdateStmt:
			//首先，先判断当前的update语句是否为多表场景
			var vister = &sql_parser_visitor.TableNameVisitor{}
			stmtNode.Accept(vister)
			if len(vister.TableNameList) == 1 {
				break
			}

			//如果是多表场景，判断是否包含表前缀
			var updateVisitor = &sql_parser_visitor.UpdateColumnVisitor{}
			stmtNode.Accept(updateVisitor)
			if updateVisitor.NotHaveTablePrefix {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsCheckUpdateTableCreateTimeColumn UPDATE 语句不建议更新表上的“创建时间”列
func (self *MysqlCheckerImpl) IsCheckUpdateTableCreateTimeColumn(ctx context.Context, input *InputRawData) *CheckResult {

	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	valueList := strings.Split(input.CheckRule.Value, ",")

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch node := stmtNode.(type) {
		case *ast.UpdateStmt:

			//首先，先获取到所有的SET条件内的字段名
			columnNameList := make([]string, 0)
			//对set进行遍历，获取到所有的
			var visitor = &sql_parser_visitor.FieldVisitor{}
			for _, list := range node.List {
				list.Accept(visitor)
				columnNameList = append(columnNameList, visitor.InsertColumnNameList...)
			}

			//接下来，遍历每一个字段名，是否属于配置名字内
			for _, name := range columnNameList {
				if dbwutils.StringInArray(name, valueList) {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsCheckUpdateTableModifyTimeColumn UPDATE 语句建议更新表上的“修改时间”列
func (self *MysqlCheckerImpl) IsCheckUpdateTableModifyTimeColumn(ctx context.Context, input *InputRawData) *CheckResult {

	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	valueList := strings.Split(input.CheckRule.Value, ",")

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch node := stmtNode.(type) {
		case *ast.UpdateStmt:

			//首先，先获取到所有的SET条件内的字段名
			columnNameList := make([]string, 0)
			//对set进行遍历，获取到所有的
			var visitor = &sql_parser_visitor.FieldVisitor{}
			for _, list := range node.List {
				list.Accept(visitor)
				columnNameList = append(columnNameList, visitor.InsertColumnNameList...)
			}

			//接下来，遍历每一个字段名，是否属于配置名字内
			for _, name := range columnNameList {
				if dbwutils.StringInArray(name, valueList) {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsCheckUpdateSetColumnSeparator UPDATE 语句检测 SET 多个列之间的分隔符（ AND 非法）
func (self *MysqlCheckerImpl) IsCheckUpdateSetColumnSeparator(ctx context.Context, input *InputRawData) *CheckResult {

	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch node := stmtNode.(type) {
		case *ast.UpdateStmt:
			for _, list := range node.List {
				switch expr := list.Expr.(type) {
				case *ast.BinaryOperationExpr:
					if expr.Op == opcode.LogicAnd {
						msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
						return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
					}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsCheckUpdateAndDeleteWhereSubQuery UPDATE/DELETE 语句检测 WHERE 条件是否包含子查询
func (self *MysqlCheckerImpl) IsCheckUpdateAndDeleteWhereSubQuery(ctx context.Context, input *InputRawData) *CheckResult {

	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	// 1.执行规则检查
	for _, stmtNode := range stmts {
		switch node := stmtNode.(type) {
		case *ast.UpdateStmt:
			if node.Where == nil {
				break
			}
			var visitor = &sql_parser_visitor.SubQueryVisitor{}
			node.Where.Accept(visitor)
			if visitor.IsContainsSubQuery {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		case *ast.DeleteStmt:
			if node.Where == nil {
				break
			}
			var visitor = &sql_parser_visitor.SubQueryVisitor{}
			node.Where.Accept(visitor)
			if visitor.IsContainsSubQuery {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictWhereInNumber 限制 WHERE 条件中 IN 子句包含元素个数
func (self *MysqlCheckerImpl) RestrictWhereInNumber(ctx context.Context, input *InputRawData) *CheckResult {
	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	//将配置值解析出来
	value, err := strconv.Atoi(input.CheckRule.Value)
	if err != nil {
		msg := fmt.Sprintf("The rule configuration fails to be resolved. Please check the rule configuration value, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
		return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
	}

	for _, stmtNode := range stmts {
		var vister = &sql_parser_visitor.PatternInVisitor{}
		stmtNode.Accept(vister)
		if vister.InNumberMaxCount > value {
			msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
			return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictInsertNotNullColumnCannotBeNull INSERT 语句不能为 NOT NULL 列插入 NULL 值
func (self *MysqlCheckerImpl) RestrictInsertNotNullColumnCannotBeNull(ctx context.Context, input *InputRawData) *CheckResult {

	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err}
	}

	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.InsertStmt:

			InsertFiledList := make([]string, 0)
			var tableName string
			indexList := make([]int, 0)
			//首先，先遍历所有的Values，获取到输入值为Null的情况下的index位置
			for _, valueList := range stmt.Lists {
				for index, value := range valueList {
					switch expr := value.(type) {
					case *test_driver.ValueExpr:
						//如果输入值的类型是null。那么就把这个值对应的位置保存下来
						if expr.Datum.Kind() == test_driver.KindNull {
							indexList = append(indexList, index)
						}
					}
				}
			}
			//其次，根据index的位置，将对应的列名全部拿到
			for i, columnInfo := range stmt.Columns {
				if dbwutils.IntInArray(i, indexList) {
					InsertFiledList = append(InsertFiledList, columnInfo.Name.String())
				}
			}

			//单独拿一次表名，用于后续根据表名获取到该表的DDL
			var tableVisitor = &sql_parser_visitor.TableNameVisitor{}
			stmt.Accept(tableVisitor)
			tableName = tableVisitor.TableNameList[0]

			//判断如果InsertFiledList为空，直接结束（说明不是一个insert。或者没有传入NULL值）
			if len(InsertFiledList) == 0 {
				return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
			}

			//接下来，根据ds信息去连接数据库，获取当前表的信息结果
			req := &datasource.DescribeTableReq{
				Source: input.DataSource,
				DB:     input.DataSource.Db,
				Table:  tableName,
			}
			resp, err := self.Ds.DescribeTable(ctx, req)
			if err != nil {
				msg := fmt.Sprintf("Failed to connect to the database. Please check whether the database is normal or try again later, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}

			//获取所有Not Null的列
			NotNullColumnList := make([]string, 0)
			for _, columnInfo := range resp.Columns {
				if !columnInfo.IsNull {
					NotNullColumnList = append(NotNullColumnList, columnInfo.Name)
				}
			}
			if len(NotNullColumnList) == 0 {
				return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
			}

			log.Info(ctx, "RestrictInsertNotNullColumnCannotBeNull NotNullColumnList is:%v, InsertFiledList is :%v", utils.Show(NotNullColumnList), utils.Show(InsertFiledList))
			//最后，遍历所有Not Null的列，看看列名是否包含在本次传入的场景中
			for _, columnName := range NotNullColumnList {
				if dbwutils.StringInArray(columnName, InsertFiledList) {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsCheckInsertTableAndColumnExist INSERT 语句检测 INSERT 的表/字段是否存在
func (self *MysqlCheckerImpl) IsCheckInsertTableAndColumnExist(ctx context.Context, input *InputRawData) *CheckResult {

	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}

	//首先，先获取所有的Insert列以及对应的表名
	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.InsertStmt:
			InsertFiledList := make([]string, 0)
			var tableName string

			var visitor = &sql_parser_visitor.FieldVisitor{}
			stmt.Accept(visitor)
			InsertFiledList = append(InsertFiledList, visitor.InsertColumnNameList...)

			//单独拿一次表名，用于后续根据表名获取到该表的DDL
			var tableVisitor = &sql_parser_visitor.TableNameVisitor{}
			stmt.Accept(tableVisitor)
			tableName = tableVisitor.TableNameList[0]

			//接下来，查询数据表和对应的字段
			req := &datasource.DescribeTableReq{
				Source: input.DataSource,
				DB:     input.DataSource.Db,
				Table:  tableName,
			}
			resp, err := self.Ds.DescribeTable(ctx, req)
			if err != nil {
				msg := fmt.Sprintf("Failed to connect to the database. Please check whether the database is normal or try again later, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}

			//如果插入场景下的列名为空，说明Insert语句没有列名，那么就直接返回结果即可（写在查询之后，是为了判断表是存在的）
			if len(InsertFiledList) == 0 {
				return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
			}

			//获取数据表对应的所有的列名
			ColumnList := make([]string, 0)
			for _, columnInfo := range resp.Columns {
				ColumnList = append(ColumnList, columnInfo.Name)
			}
			if len(ColumnList) == 0 {
				msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
				return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
			}

			log.Info(ctx, "IsCheckInsertTableAndColumnExist ColumnList is:%v, InsertFiledList is :%v", utils.Show(ColumnList), utils.Show(InsertFiledList))
			for _, name := range InsertFiledList {
				if !dbwutils.StringInArray(name, ColumnList) {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsCheckUpdatePrimaryKeyValue UPDATE 语句检测是否更新了主键
func (self *MysqlCheckerImpl) IsCheckUpdatePrimaryKeyValue(ctx context.Context, input *InputRawData) *CheckResult {

	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}

	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.UpdateStmt:
			columnNameList := make([]string, 0)
			tableNameList := make([]string, 0)

			//首先，先获取到所有的SET条件内的字段名
			var visitor = &sql_parser_visitor.FieldVisitor{}
			for _, list := range stmt.List {
				list.Accept(visitor)
				columnNameList = append(columnNameList, visitor.InsertColumnNameList...)
			}

			//单独拿一次表名，用于后续根据表名获取到该表的DDL
			var tableVisitor = &sql_parser_visitor.TableNameVisitor{}
			stmt.Accept(tableVisitor)
			tableNameList = append(tableNameList, tableVisitor.TableNameList...)

			if len(tableNameList) == 0 || len(columnNameList) == 0 {
				log.Info(ctx, "IsCheckUpdatePrimaryKeyValue parser sql, tableNameList or columnNameList is empty!")
				return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
			}

			primaryKeyList := make([]string, 0)
			//接下来，遍历所有在SQL语句中出现的表名，获取到主键列的名称
			for _, tableName := range tableNameList {
				req := &datasource.DescribeTableReq{
					Source: input.DataSource,
					DB:     input.DataSource.Db,
					Table:  tableName,
				}
				resp, err := self.Ds.DescribeTable(ctx, req)
				if err != nil {
					msg := fmt.Sprintf("Failed to connect to the database. Please check whether the database is normal or try again later, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}

				for _, index := range resp.Indexs {
					if index.Name == "PRIMARY" {
						primaryKeyList = append(primaryKeyList, index.Columns...)
					}
				}
			}

			log.Info(ctx, "IsCheckUpdatePrimaryKeyValue columnNameList is:%v, primaryKeyList is :%v", utils.Show(columnNameList), utils.Show(primaryKeyList))
			//获取到所有的主键列的名称后，遍历update更新的所有的列的列名
			for _, columnName := range columnNameList {
				if dbwutils.StringInArray(columnName, primaryKeyList) {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsCheckUpdateTableAndColumnExist UPDATE 语句检测UPDATE的表/字段是否存在
func (self *MysqlCheckerImpl) IsCheckUpdateTableAndColumnExist(ctx context.Context, input *InputRawData) *CheckResult {

	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}

	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.UpdateStmt:
			//首先，先获取到所有的SET条件内的字段名
			columnNameList := make([]string, 0)
			tableNameList := make([]string, 0)

			var visitor = &sql_parser_visitor.FieldVisitor{}
			for _, list := range stmt.List {
				list.Accept(visitor)
				columnNameList = append(columnNameList, visitor.InsertColumnNameList...)
			}

			//单独拿一次表名，用于后续根据表名获取到该表的DDL
			var tableVisitor = &sql_parser_visitor.TableNameVisitor{}
			stmt.Accept(tableVisitor)
			tableNameList = append(tableNameList, tableVisitor.TableNameList...)

			if len(tableNameList) == 0 || len(columnNameList) == 0 {
				log.Info(ctx, "IsCheckUpdateTableAndColumnExist parser sql, tableNameList or columnNameList is empty!")
				return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
			}

			TableColumnList := make([]string, 0)
			//接下来，遍历所有在SQL语句中出现的表名，获取到主键列的名称
			for _, tableName := range tableNameList {
				req := &datasource.DescribeTableReq{
					Source: input.DataSource,
					DB:     input.DataSource.Db,
					Table:  tableName,
				}
				resp, err := self.Ds.DescribeTable(ctx, req)
				if err != nil {
					msg := fmt.Sprintf("Failed to connect to the database. Please check whether the database is normal or try again later, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
				for _, columnInfo := range resp.Columns {
					TableColumnList = append(TableColumnList, columnInfo.Name)
				}
			}

			log.Info(ctx, "IsCheckUpdateTableAndColumnExist columnNameList is:%v, TableColumnList is :%v", utils.Show(columnNameList), utils.Show(TableColumnList))
			//获取到所有的主键列的名称后，遍历update更新的所有的列的列名
			for _, columnName := range columnNameList {
				if !dbwutils.StringInArray(columnName, TableColumnList) {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// IsCheckUpdateUniqueKeyValue UPDATE 语句检测是否更新了唯一键
func (self *MysqlCheckerImpl) IsCheckUpdateUniqueKeyValue(ctx context.Context, input *InputRawData) *CheckResult {

	// 0.解析SQL
	stmts, err := sqlParser(input.Sql)
	if err != nil || len(stmts) == 0 {
		log.Info(ctx, "SQL format is not valid, reason for the error:：%s", err.Error())
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}

	for _, stmtNode := range stmts {
		switch stmt := stmtNode.(type) {
		case *ast.UpdateStmt:
			//首先，先获取到所有的SET条件内的字段名
			columnNameList := make([]string, 0)
			tableNameList := make([]string, 0)

			var visitor = &sql_parser_visitor.FieldVisitor{}
			for _, list := range stmt.List {
				list.Accept(visitor)
				columnNameList = append(columnNameList, visitor.InsertColumnNameList...)
			}

			//单独拿一次表名，用于后续根据表名获取到该表的DDL
			var tableVisitor = &sql_parser_visitor.TableNameVisitor{}
			stmt.Accept(tableVisitor)
			tableNameList = append(tableNameList, tableVisitor.TableNameList...)

			if len(tableNameList) == 0 || len(columnNameList) == 0 {
				log.Info(ctx, "IsCheckUpdateUniqueKeyValue parser sql, tableNameList or columnNameList is empty!")
				return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
			}

			uniqueKeyList := make([]string, 0)
			//接下来，遍历所有在SQL语句中出现的表名，获取到主键列的名称
			for _, tableName := range tableNameList {
				req := &datasource.DescribeTableReq{
					Source: input.DataSource,
					DB:     input.DataSource.Db,
					Table:  tableName,
				}
				resp, err := self.Ds.DescribeTable(ctx, req)
				if err != nil {
					msg := fmt.Sprintf("Failed to connect to the database. Please check whether the database is normal or try again later, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}

				for _, index := range resp.Indexs {
					if index.Type == "UNIQUE" {
						uniqueKeyList = append(uniqueKeyList, index.Columns...)
					}
				}
			}

			log.Info(ctx, "IsCheckUpdateUniqueKeyValue columnNameList is:%v, uniqueKeyList is :%v", utils.Show(columnNameList), utils.Show(uniqueKeyList))
			//获取到所有的主键列的名称后，遍历update更新的所有的列的列名
			for _, columnName := range columnNameList {
				if dbwutils.StringInArray(columnName, uniqueKeyList) {
					msg := fmt.Sprintf("Triggered a security rule restriction, rule ID：%s，rule Name：%s", input.RuleID, input.RuleName)
					return &CheckResult{Result: false, ErrorMsg: errors.New(msg), Action: input.RuleName}
				}
			}
		}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) Type() shared.DataSourceType {
	return shared.MySQL
}

func (self *MysqlCheckerImpl) InitSecRuleChecker(DSType shared.DataSourceType) map[string]RuleFunc {
	mysqlValidationFunc := map[string]RuleFunc{
		"IsAllowCreateTableExecDirect":                  self.IsAllowCreateTableExecDirect,
		"IsAllowDropTableExecDirect":                    self.IsAllowDropTableExecDirect,
		"IsAllowTruncateTableExecDirect":                self.IsAllowTruncateTableExecDirect,
		"IsAllowInsertExecDirect":                       self.IsAllowInsertExecDirect,
		"IsAllowUpdateExecDirect":                       self.IsAllowUpdateExecDirect,
		"IsAllowDeleteExecDirect":                       self.IsAllowDeleteExecDirect,
		"IsAllowAlterTableExecDirect":                   self.IsAllowAlterTableExecDirect,
		"IsAllowKillExecDirect":                         self.IsAllowKillExecDirect,
		"IsAllowCreateProcessExecDirect":                self.IsAllowCreateProcessExecDirect,
		"IsAllowUnknownSqlExec":                         self.IsAllowUnknownSqlExec,
		"IsCheckDbOrTablePrivilege":                     self.IsCheckDbOrTablePrivilege,
		"IsAllowUnknownSqlPermissionsExec":              self.IsAllowUnknownSqlPermissionsExec,
		"IsCheckTableNeedHavePrimaryKey":                self.IsCheckTableNeedHavePrimaryKey,
		"IsCheckTableNeedHaveCommand":                   self.IsCheckTableNeedHaveCommand,
		"IsCheckTableHaveForeignKey":                    self.IsCheckTableHaveForeignKey,
		"IsCheckTableNameCase":                          self.IsCheckTableNameCase,
		"IsCheckTableEngine":                            self.IsCheckTableEngine,
		"IsCheckTablePartitionSettings":                 self.IsCheckTablePartitionSettings,
		"IsCheckTableIncludeColumns":                    self.IsCheckTableIncludeColumns,
		"IsCheckColumnCharset":                          self.IsCheckColumnCharset,
		"IsCheckTableCharacter":                         self.IsCheckTableCharacter,
		"IsCheckTableCollation":                         self.IsCheckTableCollation,
		"IsCheckTableNameKeyword":                       self.IsCheckTableNameKeyword,
		"IsCheckTableIndexCount":                        self.IsCheckTableIndexCount,
		"IsCheckTableColumnCount":                       self.IsCheckTableColumnCount,
		"IsCheckTableAutoIncrement":                     self.IsCheckTableAutoIncrement,
		"IsCheckTablePrimaryKeyAutoIncrement":           self.IsCheckTablePrimaryKeyAutoIncrement,
		"IsCheckColumnNameKeyword":                      self.IsCheckColumnNameKeyword,
		"IsCheckColumnNameCase":                         self.IsCheckColumnNameCase,
		"RestrictPrimaryKeyColumnTypes":                 self.RestrictPrimaryKeyColumnTypes,
		"RestrictPrimaryKeyCounts":                      self.RestrictPrimaryKeyCounts,
		"RestrictIndexKeyInColumnCounts":                self.RestrictIndexKeyInColumnCounts,
		"RestrictNormalIndexKeyFormat":                  self.RestrictNormalIndexKeyFormat,
		"RestrictUniqueIndexKeyFormat":                  self.RestrictUniqueIndexKeyFormat,
		"RestrictIndexNeedName":                         self.RestrictIndexNeedName,
		"RestrictColumnDataType":                        self.RestrictColumnDataType,
		"RestrictColumnUseEnumType":                     self.RestrictColumnUseEnumType,
		"RestrictColumnCheck":                           self.RestrictColumnCheck,
		"RestrictColumnUseFloatType":                    self.RestrictColumnUseFloatType,
		"RestrictColumnHasDefaultValue":                 self.RestrictColumnHasDefaultValue,
		"RestrictColumnNotNull":                         self.RestrictColumnNotNull,
		"RestrictIncrementColumnName":                   self.RestrictIncrementColumnName,
		"RestrictIncrementColumnIsUnsigned":             self.RestrictIncrementColumnIsUnsigned,
		"RestrictIndexColumnIsNotNull":                  self.RestrictIndexColumnIsNotNull,
		"RestrictCharColumnLength":                      self.RestrictCharColumnLength,
		"RestrictVarCharColumnLength":                   self.RestrictVarCharColumnLength,
		"RestrictColumnUseZeroFill":                     self.RestrictColumnUseZeroFill,
		"RestrictColumnNeedComment":                     self.RestrictColumnNeedComment,
		"RestrictOffsetSizeInSelect":                    self.RestrictOffsetSizeInSelect,
		"IsCheckSelectNeedWhere":                        self.IsCheckSelectNeedWhere,
		"RestrictGroupByConstantStatementsInSelect":     self.RestrictGroupByConstantStatementsInSelect,
		"RestrictTableAssociationsCountInSelect":        self.RestrictTableAssociationsCountInSelect,
		"RestrictAsteriskStatementInSelect":             self.RestrictAsteriskStatementInSelect,
		"RestrictOrderByConstantInSelect":               self.RestrictOrderByConstantInSelect,
		"RestrictOrderByDifferentDirectionsInSelect":    self.RestrictOrderByDifferentDirectionsInSelect,
		"RestrictGroupByFuncStatementsInSelect":         self.RestrictGroupByFuncStatementsInSelect,
		"RestrictOrderByFuncStatementsInSelect":         self.RestrictOrderByFuncStatementsInSelect,
		"RestrictOrderByRandStatementsInSelect":         self.RestrictOrderByRandStatementsInSelect,
		"RestrictHavingStatementsInSelect":              self.RestrictHavingStatementsInSelect,
		"RestrictUnionStatementsInSelect":               self.RestrictUnionStatementsInSelect,
		"RestrictLikeStatementsUsePreWildcards":         self.RestrictLikeStatementsUsePreWildcards,
		"RestrictLikeStatementsWithoutWildcards":        self.RestrictLikeStatementsWithoutWildcards,
		"RestrictReverseQueryInWhere":                   self.RestrictReverseQueryInWhere,
		"RestrictFilterConnectWithOr":                   self.RestrictFilterConnectWithOr,
		"RestrictOrderByDifferentTablesInSelect":        self.RestrictOrderByDifferentTablesInSelect,
		"RestrictMultipleTableUpdateAndDeleteNumber":    self.RestrictMultipleTableUpdateAndDeleteNumber,
		"IsCheckMultipleTableUpdateAndDeleteJoinHaveOn": self.IsCheckMultipleTableUpdateAndDeleteJoinHaveOn,
		"IsCheckUpdateAndDeleteHaveWhere":               self.IsCheckUpdateAndDeleteHaveWhere,
		"IsCheckInsertCannotBeDuplicated":               self.IsCheckInsertCannotBeDuplicated,
		"IsCheckInsertColAndValueMatch":                 self.IsCheckInsertColAndValueMatch,
		"IsCheckInsertNeedColumnList":                   self.IsCheckInsertNeedColumnList,
		"RestrictInsertNotNullColumnCannotBeNull":       self.RestrictInsertNotNullColumnCannotBeNull,
		"IsAllowFuncSysdate":                            self.IsAllowFuncSysdate,
		"RestrictInsertValuesNumber":                    self.RestrictInsertValuesNumber,
		"IsCheckInsertTableAndColumnExist":              self.IsCheckInsertTableAndColumnExist,
		"IsCheckUpdateSetColumnTablePrefix":             self.IsCheckUpdateSetColumnTablePrefix,
		"IsCheckUpdateTableCreateTimeColumn":            self.IsCheckUpdateTableCreateTimeColumn,
		"IsCheckUpdateTableModifyTimeColumn":            self.IsCheckUpdateTableModifyTimeColumn,
		"IsCheckUpdateSetColumnSeparator":               self.IsCheckUpdateSetColumnSeparator,
		"IsCheckUpdateAndDeleteWhereSubQuery":           self.IsCheckUpdateAndDeleteWhereSubQuery,
		"RestrictWhereInNumber":                         self.RestrictWhereInNumber,
		"IsCheckUpdatePrimaryKeyValue":                  self.IsCheckUpdatePrimaryKeyValue,
		"IsCheckUpdateTableAndColumnExist":              self.IsCheckUpdateTableAndColumnExist,
		"IsCheckUpdateUniqueKeyValue":                   self.IsCheckUpdateUniqueKeyValue,
		// 新增多云的DML规则
		"RestrictSQLLength":               self.RestrictSQLLength,
		"RestrictSQLCrossDB":              self.RestrictSQLCrossDB,
		"RestrictSQLWithOutTableName":     self.RestrictSQLWithOutTableName,
		"RestrictSQLStatement":            self.RestrictSQLStatement,
		"RestrictInsertWithOutColumnInfo": self.RestrictInsertWithOutColumnInfo,
		"RestrictInsertOnDuplicateKey":    self.RestrictInsertOnDuplicateKey,
		"RestrictInsertByReplace":         self.RestrictInsertByReplace,
		"RestrictUpdateWithOnlyValue":     self.RestrictUpdateWithOnlyValue,
	}
	return mysqlValidationFunc
}

func sqlParser(sqlText string) ([]ast.StmtNode, error) {
	p := parser.New()
	stmts, _, err := p.Parse(sqlText, "UTF-8", "")
	if err != nil {
		return nil, fmt.Errorf("sql parser failed")
	}
	return stmts, nil
}

type ParsedDsl struct {
	ActionYes string
	ActionNo  string
	Factor    string
	Value     interface{}
}

func (self *MysqlCheckerImpl) checkDSL(ctx context.Context, input *InputRawData) (*ParsedDsl, error) {
	// 基础配置类型主要为值类型，不需要解析DSL
	if input.CheckRule.DetectPoint == model.DetectionPointType_BaseConfigurationSpecs.String() {
		return &ParsedDsl{Value: input.CheckRule.Value}, nil
	}
	// 1.解析DSL
	// 解析检测点类型
	detectPoint := input.CheckRule.DetectPoint
	detector, ok := DetectionSet[detectPoint]
	if !ok {
		return nil, fmt.Errorf("the current detection point type is illegal:%s", detectPoint)
	}
	supportedAction := detector.Action
	supportedFactor := detector.Factor
	// 解析dsl_flow
	dslFlow := input.CheckRule.DslFlow
	actionYes := dslFlow.ActionYes
	actionNo := dslFlow.ActionNo
	inputFactorName := dslFlow.FactorName
	inputFactorValue := dslFlow.FactorValue
	if actionYes != "" {
		// 判断actionYes是否合法
		_, okYes := supportedAction[actionYes]
		if !okYes {
			return nil, fmt.Errorf("the current ActionYes action is illegal:%s", actionYes)
		}
	}
	if actionNo != "" {
		// 判断actionNo是否合法
		_, okNo := supportedAction[actionNo]
		if !okNo {
			return nil, fmt.Errorf("the current detection point type is illegal: %s", actionNo)
		}
	}
	// 判断factor类型是否合法
	factorVal, okFactor := supportedFactor[inputFactorName]
	if !okFactor {
		return nil, fmt.Errorf("the current factor type is illegal: %s", inputFactorName)
	}
	// factor数值类型断言
	pass, converted := checkAndConvertedFactorValue(ctx, inputFactorValue, factorVal)
	if !pass {
		return nil, fmt.Errorf("the current factor value is illegal: %s", inputFactorValue)
	}
	return &ParsedDsl{Value: converted, ActionNo: actionNo, ActionYes: actionYes, Factor: inputFactorName}, nil
}

func checkAndConvertedFactorValue(ctx context.Context, inputFactorValue interface{}, factorVal []string) (bool, interface{}) {
	switch value := inputFactorValue.(type) {
	case string:
		log.Info(ctx, "factor value type is string:%s", inputFactorValue)
		for _, item := range factorVal {
			if inputFactorValue == item {
				return true, value
			}
		}
		return false, value
	case []string:
		log.Info(ctx, "factor value type is slice:%s", inputFactorValue)
		return dbwutils.AllStrElementsInSlice(value, factorVal), value
	case []interface{}:
		log.Info(ctx, "factor value type is interface slice:%s", inputFactorValue)
		switch reflect.TypeOf(factorVal[0]).String() {
		case "string":
			var convertedInputVal []string
			for _, v := range value {
				val, ok := v.(string)
				if ok {
					convertedInputVal = append(convertedInputVal, val)
				}
			}
			return dbwutils.AllStrElementsInSlice(convertedInputVal, factorVal), convertedInputVal
		default:
			return false, value
		}
	default:
		log.Warn(ctx, "Unsupported factor value:%s", value)
		return false, value
	}
}

func checkStatementNodeType(node ast.StmtNode, dsl *ParsedDsl) bool {
	nodeType := reflect.TypeOf(node)
	nodeTypeStrs := strings.Split(nodeType.String(), ".")
	var nodeTypeStr = nodeType.String()
	if len(nodeTypeStrs) > 1 {
		nodeTypeStr = nodeTypeStrs[len(nodeTypeStrs)-1]
	}
	targetStmt, ok := dsl.Value.(string)
	if !ok {
		return false
	}
	nodeTypeSet, ok := StmtTypeMap[targetStmt]
	if !ok {
		return false
	}
	for _, nodeType := range nodeTypeSet {
		if nodeTypeStr == nodeType {
			switch dsl.ActionYes {
			case "allow_execute_direct":
				return true
			case "allow_submit_ticket":
				return false
			case "reject_execute":
				return false
			default:
				return false
			}
		}
	}
	return true
}

// RestrictSQLLength 配置SQL长度不超过
func (self *MysqlCheckerImpl) RestrictSQLLength(ctx context.Context, input *InputRawData) *CheckResult {
	if int64(len(input.Sql)) > conv.StrToInt64(input.CheckRule.Value, 0) {
		return &CheckResult{Result: true, ErrorMsg: fmt.Errorf("sql length exceeds the limit")}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictSQLCrossDB 不支持SQL跨DB
func (self *MysqlCheckerImpl) RestrictSQLCrossDB(ctx context.Context, input *InputRawData) *CheckResult {
	// 1、解析SQL中包含的DB数量
	dbs, err := GetSQLInfo(input.Sql)
	if err != nil {
		log.Warn(ctx, "Get SQL DBs error %v", err)
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}
	if len(dbs.Database) > 1 {
		log.Warn(ctx, "Get SQL DBs more than 2,cross db is not allowed", err)
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictSQLWithOutTableName 不支持无表名的SQL
func (self *MysqlCheckerImpl) RestrictSQLWithOutTableName(ctx context.Context, input *InputRawData) *CheckResult {
	// 1、解析SQL中包含的table数量
	dbs, err := GetSQLInfo(input.Sql)
	if err != nil {
		log.Warn(ctx, "Get SQL DBs error %v", err)
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}
	if len(dbs.Table) > 1 {
		log.Warn(ctx, "Get SQL DBs more than 2,cross db is not allowed", err)
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}

	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}

}
func (self *MysqlCheckerImpl) RestrictSQLStatement(ctx context.Context, input *InputRawData) *CheckResult {
	// 1、解析SQL的类型是否是insert|delete|update
	dbs, err := GetSQLInfo(input.Sql)
	if err != nil {
		log.Warn(ctx, "Get SQL DBs error %v", err)
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}
	if !dbs.IsAllowStmt {
		log.Warn(ctx, "SQL only support insert,update,delete")
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) RestrictInsertWithOutColumnInfo(ctx context.Context, input *InputRawData) *CheckResult {
	// 1、解析SQL的类型是否是insert|delete|update
	dbs, err := GetSQLInfo(input.Sql)
	if err != nil {
		log.Warn(ctx, "Get SQL DBs error %v", err)
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}
	if !dbs.InsertFixColumn {
		log.Warn(ctx, "insert SQL not fix target columns ")
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}

}
func (self *MysqlCheckerImpl) RestrictInsertOnDuplicateKey(ctx context.Context, input *InputRawData) *CheckResult {
	// 1、解析SQL的类型是否是insert|delete|update
	dbs, err := GetSQLInfo(input.Sql)
	if err != nil {
		log.Warn(ctx, "Get SQL DBs error %v", err)
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}
	if dbs.OnDuplicate {
		log.Warn(ctx, "insert SQL not support on duplicate key ")
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

func (self *MysqlCheckerImpl) RestrictInsertByReplace(ctx context.Context, input *InputRawData) *CheckResult {
	// 1、解析SQL的类型是否是insert|delete|update
	dbs, err := GetSQLInfo(input.Sql)
	if err != nil {
		log.Warn(ctx, "Get SQL DBs error %v", err)
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}
	if dbs.IsReplace {
		log.Warn(ctx, "insert SQL not support on duplicate key ")
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

// RestrictUpdateWithOnlyValue 只支持update t set id =1 不支持update t set id=id +1
func (self *MysqlCheckerImpl) RestrictUpdateWithOnlyValue(ctx context.Context, input *InputRawData) *CheckResult {
	// 1、解析SQL的类型是否是insert|delete|update
	dbs, err := GetSQLInfo(input.Sql)
	if err != nil {
		log.Warn(ctx, "Get SQL DBs error %v", err)
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}
	if dbs.UpdateWithColumnField {
		log.Warn(ctx, "update SQL contains column field,not support ")
		return &CheckResult{Result: true, ErrorMsg: err, Action: input.RuleName}
	}
	return &CheckResult{Result: true, ErrorMsg: nil, Action: input.RuleName}
}

type GetDatabaseVisitor struct {
	Database              map[string]bool
	Table                 []string
	IsAllowStmt           bool
	InsertFixColumn       bool
	OnDuplicate           bool
	IsReplace             bool
	UpdateWithColumnField bool
}

func (d *GetDatabaseVisitor) Enter(in ast.Node) (out ast.Node, skipChildren bool) {
	switch node := in.(type) {
	case *ast.TableName: // 所有表的集合
		if node.Schema.String() != "" {
			d.Database[node.Schema.String()] = true
		}
		d.Table = append(d.Table, node.Name.String())
	case *ast.DeleteStmt:
		d.IsAllowStmt = true
	case *ast.UpdateStmt:
		d.IsAllowStmt = true
		if len(node.List) > 0 {
			switch node.List[0].Expr.(type) {
			case *ast.BinaryOperationExpr:
				d.UpdateWithColumnField = true
			}
		}
	case *ast.InsertStmt:
		d.IsAllowStmt = true
		if len(node.Columns) > 0 {
			d.InsertFixColumn = true
		}
		if len(node.OnDuplicate) > 0 {
			d.OnDuplicate = true
		}
		if node.IsReplace {
			d.IsReplace = true
		}
	}
	return in, false
}

func (d *GetDatabaseVisitor) Leave(in ast.Node) (out ast.Node, ok bool) {
	return in, true
}

func GetSQLInfo(sql string) (*GetDatabaseVisitor, error) {
	stmt, err := parser.New().ParseOneStmt(sql, "", "")
	if err != nil {
		return &GetDatabaseVisitor{}, err
	}
	var getDBVisitor = &GetDatabaseVisitor{Database: make(map[string]bool, 0)}
	stmt.Accept(getDBVisitor)
	return getDBVisitor, nil
}
