package system_rule

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"go.uber.org/dig"
)

type VedbMysqlCheckerImpl struct {
	SecurityEngineService
	MysqlCheckerImpl MysqlCheckerImpl
	Ds               datasource.DataSourceService
}

type NewVedbMySQLSecurityEngineIn struct {
	dig.In
	WorkflowDal dal.WorkflowDAL
	Ds          datasource.DataSourceService
}

type NewVedbMySQLSecurityEngineOut struct {
	dig.Out
	Engine SecurityEngineService `group:"securityengine"`
}

func NewVedbMySQLSecurityEngine(in NewVedbMySQLSecurityEngineIn) NewVedbMySQLSecurityEngineOut {
	return NewVedbMySQLSecurityEngineOut{
		Engine: NewSecurityEngineServiceDecorator(&VedbMysqlCheckerImpl{
			SecurityEngineService: NewSecurityEngineServiceDecorator(nil).Export(),
			MysqlCheckerImpl: MysqlCheckerImpl{
				SecurityEngineService: NewSecurityEngineServiceDecorator(nil).Export(),
				WorkflowDal:           in.WorkflowDal,
				Ds:                    in.Ds,
			},
		}).Export(),
	}
}

func (self *VedbMysqlCheckerImpl) Type() shared.DataSourceType {
	return shared.VeDBMySQL
}

func (self *VedbMysqlCheckerImpl) InitSecRuleChecker(DSType shared.DataSourceType) map[string]RuleFunc {
	mysqlValidationFunc := map[string]RuleFunc{
		"IsAllowCreateTableExecDirect":                  self.MysqlCheckerImpl.IsAllowCreateTableExecDirect,
		"IsAllowDropTableExecDirect":                    self.MysqlCheckerImpl.IsAllowDropTableExecDirect,
		"IsAllowTruncateTableExecDirect":                self.MysqlCheckerImpl.IsAllowTruncateTableExecDirect,
		"IsAllowInsertExecDirect":                       self.MysqlCheckerImpl.IsAllowInsertExecDirect,
		"IsAllowUpdateExecDirect":                       self.MysqlCheckerImpl.IsAllowUpdateExecDirect,
		"IsAllowDeleteExecDirect":                       self.MysqlCheckerImpl.IsAllowDeleteExecDirect,
		"IsAllowAlterTableExecDirect":                   self.MysqlCheckerImpl.IsAllowAlterTableExecDirect,
		"IsAllowKillExecDirect":                         self.MysqlCheckerImpl.IsAllowKillExecDirect,
		"IsAllowCreateProcessExecDirect":                self.MysqlCheckerImpl.IsAllowCreateProcessExecDirect,
		"IsAllowUnknownSqlExec":                         self.MysqlCheckerImpl.IsAllowUnknownSqlExec,
		"IsCheckDbOrTablePrivilege":                     self.MysqlCheckerImpl.IsCheckDbOrTablePrivilege,
		"IsAllowUnknownSqlPermissionsExec":              self.MysqlCheckerImpl.IsAllowUnknownSqlPermissionsExec,
		"IsCheckTableNeedHavePrimaryKey":                self.MysqlCheckerImpl.IsCheckTableNeedHavePrimaryKey,
		"IsCheckTableNeedHaveCommand":                   self.MysqlCheckerImpl.IsCheckTableNeedHaveCommand,
		"IsCheckTableHaveForeignKey":                    self.MysqlCheckerImpl.IsCheckTableHaveForeignKey,
		"IsCheckTableNameCase":                          self.MysqlCheckerImpl.IsCheckTableNameCase,
		"IsCheckTableEngine":                            self.MysqlCheckerImpl.IsCheckTableEngine,
		"IsCheckTablePartitionSettings":                 self.MysqlCheckerImpl.IsCheckTablePartitionSettings,
		"IsCheckTableIncludeColumns":                    self.MysqlCheckerImpl.IsCheckTableIncludeColumns,
		"IsCheckColumnCharset":                          self.MysqlCheckerImpl.IsCheckColumnCharset,
		"IsCheckTableCharacter":                         self.MysqlCheckerImpl.IsCheckTableCharacter,
		"IsCheckTableCollation":                         self.MysqlCheckerImpl.IsCheckTableCollation,
		"IsCheckTableNameKeyword":                       self.MysqlCheckerImpl.IsCheckTableNameKeyword,
		"IsCheckTableIndexCount":                        self.MysqlCheckerImpl.IsCheckTableIndexCount,
		"IsCheckTableColumnCount":                       self.MysqlCheckerImpl.IsCheckTableColumnCount,
		"IsCheckTableAutoIncrement":                     self.MysqlCheckerImpl.IsCheckTableAutoIncrement,
		"IsCheckTablePrimaryKeyAutoIncrement":           self.MysqlCheckerImpl.IsCheckTablePrimaryKeyAutoIncrement,
		"IsCheckColumnNameKeyword":                      self.MysqlCheckerImpl.IsCheckColumnNameKeyword,
		"IsCheckColumnNameCase":                         self.MysqlCheckerImpl.IsCheckColumnNameCase,
		"RestrictPrimaryKeyColumnTypes":                 self.MysqlCheckerImpl.RestrictPrimaryKeyColumnTypes,
		"RestrictPrimaryKeyCounts":                      self.MysqlCheckerImpl.RestrictPrimaryKeyCounts,
		"RestrictIndexKeyInColumnCounts":                self.MysqlCheckerImpl.RestrictIndexKeyInColumnCounts,
		"RestrictNormalIndexKeyFormat":                  self.MysqlCheckerImpl.RestrictNormalIndexKeyFormat,
		"RestrictUniqueIndexKeyFormat":                  self.MysqlCheckerImpl.RestrictUniqueIndexKeyFormat,
		"RestrictIndexNeedName":                         self.MysqlCheckerImpl.RestrictIndexNeedName,
		"RestrictColumnDataType":                        self.MysqlCheckerImpl.RestrictColumnDataType,
		"RestrictColumnUseEnumType":                     self.MysqlCheckerImpl.RestrictColumnUseEnumType,
		"RestrictColumnCheck":                           self.MysqlCheckerImpl.RestrictColumnCheck,
		"RestrictColumnUseFloatType":                    self.MysqlCheckerImpl.RestrictColumnUseFloatType,
		"RestrictColumnHasDefaultValue":                 self.MysqlCheckerImpl.RestrictColumnHasDefaultValue,
		"RestrictColumnNotNull":                         self.MysqlCheckerImpl.RestrictColumnNotNull,
		"RestrictIncrementColumnName":                   self.MysqlCheckerImpl.RestrictIncrementColumnName,
		"RestrictIncrementColumnIsUnsigned":             self.MysqlCheckerImpl.RestrictIncrementColumnIsUnsigned,
		"RestrictIndexColumnIsNotNull":                  self.MysqlCheckerImpl.RestrictIndexColumnIsNotNull,
		"RestrictCharColumnLength":                      self.MysqlCheckerImpl.RestrictCharColumnLength,
		"RestrictVarCharColumnLength":                   self.MysqlCheckerImpl.RestrictVarCharColumnLength,
		"RestrictColumnUseZeroFill":                     self.MysqlCheckerImpl.RestrictColumnUseZeroFill,
		"RestrictColumnNeedComment":                     self.MysqlCheckerImpl.RestrictColumnNeedComment,
		"IsCheckSelectNeedWhere":                        self.MysqlCheckerImpl.IsCheckSelectNeedWhere,
		"RestrictOffsetSizeInSelect":                    self.MysqlCheckerImpl.RestrictOffsetSizeInSelect,
		"RestrictGroupByConstantStatementsInSelect":     self.MysqlCheckerImpl.RestrictGroupByConstantStatementsInSelect,
		"RestrictTableAssociationsCountInSelect":        self.MysqlCheckerImpl.RestrictTableAssociationsCountInSelect,
		"RestrictAsteriskStatementInSelect":             self.MysqlCheckerImpl.RestrictAsteriskStatementInSelect,
		"RestrictOrderByConstantInSelect":               self.MysqlCheckerImpl.RestrictOrderByConstantInSelect,
		"RestrictOrderByDifferentDirectionsInSelect":    self.MysqlCheckerImpl.RestrictOrderByDifferentDirectionsInSelect,
		"RestrictGroupByFuncStatementsInSelect":         self.MysqlCheckerImpl.RestrictGroupByFuncStatementsInSelect,
		"RestrictOrderByFuncStatementsInSelect":         self.MysqlCheckerImpl.RestrictOrderByFuncStatementsInSelect,
		"RestrictOrderByRandStatementsInSelect":         self.MysqlCheckerImpl.RestrictOrderByRandStatementsInSelect,
		"RestrictHavingStatementsInSelect":              self.MysqlCheckerImpl.RestrictHavingStatementsInSelect,
		"RestrictUnionStatementsInSelect":               self.MysqlCheckerImpl.RestrictUnionStatementsInSelect,
		"RestrictLikeStatementsUsePreWildcards":         self.MysqlCheckerImpl.RestrictLikeStatementsUsePreWildcards,
		"RestrictLikeStatementsWithoutWildcards":        self.MysqlCheckerImpl.RestrictLikeStatementsWithoutWildcards,
		"RestrictReverseQueryInWhere":                   self.MysqlCheckerImpl.RestrictReverseQueryInWhere,
		"RestrictFilterConnectWithOr":                   self.MysqlCheckerImpl.RestrictFilterConnectWithOr,
		"RestrictOrderByDifferentTablesInSelect":        self.MysqlCheckerImpl.RestrictOrderByDifferentTablesInSelect,
		"RestrictMultipleTableUpdateAndDeleteNumber":    self.MysqlCheckerImpl.RestrictMultipleTableUpdateAndDeleteNumber,
		"IsCheckMultipleTableUpdateAndDeleteJoinHaveOn": self.MysqlCheckerImpl.IsCheckMultipleTableUpdateAndDeleteJoinHaveOn,
		"IsCheckUpdateAndDeleteHaveWhere":               self.MysqlCheckerImpl.IsCheckUpdateAndDeleteHaveWhere,
		"IsCheckInsertCannotBeDuplicated":               self.MysqlCheckerImpl.IsCheckInsertCannotBeDuplicated,
		"IsCheckInsertColAndValueMatch":                 self.MysqlCheckerImpl.IsCheckInsertColAndValueMatch,
		"IsCheckInsertNeedColumnList":                   self.MysqlCheckerImpl.IsCheckInsertNeedColumnList,
		"RestrictInsertNotNullColumnCannotBeNull":       self.MysqlCheckerImpl.RestrictInsertNotNullColumnCannotBeNull,
		"IsAllowFuncSysdate":                            self.MysqlCheckerImpl.IsAllowFuncSysdate,
		"RestrictInsertValuesNumber":                    self.MysqlCheckerImpl.RestrictInsertValuesNumber,
		"IsCheckInsertTableAndColumnExist":              self.MysqlCheckerImpl.IsCheckInsertTableAndColumnExist,
		"IsCheckUpdateSetColumnTablePrefix":             self.MysqlCheckerImpl.IsCheckUpdateSetColumnTablePrefix,
		"IsCheckUpdateTableCreateTimeColumn":            self.MysqlCheckerImpl.IsCheckUpdateTableCreateTimeColumn,
		"IsCheckUpdateTableModifyTimeColumn":            self.MysqlCheckerImpl.IsCheckUpdateTableModifyTimeColumn,
		"IsCheckUpdateSetColumnSeparator":               self.MysqlCheckerImpl.IsCheckUpdateSetColumnSeparator,
		"IsCheckUpdateAndDeleteWhereSubQuery":           self.MysqlCheckerImpl.IsCheckUpdateAndDeleteWhereSubQuery,
		"RestrictWhereInNumber":                         self.MysqlCheckerImpl.RestrictWhereInNumber,
		"IsCheckUpdatePrimaryKeyValue":                  self.MysqlCheckerImpl.IsCheckUpdatePrimaryKeyValue,
		"IsCheckUpdateTableAndColumnExist":              self.MysqlCheckerImpl.IsCheckUpdateTableAndColumnExist,
		"IsCheckUpdateUniqueKeyValue":                   self.MysqlCheckerImpl.IsCheckUpdateUniqueKeyValue,
		// 新增多云的DML规则
		"RestrictSQLLength":               self.MysqlCheckerImpl.RestrictSQLLength,
		"RestrictSQLCrossDB":              self.MysqlCheckerImpl.RestrictSQLCrossDB,
		"RestrictSQLWithOutTableName":     self.MysqlCheckerImpl.RestrictSQLWithOutTableName,
		"RestrictSQLStatement":            self.MysqlCheckerImpl.RestrictSQLStatement,
		"RestrictInsertWithOutColumnInfo": self.MysqlCheckerImpl.RestrictInsertWithOutColumnInfo,
		"RestrictInsertOnDuplicateKey":    self.MysqlCheckerImpl.RestrictInsertOnDuplicateKey,
		"RestrictInsertByReplace":         self.MysqlCheckerImpl.RestrictInsertByReplace,
		"RestrictUpdateWithOnlyValue":     self.MysqlCheckerImpl.RestrictUpdateWithOnlyValue,
	}
	return mysqlValidationFunc
}
