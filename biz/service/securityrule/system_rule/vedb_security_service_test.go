package system_rule

import (
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"reflect"
	"testing"
)

func TestVedbMysqlCheckerImpl_InitSecRuleChecker_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("TestVedbMysqlCheckerImpl_InitSecRuleChecker", t, func() {
		mockey.PatchConvey("成功场景 - 正常初始化并返回所有规则函数", func() {
			// 场景描述：
			// 验证当调用 InitSecRuleChecker 方法时，能够成功初始化并返回一个包含所有预定义 MySQL 安全规则函数的映射。
			// 数据构造：
			// - 创建一个 VedbMysqlCheckerImpl 的实例。
			// 逻辑链路：
			// 1. 调用 InitSecRuleChecker 方法。
			// 2. 验证返回的 map 不为 nil。
			// 3. 验证 map 中包含的规则函数数量与预期一致。
			// 4. 抽样验证几个关键的规则函数存在，并且其函数指针与 MysqlCheckerImpl 中对应的方法一致。
			// 5. 验证一个不存在的规则键返回 nil。

			// 数据构造
			checker := &VedbMysqlCheckerImpl{}

			// 调用
			ruleFuncs := checker.InitSecRuleChecker(shared.VeDBMySQL)

			// 断言
			convey.So(ruleFuncs, convey.ShouldNotBeNil)
			// 根据源文件，共有 92 个规则
			convey.So(len(ruleFuncs), convey.ShouldEqual, 92)

			// 抽样检查几个规则是否存在且函数指针正确
			convey.So(ruleFuncs["IsAllowCreateTableExecDirect"], convey.ShouldNotBeNil)
			convey.So(reflect.ValueOf(ruleFuncs["IsAllowCreateTableExecDirect"]).Pointer(), convey.ShouldEqual, reflect.ValueOf(checker.MysqlCheckerImpl.IsAllowCreateTableExecDirect).Pointer())

			convey.So(ruleFuncs["IsCheckUpdateUniqueKeyValue"], convey.ShouldNotBeNil)
			convey.So(reflect.ValueOf(ruleFuncs["IsCheckUpdateUniqueKeyValue"]).Pointer(), convey.ShouldEqual, reflect.ValueOf(checker.MysqlCheckerImpl.IsCheckUpdateUniqueKeyValue).Pointer())

			convey.So(ruleFuncs["RestrictSQLLength"], convey.ShouldNotBeNil)
			convey.So(reflect.ValueOf(ruleFuncs["RestrictSQLLength"]).Pointer(), convey.ShouldEqual, reflect.ValueOf(checker.MysqlCheckerImpl.RestrictSQLLength).Pointer())

			convey.So(ruleFuncs["RestrictUpdateWithOnlyValue"], convey.ShouldNotBeNil)
			convey.So(reflect.ValueOf(ruleFuncs["RestrictUpdateWithOnlyValue"]).Pointer(), convey.ShouldEqual, reflect.ValueOf(checker.MysqlCheckerImpl.RestrictUpdateWithOnlyValue).Pointer())

			// 检查一个不存在的key
			convey.So(ruleFuncs["ThisRuleShouldNotExist"], convey.ShouldBeNil)
		})
	})
}
