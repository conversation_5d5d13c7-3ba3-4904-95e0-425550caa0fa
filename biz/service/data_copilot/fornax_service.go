package data_copilot

import (
	"context"
	"encoding/json"
	"errors"
	"go.uber.org/dig"
	"io"
	"strings"
	"time"

	"code.byted.org/flowdevops/fornax_sdk"
	"code.byted.org/flowdevops/fornax_sdk/domain"
	"code.byted.org/flowdevops/fornax_sdk/domain/chatmodel"
	"code.byted.org/flowdevops/fornax_sdk/domain/prompt/execution"
	configservice "code.byted.org/infcs/dbw-mgr/biz/service/config"
	consts2 "code.byted.org/infcs/dbw-mgr/biz/service/data_copilot/consts"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
)

type FornaxService interface {
	Init(ctx context.Context)
	ExecutePrompt(ctx context.Context, chatList []*consts2.ChatMessage, PromptKey string, variables map[string]interface{}) *consts2.ChatMessage
	StreamExecutePrompt(ctx context.Context, chatList []*consts2.ChatMessage, PromptKey string, dataCh chan model.CopilotStreamResponse, messageID string, variables map[string]any) *consts2.ChatMessage
}

type fornaxService struct {
	cfg          configservice.ConfigProvider
	fornaxClient *fornax_sdk.Client
}

type FornaxServiceIn struct {
	dig.In
	Cfg configservice.ConfigProvider
}

func NewFornaxService(in FornaxServiceIn) FornaxService {
	h := &fornaxService{
		cfg: in.Cfg,
	}

	return h
}

func (self *fornaxService) Init(ctx context.Context) {
	//构建调用AI的请求，获取AI返回结果
	ak := self.cfg.Get(ctx).FornaxAccessKey
	sk := self.cfg.Get(ctx).FornaxSecretKey
	log.Info(ctx, "get fornax config, ak is %s, sk is %s", ak, sk)

	config := &domain.Config{
		Identity: &domain.Identity{
			AK: ak,
			SK: sk,
		},
	}
	client, err := fornax_sdk.NewClient(config, domain.AppendTracerInitConfig(domain.WithTracerSpanLoggerEnabled(true)))
	if err != nil {
		log.Error(ctx, "fornaxService init err, restart NewClient")
		newClient, err := fornax_sdk.NewClient(config)
		if err != nil {
			log.Error(ctx, "fornaxService init err, restart NewClient err, err is:%v", err)
			return
		}
		client = newClient
	}
	self.fornaxClient = client
}

func (self *fornaxService) ExecutePrompt(ctx context.Context, chatList []*consts2.ChatMessage, PromptKey string, variables map[string]interface{}) *consts2.ChatMessage {
	fornaxMessageList := convertChatMessageListToFornaxMessage(ctx, chatList)
	log.Info(ctx, "fornaxService.ExecutePrompt fornaxMessageList", utils.Show(fornaxMessageList))

	if variables == nil {
		variables = make(map[string]interface{})
	}

	// 再额外追加一个当前时间，指定为北京时间
	currentTime := time.Now().Unix()
	variables["CurrentTime"] = currentTime
	variables["BeiJingISOTime"] = timestampToISO8601Beijing(currentTime)

	newCtx := context.TODO()
	result, err := self.fornaxClient.ExecutePromptLocal(newCtx, &execution.ExecutePromptLocalParam{
		PromptKey:    PromptKey,
		Version:      nil,
		ChatMessages: fornaxMessageList,
		Variables:    variables,
	})
	if err != nil {
		log.Warn(ctx, "ExecutePrompt ExecutePromptLocal err, err is:%v", err)
		return nil
	}
	//将fornax SDK中的message转换为DBW自己的message
	resp := &result.Choices[0].Message
	message := consts2.ChatMessage{}

	err = json.Unmarshal([]byte(utils.Show(resp)), &message)
	if err != nil {
		log.Info(ctx, "ExecutePromptLocal Unmarshal err, AI response Content is:%v", utils.Show(resp))
		return nil
	}

	log.Info(ctx, "ExecutePromptLocal finished, AI response Content is:%v", utils.Show(message))
	return &message
}

func (self *fornaxService) StreamExecutePrompt(ctx context.Context, chatList []*consts2.ChatMessage, PromptKey string, dataCh chan model.CopilotStreamResponse, messageID string, variables map[string]any) *consts2.ChatMessage {
	fornaxMessageList := convertChatMessageListToFornaxMessage(ctx, chatList)
	log.Info(ctx, "fornaxService.StreamExecutePrompt start,  fornaxMessageList is:%v", utils.Show(fornaxMessageList))

	if variables == nil {
		variables = make(map[string]interface{})
	}
	// 再额外追加一个当前时间，指定为北京时间
	currentTime := time.Now().Unix()
	variables["CurrentTime"] = currentTime
	variables["BeiJingISOTime"] = timestampToISO8601Beijing(currentTime)

	newCtx := context.TODO()
	streamReader, err := self.fornaxClient.StreamExecutePromptLocal(newCtx, &execution.ExecutePromptLocalParam{
		PromptKey:    PromptKey,
		Version:      nil,
		ChatMessages: fornaxMessageList,
		Variables:    variables,
	})

	message := &consts2.ChatMessage{}
	if err != nil {
		log.Warn(ctx, "StreamExecutePrompt error: %v", err)
		return message
	}

	toolCallMap := make(map[string]*consts2.ToolCall)
	respContent := ""
	//在这个循环内，会按照流式的顺序一个一个的接收返回信息
	for {
		resp, err := streamReader.Recv(context.Background())
		if errors.Is(err, io.EOF) {
			log.Info(ctx, "StreamExecutePrompt stream end")
			break
		}

		if err != nil {
			log.Warn(ctx, "StreamExecutePrompt recv error: %v", err)
			return message
		}

		//如果本轮中获取的数据中没有Choices信息，那么就可以直接下一轮了
		if resp.Choices == nil || len(resp.Choices) == 0 {
			continue
		}

		//这里需要进行两手处理
		//第一手：将流式数据转换为标准的Message数据，用于可能存在的FunctionCall的处理
		for _, choice := range resp.Choices {
			delta := choice.Delta
			message.Role = consts2.ParseRoleType(string(delta.Role))
			//接下来，遍历处理ToolCalls的信息
			calls := delta.ToolCalls
			if calls != nil && len(calls) > 0 {
				for _, toolCallInfo := range calls {
					builder, exists := toolCallMap[toolCallInfo.ID]
					if !exists {
						builder = &consts2.ToolCall{
							ID:   toolCallInfo.ID,
							Type: consts2.ParseToolType(string(toolCallInfo.Type)),
							FunctionCall: &consts2.FunctionCall{
								Name: toolCallInfo.FunctionCall.Name,
							},
						}
					}
					arg := strings.ReplaceAll(toolCallInfo.FunctionCall.Arguments, `\"`, `"`)
					builder.FunctionCall.Arguments += arg
					toolCallMap[toolCallInfo.ID] = builder
				}
			}

			//然后再处理Content的内容信息
			if delta.Content != "" || delta.ReasoningContent != "" {
				//默认情况下，如果有Content的内容输出，就代表着当前的流式结束
				state := model.StreamResponseState_RESULT_OUTPUT
				toolsNum := 0
				if delta.ReasoningContent != "" {
					//如果此时有深度思考的内容输出，那么代表着正在进行深度思考，同时：
					//这里要针对输入的ChatList来进行判断，如果ChatList内不包含任何Tool类型的消息，说明本次模型调用不涉及到FC，那么state的状态位就应该是语义分析，否则就是工具分析
					state = model.StreamResponseState_SEMANTIC_ANALYZER
					for _, chatInfo := range chatList {
						if chatInfo.Role == consts2.RoleTypeTool {
							state = model.StreamResponseState_AGENT_RESULT_ANALYZER
							toolsNum++
						}
					}
				}

				//这里需要单独针对\n\n的这种模型返回的数据进行一次处理，这种情况下，需要把state的状态位设置为语意分析
				if delta.Content == "\n\n" {
					state = model.StreamResponseState_FUNCTION_CALL
				}

				//在保存respContent的时候，只要当ReasoningContent不存在，但是Content存在的时候，才要保存下来
				if delta.ReasoningContent == "" && delta.Content != "" {
					respContent += delta.Content
				}

				dataCh <- model.CopilotStreamResponse{
					RoleType:         string(delta.Role),
					State:            state,
					Content:          delta.Content,
					ReasoningContent: delta.ReasoningContent,
					MessageID:        messageID,
				}
			}
		}
	}

	//等到整个循环处理完成后，ToolCall信息和Content的信息都已经全部处理OK，然后放到Message里面返回出去
	toolcalls := make([]*consts2.ToolCall, 0)
	for _, toolCall := range toolCallMap {
		toolcalls = append(toolcalls, toolCall)
	}
	message.ToolCalls = toolcalls
	//这里在处理的时候，如果没有ToolCall的场景，才需要保存Content
	if toolcalls == nil || len(toolcalls) == 0 {
		message.Content = respContent
		message.ToolCalls = nil

		//结束流式，这里需要判断本次返回内容是否有ToolCalls：
		// 如果有：代表着和前端的流式过程还没有结束，需要等待服务端端与大模型进行第X次交互
		// 如果没有：代表着服务端已经和大模型交互完成，这是最终的结果返回了
		close(dataCh)
	} else {
		//如果存在FunctionCall，就把tools内容构建好发送给前端
		calls := []*model.FunctionCall{}
		for _, val := range toolcalls {
			tool := &model.FunctionCall{
				Name:       val.FunctionCall.Name,
				Arguments:  val.FunctionCall.Arguments,
				ToolCallID: val.ID,
			}
			calls = append(calls, tool)
		}
		dataCh <- model.CopilotStreamResponse{
			RoleType:  string(consts2.RoleTypeAssistant),
			State:     model.StreamResponseState_FUNCTION_CALL,
			MessageID: messageID,
			Tools:     calls,
		}
	}

	log.Info(ctx, "StreamExecutePrompt finished, message is:%v", utils.Show(message))
	return message
}

func convertChatMessageListToFornaxMessage(ctx context.Context, chatList []*consts2.ChatMessage) []*chatmodel.ChatMessage {

	fornaxMessageList := []*chatmodel.ChatMessage{}

	for _, message := range chatList {
		fornaxMessage := &chatmodel.ChatMessage{}

		err := json.Unmarshal([]byte(utils.Show(message)), fornaxMessage)
		if err != nil {
			log.Info(ctx, "ConvertChatMessageListToFornaxMessage Unmarshal err, err is:%v", err)
			return nil
		}
		fornaxMessageList = append(fornaxMessageList, fornaxMessage)
	}

	return fornaxMessageList
}

func timestampToISO8601Beijing(timestamp int64) string {
	// 1. 将秒级时间戳转换为 UTC 时间的 time.Time 实例
	utcTime := time.Unix(timestamp, 0).UTC()

	// 2. 获取北京时间的时区（Asia/Shanghai 对应 UTC+8）
	beijingLoc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return ""
	}

	// 3. 将 UTC 时间转换为北京时间
	beijingTime := utcTime.In(beijingLoc)

	// 4. 格式化为 ISO-8601 字符串（包含时区偏移 +08:00）
	iso8601 := beijingTime.Format("2006-01-02T15:04:05+08:00")

	return iso8601
}
