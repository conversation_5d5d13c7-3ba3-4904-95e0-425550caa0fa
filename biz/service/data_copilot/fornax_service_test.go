package data_copilot

import (
	"context"
	"errors"
	"testing"

	"code.byted.org/flowdevops/fornax_sdk"
	"code.byted.org/infcs/dbw-mgr/biz/config"
	configservice "code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/ds-lib/common/log"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
)

// Mock_ConfigProvider_fornaxService_Init is a mock type for the ConfigProvider interface.
type Mock_ConfigProvider_fornaxService_Init struct {
	configservice.ConfigProvider
}

// Get is a mock method for the Get interface method.
func (m *Mock_ConfigProvider_fornaxService_Init) Get(ctx context.Context) *config.Config {
	return &config.Config{}
}

func Test_fornaxService_Init_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("Test_fornaxService_Init", t, func() {
		ctx := context.Background()
		mockCfgProvider := &Mock_ConfigProvider_fornaxService_Init{}
		self := &fornaxService{
			cfg: mockCfgProvider,
		}

		mockey.PatchConvey("成功场景: 首次初始化成功", func() {
			// 场景描述：
			// 当所有依赖都正常返回，第一次调用 fornax_sdk.NewClient 成功时，验证目标函数能正确初始化 fornaxClient。
			// 数据构造：
			// - self.cfg.Get 返回包含有效 AK/SK 的配置。
			// - fornax_sdk.NewClient 首次调用返回一个有效的 client 和 nil 错误。
			// 逻辑链路：
			// 1. 调用 self.cfg.Get 获取配置。
			// 2. 调用 log.Info 记录日志。
			// 3. 第一次调用 fornax_sdk.NewClient 成功。
			// 4. 将返回的 client 赋值给 self.fornaxClient。
			// 5. 验证 self.fornaxClient 已被正确赋值。

			// 数据构造
			mockClient := &fornax_sdk.Client{}

			// Mock
			mockey.Mock((*Mock_ConfigProvider_fornaxService_Init).Get).Return(&config.Config{
				FornaxAccessKey: "test-ak",
				FornaxSecretKey: "test-sk",
			}).Build()
			mockey.Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
			mockey.Mock(fornax_sdk.NewClient).Return(mockClient, nil).Build()

			// 调用
			self.Init(ctx)

			// 断言
			convey.So(self.fornaxClient, convey.ShouldEqual, mockClient)
		})

		mockey.PatchConvey("成功场景: 首次初始化失败后重试成功", func() {
			// 场景描述：
			// 当第一次调用 fornax_sdk.NewClient 失败，但第二次重试成功时，验证目标函数能正确初始化 fornaxClient。
			// 数据构造：
			// - self.cfg.Get 返回包含有效 AK/SK 的配置。
			// - fornax_sdk.NewClient 首次调用返回错误，第二次调用返回有效的 client 和 nil 错误。
			// 逻辑链路：
			// 1. 调用 self.cfg.Get 获取配置。
			// 2. 调用 log.Info 记录日志。
			// 3. 第一次调用 fornax_sdk.NewClient 返回错误。
			// 4. 调用 log.Error 记录错误日志。
			// 5. 第二次调用 fornax_sdk.NewClient 成功。
			// 6. 将第二次返回的 client 赋值给 self.fornaxClient。
			// 7. 验证 self.fornaxClient 已被正确赋值。

			// 数据构造
			mockClient := &fornax_sdk.Client{}
			retryErr := errors.New("first attempt failed")

			// Mock
			mockey.Mock((*Mock_ConfigProvider_fornaxService_Init).Get).Return(&config.Config{
				FornaxAccessKey: "test-ak",
				FornaxSecretKey: "test-sk",
			}).Build()
			mockey.Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
			mockey.Mock(log.Error).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
			mockey.Mock(fornax_sdk.NewClient).Return(
				mockey.Sequence(nil, retryErr).Then(mockClient, nil),
			).Build()

			// 调用
			self.Init(ctx)

			// 断言
			convey.So(self.fornaxClient, convey.ShouldEqual, mockClient)
		})

		mockey.PatchConvey("失败场景: 两次初始化均失败", func() {
			// 场景描述：
			// 当第一次和第二次调用 fornax_sdk.NewClient 均失败时，验证 self.fornaxClient 保持为 nil。
			// 数据构造：
			// - self.cfg.Get 返回包含有效 AK/SK 的配置。
			// - fornax_sdk.NewClient 两次调用均返回错误。
			// 逻辑链路：
			// 1. 调用 self.cfg.Get 获取配置。
			// 2. 调用 log.Info 记录日志。
			// 3. 第一次调用 fornax_sdk.NewClient 返回错误。
			// 4. 调用 log.Error 记录错误日志。
			// 5. 第二次调用 fornax_sdk.NewClient 再次返回错误。
			// 6. 调用 log.Error 记录第二次错误日志。
			// 7. 函数返回，self.fornaxClient 未被赋值。
			// 8. 验证 self.fornaxClient 为 nil。

			// 数据构造
			err1 := errors.New("first attempt failed")
			err2 := errors.New("second attempt failed")

			// Mock
			mockey.Mock((*Mock_ConfigProvider_fornaxService_Init).Get).Return(&config.Config{
				FornaxAccessKey: "test-ak",
				FornaxSecretKey: "test-sk",
			}).Build()
			mockey.Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
			mockey.Mock(log.Error).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
			mockey.Mock(fornax_sdk.NewClient).Return(
				mockey.Sequence(nil, err1).Then(nil, err2),
			).Build()

			// 调用
			self.Init(ctx)

			// 断言
			convey.So(self.fornaxClient, convey.ShouldBeNil)
		})
	})
}

// Mock_ConfigProvider_NewFornaxService is a mock for the ConfigProvider interface.
type Mock_ConfigProvider_NewFornaxService struct {
	configservice.ConfigProvider
}

func Test_NewFornaxService_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("TestNewFornaxService", t, func() {
		mockey.PatchConvey("成功场景: 正常创建FornaxService实例", func() {
			// 场景描述：
			// 验证 NewFornaxService 函数能够基于传入的依赖成功创建一个 FornaxService 实例。
			// 数据构造：
			// - 构造一个 FornaxServiceIn 实例，其中包含一个 mock 的 ConfigProvider。
			// 逻辑链路：
			// 1. 准备一个 FornaxServiceIn 结构体作为输入参数。
			// 2. 调用 NewFornaxService 函数。
			// 3. 验证返回的 FornaxService 实例不为 nil，表示实例创建成功。

			// 数据构造
			mockCfgProvider := &Mock_ConfigProvider_NewFornaxService{}
			in := FornaxServiceIn{
				Cfg: mockCfgProvider,
			}

			// Mock
			// 该函数为构造函数，仅进行字段赋值，无需 mock。

			// 调用
			service := NewFornaxService(in)

			// 断言
			convey.So(service, convey.ShouldNotBeNil)
		})
	})
}
