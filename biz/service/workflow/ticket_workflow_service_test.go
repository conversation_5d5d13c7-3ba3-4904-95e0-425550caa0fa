package workflow

import (
	"bytes"
	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	service_config "code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/go-playground/assert"
	"github.com/smartystreets/goconvey/convey"
	"io"
	"io/ioutil"
	"net/http"
	"testing"
)

type mockConfigProviderImpl struct {
}

func (self *mockConfigProviderImpl) Get(ctx context.Context) *config.Config {
	return &config.Config{}
}

func (self *mockConfigProviderImpl) Update(ctx context.Context, cnf *config.Config) error {
	return nil
}

func (self *mockConfigProviderImpl) Refresh(ctx context.Context) {

}

func (self *mockConfigProviderImpl) AddUpdateHook(fn func(*shared.ConfigUpdated)) {

}
func getTicketWorkflowService() ticketWorkflowService {
	return ticketWorkflowService{
		workflowDal: &dal.Workflow{},
		cnf:         &mockConfigProviderImpl{},
	}
}

func mockConfigRes() *config.Config {
	return &config.Config{
		BpmDefaultConfigId:   123,
		BpmFlowUrl:           "http://127.0.0.1/",
		BpmBasicAuthUser:     "user",
		BpmBasicAuthPassword: "password",
	}
}

func TestCreateBpmWorkflowRecord(t *testing.T) {
	service := getTicketWorkflowService()

	mock1 := mockey.Mock((*mockConfigProviderImpl).Get).Return(mockConfigRes()).Build()
	defer mock1.UnPatch()
	mockRespBody := "{\n    \"data\": {\n        \"id\": 1399419,\n        \"creator\": \"chengxin.sora\",\n        \"assignee\": \"\",\n        \"status\": \"step1\",\n        \"status_name\": \"step1\",\n        \"workflow_config\": 9671,\n        \"workflow_config_version\": 32,\n        \"workflow_key\": \"dbw_test\",\n        \"workflow_name\": \"dbw_test\",\n        \"target_system\": \"dbw_test_cx\",\n        \"branch\": \"main\",\n        \"version\": 1,\n        \"config\": {\n            \"test123\": \"test123\",\n            \"_context_\": {\n                \"_creator_type\": \"person_account\",\n                \"status_alias_map\": {},\n                \"status_actions_map\": {}\n            }\n        },\n        \"diff\": [],\n        \"is_platform\": 0,\n        \"need_check_config\": true,\n        \"current_assignees\": \"\",\n        \"viewers\": null,\n        \"status_alias_list\": null,\n        \"idempotent_id\": null,\n        \"creator_department\": \"Data-基础架构-数据库-生态工具与平台-生态工具\",\n        \"ctime\": \"2023-06-12T15:55:08\",\n        \"utime\": \"2023-06-12T15:55:08\",\n        \"finished\": 0\n    },\n    \"message\": \"success\",\n    \"code\": 0\n}"
	mock2 := mockey.Mock(io.ReadAll).Return(mockRespBody, nil).Build()
	defer mock2.UnPatch()
	// TODO 有失败的可能性，因为client.Do 只有一行，有一定概率mock失败
	mock3 := mockey.Mock((*http.Client).Do).Return(&http.Response{}, nil).Build()
	defer mock3.UnPatch()

	res, err := service.CreateBpmWorkflowRecord(context.Background(), "xx", BpmConfigData{})
	if err != nil {
		t.Fatal("failed", err)
	}
	assert.Equal(t, res, int64(1399419))
}

func TestCreateBpmWorkflowRecordError1(t *testing.T) {
	service := getTicketWorkflowService()

	mock1 := mockey.Mock((*mockConfigProviderImpl).Get).Return(mockConfigRes()).Build()
	defer mock1.UnPatch()
	mockRespBody := "{\n    \"data\": {\n        \"id\": 1399419,\n        \"creator\": \"chengxin.sora\",\n        \"assignee\": \"\",\n        \"status\": \"step1\",\n        \"status_name\": \"step1\",\n        \"workflow_config\": 9671,\n        \"workflow_config_version\": 32,\n        \"workflow_key\": \"dbw_test\",\n        \"workflow_name\": \"dbw_test\",\n        \"target_system\": \"dbw_test_cx\",\n        \"branch\": \"main\",\n        \"version\": 1,\n        \"config\": {\n            \"test123\": \"test123\",\n            \"_context_\": {\n                \"_creator_type\": \"person_account\",\n                \"status_alias_map\": {},\n                \"status_actions_map\": {}\n            }\n        },\n        \"diff\": [],\n        \"is_platform\": 0,\n        \"need_check_config\": true,\n        \"current_assignees\": \"\",\n        \"viewers\": null,\n        \"status_alias_list\": null,\n        \"idempotent_id\": null,\n        \"creator_department\": \"Data-基础架构-数据库-生态工具与平台-生态工具\",\n        \"ctime\": \"2023-06-12T15:55:08\",\n        \"utime\": \"2023-06-12T15:55:08\",\n        \"finished\": 0\n    },\n    \"message\": \"success\",\n    \"code\": 0\n}"
	mock2 := mockey.Mock(io.ReadAll).Return(mockRespBody, fmt.Errorf("test")).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*http.Client).Do).Return(&http.Response{}, nil).Build()
	defer mock3.UnPatch()

	_, err := service.CreateBpmWorkflowRecord(context.Background(), "xx", BpmConfigData{})
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestCreateBpmWorkflowRecordError2(t *testing.T) {
	service := getTicketWorkflowService()

	mock1 := mockey.Mock((*mockConfigProviderImpl).Get).Return(mockConfigRes()).Build()
	defer mock1.UnPatch()
	mockRespBody := "{\n    \"data\": {\n        \"id\": 1399419,\n        \"creator\": \"chengxin.sora\",\n        \"assignee\": \"\",\n        \"status\": \"step1\",\n        \"status_name\": \"step1\",\n        \"workflow_config\": 9671,\n        \"workflow_config_version\": 32,\n        \"workflow_key\": \"dbw_test\",\n        \"workflow_name\": \"dbw_test\",\n        \"target_system\": \"dbw_test_cx\",\n        \"branch\": \"main\",\n        \"version\": 1,\n        \"config\": {\n            \"test123\": \"test123\",\n            \"_context_\": {\n                \"_creator_type\": \"person_account\",\n                \"status_alias_map\": {},\n                \"status_actions_map\": {}\n            }\n        },\n        \"diff\": [],\n        \"is_platform\": 0,\n        \"need_check_config\": true,\n        \"current_assignees\": \"\",\n        \"viewers\": null,\n        \"status_alias_list\": null,\n        \"idempotent_id\": null,\n        \"creator_department\": \"Data-基础架构-数据库-生态工具与平台-生态工具\",\n        \"ctime\": \"2023-06-12T15:55:08\",\n        \"utime\": \"2023-06-12T15:55:08\",\n        \"finished\": 0\n    },\n    \"message\": \"success\",\n    \"code\": 0\n}"
	mock2 := mockey.Mock(io.ReadAll).Return(mockRespBody, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*http.Client).Do).Return(&http.Response{}, fmt.Errorf("test")).Build()
	defer mock3.UnPatch()

	_, err := service.CreateBpmWorkflowRecord(context.Background(), "xx", BpmConfigData{})
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestPassWorkflowRecord(t *testing.T) {
	service := getTicketWorkflowService()

	mock1 := mockey.Mock((*mockConfigProviderImpl).Get).Return(mockConfigRes()).Build()
	defer mock1.UnPatch()
	mockRespBody := "{\n    \"data\": {\n        \"id\": 1399419,\n        \"creator\": \"chengxin.sora\",\n        \"assignee\": \"\",\n        \"status\": \"step1\",\n        \"status_name\": \"step1\",\n        \"workflow_config\": 9671,\n        \"workflow_config_version\": 32,\n        \"workflow_key\": \"dbw_test\",\n        \"workflow_name\": \"dbw_test\",\n        \"target_system\": \"dbw_test_cx\",\n        \"branch\": \"main\",\n        \"version\": 1,\n        \"config\": {\n            \"test123\": \"test123\",\n            \"_context_\": {\n                \"_creator_type\": \"person_account\",\n                \"status_alias_map\": {},\n                \"status_actions_map\": {}\n            }\n        },\n        \"diff\": [],\n        \"is_platform\": 0,\n        \"need_check_config\": true,\n        \"current_assignees\": \"\",\n        \"viewers\": null,\n        \"status_alias_list\": null,\n        \"idempotent_id\": null,\n        \"creator_department\": \"Data-基础架构-数据库-生态工具与平台-生态工具\",\n        \"ctime\": \"2023-06-12T15:55:08\",\n        \"utime\": \"2023-06-12T15:55:08\",\n        \"finished\": 0\n    },\n    \"message\": \"success\",\n    \"code\": 0\n}"
	mock2 := mockey.Mock(io.ReadAll).Return(mockRespBody, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*http.Client).Do).Return(&http.Response{}, nil).Build()
	defer mock3.UnPatch()

	err := service.PassWorkflowRecord(context.Background(), &dao.BpmFlowInfo{WorkflowId: 123, FlowStep: 1}, 3, "123")
	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestRejectWorkflowRecord(t *testing.T) {
	service := getTicketWorkflowService()

	mock1 := mockey.Mock((*mockConfigProviderImpl).Get).Return(mockConfigRes()).Build()
	defer mock1.UnPatch()
	mockRespBody := "{\n    \"data\": {\n        \"id\": 1399419,\n        \"creator\": \"chengxin.sora\",\n        \"assignee\": \"\",\n        \"status\": \"step1\",\n        \"status_name\": \"step1\",\n        \"workflow_config\": 9671,\n        \"workflow_config_version\": 32,\n        \"workflow_key\": \"dbw_test\",\n        \"workflow_name\": \"dbw_test\",\n        \"target_system\": \"dbw_test_cx\",\n        \"branch\": \"main\",\n        \"version\": 1,\n        \"config\": {\n            \"test123\": \"test123\",\n            \"_context_\": {\n                \"_creator_type\": \"person_account\",\n                \"status_alias_map\": {},\n                \"status_actions_map\": {}\n            }\n        },\n        \"diff\": [],\n        \"is_platform\": 0,\n        \"need_check_config\": true,\n        \"current_assignees\": \"\",\n        \"viewers\": null,\n        \"status_alias_list\": null,\n        \"idempotent_id\": null,\n        \"creator_department\": \"Data-基础架构-数据库-生态工具与平台-生态工具\",\n        \"ctime\": \"2023-06-12T15:55:08\",\n        \"utime\": \"2023-06-12T15:55:08\",\n        \"finished\": 0\n    },\n    \"message\": \"success\",\n    \"code\": 0\n}"
	mock2 := mockey.Mock(io.ReadAll).Return(mockRespBody, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*http.Client).Do).Return(&http.Response{}, nil).Build()
	defer mock3.UnPatch()

	err := service.RejectWorkflowRecord(context.Background(), &dao.BpmFlowInfo{WorkflowId: 123, FlowStep: 1}, "123")
	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestCancelWorkflowRecord(t *testing.T) {
	service := getTicketWorkflowService()

	mock1 := mockey.Mock((*mockConfigProviderImpl).Get).Return(mockConfigRes()).Build()
	defer mock1.UnPatch()
	mockRespBody := "{\n    \"data\": {\n        \"id\": 1399419,\n        \"creator\": \"chengxin.sora\",\n        \"assignee\": \"\",\n        \"status\": \"step1\",\n        \"status_name\": \"step1\",\n        \"workflow_config\": 9671,\n        \"workflow_config_version\": 32,\n        \"workflow_key\": \"dbw_test\",\n        \"workflow_name\": \"dbw_test\",\n        \"target_system\": \"dbw_test_cx\",\n        \"branch\": \"main\",\n        \"version\": 1,\n        \"config\": {\n            \"test123\": \"test123\",\n            \"_context_\": {\n                \"_creator_type\": \"person_account\",\n                \"status_alias_map\": {},\n                \"status_actions_map\": {}\n            }\n        },\n        \"diff\": [],\n        \"is_platform\": 0,\n        \"need_check_config\": true,\n        \"current_assignees\": \"\",\n        \"viewers\": null,\n        \"status_alias_list\": null,\n        \"idempotent_id\": null,\n        \"creator_department\": \"Data-基础架构-数据库-生态工具与平台-生态工具\",\n        \"ctime\": \"2023-06-12T15:55:08\",\n        \"utime\": \"2023-06-12T15:55:08\",\n        \"finished\": 0\n    },\n    \"message\": \"success\",\n    \"code\": 0\n}"
	mock2 := mockey.Mock(io.ReadAll).Return(mockRespBody, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*http.Client).Do).Return(&http.Response{}, nil).Build()
	defer mock3.UnPatch()

	err := service.CancelWorkflowRecord(context.Background(), 123, "123")
	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestDescribeWorkflowLogs(t *testing.T) {
	service := getTicketWorkflowService()

	mock1 := mockey.Mock((*mockConfigProviderImpl).Get).Return(mockConfigRes()).Build()
	defer mock1.UnPatch()
	mockRespBody := "{\n    \"data\": [\n        {\n            \"id\": 19489281,\n            \"creator\": \"dbw_test_cx_platform\",\n            \"workflow\": 1412369,\n            \"ctime\": \"2023-06-15T17:11:56\",\n            \"tag\": 1,\n            \"creator_department\": \"\",\n            \"content\": \"pass: dbw_test_cx_platform 将流程状态从step3变更为结束  approval: 1\",\n            \"old_status\": \"step3\",\n            \"status\": \"结束\",\n            \"create_time\": \"2023-06-15T17:11:56+08:00\"\n        },\n        {\n            \"id\": 19489270,\n            \"creator\": \"dbw_test_cx_platform\",\n            \"workflow\": 1412369,\n            \"ctime\": \"2023-06-15T17:11:26\",\n            \"tag\": 1,\n            \"creator_department\": \"\",\n            \"content\": \"pass: dbw_test_cx_platform 将流程状态从step2变更为step3  approval: 2\",\n            \"old_status\": \"step2\",\n            \"status\": \"step3\",\n            \"create_time\": \"2023-06-15T17:11:26+08:00\"\n        },\n        {\n            \"id\": 19489136,\n            \"creator\": \"dbw_test_cx_platform\",\n            \"workflow\": 1412369,\n            \"ctime\": \"2023-06-15T17:03:01\",\n            \"tag\": 1,\n            \"creator_department\": \"\",\n            \"content\": \"pass: dbw_test_cx_platform 将流程状态从step1变更为step2  approval: 3\",\n            \"old_status\": \"step1\",\n            \"status\": \"step2\",\n            \"create_time\": \"2023-06-15T17:03:01+08:00\"\n        },\n        {\n            \"id\": 19487367,\n            \"creator\": \"dbw_test_cx_platform\",\n            \"workflow\": 1412369,\n            \"ctime\": \"2023-06-15T15:33:44\",\n            \"tag\": 1,\n            \"creator_department\": \"\",\n            \"content\": \"dbw_test_cx_platform 创建了流程工单\",\n            \"old_status\": \"\",\n            \"status\": \"\",\n            \"create_time\": \"2023-06-15T15:33:44+08:00\"\n        }\n    ],\n    \"page\": {\n        \"total_items\": 4,\n        \"current_page\": 1,\n        \"total_page\": 1,\n        \"page_size\": 10\n    },\n    \"message\": \"success\",\n    \"code\": 0\n}"
	mock2 := mockey.Mock(io.ReadAll).Return(mockRespBody, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*http.Client).Do).Return(&http.Response{}, nil).Build()
	defer mock3.UnPatch()

	res, err := service.DescribeWorkflowLogs(context.Background(), 123, "xx")
	if err != nil {
		t.Fatal("failed", err)
	}
	assert.Equal(t, len(res.Data), 4)
}

func Test111(t *testing.T) {
	mac := hmac.New(sha256.New, []byte("dbw_internal_account"))
	mac.Write([]byte("vedbm-phib3qlhkzm2"))
	expectedMac := hex.EncodeToString(mac.Sum(nil))
	fmt.Println("Dbw_" + expectedMac[:26])
}

// Mock_ConfigProvider_CreateBpmWorkflowRecord is a mock for the ConfigProvider interface.
type Mock_ConfigProvider_CreateBpmWorkflowRecord struct {
	service_config.ConfigProvider
}

// Get returns a mock configuration.
func (m *Mock_ConfigProvider_CreateBpmWorkflowRecord) Get(ctx context.Context) *config.Config {
	return &config.Config{}
}
func Test_ticketWorkflowService_CreateBpmWorkflowRecord_BitsUTGen(t *testing.T) {
	// a common selfService instance for all test cases
	selfService := &ticketWorkflowService{
		cnf:         &Mock_ConfigProvider_CreateBpmWorkflowRecord{},
		workflowDal: nil, // not used in the target function
	}

	mockey.PatchConvey("Test_ticketWorkflowService_CreateBpmWorkflowRecord", t, func() {
		// common inputs
		ctx := context.Background()
		configData := BpmConfigData{
			TicketType: model.TicketType_NormalSqlChange.String(),
			Assignee:   []string{"test.user"},
		}

		mockey.PatchConvey("成功场景 - JWT认证, 普通环境", func() {
			// 场景描述：测试在普通环境下使用JWT认证成功创建BPM工单的场景。
			// 数据构造：
			// - BpmType: BPMAuthTypeJWT
			// - x-bytecloud-env: "online" (非boe)
			// - X-Request-Ticket-From: "default" (非multi_cloud)
			// - X-Jwt-Token: A valid token
			// 逻辑链路：
			// 1. Mock GetApprovalConfigId 返回一个有效的配置ID。
			// 2. Mock GetJwtToken 返回一个非空JWT。
			// 3. Mock GetByteCloudEnv 返回 "online"，触发使用 getBpmRecordUrl。
			// 4. Mock GetRequestSrc 返回 "default"，触发默认的认证分支。
			// 5. Mock http.Client.Do 返回一个成功的BPM响应。
			// 6. 验证函数返回正确的BPM工单ID且无错误。
			bpmType := BPMAuthTypeJWT
			expectedBpmID := int64(12345)

			mockey.Mock((*ticketWorkflowService).GetApprovalConfigId).Return(int64(1001)).Build()
			mockey.Mock(GetJwtToken).Return("test-jwt-token").Build()
			mockey.Mock(fwctx.GetTenantID).Return("test-tenant").Build()
			mockey.Mock((*ticketWorkflowService).GetByteCloudRegion).Return("cn-beijing").Build()
			mockey.Mock((*ticketWorkflowService).GetByteCloudEndpoint).Return("test-endpoint").Build()
			mockey.Mock(GetByteCloudEnv).Return("online").Build()
			mockey.Mock((*ticketWorkflowService).getBpmRecordUrl).Return("http://bpm.test/record").Build()
			mockey.Mock(GetRequestSrc).Return("default").Build()

			// Mock HTTP response
			successResp := BpmResponse{
				Data:    BpmData{Id: expectedBpmID},
				Message: "success",
				Code:    0,
			}
			respBody, _ := json.Marshal(successResp)
			httpResp := &http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewReader(respBody)),
			}
			mockey.Mock((*http.Client).Do).Return(httpResp, nil).Build()

			id, err := selfService.CreateBpmWorkflowRecord(ctx, bpmType, configData)

			convey.So(err, convey.ShouldBeNil)
			convey.So(id, convey.ShouldEqual, expectedBpmID)
		})

		mockey.PatchConvey("成功场景 - BasicAuth认证, 多云BOE环境", func() {
			// 场景描述：测试在多云BOE环境下使用BasicAuth认证成功创建BPM工单的场景。
			// 数据构造：
			// - BpmType: BPMAuthTypeBasicAuth
			// - x-bytecloud-env: "boe"
			// - X-Request-Ticket-From: "multi_cloud"
			// - X-Jwt-Token: is empty, fallback to get from multi cloud
			// 逻辑链路：
			// 1. Mock GetApprovalConfigId 返回一个有效的配置ID。
			// 2. Mock GetJwtToken 返回空，触发 GetJwtTokenForMultiCloud。
			// 3. Mock GetByteCloudEnv 返回 "boe"，触发使用 getBpmRecordUrlBOE。
			// 4. Mock GetRequestSrc 返回 "multi_cloud"，触发多云认证分支。
			// 5. Mock getBpmBasicUser 和 getBpmBasicPassword 返回认证信息。
			// 6. Mock http.Client.Do 返回一个成功的BPM响应。
			// 7. 验证函数返回正确的BPM工单ID且无错误。
			bpmType := BPMAuthTypeBasicAuth
			expectedBpmID := int64(54321)

			mockey.Mock((*ticketWorkflowService).GetApprovalConfigId).Return(int64(1002)).Build()
			mockey.Mock(GetJwtToken).Return("").Build()
			mockey.Mock(GetJwtTokenForMultiCloud).Return("multicloud-jwt-token").Build()
			mockey.Mock(fwctx.GetTenantID).Return("test-tenant-mc").Build()
			mockey.Mock((*ticketWorkflowService).GetByteCloudRegion).Return("cn-langfang-boe").Build()
			mockey.Mock((*ticketWorkflowService).GetByteCloudEndpoint).Return("test-endpoint-boe").Build()
			mockey.Mock(GetByteCloudEnv).Return(EnvBoe).Build()
			mockey.Mock((*ticketWorkflowService).getBpmRecordUrlBOE).Return("http://bpm-boe.test/record").Build()
			mockey.Mock(GetRequestSrc).Return(requestSrcMultiCloud).Build()
			mockey.Mock((*ticketWorkflowService).getBpmBasicUser).Return("testuser").Build()
			mockey.Mock((*ticketWorkflowService).getBpmBasicPassword).Return("testpass").Build()

			// Mock HTTP response
			successResp := BpmResponse{
				Data:    BpmData{Id: expectedBpmID},
				Message: "success",
				Code:    0,
			}
			respBody, _ := json.Marshal(successResp)
			httpResp := &http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewReader(respBody)),
			}
			mockey.Mock((*http.Client).Do).Return(httpResp, nil).Build()

			id, err := selfService.CreateBpmWorkflowRecord(ctx, bpmType, configData)

			convey.So(err, convey.ShouldBeNil)
			convey.So(id, convey.ShouldEqual, expectedBpmID)
		})

		mockey.PatchConvey("失败场景 - http.NewRequest失败", func() {
			// 场景描述：模拟创建HTTP请求失败的场景。
			// 数据构造：无特殊构造。
			// 逻辑链路：
			// 1. Mock http.NewRequest 直接返回一个错误。
			// 2. 验证函数返回-1和包含特定错误信息的error。
			mockey.Mock((*ticketWorkflowService).GetApprovalConfigId).Return(int64(1001)).Build()
			mockey.Mock(GetJwtToken).Return("test-jwt-token").Build()
			mockey.Mock(fwctx.GetTenantID).Return("test-tenant").Build()
			mockey.Mock((*ticketWorkflowService).GetByteCloudRegion).Return("cn-beijing").Build()
			mockey.Mock((*ticketWorkflowService).GetByteCloudEndpoint).Return("test-endpoint").Build()
			mockey.Mock(GetByteCloudEnv).Return("online").Build()
			mockey.Mock((*ticketWorkflowService).getBpmRecordUrl).Return("http://bpm.test/record").Build()

			// Mock http.NewRequest to fail
			mockey.Mock(http.NewRequest).Return(nil, errors.New("mock new request error")).Build()

			id, err := selfService.CreateBpmWorkflowRecord(ctx, BPMAuthTypeJWT, configData)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(id, convey.ShouldEqual, -1)
			convey.So(err.Error(), convey.ShouldContainSubstring, "CreateBpmWorkflowRecord NewRequest error")
		})

		mockey.PatchConvey("失败场景 - client.Do请求失败", func() {
			// 场景描述：模拟HTTP客户端执行请求时发生网络错误。
			// 数据构造：无特殊构造。
			// 逻辑链路：
			// 1. 正常构造请求。
			// 2. Mock http.Client.Do 返回一个网络错误。
			// 3. 验证函数返回-1和包含特定错误信息的error。
			mockey.Mock((*ticketWorkflowService).GetApprovalConfigId).Return(int64(1001)).Build()
			mockey.Mock(GetJwtToken).Return("test-jwt-token").Build()
			mockey.Mock(fwctx.GetTenantID).Return("test-tenant").Build()
			mockey.Mock((*ticketWorkflowService).GetByteCloudRegion).Return("cn-beijing").Build()
			mockey.Mock((*ticketWorkflowService).GetByteCloudEndpoint).Return("test-endpoint").Build()
			mockey.Mock(GetByteCloudEnv).Return("online").Build()
			mockey.Mock((*ticketWorkflowService).getBpmRecordUrl).Return("http://bpm.test/record").Build()
			mockey.Mock(GetRequestSrc).Return("default").Build()

			mockey.Mock((*http.Client).Do).Return(nil, errors.New("network connection failed")).Build()

			id, err := selfService.CreateBpmWorkflowRecord(ctx, BPMAuthTypeJWT, configData)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(id, convey.ShouldEqual, -1)
			convey.So(err.Error(), convey.ShouldContainSubstring, "client do create workflow record error")
		})

		mockey.PatchConvey("失败场景 - BPM响应解析失败", func() {
			// 场景描述：模拟BPM服务返回了非法的JSON格式，导致响应解析失败。
			// 数据构造：无特殊构造。
			// 逻辑链路：
			// 1. Mock http.Client.Do 返回一个StatusCode为200，但Body为非法JSON字符串的响应。
			// 2. 验证函数返回-1和包含JSON解析失败信息的error。
			mockey.Mock((*ticketWorkflowService).GetApprovalConfigId).Return(int64(1001)).Build()
			mockey.Mock(GetJwtToken).Return("test-jwt-token").Build()
			mockey.Mock(fwctx.GetTenantID).Return("test-tenant").Build()
			mockey.Mock((*ticketWorkflowService).GetByteCloudRegion).Return("cn-beijing").Build()
			mockey.Mock((*ticketWorkflowService).GetByteCloudEndpoint).Return("test-endpoint").Build()
			mockey.Mock(GetByteCloudEnv).Return("online").Build()
			mockey.Mock((*ticketWorkflowService).getBpmRecordUrl).Return("http://bpm.test/record").Build()
			mockey.Mock(GetRequestSrc).Return("default").Build()

			httpResp := &http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewBufferString("not a valid json")),
			}
			mockey.Mock((*http.Client).Do).Return(httpResp, nil).Build()

			id, err := selfService.CreateBpmWorkflowRecord(ctx, BPMAuthTypeJWT, configData)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(id, convey.ShouldEqual, -1)
			// The target function logs a wrapped error but returns the original error from GetBpmResponse.
			// We assert on the content of the original error.
			convey.So(err.Error(), convey.ShouldContainSubstring, "json反序列化失败")
		})

		mockey.PatchConvey("失败场景 - BPM返回业务错误", func() {
			// 场景描述：模拟BPM服务返回了业务错误码（code: -1）。
			// 数据构造：无特殊构造。
			// 逻辑链路：
			// 1. Mock http.Client.Do 返回一个code为-1的BPM响应。
			// 2. 验证AnalyzeBpmResponse会正确解析并返回错误。
			// 3. 验证函数返回-1和包含BPM错误信息的error。
			mockey.Mock((*ticketWorkflowService).GetApprovalConfigId).Return(int64(1001)).Build()
			mockey.Mock(GetJwtToken).Return("test-jwt-token").Build()
			mockey.Mock(fwctx.GetTenantID).Return("test-tenant").Build()
			mockey.Mock((*ticketWorkflowService).GetByteCloudRegion).Return("cn-beijing").Build()
			mockey.Mock((*ticketWorkflowService).GetByteCloudEndpoint).Return("test-endpoint").Build()
			mockey.Mock(GetByteCloudEnv).Return("online").Build()
			mockey.Mock((*ticketWorkflowService).getBpmRecordUrl).Return("http://bpm.test/record").Build()
			mockey.Mock(GetRequestSrc).Return("default").Build()

			errorResp := BpmResponse{
				Data:    BpmData{},
				Message: "BPM business error",
				Code:    -1,
			}
			respBody, _ := json.Marshal(errorResp)
			httpResp := &http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(bytes.NewReader(respBody)),
			}
			mockey.Mock((*http.Client).Do).Return(httpResp, nil).Build()

			id, err := selfService.CreateBpmWorkflowRecord(ctx, BPMAuthTypeJWT, configData)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(id, convey.ShouldEqual, -1)
			// The target function logs a wrapped error but returns the original error from AnalyzeBpmResponse.
			// We assert on the content of the original error.
			convey.So(err.Error(), convey.ShouldContainSubstring, "请求bpm失败：BPM business error")
		})
	})
}

func Test_ticketWorkflowService_GetApprovalConfigId_BitsUTGen(t *testing.T) {
	// 待测目标
	selfService := &ticketWorkflowService{}
	ctx := context.Background()

	mockey.PatchConvey("Test_ticketWorkflowService_GetApprovalConfigId", t, func() {
		// Mock通用依赖
		mockey.Mock(log.Info).Return().Build()
		mockey.Mock((*ticketWorkflowService).GetMultiCloudDMLConfigId).Return(int64(1)).Build()
		mockey.Mock((*ticketWorkflowService).GetMultiCloudByteCloudDMLConfigId).Return(int64(2)).Build()
		mockey.Mock((*ticketWorkflowService).GetMultiCloudDMLConfigIdBOE).Return(int64(3)).Build()
		mockey.Mock((*ticketWorkflowService).GetMultiCloudDDLConfigId).Return(int64(11)).Build()
		mockey.Mock((*ticketWorkflowService).GetMultiCloudByteCloudDDLConfigId).Return(int64(12)).Build()
		mockey.Mock((*ticketWorkflowService).GetMultiCloudDDLConfigIdBOE).Return(int64(13)).Build()

		mockey.PatchConvey("场景: TicketType=NormalSqlChange", func() {
			mockey.PatchConvey("子场景: 默认环境", func() {
				// 场景描述：
				// 当 ticketType 为 NormalSqlChange，且请求来源和环境都不是特殊情况时，获取默认的 DML 配置 ID。
				// 数据构造：
				// - ticketType: model.TicketType_NormalSqlChange
				// - context extra: X-Request-Ticket-From 和 X-Bytecloud-Env 都不为特殊值
				// 逻辑链路：
				// 1. Mock fwctx.GetExtra 返回一个空的 extra map。
				// 2. GetRequestSrc 和 GetByteCloudEnv 将返回空字符串。
				// 3. 函数将进入 NormalSqlChange case 的默认分支。
				// 4. 预期调用 GetMultiCloudDMLConfigId 并返回其结果。

				// Mock
				mockey.Mock(fwctx.GetExtra).Return(make(map[string]string)).Build()

				// 调用
				result := selfService.GetApprovalConfigId(ctx, model.TicketType_NormalSqlChange.String())

				// 断言
				convey.So(result, convey.ShouldEqual, int64(1))
			})

			mockey.PatchConvey("子场景: 请求来源为 multi_cloud", func() {
				// 场景描述：
				// 当 ticketType 为 NormalSqlChange，请求来源为 multi_cloud 时，获取多云字节云的 DML 配置 ID。
				// 数据构造：
				// - ticketType: model.TicketType_NormalSqlChange
				// - context extra: X-Request-Ticket-From = "multi_cloud"
				// 逻辑链路：
				// 1. Mock fwctx.GetExtra 返回包含 X-Request-Ticket-From 的 map。
				// 2. GetRequestSrc 返回 "multi_cloud"。
				// 3. 函数将进入 NormalSqlChange case，并满足 requestSrc == requestSrcMultiCloud 的条件。
				// 4. 预期调用 GetMultiCloudByteCloudDMLConfigId 并返回其结果。

				// Mock
				mockey.Mock(fwctx.GetExtra).Return(map[string]string{
					"X-Request-Ticket-From": requestSrcMultiCloud,
				}).Build()

				// 调用
				result := selfService.GetApprovalConfigId(ctx, model.TicketType_NormalSqlChange.String())

				// 断言
				convey.So(result, convey.ShouldEqual, int64(2))
			})

			mockey.PatchConvey("子场景: 环境为 boe", func() {
				// 场景描述：
				// 当 ticketType 为 NormalSqlChange，环境为 boe 时，获取 BOE 环境的 DML 配置 ID。
				// 数据构造：
				// - ticketType: model.TicketType_NormalSqlChange
				// - context extra: X-Bytecloud-Env = "boe"
				// 逻辑链路：
				// 1. Mock fwctx.GetExtra 返回包含 X-Bytecloud-Env 的 map。
				// 2. GetByteCloudEnv 返回 "boe"。
				// 3. 函数将进入 NormalSqlChange case，并满足 xBytecloudEnv == EnvBoe 的条件。
				// 4. 预期调用 GetMultiCloudDMLConfigIdBOE 并返回其结果。

				// Mock
				mockey.Mock(fwctx.GetExtra).Return(map[string]string{
					"X-Bytecloud-Env": EnvBoe,
				}).Build()

				// 调用
				result := selfService.GetApprovalConfigId(ctx, model.TicketType_NormalSqlChange.String())

				// 断言
				convey.So(result, convey.ShouldEqual, int64(3))
			})

			mockey.PatchConvey("子场景: 请求来源为 multi_cloud 且环境为 boe", func() {
				// 场景描述：
				// 当 ticketType 为 NormalSqlChange，请求来源为 multi_cloud 且环境为 boe 时，由于 boe 判断在后，应获取 BOE 环境的 DML 配置 ID。
				// 数据构造：
				// - ticketType: model.TicketType_NormalSqlChange
				// - context extra: X-Request-Ticket-From = "multi_cloud", X-Bytecloud-Env = "boe"
				// 逻辑链路：
				// 1. Mock fwctx.GetExtra 返回包含 X-Request-Ticket-From 和 X-Bytecloud-Env 的 map。
				// 2. GetRequestSrc 返回 "multi_cloud"，GetByteCloudEnv 返回 "boe"。
				// 3. 函数进入 NormalSqlChange case，先将 ApprovalConfigId 设为 GetMultiCloudByteCloudDMLConfigId 的结果，然后被 GetMultiCloudDMLConfigIdBOE 的结果覆盖。
				// 4. 预期最终调用 GetMultiCloudDMLConfigIdBOE 并返回其结果。

				// Mock
				mockey.Mock(fwctx.GetExtra).Return(map[string]string{
					"X-Request-Ticket-From": requestSrcMultiCloud,
					"X-Bytecloud-Env":       EnvBoe,
				}).Build()

				// 调用
				result := selfService.GetApprovalConfigId(ctx, model.TicketType_NormalSqlChange.String())

				// 断言
				convey.So(result, convey.ShouldEqual, int64(3))
			})
		})

		mockey.PatchConvey("场景: TicketType=FreeLockStructChange", func() {
			mockey.PatchConvey("子场景: 默认环境", func() {
				// 场景描述：
				// 当 ticketType 为 FreeLockStructChange，且请求来源和环境都不是特殊情况时，获取默认的 DDL 配置 ID。
				// 数据构造：
				// - ticketType: model.TicketType_FreeLockStructChange
				// - context extra: 空
				// 逻辑链路：
				// 1. Mock fwctx.GetExtra 返回空 map。
				// 2. 函数进入 FreeLockStructChange case 的默认分支。
				// 3. 预期调用 GetMultiCloudDDLConfigId 并返回其结果。

				// Mock
				mockey.Mock(fwctx.GetExtra).Return(make(map[string]string)).Build()

				// 调用
				result := selfService.GetApprovalConfigId(ctx, model.TicketType_FreeLockStructChange.String())

				// 断言
				convey.So(result, convey.ShouldEqual, int64(11))
			})

			mockey.PatchConvey("子场景: 请求来源为 multi_cloud", func() {
				// 场景描述：
				// 当 ticketType 为 FreeLockStructChange，请求来源为 multi_cloud 时，获取多云字节云的 DDL 配置 ID。
				// 数据构造：
				// - ticketType: model.TicketType_FreeLockStructChange
				// - context extra: X-Request-Ticket-From = "multi_cloud"
				// 逻辑链路：
				// 1. Mock fwctx.GetExtra 返回包含 X-Request-Ticket-From 的 map。
				// 2. 函数进入 FreeLockStructChange case，并满足 requestSrc == requestSrcMultiCloud 的条件。
				// 3. 预期调用 GetMultiCloudByteCloudDDLConfigId 并返回其结果。

				// Mock
				mockey.Mock(fwctx.GetExtra).Return(map[string]string{
					"X-Request-Ticket-From": requestSrcMultiCloud,
				}).Build()

				// 调用
				result := selfService.GetApprovalConfigId(ctx, model.TicketType_FreeLockStructChange.String())

				// 断言
				convey.So(result, convey.ShouldEqual, int64(12))
			})

			mockey.PatchConvey("子场景: 环境为 boe", func() {
				// 场景描述：
				// 当 ticketType 为 FreeLockStructChange，环境为 boe 时，获取 BOE 环境的 DDL 配置 ID。
				// 数据构造：
				// - ticketType: model.TicketType_FreeLockStructChange
				// - context extra: X-Bytecloud-Env = "boe"
				// 逻辑链路：
				// 1. Mock fwctx.GetExtra 返回包含 X-Bytecloud-Env 的 map。
				// 2. 函数进入 FreeLockStructChange case，并满足 xBytecloudEnv == EnvBoe 的条件。
				// 3. 预期调用 GetMultiCloudDDLConfigIdBOE 并返回其结果。

				// Mock
				mockey.Mock(fwctx.GetExtra).Return(map[string]string{
					"X-Bytecloud-Env": EnvBoe,
				}).Build()

				// 调用
				result := selfService.GetApprovalConfigId(ctx, model.TicketType_FreeLockStructChange.String())

				// 断言
				convey.So(result, convey.ShouldEqual, int64(13))
			})

			mockey.PatchConvey("子场景: 请求来源为 multi_cloud 且环境为 boe", func() {
				// 场景描述：
				// 当 ticketType 为 FreeLockStructChange，请求来源为 multi_cloud 且环境为 boe 时，由于 boe 判断在后，应获取 BOE 环境的 DDL 配置 ID。
				// 数据构造：
				// - ticketType: model.TicketType_FreeLockStructChange
				// - context extra: X-Request-Ticket-From = "multi_cloud", X-Bytecloud-Env = "boe"
				// 逻辑链路：
				// 1. Mock fwctx.GetExtra 返回包含 X-Request-Ticket-From 和 X-Bytecloud-Env 的 map。
				// 2. 函数进入 FreeLockStructChange case，先将 ApprovalConfigId 设为 GetMultiCloudByteCloudDDLConfigId 的结果，然后被 GetMultiCloudDDLConfigIdBOE 的结果覆盖。
				// 4. 预期最终调用 GetMultiCloudDDLConfigIdBOE 并返回其结果。

				// Mock
				mockey.Mock(fwctx.GetExtra).Return(map[string]string{
					"X-Request-Ticket-From": requestSrcMultiCloud,
					"X-Bytecloud-Env":       EnvBoe,
				}).Build()

				// 调用
				result := selfService.GetApprovalConfigId(ctx, model.TicketType_FreeLockStructChange.String())

				// 断言
				convey.So(result, convey.ShouldEqual, int64(13))
			})
		})

		mockey.PatchConvey("场景: TicketType=default", func() {
			mockey.PatchConvey("子场景: 默认环境", func() {
				// 场景描述：
				// 当 ticketType 为未知的类型时，应走默认逻辑，行为与 NormalSqlChange 一致，获取默认的 DML 配置 ID。
				// 数据构造：
				// - ticketType: "UnknownType"
				// - context extra: 空
				// 逻辑链路：
				// 1. Mock fwctx.GetExtra 返回空 map。
				// 2. 函数进入 switch 的 default case。
				// 3. 预期调用 GetMultiCloudDMLConfigId 并返回其结果。

				// Mock
				mockey.Mock(fwctx.GetExtra).Return(make(map[string]string)).Build()

				// 调用
				result := selfService.GetApprovalConfigId(ctx, "UnknownType")

				// 断言
				convey.So(result, convey.ShouldEqual, int64(1))
			})

			mockey.PatchConvey("子场景: 请求来源为 multi_cloud", func() {
				// 场景描述：
				// 当 ticketType 为未知的类型，请求来源为 multi_cloud 时，应走默认逻辑，行为与 NormalSqlChange 一致，获取多云字节云的 DML 配置 ID。
				// 数据构造：
				// - ticketType: "UnknownType"
				// - context extra: X-Request-Ticket-From = "multi_cloud"
				// 逻辑链路：
				// 1. Mock fwctx.GetExtra 返回包含 X-Request-Ticket-From 的 map。
				// 2. 函数进入 switch 的 default case，并满足 requestSrc == requestSrcMultiCloud 的条件。
				// 3. 预期调用 GetMultiCloudByteCloudDMLConfigId 并返回其结果。

				// Mock
				mockey.Mock(fwctx.GetExtra).Return(map[string]string{
					"X-Request-Ticket-From": requestSrcMultiCloud,
				}).Build()

				// 调用
				result := selfService.GetApprovalConfigId(ctx, "UnknownType")

				// 断言
				convey.So(result, convey.ShouldEqual, int64(2))
			})
		})
	})
}
