package workflow

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"strings"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	dslibutils "code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	parser "code.byted.org/infcs/ds-sql-parser"
	"code.byted.org/infcs/ds-sql-parser/ast"
	"github.com/qjpcpu/fp"
)

// PreCheck 预检查
func (selfService *ticketService) PreCheck(ctx context.Context, req *model.PreCheckTicketReq) (ret *model.PreCheckTicketResp, err error) {
	// 1.检查是否需要进行预检查,判断ticket的执行状态
	_, ticket, err := selfService.IsNeedCheck(ctx, dslibutils.MustStrToInt64(req.TicketId))
	if err != nil {
		return nil, consts.ErrorWithParam(model.ErrorCode_InputParamError, fmt.Sprintf("ticket %v is not need precheck %v", req.TicketId, err))
	}
	// 2.判断当前用户角色是否可以直接提交,如果是主账户、实例Owner、实例DBA三者之一,则可以直接提交
	var userId = selfService.getUserId(ctx)
	isUpperAccount, err := selfService.workflowDal.IsUpperAccount(ctx, fwctx.GetTenantID(ctx), userId, ticket.InstanceId)
	if err != nil {
		log.Warn(ctx, "check super account error %s", err)
	}
	log.Info(ctx, "ticket: %v isUpperAccount is %v, tenant id is %v,user id is %v", req.TicketId, isUpperAccount, fwctx.GetTenantID(ctx), userId)
	// 前端调用PreCheck的时候,会走到这个逻辑
	if ticket.TicketStatus != TicketUndo {
		// online ddl 走新的逻辑
		if ticket.TicketType == int32(model.TicketType_FreeLockStructChange) && selfService.ticketCommonService.IsInGhostDDLWhite(ctx, fwctx.GetTenantID(ctx), ticket.InstanceType.String()) {
			res, err := selfService.getNewPreCheckResult(ctx, req.TicketId, ticket.TenantId)
			if res != nil {
				res.IsContainDDL = true
			}
			return res, err
		} else {
			res, err := selfService.getPreCheckEnableSubmitFlag(ctx, dslibutils.MustStrToInt64(req.TicketId), isUpperAccount)
			// 解析SQL语句,查看是否包含DDL,记录到里面
			parseStruct, _ := selfService.ParseSqlTextToStruct(ctx, &shared.Ticket{
				TicketId:     ticket.TicketId,
				InstanceType: ticket.InstanceType,
				SqlText:      ticket.SqlText,
			}) // 如果报错或者解析结果为空,则认为不包含DDL
			if parseStruct == nil {
				parseStruct = &TicketSqlTextParseResult{}
			}
			if res != nil {
				res.IsContainDDL = parseStruct.isContainDDL
			}
			return res, err
		}
	}
	// 归档  不需要进行预检查
	if ticket.CreateFrom == TicketFromDataArchive {
		return selfService.NoPreCheck(ctx, ticket)
	}
	log.Info(ctx, "ticket: %v need check", utils.Show(ticket))

	// 2.修改工单状态为预检查中
	if err = selfService.ChangeTicketStatus(ctx, ticket.TicketId, TicketPreCheck, "ticket start precheck"); err != nil {
		log.Warn(ctx, "ticket: %d change status error: %s", ticket.TicketId, err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, fmt.Sprintf("ticket: %d change status error: %s", ticket.TicketId, err.Error()))
	}

	// 3.检查实例是否存在、是否running
	isInstanceAvailable, err := selfService.IsInstanceAvailable(ctx, ticket.TenantId, ticket.InstanceId)
	if err != nil {
		log.Warn(ctx, "ticket: %d check instance error: %s", ticket.TicketId, err.Error())
		selfService.ChangeTicketStatus(ctx, ticket.TicketId, TicketPreCheckError,
			fmt.Sprintf("ticket: %d check instance error: %s", ticket.TicketId, err.Error()))
		return nil, consts.ErrorOf(model.ErrorCode_InstanceNotInSecureMode)
	}
	if !isInstanceAvailable {
		log.Warn(ctx, "ticket: %d instance %v is not available", ticket.TicketId, ticket.InstanceId)
		selfService.ChangeTicketStatus(ctx, ticket.TicketId, TicketPreCheckError,
			fmt.Sprintf("ticket: %v instance %v is not available", ticket.TicketId, ticket.InstanceId))
		return nil, consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	if !selfService.IsInstanceRunning(ctx, ticket.InstanceId, ticket.InstanceType) {
		log.Warn(ctx, "ticket: %d instance %v is not running", ticket.TicketId, ticket.InstanceId)
		selfService.ChangeTicketStatus(ctx, ticket.TicketId, TicketPreCheckError,
			fmt.Sprintf("ticket: %d instance %v is not running", ticket.TicketId, ticket.InstanceId))
		return nil, consts.ErrorOf(model.ErrorCode_CheckInstanceError)
	}
	log.Info(ctx, "ticket: %v instance check success", req.TicketId)

	// online ddl 走新的逻辑
	if ticket.TicketType == int32(model.TicketType_FreeLockStructChange) && selfService.ticketCommonService.IsInGhostDDLWhite(ctx, fwctx.GetTenantID(ctx), ticket.InstanceType.String()) {
		res, err := selfService.newDbwPreCheck(ctx, ticket)
		if res != nil {
			res.IsContainDDL = true
		}
		return res, err
	}

	// 4.进行预检查
	ret = selfService.initCreateTicketResp(model.PreCheckStatus_Undo)
	//"Step":0,"PrecheckFinished":false,到这里，step和precheckFinished还没有具体赋值
	if err = selfService.preCheck(ctx, ticket, ret); err != nil {
		log.Warn(ctx, "ticket %v: true preCheck error:  %s", ticket.TicketId, err.Error())
		selfService.ChangeTicketStatus(ctx, ticket.TicketId, TicketPreCheckError,
			fmt.Sprintf("ticket %v: true preCheck error:  %s", ticket.TicketId, err.Error()))
		return ret, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	log.Info(ctx, "ticket: %d preCheck complete,ret is %v", ticket.TicketId, utils.Show(ret))

	// 5.落库
	if err = selfService.UpdatePreCheckResult(ctx, dslibutils.MustStrToInt64(req.TicketId), ret); err != nil {
		return ret, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	// 6.修改res，将英文名改为中文名
	selfService.changItemNameEnToCn(ret)
	// i18n
	site := selfService.i18nSvc.GetLanguage(ctx)
	if site == "en" {
		for _, res := range ret.CheckItems {
			res.Item = selfService.i18nSvc.TranslateByConf(res.Item)
		}
	}
	// 7.修改工单状态
	if err = selfService.changeTicketStateAfterPreCheck(ctx, ret, ticket); err != nil {
		log.Warn(ctx, "changeTicketStateAfterPreCheck error:%s", err.Error())
		return ret, err
	}
	return ret, nil
}

func (selfService *ticketService) changeTicketStateAfterPreCheck(ctx context.Context, ret *model.PreCheckTicketResp, ticket *shared.Ticket) (err error) {
	// 7.修改工单状态
	if !ret.AllPass {
		log.Info(ctx, "ticket %v GetPreCheckMemo(ret) is %v", ticket.TicketId, GetPreCheckMemo(ret))
		err = selfService.ChangeTicketStatus(ctx, ticket.TicketId, TicketPreCheckError, GetPreCheckMemo(ret))
	} else {
		flowInfo := &dao.BpmFlowInfo{
			TicketId:     ticket.TicketId,
			TenantId:     ticket.TenantId,
			InstanceId:   ticket.InstanceId,
			FlowStep:     0,
			CreateUserId: ticket.CreateUserId,
		}
		err = selfService.ChangePreCheckTicketStatusAndOperator(ctx, ticket, TicketExamine, flowInfo)
	}
	if err != nil {
		log.Warn(ctx, "ticket: %d,modify ticket status error:%s", ticket.TicketId, err.Error())
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return nil
}

func (selfService *ticketService) getUserId(ctx context.Context) string {
	var userId string
	if fwctx.GetUserID(ctx) == "" {
		userId = fwctx.GetTenantID(ctx)
	} else {
		userId = fwctx.GetUserID(ctx)
	}
	return userId
}

func (selfService *ticketService) IsNeedCheck(ctx context.Context, ticketId int64) (bool, *shared.Ticket, error) {
	ticket, err := selfService.workflowDal.DescribeByTicketID(ctx, ticketId)
	if err != nil {
		errMsg := fmt.Sprintf("get ticket detail failed,ticketId:%d, err:%v", ticketId, err)
		log.Warn(ctx, errMsg)
		return false, nil, fmt.Errorf("failed to obtain ticket details")
	}
	return selfService.isNeedPreCheck(ticket.TicketStatus), ChangeTicketType(ticket), nil
}

func (selfService *ticketService) getNewPreCheckResult(ctx context.Context, ticketId string, tenantId string) (*model.PreCheckTicketResp, error) {
	result := &model.PreCheckTicketResp{AllPass: true, PrecheckFinished: false}
	res, err := selfService.ticketRepo.GetPreCheckResult(ctx, ticketId, tenantId)
	if err != nil {
		log.Warn(ctx, "get preCheck result error:%s", err.Error())
		return nil, err
	}
	if len(res) < OnlineDDlCheckNum {
		result.AllPass = false
		return result, nil
	}
	result.PrecheckFinished = true
	for _, value := range res {
		if value.PreCheckState == model.PreCheckState_Error {
			result.AllPass = false
		}
	}
	result.EnableSubmitTicket = result.AllPass
	return result, nil
}

func (selfService *ticketService) getPreCheckResult(ctx context.Context, ticketId int64) (*model.PreCheckTicketResp, error) {
	ticketPreCheckResult, err := selfService.workflowDal.GetPreCheckResult(ctx, ticketId)
	if err != nil {
		log.Warn(ctx, "get pre check results failed，err:%v ", err.Error())
		return nil, err
	}

	allPass := true
	var checkItems []*model.CheckItem
	var step int32
	for _, checkResult := range ticketPreCheckResult {
		checkItem := &model.CheckItem{
			Item:   checkResult.Item,
			Memo:   checkResult.Memo,
			Status: model.PreCheckStatus(checkResult.Status),
		}
		switch checkResult.Item {
		case PreCheckSyntax: // 语法检查
			checkItem.ItemType = model.ItemType_PreCheckSyntax
		case PreCheckPermission:
			checkItem.ItemType = model.ItemType_PreCheckPermission
		case PreCheckExplain:
			checkItem.ItemType = model.ItemType_PreCheckExplain
		case PreCheckSecurityRule:
			checkItem.ItemType = model.ItemType_PreCheckSecurityRule
		}
		if checkItem.Status != model.PreCheckStatus_Pass {
			allPass = false
		}
		if checkItem.Status != model.PreCheckStatus_Undo {
			step = step + 1
		}
		checkItems = append(checkItems, checkItem)
	}
	resp := &model.PreCheckTicketResp{
		AllPass:    allPass,
		CheckItems: checkItems,
		Step:       step,
	}

	if step == int32(len(CheckItemNames)) {
		resp.PrecheckFinished = true
	}
	selfService.changItemNameEnToCn(resp)
	// i18n
	site := selfService.i18nSvc.GetLanguage(ctx)
	if site == "en" {
		for _, res := range resp.CheckItems {
			res.Item = selfService.i18nSvc.TranslateByConf(res.Item)
		}
	}
	return resp, nil
}

func (selfService *ticketService) IsInstanceAvailable(ctx context.Context, tenantId string, instanceId string) (bool, error) {
	isInstanceAvailable, err := selfService.workflowDal.IsInstanceAvailable(ctx, tenantId, instanceId)
	if err != nil {
		log.Info(ctx, "failed to obtain instance information: err：%v", err)
		return false, err
	}
	return isInstanceAvailable, nil
}

func (selfService *ticketService) IsInstanceRunning(ctx context.Context, instanceId string, instanceType shared.DataSourceType) bool {
	inst, err := selfService.ds.ListInstance(ctx, &datasource.ListInstanceReq{
		InstanceId: instanceId,
		Type:       instanceType,
		PageNumber: 1,
		PageSize:   10,
	})
	if err != nil {
		log.Warn(ctx, "rds api err %s", err.Error())
		return false
	}
	if inst != nil && inst.Total == 0 {
		log.Warn(ctx, "instance not found %s", instanceId)
		return false
	}
	for _, val := range inst.InstanceList {
		if *val.InstanceId == instanceId && val.InstanceStatus == "Running" {
			return true
		}
	}
	return false
}

func (selfService *ticketService) preCheck(ctx context.Context, ticket *shared.Ticket, resp *model.PreCheckTicketResp) error {
	//首先先构建一个ds（注意：在PG场景下，获取到的内网IP没办法连接到数据库，需要内部自行再次获取含有shuttle转换后的ds）
	ds, err := selfService.getDBDataSource(ctx, ticket.InstanceId, ticket.DbName, ticket.InstanceType)
	if err != nil {
		log.Warn(ctx, "get datasource error %v", err)
		return err
	}
	log.Info(ctx, "ticket %v: explain datasource instanceType is %s,instanceId is %s,address is %s,dbname is %s,user is %s",
		ticket.TicketId, ds.Type, ds.InstanceId, ds.Address, ds.Db, ds.User)

	// 0、解析SQL语句
	parseStruct, err := selfService.ParseSqlTextToStruct(ctx, ticket)
	if err != nil {
		// 正常来讲,是无法走到这里的,工单创建的时候,已经做过语法校验了
		log.Warn(ctx, "parse ticket %v sql text error:%v", ticket.TicketId, err)
		return consts.ErrorWithParam(model.ErrorCode_PrecheckTicketError, "parse ticket sql error")
	}
	resp.IsContainDDL = parseStruct.isContainDDL
	log.Info(ctx, "ticket %v parse sql struct is %v,sqls is %v", ticket.TicketId, utils.Show(parseStruct.stmts), utils.Show(parseStruct.sqls))
	/* 1、Explain方法
	计算函数
		如果是无锁结构变更(基本都是alter table语句),无法使用explain语法获取行数,生成一个select语法的sql来计算影响行数
		如果是普通SQL变更,需要分情况讨论
			1、是alter或者drop等语句,也无法使用explain语法获取行数,生成一个select语法的sql来计算影响行数
			2、是update、delete、insert等语句，可以使用explain语法获取行数
			3、如果是create语句，则影响行数直接设置为0
		如果是无锁SQL变更,因为只支持update、delete和insert...into select语句，所以直接用explain计算即可
	*/
	sqlList := selfService.generateCountSQL(ctx, ticket, parseStruct)
	log.Info(ctx, "ticket %v sql list for explain is %v", ticket.TicketId, sqlList)
	if len(sqlList) == 0 { // 说明当前没有要执行的语句,直接将explain结果赋值为0,理论上走不到这里
		log.Warn(ctx, "ticket %v len of sqlList is 0", ticket.TicketId)
		resp.CheckItems[2].Status = model.PreCheckStatus_Error
		resp.CheckItems[2].Memo = DefaultMemoForExplain
		resp.AllPass = false
		return nil
	}
	explainRes, err := selfService.explainCommands(ctx, resp, &sqlList, ds)
	if err != nil {
		log.Warn(ctx, "explain ticket %v commands error:%v", ticket.TicketId, err)
		resp.AllPass = false
		// 如果Explain报错,不管是主账号还是子账号，无法提交工单
		resp.EnableSubmitTicket = false
	}
	explainRes.sqls = parseStruct.sqls
	log.Info(ctx, "ticket: %v explain command complete,explain check result is %v,sqls is %v",
		ticket.TicketId, utils.Show(explainRes.checkResult), utils.Show(explainRes.sqls))
	selfService.UpdatePreCheckResult(ctx, ticket.TicketId, resp)

	// 2. SQL语法检查
	syntaxRes, checkRes := selfService.checkSqlFormat(ctx, ticket, resp, parseStruct)
	if !checkRes {
		log.Warn(ctx, "check ticket %v sql format false", ticket.TicketId)
		resp.AllPass = false
		// 如果语法检查报错,不管是主账号还是子账号，无法提交工单,这个是我们能识别出来的
		resp.EnableSubmitTicket = false
	}
	syntaxRes.sqls = parseStruct.sqls
	log.Info(ctx, "ticket %v check sql format complete,sytaxRes check result is %v,sqls is %v",
		ticket.TicketId, utils.Show(syntaxRes.checkResult), utils.Show(syntaxRes.sqls))
	selfService.UpdatePreCheckResult(ctx, ticket.TicketId, resp)

	// 3. SQL权限检查（权限检查区分子账号和主账号）
	// 主账号权限部分不做校验,直接过
	// 子账号权限部分,如果有一个SQL语句没有通过,那么就不允许提交工单
	permissionRes, isPermissionsPass := selfService.checkPermissions(ctx, ticket, resp, parseStruct)
	if !isPermissionsPass {
		// 如果没过,说明是子账号的权限,主账号走不到这里，所以不允许子账号过权限问题
		log.Warn(ctx, "ticket %v permission not pass,please check", ticket.TicketId)
		resp.AllPass = false
		// 主账号的安全检查报错,可以提交工单
		resp.EnableSubmitTicket = false
	}
	permissionRes.sqls = parseStruct.sqls
	log.Info(ctx, "ticket %v check permission complete,permissionRes check result is %v ,sqls is %v",
		ticket.TicketId, utils.Show(permissionRes.checkResult), utils.Show(permissionRes.sqls))
	selfService.UpdatePreCheckResult(ctx, ticket.TicketId, resp)

	// 4. 安全检查
	resp.CheckItems[3].Memo = "Pass"
	resp.CheckItems[3].Status = model.PreCheckStatus_Pass
	for _, val := range parseStruct.sqls {
		if err = selfService.priSvc.SecurityCheckForTicket(ctx, val, ds, model.SqlExecutionType_SqlTicket, ticket.TicketId); err != nil {
			log.Warn(ctx, "security rule check error:%v", err.Error())
			resp.CheckItems[3].Status = model.PreCheckStatus_Error
			resp.CheckItems[3].Memo = fmt.Sprintf("security rule check error:%v", err.Error())
			resp.AllPass = false
		}
	}
	param := &dal.SecRuleExecuteRecordParam{
		OrderType: dslibutils.Int8Ref(int8(model.SqlExecutionType_SqlTicket)),
		OrderId:   conv.Int64ToStr(ticket.TicketId),
	}
	list, err := selfService.executeRecordDal.List(ctx, param)
	if err != nil {
		log.Info(ctx, "DescribeRuleExecuteRecordSummaryResult list err, err is:%v", err)
		resp.CheckItems[3].Status = model.PreCheckStatus_Error
		resp.CheckItems[3].Memo = fmt.Sprintf("security rule check error:%v", err)
		resp.AllPass = false
	}
	isUpperAccount, _ := selfService.workflowDal.IsUpperAccount(ctx, ticket.TenantId, ticket.CreateUserId, ticket.InstanceId)
	for _, val := range list.RuleExecuteRecords {
		// 有一个安全规则不通过,不影响提交工单
		if val.HighRiskCount != 0 || val.MiddleRiskCount != 0 || val.LowRiskCount != 0 {
			resp.CheckItems[3].Status = model.PreCheckStatus_Error
			resp.CheckItems[3].Memo = fmt.Sprintf("high %v middle %v low %v", val.HighRiskCount, val.MiddleRiskCount, val.LowRiskCount)
			resp.AllPass = false
		}
		log.Info(ctx, "ticket %v isUpperAccount is %v high risk count is %v", ticket.TicketId, isUpperAccount, val.HighRiskCount)
		// 对于低权限账号,有高风险的安全规则,则不允许提交工单，高权限账号安全规则不拦截
		if !isUpperAccount && val.HighRiskCount != 0 {
			log.Warn(ctx, "this user is not upper account,security rule check has some problem,can not submit ticket %v", ticket.TicketId)
			resp.EnableSubmitTicket = false
		}
	}
	selfService.UpdatePreCheckResult(ctx, ticket.TicketId, resp)

	var entrys = make([]*dao.TicketPreCheckDetail, 0)
	//FixMe 跟长度可能不一样？
	log.Info(ctx, "len of explainRes is %v,len of syntaxRes is %v,len of permissionRes is %v",
		len(explainRes.checkResult), len(syntaxRes.checkResult), len(permissionRes.checkResult))
	for idx, val := range parseStruct.sqls {
		entry := &dao.TicketPreCheckDetail{
			TicketId:              ticket.TicketId,
			TenantId:              fwctx.GetTenantID(ctx),
			SqlText:               val,
			ExplainResult:         explainRes.checkResult[idx],
			SyntaxCheckResult:     syntaxRes.checkResult[idx],
			PermissionCheckResult: permissionRes.checkResult[idx],
			SecurityCheckResult:   "",
		}
		entrys = append(entrys, entry)
	}
	if err = selfService.preCheckDetailDal.CreatePreCheckDetail(ctx, entrys); err != nil {
		log.Warn(ctx, "create pre check detail error:%v", err.Error())
		return err
	}
	log.Info(ctx, "ticket %v pre check complete,result is %v", ticket.TicketId, utils.Show(resp))
	return nil
}

func (selfService *ticketService) checkPermissions(ctx context.Context, ticket *shared.Ticket,
	resp *model.PreCheckTicketResp, parseStruct *TicketSqlTextParseResult) (*PrecheckTicketPermissionResult, bool) {
	// TODO 这里有点难搞,因为这里需要判断用户的SQL语句的类型来决定权限
	// 1.判断用户是不是高权限账户，如果是主账户、实例dba、实例owner，这三个中的一个，那么就放行
	isUpperAccount, err := selfService.workflowDal.IsUpperAccount(ctx, ticket.TenantId, ticket.CreateUserId, ticket.InstanceId)
	if err != nil {
		log.Warn(ctx, "failed to get and create user role，err：%v", err)
		resp.CheckItems[1].Status = model.PreCheckStatus_Error
		resp.CheckItems[1].Memo = fmt.Sprintf("failed to get and create user role，err：%v", err)
		return &PrecheckTicketPermissionResult{
			checkResult: GetSliceAllSameString(len(parseStruct.sqls), fmt.Sprintf("failed to get and create user role，err：%v", err)),
		}, false
	}
	if isUpperAccount {
		// 主账号,不检查权限,直接放行
		log.Info(ctx, "ticket %v is created by upper account", ticket.TicketId)
		resp.CheckItems[1].Status = model.PreCheckStatus_Pass
		resp.CheckItems[1].Memo = "Pass"
		return &PrecheckTicketPermissionResult{
			checkResult: GetSliceAllSameString(len(parseStruct.sqls), "Pass"),
		}, true
	}
	// 2.对每条sql进行分析，拆出对应的库，表，列
	res, err := selfService.checkSqlPermission(ctx, ticket, parseStruct)
	if err != nil {
		resp.CheckItems[1].Memo = err.Error()
		resp.CheckItems[1].Status = model.PreCheckStatus_Error
		return res, false
	}
	for _, val := range res.checkResult {
		if val != "Pass" {
			resp.CheckItems[1].Status = model.PreCheckStatus_Error
			resp.CheckItems[1].Memo = "check permission failed"
			return res, false
		}
	}
	resp.CheckItems[1].Memo = "Pass"
	resp.CheckItems[1].Status = model.PreCheckStatus_Pass
	return res, true
}

func (selfService *ticketService) checkInstancePrivilege(instanceID string, instancePrivilege *[]*dao.UserInstancePrivilege) bool {
	return fp.StreamOf(*instancePrivilege).Filter(func(instancePrivilege *dao.UserInstancePrivilege) bool {
		return instancePrivilege.InstanceId == instanceID
	}).Exists()
}

func (selfService *ticketService) getUserAllInstancePrivilege(ctx context.Context, userId string, instanceId string, tenantId string, privilegeType string) (*UserPrivilege, error) {
	allUserPrivilege := &UserPrivilege{}
	// instance
	instancePrivilege, err := selfService.workflowDal.GetUserInstancePrivilege(ctx, userId, instanceId, tenantId, privilegeType)
	if err != nil {
		return nil, err
	}
	allUserPrivilege.instancePrivilege = instancePrivilege
	// database
	databasePrivilege, err := selfService.workflowDal.GetUserDatabasePrivilege(ctx, userId, instanceId, tenantId, privilegeType)
	if err != nil {
		return nil, err
	}
	allUserPrivilege.databasePrivilege = databasePrivilege
	// table
	tablePrivilege, err := selfService.workflowDal.GetUserTablePrivilege(ctx, userId, instanceId, tenantId, privilegeType)
	if err != nil {
		return nil, err
	}
	allUserPrivilege.tablePrivilege = tablePrivilege
	// column
	columnPrivilege, err := selfService.workflowDal.GetUserColumnPrivilege(ctx, userId, instanceId, tenantId, privilegeType)
	if err != nil {
		return nil, err
	}
	allUserPrivilege.columnPrivilege = columnPrivilege
	return allUserPrivilege, nil
}

func (selfService *ticketService) checkSqlPermission(ctx context.Context, ticket *shared.Ticket, parseStruct *TicketSqlTextParseResult) (*PrecheckTicketPermissionResult, error) {
	switch ticket.InstanceType {
	case shared.MySQL, shared.VeDBMySQL, shared.MySQLSharding, shared.MetaMySQL:
		return selfService.checkNormalSQLPermission(ctx, ticket, parseStruct)
	case shared.Postgres:
		return selfService.checkPgSQLPermission(ctx, ticket, parseStruct)
	}
	return &PrecheckTicketPermissionResult{
		checkResult: GetSliceAllSameString(len(parseStruct.sqls), "unknown ds type"),
	}, nil
}

func (selfService *ticketService) checkUpdateStmt(stmt *ast.UpdateStmt, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	tables := selfService.getTables(stmt)
	for _, table := range tables {
		//// 如果sql存在库名且库名和执行db不一致，则不允许执行
		//if table.Schema.String() != "" && table.Schema.String() != executeDbName {
		//	return fmt.Errorf("exec db:%s, sql db:%s inconsistent，execution not allowed", executeDbName, table.Schema.String())
		//}
		// err为空,说明有实例、库、或者表的权限,直接跳过;不为nil,才去检查列的权限
		if err := selfService.checkTablePermission(table, allUserPrivilege, executeDbName, instanceId); err == nil {
			continue
		}
		if err := selfService.checkColumnPrivilege(executeDbName, table.Name.String(), stmt.List, allUserPrivilege.columnPrivilege); err != nil {
			return err
		}
	}
	return nil
}

func (selfService *ticketService) checkDeleteStmt(stmt *ast.DeleteStmt, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	//table := stmt.TableRefs.TableRefs.Left.(*ast.TableSource).Source.(*ast.TableName)
	tables := selfService.getTables(stmt)
	for _, table := range tables {
		if err := selfService.checkTablePermission(table, allUserPrivilege, executeDbName, instanceId); err != nil {
			return err
		}
	}
	return nil
}

func (selfService *ticketService) checkInsertStmt(stmt *ast.InsertStmt, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	//table := stmt.Table.TableRefs.Left.(*ast.TableSource).Source.(*ast.TableName)
	tables := selfService.getTables(stmt)
	for _, table := range tables {
		if err := selfService.checkTablePermission(table, allUserPrivilege, executeDbName, instanceId); err != nil {
			return err
		}
	}
	return nil
}

func (selfService *ticketService) checkAlterTableStmt(stmt *ast.AlterTableStmt, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	tables := selfService.getTables(stmt)
	for _, table := range tables {
		if err := selfService.checkTablePermission(table, allUserPrivilege, executeDbName, instanceId); err != nil {
			return err
		}
	}
	return nil
}

func (selfService *ticketService) checkCreateTableStmt(stmt *ast.CreateTableStmt, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	tables := selfService.getTables(stmt)
	for _, table := range tables {
		if err := selfService.checkTablePermission(table, allUserPrivilege, executeDbName, instanceId); err != nil {
			return err
		}
	}
	return nil
}

func (selfService *ticketService) checkDropTableStmt(stmt *ast.DropTableStmt, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	tables := selfService.getTables(stmt)
	for _, table := range tables {
		if err := selfService.checkTablePermission(table, allUserPrivilege, executeDbName, instanceId); err != nil {
			return err
		}
	}
	return nil
}

func (selfService *ticketService) checkTruncateTableStmt(stmt *ast.TruncateTableStmt, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	tables := selfService.getTables(stmt)
	for _, table := range tables {
		if err := selfService.checkTablePermission(table, allUserPrivilege, executeDbName, instanceId); err != nil {
			return err
		}
	}
	return nil
}

func (selfService *ticketService) checkRepairTableStmt(stmt *ast.RepairTableStmt, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	tables := selfService.getTables(stmt)
	for _, table := range tables {
		if err := selfService.checkTablePermission(table, allUserPrivilege, executeDbName, instanceId); err != nil {
			return err
		}
	}
	return nil
}

func (selfService *ticketService) checkRenameTableStmt(stmt *ast.RenameTableStmt, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	tables := selfService.getTables(stmt)
	for _, table := range tables {
		if err := selfService.checkTablePermission(table, allUserPrivilege, executeDbName, instanceId); err != nil {
			return err
		}
	}
	return nil
}

func (selfService *ticketService) checkCreateIndexStmt(stmt *ast.CreateIndexStmt, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	tables := selfService.getTables(stmt)
	for _, table := range tables {
		if err := selfService.checkTablePermission(table, allUserPrivilege, executeDbName, instanceId); err != nil {
			return err
		}
	}
	return nil
}

func (selfService *ticketService) checkDropIndexStmt(stmt *ast.DropIndexStmt, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	tables := selfService.getTables(stmt)
	for _, table := range tables {
		if err := selfService.checkTablePermission(table, allUserPrivilege, executeDbName, instanceId); err != nil {
			return err
		}
	}
	return nil
}

func (selfService *ticketService) checkCreateDatabaseStmt(stmt *ast.CreateDatabaseStmt, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	if err := selfService.checkDatabasePermission(stmt.Name, allUserPrivilege, executeDbName, instanceId); err != nil {
		return err
	}
	return nil
}

func (selfService *ticketService) checkDropDatabaseStmt(stmt *ast.DropDatabaseStmt, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	if err := selfService.checkDatabasePermission(stmt.Name, allUserPrivilege, executeDbName, instanceId); err != nil {
		return err
	}
	return nil
}

func (selfService *ticketService) checkAlterDatabaseStmt(stmt *ast.AlterDatabaseStmt, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	if err := selfService.checkDatabasePermission(stmt.Name, allUserPrivilege, executeDbName, instanceId); err != nil {
		return err
	}
	return nil
}

type PrecheckTicketExplainResult struct {
	checkResult []string
	sqls        []string
}

type PrecheckTicketSytaxResult struct {
	checkResult []string
	sqls        []string
}

type PrecheckTicketPermissionResult struct {
	checkResult []string
	sqls        []string
}

// ExplainCommand explain多条命令
func (selfService *ticketService) explainCommands(ctx context.Context, resp *model.PreCheckTicketResp, sqlList *[]string,
	ds *shared.DataSource) (*PrecheckTicketExplainResult, error) {
	log.Info(ctx, "ticket: explain sqlList is %v", *sqlList)
	var result = &PrecheckTicketExplainResult{
		checkResult: make([]string, len(*sqlList)),
	}
	//FixME 对于Pg,先不考虑explain,因为pg的explain解析的时候,有些问题
	if ds.Type == shared.Postgres {
		resp.CheckItems[2].Status = model.PreCheckStatus_Pass
		resp.CheckItems[2].Memo = DefaultMemoForExplain
		for idx, _ := range *sqlList {
			result.checkResult[idx] = DefaultMemoForExplain
		}
		return result, nil
	}
	var (
		affectRows int64 // explain的总影响行数
		explainErr error
	)

	for idx, command := range *sqlList {
		explainRes, err := selfService.ExplainCommand(ctx, ds, command)
		if err != nil {
			log.Warn(ctx, "explain command %s error:%s ", command, err.Error())
			// 如果这一条explain解析失败,记录失败的原因,标记explain失败,开始解析下一条
			result.checkResult[idx] = err.Error()
			explainErr = err
			continue
		}
		var rowsAffect int64 = 0 // 一个语句可能explain里面包含多条记录,所以需要累加
		for _, val := range explainRes.Command {
			rowsAffect = rowsAffect + dslibutils.MustStrToInt64(dealRows(val.Rows))
		}
		result.checkResult[idx] = conv.Int64ToStr(rowsAffect)
		affectRows += rowsAffect
	}
	if explainErr != nil {
		resp.CheckItems[2].Status = model.PreCheckStatus_Error
		resp.CheckItems[2].Memo = DefaultMemoForExplain
		return result, explainErr
	}
	resp.CheckItems[2].Status = model.PreCheckStatus_Pass
	resp.CheckItems[2].Memo = strconv.FormatInt(affectRows, 10)
	return result, nil
}

func (selfService *ticketService) CheckNormalSqlFormat(ctx context.Context, ticket *shared.Ticket, resp *model.PreCheckTicketResp, parseStruct *TicketSqlTextParseResult) bool {
	switch ticket.InstanceType {
	case shared.MySQL, shared.VeDBMySQL, shared.MySQLSharding, shared.MetaMySQL:
		return selfService.checkNormalSQLFormat(ctx, resp, parseStruct)
	case shared.Postgres:
		return selfService.checkPgNormalSQLFormat(ctx, ticket, resp, parseStruct)
	default:
		return false
	}
}

// CheckFreeLockDDLSqlFormat 无锁DDL的表结构变更
func (selfService *ticketService) CheckFreeLockDDLSqlFormat(ctx context.Context, ticket *shared.Ticket, resp *model.PreCheckTicketResp,
	parseStruct *TicketSqlTextParseResult) (*PrecheckTicketSytaxResult, bool) {
	switch ticket.InstanceType {
	case shared.MySQL, shared.VeDBMySQL, shared.MySQLSharding, shared.MetaMySQL:
		return selfService.checkFreeLockDDLSQLFormat(ctx, ticket, resp, parseStruct)
	case shared.Postgres: // Pg OnlineDDL语法解析暂时还没有
		return selfService.checkPgFreeLockDDLSQLFormat(ctx, ticket, resp, parseStruct)
	default:
		return &PrecheckTicketSytaxResult{
			checkResult: []string{
				"unknown instance type",
			},
		}, false
	}
}

// CheckFreeLockDMLSqlFormat 检查无锁DML语句的索引是否包含主键或者非空唯一索引
func (selfService *ticketService) CheckFreeLockDMLSqlFormat(ctx context.Context, ticket *shared.Ticket, resp *model.PreCheckTicketResp,
	parseStruct *TicketSqlTextParseResult) (*PrecheckTicketSytaxResult, bool) {
	switch ticket.InstanceType {
	case shared.MySQL, shared.VeDBMySQL, shared.MetaMySQL:
		return selfService.checkFreeLockDMLSQLFormat(ctx, ticket, resp, parseStruct)
	case shared.Postgres: // Pg语法解析单独写一份
		return selfService.checkPgFreeLockDMLSQLFormat(ctx, ticket, resp, parseStruct)
	case shared.MySQLSharding:
		_, isSuccess := selfService.CheckShardingFreeLockDMLSQLFormat(ctx, ticket, resp)
		return &PrecheckTicketSytaxResult{
			checkResult: []string{
				"",
			},
		}, isSuccess
	default:
		return &PrecheckTicketSytaxResult{
			checkResult: []string{
				"Unknown instance type",
			},
		}, false
	}
}

/* generateCountSQL 生成计算影响行数的SQL
* 1、对于create语句我们不计算它的影响行数
* 2、对于alter table、drop table、truncate等语句,我们计算一下当前目标的表里面保存的行数
 */
func (selfService *ticketService) generateCountSQL(ctx context.Context, ticket *shared.Ticket, parseStruct *TicketSqlTextParseResult) []string {
	switch ticket.InstanceType {
	case shared.MySQL, shared.VeDBMySQL, shared.MySQLSharding, shared.MetaMySQL:
		return selfService.getCountSQL(ctx, ticket, parseStruct)
	case shared.Postgres: // Pg语法解析单独写一份
		return selfService.getPgCountSQL(ctx, ticket, parseStruct)
	default:
		return []string{}
	}

}

func (selfService *ticketService) CheckNormalDDLSqlFormat(ctx context.Context, ticket *shared.Ticket, resp *model.PreCheckTicketResp) bool {
	// 0、解析SQL
	p := parser.New()
	stmts, _, err := p.Parse(ticket.SqlText, "utf8", "")
	if err != nil || len(stmts) == 0 {
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = fmt.Sprintf("SQL statement error or unsupported, reason: %v", err)
		return false
	}

	//1、判断是否多条语句
	if len(stmts) > 1 {
		log.Warn(ctx, "SQL format is illegal，normal DDL ticket only support single SQL")
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = "SQL format is illegal，normal DDL  ticket only support single SQL"
		return false
	}

	// 2、判断是否是合法语句
	nodeType := reflect.TypeOf(stmts[0])
	nodeTypeStrs := strings.Split(nodeType.String(), ".")
	var nodeTypeStr = nodeType.String()
	if len(nodeTypeStrs) > 1 {
		nodeTypeStr = nodeTypeStrs[len(nodeTypeStrs)-1]
	}
	log.Info(ctx, "nodeType is %v,nodeTypeStrs is %v,nodeTypeStr is %v", nodeType, nodeTypeStrs, nodeTypeStr)

	if _, exist := EnabledNormalDDLCommandsType[nodeTypeStr]; !exist {
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = fmt.Sprintf("free lock DML ticket not support %s sql type", nodeTypeStr)
		return false
	}

	resp.CheckItems[0].Status = model.PreCheckStatus_Pass
	resp.CheckItems[0].Memo = "Pass"
	return true
}

// 获取预检查是否允许提交工单的flag
func (selfService *ticketService) getPreCheckEnableSubmitFlag(ctx context.Context, ticketId int64, isUpperAccount bool) (ret *model.PreCheckTicketResp, err error) {
	log.Info(ctx, "ticket: %d is not need check", ticketId)
	result, err := selfService.getPreCheckResult(ctx, ticketId)
	if err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	log.Info(ctx, "ticket %v not need check,result is %v", ticketId, utils.Show(result))
	for _, val := range result.CheckItems {
		// 语法报错(我们能识别到的)和Explain报错,所有用户都不允许提交
		if val.ItemType == model.ItemType_PreCheckSyntax && val.Status == model.PreCheckStatus_Error {
			log.Warn(ctx, "precheck syntax error,ticket is %vdescribe precheck detail for ticket", ticketId)
			result.EnableSubmitTicket = false
			return result, nil
		}
		if val.ItemType == model.ItemType_PreCheckExplain && val.Status == model.PreCheckStatus_Error {
			log.Warn(ctx, "precheck explain error,ticket is %v", ticketId)
			result.EnableSubmitTicket = false
			return result, nil
		}
		// 子用户权限报错,不允许提交
		if val.ItemType == model.ItemType_PreCheckPermission && val.Status == model.PreCheckStatus_Error && !isUpperAccount {
			log.Warn(ctx, "precheck permission error,ticket is %v", ticketId)
			result.EnableSubmitTicket = false
			return result, nil
		}
		// 子用户有高危安全规则报错,不允许提交
		if val.ItemType == model.ItemType_PreCheckSecurityRule && val.Status == model.PreCheckStatus_Error && !isUpperAccount {
			res := strings.Split(val.Memo, " ")
			if len(res) == 6 && dslibutils.MustStrToInt64(res[1]) > 0 {
				log.Warn(ctx, "precheck security rule error,ticket is %v", ticketId)
				result.EnableSubmitTicket = false
				return result, nil
			}
		}
	}
	result.EnableSubmitTicket = true
	// 如果已经允许提交了,那么就把预检查的状态改为已完成
	result.PrecheckFinished = true
	return result, nil
}
