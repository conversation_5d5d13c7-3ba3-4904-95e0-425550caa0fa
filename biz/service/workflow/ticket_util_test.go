package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"testing"
)

func Test_ChangeTicketType(t *testing.T) {
	tests := []struct {
		name string
		para *dao.Ticket
		want *shared.Ticket
	}{
		{
			name: "test_0",
			para: &dao.Ticket{TicketId: 0},
			want: &shared.Ticket{TicketId: 0},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ChangeTicketType(tt.para)
			if got.TicketId != tt.want.TicketId {
				t.<PERSON>("ChangeTicketType() got err %v", got)
			}
		})
	}
}

func Test_formatActionRecordBody(t *testing.T) {
	tests := []struct {
		name      string
		action    int
		step      int
		totalStep int
		comment   string
	}{
		{
			name:      "test_0",
			action:    0,
			step:      0,
			totalStep: 0,
		},
		{
			name:      "test_1",
			action:    1,
			step:      0,
			totalStep: 1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := formatActionRecordBody(RequestRecord{
				action:    tt.action,
				step:      tt.step,
				totalStep: tt.totalStep,
			})
			if tt.action == reject {
				if got["op_key"] != "reject" {
					t.Errorf("formatActionRecordBody() got %v, want %v", got["op_key"], "reject")
				}
			}
			if tt.action == pass {
				if got["op_key"] != "next" {
					t.Errorf("formatActionRecordBody() got %v, want %v", got["op_key"], "next")
				}
			}

		})
	}
}

func Test_formatPassRecordBody(t *testing.T) {
	tests := []struct {
		name      string
		step      int
		totalStep int
		approval  string
	}{
		{
			name:      "test_0",
			step:      0,
			totalStep: 0,
			approval:  "",
		},
		{
			name:      "test_1",
			step:      0,
			totalStep: 1,
			approval:  "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := formatPassRecordBody(tt.step, tt.totalStep, tt.approval)
			if got["op_key"] != "next" {
				t.Errorf("formatPassRecordBody() got %v, want %v", got["status"], "next")
			}
		})
	}
}

func Test_formatRejectRecordBody(t *testing.T) {
	tests := []struct {
		name    string
		comment string
		want    map[string]string
	}{
		{
			name:    "test_0",
			comment: "test_0",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := formatRejectRecordBody(tt.comment)
			if got["status"] != "reject" {
				t.Errorf("formatRejectRecordBody() got %v, want %v", got["status"], "reject")
			}
		})
	}
}

func Test_formatCreateRecordBody(t *testing.T) {
	tests := []struct {
		name string
		i    int64
		want map[string]interface{}
	}{
		{
			name: "test_0",
			i:    int64(0),
			want: map[string]interface{}{
				"workflow_config_id": int64(0),
				"config": map[string]interface{}{
					"approval_assignee":  "<EMAIL>",
					"approval2_assignee": "<EMAIL>",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := formatCreateRecordBody(tt.i)
			if got["workflow_config_id"].(int64) != tt.want["workflow_config_id"].(int64) {
				t.Errorf("formatCreateRecordBody() got %v, want %v", got["workflow_config_id"], tt.want["workflow_config_id"])
			}
		})
	}
}

func TestArrToMap(t *testing.T) {
	a := []string{
		"I", "am", "a", "test",
	}
	res := arrToMap(&a)
	if res == nil {
		t.Fatal("error")
	}

	res1 := arrToMap(nil)
	if res1 == nil {
		t.Fatal("error")
	}

}

func TestDealRows(t *testing.T) {
	a := "I am a test"
	b := ""
	resa := dealRows(a)
	if resa != "I am a test" {
		t.Fatal("error")
	}
	resb := dealRows(b)
	if resb != "0" {
		t.Fatal("error")
	}
}

func TestGenerateExecWindow(t *testing.T) {
	res := GenerateExecWindow(1, 10)
	if res == "" {
		t.Fatal("error")
	}
}

func TestIsFinished(t *testing.T) {
	if isFinished("test") != false {
		t.Fatal("error")
	}
}

func TestGetIndexInfoRespSharedToDatasource(t *testing.T) {
	GetIndexInfoRespSharedToDatasource(&shared.GetIndexInfoResp{
		TableIndexInfo: []*shared.TableIndexInfo{
			{
				TableName: "test",
			},
		},
	})
}

func TestGetIndexValueRespSharedToDatasource(t *testing.T) {
	GetIndexValueRespSharedToDatasource(&shared.GetIndexValueResp{
		TableIndexValue: []*shared.TableIndexValue{
			{
				IndexValue: "test",
			},
		},
	})
}

func TestIsEnableNormalSQLChangeTicket(t *testing.T) {
	IsEnableNormalSQLChangeTicket(shared.MySQL)
}
func TestIsEnableFreeLockSQLChangeTicket(t *testing.T) {
	IsEnableFreeLockSQLChangeTicket(shared.MySQL)
}
func TestIsEnableOnlineDDLTicket(t *testing.T) {
	IsEnableOnlineDDLTicket(shared.MySQL)
}

func TestGetJwtTokenForMultiCloud_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("TestGetJwtTokenForMultiCloud", t, func() {
		mockey.PatchConvey("成功场景 - 从 X-Jwt-Token 获取token", func() {
			// 场景描述：
			// 当上下文的extra中存在 "X-Jwt-Token" 时，函数应正确提取并返回该token。
			// 数据构造：
			// - context.Context: 一个标准的上下文。
			// - extra: 一个包含 "X-Jwt-Token" 键值对的 map。
			// 逻辑链路：
			// 1. Mock fwctx.GetExtra 返回一个包含 "X-Jwt-Token": "test_token" 的 map。
			// 2. Mock log.Info 以避免在测试中打印日志。
			// 3. 调用 GetJwtTokenForMultiCloud。
			// 4. 断言返回的 token 为 "test_token"。

			// Mock
			mockey.Mock(fwctx.GetExtra).Return(map[string]string{
				"X-Jwt-Token": "test_token",
			}).Build()
			mockey.Mock(log.Info).Return().Build()

			// 调用
			token := GetJwtTokenForMultiCloud(context.Background())

			// 断言
			convey.So(token, convey.ShouldEqual, "test_token")
		})

		mockey.PatchConvey("成功场景 - 从 X-Jwt-Token 获取多个token中的第一个", func() {
			// 场景描述：
			// 当 "X-Jwt-Token" 的值包含多个由逗号分隔的token时，函数应只返回第一个token。
			// 数据构造：
			// - context.Context: 一个标准的上下文。
			// - extra: 一个 map，其 "X-Jwt-Token" 的值为 "token1,token2,token3"。
			// 逻辑链路：
			// 1. Mock fwctx.GetExtra 返回一个包含 "X-Jwt-Token": "token1,token2,token3" 的 map。
			// 2. Mock log.Info 以避免在测试中打印日志。
			// 3. 调用 GetJwtTokenForMultiCloud。
			// 4. 断言返回的 token 为 "token1"。

			// Mock
			mockey.Mock(fwctx.GetExtra).Return(map[string]string{
				"X-Jwt-Token": "token1,token2,token3",
			}).Build()
			mockey.Mock(log.Info).Return().Build()

			// 调用
			token := GetJwtTokenForMultiCloud(context.Background())

			// 断言
			convey.So(token, convey.ShouldEqual, "token1")
		})

		mockey.PatchConvey("成功场景 - X-Jwt-Token不存在, 从 X-Jwt-Dbw 获取token", func() {
			// 场景描述：
			// 当上下文的extra中不存在 "X-Jwt-Token" 但存在 "X-Jwt-Dbw" 时，函数应正确提取并返回 "X-Jwt-Dbw" 的token。
			// 数据构造：
			// - context.Context: 一个标准的上下文。
			// - extra: 一个不包含 "X-Jwt-Token" 但包含 "X-Jwt-Dbw" 键值对的 map。
			// 逻辑链路：
			// 1. Mock fwctx.GetExtra 返回一个包含 "X-Jwt-Dbw": "test_dbw_token" 的 map。
			// 2. Mock log.Info 以避免在测试中打印日志。
			// 3. 调用 GetJwtTokenForMultiCloud。
			// 4. 断言返回的 token 为 "test_dbw_token"。

			// Mock
			mockey.Mock(fwctx.GetExtra).Return(map[string]string{
				"X-Jwt-Dbw": "test_dbw_token",
			}).Build()
			mockey.Mock(log.Info).Return().Build()

			// 调用
			token := GetJwtTokenForMultiCloud(context.Background())

			// 断言
			convey.So(token, convey.ShouldEqual, "test_dbw_token")
		})

		mockey.PatchConvey("成功场景 - X-Jwt-Token不存在, 从 X-Jwt-Dbw 获取多个token中的第一个", func() {
			// 场景描述：
			// 当 "X-Jwt-Token" 不存在，且 "X-Jwt-Dbw" 的值包含多个由逗号分隔的token时，函数应只返回 "X-Jwt-Dbw" 的第一个token。
			// 数据构造：
			// - context.Context: 一个标准的上下文。
			// - extra: 一个 map，其 "X-Jwt-Dbw" 的值为 "dbw_token1,dbw_token2"。
			// 逻辑链路：
			// 1. Mock fwctx.GetExtra 返回一个包含 "X-Jwt-Dbw": "dbw_token1,dbw_token2" 的 map。
			// 2. Mock log.Info 以避免在测试中打印日志。
			// 3. 调用 GetJwtTokenForMultiCloud。
			// 4. 断言返回的 token 为 "dbw_token1"。

			// Mock
			mockey.Mock(fwctx.GetExtra).Return(map[string]string{
				"X-Jwt-Dbw": "dbw_token1,dbw_token2",
			}).Build()
			mockey.Mock(log.Info).Return().Build()

			// 调用
			token := GetJwtTokenForMultiCloud(context.Background())

			// 断言
			convey.So(token, convey.ShouldEqual, "dbw_token1")
		})

		mockey.PatchConvey("成功场景 - 同时存在 X-Jwt-Token 和 X-Jwt-Dbw, 优先使用 X-Jwt-Token", func() {
			// 场景描述：
			// 当上下文的extra中同时存在 "X-Jwt-Token" 和 "X-Jwt-Dbw" 时，函数应优先提取并返回 "X-Jwt-Token" 的值。
			// 数据构造：
			// - context.Context: 一个标准的上下文。
			// - extra: 一个同时包含 "X-Jwt-Token" 和 "X-Jwt-Dbw" 键值对的 map。
			// 逻辑链路：
			// 1. Mock fwctx.GetExtra 返回一个包含 "X-Jwt-Token": "priority_token" 和 "X-Jwt-Dbw": "other_token" 的 map。
			// 2. Mock log.Info 以避免在测试中打印日志。
			// 3. 调用 GetJwtTokenForMultiCloud。
			// 4. 断言返回的 token 为 "priority_token"。

			// Mock
			mockey.Mock(fwctx.GetExtra).Return(map[string]string{
				"X-Jwt-Token": "priority_token",
				"X-Jwt-Dbw":   "other_token",
			}).Build()
			mockey.Mock(log.Info).Return().Build()

			// 调用
			token := GetJwtTokenForMultiCloud(context.Background())

			// 断言
			convey.So(token, convey.ShouldEqual, "priority_token")
		})

		mockey.PatchConvey("失败场景 - X-Jwt-Token 和 X-Jwt-Dbw 都不存在", func() {
			// 场景描述：
			// 当上下文的extra中既不存在 "X-Jwt-Token" 也不存在 "X-Jwt-Dbw" 时，函数应返回空字符串。
			// 数据构造：
			// - context.Context: 一个标准的上下文。
			// - extra: 一个空的 map。
			// 逻辑链路：
			// 1. Mock fwctx.GetExtra 返回一个空的 map。
			// 2. Mock log.Info 以避免在测试中打印日志。
			// 3. 调用 GetJwtTokenForMultiCloud。
			// 4. 断言返回的 token 为空字符串。

			// Mock
			mockey.Mock(fwctx.GetExtra).Return(map[string]string{}).Build()
			mockey.Mock(log.Info).Return().Build()

			// 调用
			token := GetJwtTokenForMultiCloud(context.Background())

			// 断言
			convey.So(token, convey.ShouldBeEmpty)
		})
	})
}
