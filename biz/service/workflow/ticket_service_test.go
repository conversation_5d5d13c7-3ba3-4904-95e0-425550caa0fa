package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/infrastructure/actors"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	config2 "code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/repository"
	utils001 "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	v2 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2022-01-01/kitex_gen/model/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/ds-sql-parser"
	"code.byted.org/infcs/ds-sql-parser/ast"
	"code.byted.org/infcs/protoactor-go/cluster"
	"context"
	"errors"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/go-playground/assert"
	"github.com/golang/mock/gomock"
	_ "github.com/pingcap/tidb/types/parser_driver"
	"github.com/smartystreets/goconvey/convey"
	"testing"
	"time"
)

func getService() ticketService {
	return ticketService{
		dbwInstance:       &mocks.MockDbwInstanceDAL{},
		dbwUser:           &mocks.MockDbwUserDAL{},
		workflowDal:       &mocks.MockWorkflowDAL{},
		preCheckDetailDal: &mocks.MockPreCheckDetailDAL{},
		idg:               idgen.New(actors.NewActorClient()),
		ds:                &mocks.MockDataSourceService{},
		actorClient:       &mocks.MockActorClient{},
		i18nSvc:           &mocks.MockI18nServiceInterface{},
		ps:                &mocks.MockCommandParser{},
		msgService:        &mocks.MockMsgService{},
		conf:              &mocks.MockConfigProvider{},
		c3ConfProvider:    &config2.MockC3ConfigProvider{},
		crossAuthSvc:      &mocks.MockCrossServiceAuthorizationService{},
		flowRepo:          &repository.MockApprovalFlowRepo{},
	}
}

func TestNewTicketService(t *testing.T) {
	NewTicketService(NewTicketServiceIn{})
}

func TestGetTicket(t *testing.T) {
	service := getService()
	mock := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{TicketId: 123}, nil).Build()
	defer mock.UnPatch()
	ticket, err := service.GetTicket(context.Background(), 123)
	if err != nil {
		t.Fatal("failed", err)
	}
	assert.Equal(t, ticket.TicketId, int64(123))
}

func TestGetTicketError(t *testing.T) {
	service := getService()
	mock := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(nil, fmt.Errorf("test")).Build()
	defer mock.UnPatch()
	_, err := service.GetTicket(context.Background(), 123)
	if err == nil {
		t.Fatal("failed", err)
	}
}

//func TestIsNeedCheck(t *testing.T) {
//	service := getService()
//	time.Sleep(10 * time.Millisecond)
//	mock := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{TicketId: 123, TicketStatus: TicketPreCheck}, nil).Build()
//	defer mock.UnPatch()
//	isNeedCheck, ticket, err := service.IsNeedCheck(context.Background(), 123)
//	if err != nil {
//		t.Fatal("failed", err)
//	}
//	assert.Equal(t, ticket.TicketId, int64(123))
//	assert.Equal(t, isNeedCheck, true)
//}

func TestIsNeedCheckError(t *testing.T) {
	service := getService()
	mock := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(nil, fmt.Errorf("test")).Build()
	defer mock.UnPatch()
	_, _, err := service.IsNeedCheck(context.Background(), 123)
	if err == nil {
		t.Fatal("failed", err)
	}
}

//func TestTurePreCheck(t *testing.T) {
//	service := getService()
//
//	ds := &shared.DataSource{}
//	mock1 := mockey.Mock((*ticketService).explainCommands).Return(true, nil).Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock((*ticketService).checkPermissions).Return(true, nil).Build()
//	defer mock2.UnPatch()
//	mock3 := mockey.Mock((*ticketService).SecurityRuleCheck).Return(true, nil).Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*ticketService).getDBDataSource).Return(ds, nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*mocks.MockCommandParser).Explain).Return(nil, fmt.Errorf("error")).Build()
//	resp := service.initCreateTicketResp(model.PreCheckStatus_Undo)
//	service.preCheck(context.Background(), &shared.Ticket{
//		InstanceType: shared.MySQL,
//		TicketType:   int32(model.TicketType_NormalSqlChange),
//		SqlText:      "update t_test set a = 1; delete from t_test where b  =1;"}, resp)
//	mock5.UnPatch()
//
//	mock6 := mockey.Mock((*mocks.MockCommandParser).Explain).Return([]*parser2.Command{
//		{}, {},
//	}, nil).Build()
//	mock7 := mockey.Mock((service).generateCountSQL).Return([]string{}, nil).Build()
//	service.preCheck(context.Background(), &shared.Ticket{
//		InstanceType: shared.MySQL,
//		TicketType:   int32(model.TicketType_NormalSqlChange),
//		SqlText:      "update t_test set a = 1; delete from t_test where b  =1;"}, resp)
//	defer mock6.UnPatch()
//	defer mock7.UnPatch()
//}

func TestChangeTicketStatus(t *testing.T) {
	service := getService()
	mock := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatus).Return(fmt.Errorf("test")).Build()
	defer mock.UnPatch()

	err := service.ChangeTicketStatus(context.Background(), 1, 1, "")
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestDescribeTickets(t *testing.T) {
	// TODO 过不了
}

func TestDescribeTicketDetail(t *testing.T) {
	// TODO 过不了
}

func TestModifyTicket(t *testing.T) {
	service := getService()

	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateById).Return(nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).IsInstanceAvailable).Return(true, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{
		TicketId:     1,
		FlowConfigId: 1,
		WorkflowId:   1,
		TicketType:   0,
		TicketStatus: 0,
		ExecuteType:  0,
	}, nil).Build()
	defer mock3.UnPatch()

	err := service.ModifyTicket(context.Background(), &model.ModifyTicketReq{TicketId: "1"})
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestModifyTicketError1(t *testing.T) {
	service := getService()

	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateById).Return(nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).IsInstanceAvailable).Return(true, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{
		TicketId:     1,
		FlowConfigId: 1,
		WorkflowId:   1,
		TicketType:   0,
		TicketStatus: 0,
		ExecuteType:  0,
	}, errors.New("no record")).Build()
	defer mock3.UnPatch()

	err := service.ModifyTicket(context.Background(), &model.ModifyTicketReq{TicketId: "1"})
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestModifyTicketError2(t *testing.T) {
	service := getService()

	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateById).Return(nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).IsInstanceAvailable).Return(false, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{
		TicketId:     1,
		FlowConfigId: 1,
		WorkflowId:   1,
		TicketType:   0,
		TicketStatus: 0,
		ExecuteType:  0,
	}, nil).Build()
	defer mock3.UnPatch()

	err := service.ModifyTicket(context.Background(), &model.ModifyTicketReq{TicketId: "1"})
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestModifyTicketFlow(t *testing.T) {
	service := getService()

	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).ModifyTicketFlow).Return(fmt.Errorf("test")).Build()
	defer mock1.UnPatch()

	err := service.ModifyTicketFlow(context.Background(), &dao.FlowInfo{TicketId: 3})
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestIsAutoExecute(t *testing.T) {
	service := getService()

	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).IsAutoExecute).Return(false, fmt.Errorf("test")).Build()
	defer mock1.UnPatch()

	_, err := service.IsAutoExecute(context.Background(), 1)
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestGetAndCheckTicketFlow(t *testing.T) {
	service := getService()

	mockFlow := dao.BpmFlowInfo{
		TicketId:       1,
		TenantId:       "1",
		CurrentUserIds: "1",
	}
	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).GetBpmWorkflow).Return(&mockFlow, nil).Build()
	defer mock1.UnPatch()

	_, err := service.GetAndCheckTicketFlow(context.Background(), 1, "1", "1")
	if err != nil {
		t.Fatal("failed", err)
	}
	mockFlow.TenantId = "2"
	_, err = service.GetAndCheckTicketFlow(context.Background(), 1, "1", "1")
	if err != nil {
		t.Fatal("failed", err)
	}
	mockFlow.TenantId = "1"
	mockFlow.CurrentUserIds = "12"
	_, err = service.GetAndCheckTicketFlow(context.Background(), 1, "1", "1")
	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestGetBpmWorkflow(t *testing.T) {
	service := getService()

	mockErr := fmt.Errorf("test")
	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).GetBpmWorkflow).Return(&dao.BpmFlowInfo{}, mockErr).Build()
	defer mock1.UnPatch()

	service.GetBpmWorkflow(context.Background(), 3)
}

func TestExecuteTicket(t *testing.T) {
	service := getService()

	mock1 := mockey.Mock((*ticketService).GetTicket).Return(&shared.Ticket{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*ticketService).executeCommands).Return("", fmt.Errorf("test")).Build()
	defer mock2.UnPatch()

	_, err := service.ExecuteTicket(context.Background(), &model.ExecuteTicketReq{TicketId: "123"})
	if err == nil {
		t.Fatal("failed", err)
	}
	// assert.Equal(t, resp.AllPass, false)
}

func TestGetInstanceOwnerIds(t *testing.T) {
	service := getService()

	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).GetInstanceOwnerIds).Return("", fmt.Errorf("test")).Build()
	defer mock1.UnPatch()
	_, err := service.GetInstanceOwnerIds(context.Background(), "123", "instanceId")
	if err == nil {
		t.Fatal("failed", err)
	}

	mock1.UnPatch()
	time.Sleep(300 * time.Millisecond)
	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).GetInstanceOwnerIds).Return("", nil).Build()
	defer mock2.UnPatch()

	_, err = service.GetInstanceOwnerIds(context.Background(), "123", "instanceId")
	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestGetInstanceDbaIds(t *testing.T) {
	service := getService()

	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).GetInstanceDbaIds).Return("", fmt.Errorf("test")).Build()
	defer mock1.UnPatch()
	_, err := service.GetInstanceDbaIds(context.Background(), "123", "instanceId")
	if err == nil {
		t.Fatal("failed", err)
	}

	mock1.UnPatch()
	time.Sleep(300 * time.Millisecond)
	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).GetInstanceDbaIds).Return("", nil).Build()
	defer mock2.UnPatch()

	_, err = service.GetInstanceDbaIds(context.Background(), "123", "instanceId")
	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestGetTenantAdminIds(t *testing.T) {
	service := getService()

	mockRes := []string{"user1"}
	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).GetTenantAdminIds).Return(&mockRes, fmt.Errorf("test")).Build()
	_, err := service.GetTenantAdminIds(context.Background(), "123")
	if err == nil {
		t.Fatal("failed", err)
	}

	mock1.UnPatch()
	time.Sleep(300 * time.Millisecond)
	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).GetTenantAdminIds).Return(&mockRes, nil).Build()
	defer mock2.UnPatch()

	res, err := service.GetTenantAdminIds(context.Background(), "123")
	if err != nil {
		t.Fatal("failed", err)
	}
	assert.Equal(t, len(*res), 1)
}

func TestGetUserNameByIds(t *testing.T) {
	service := getService()

	var mockRes []*dao.TicketUserInfo
	info := &dao.TicketUserInfo{
		UserId:   "123",
		UserName: "cx",
	}
	mockRes = append(mockRes, info)

	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).GetUserNameByIds).Return(&mockRes, fmt.Errorf("test")).Build()

	_, err := service.GetUserNameByIds(context.Background(), "123", "123")
	if err == nil {
		t.Fatal("failed", err)
	}
	mock1.UnPatch()
	time.Sleep(300 * time.Millisecond)

	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).GetUserNameByIds).Return(&mockRes, nil).Build()
	defer mock2.UnPatch()

	res, err := service.GetUserNameByIds(context.Background(), "123", "123")
	if err != nil {
		t.Fatal("failed", err)
	}
	mock1.UnPatch()
	assert.Equal(t, len(*res), 1)
}

//func TestPreCheck(t *testing.T) {
//	service := getService()
//
//	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{TicketId: 123, TicketStatus: TicketPreCheck}, nil).Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock((*ticketService).ChangeTicketStatus).Return(nil).Build()
//	defer mock2.UnPatch()
//	mock3 := mockey.Mock((*ticketService).preCheck).Return(nil).Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*ticketService).UpdatePreCheckResult).Return(nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*ticketService).ChangePreCheckTicketStatusAndOperator).Return(nil).Build()
//	defer mock5.UnPatch()
//	msgMock := mockey.Mock((*mocks.MockMsgService).SendMessageByEvent).Return(nil).Build()
//	defer msgMock.UnPatch()
//
//	mock6 := mockey.Mock((*ticketService).IsInstanceAvailable).Return(true, nil).Build()
//	mock61 := mockey.Mock((*ticketService).IsInstanceRunning).Return(true).Build()
//	defer mock61.UnPatch()
//	i18nMock := mockey.Mock((*mocks.MockI18nServiceInterface).GetLanguage).Return("VolcanoEngine").Build()
//	defer i18nMock.UnPatch()
//	res, err := service.PreCheck(context.Background(), &model.PreCheckTicketReq{TicketId: "123"})
//	if err != nil {
//		t.Fatal("failed", err)
//	}
//	assert.Equal(t, res.AllPass, false)
//
//	mock6.UnPatch()
//	time.Sleep(300 * time.Millisecond)
//	mock7 := mockey.Mock((*ticketService).IsInstanceAvailable).Return(false, nil).Build()
//	defer mock7.UnPatch()
//	_, err = service.PreCheck(context.Background(), &model.PreCheckTicketReq{TicketId: "123"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//}

//func TestGetNextUserIdInfo(t *testing.T) {
//	service := getService()
//
//	mock1 := mockey.Mock((*ticketService).getInstanceOwnerRole).Return(&dao.UserRole{Id: "1", Role: dao.OwnerUser}, nil).Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock((*ticketService).getInstanceDbaRole).Return(&dao.UserRole{Id: "2", Role: dao.DbaUser}, nil).Build()
//	defer mock2.UnPatch()
//	mock3 := mockey.Mock((*ticketService).getManagerRole).Return(&dao.UserRole{Id: "3", Role: dao.AdminUser}, nil).Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*ticketService).getUserRole).Return(dao.NormalUser, nil).Build()
//	defer mock4.UnPatch()
//
//	mockFlowInfo := &dao.BpmFlowInfo{
//		CreateUserId: "xx",
//		InstanceId:   "xxx",
//		TenantId:     "xxx",
//		FlowStep:     0,
//	}
//	user, err := service.GetNextUserIdInfo(context.Background(), mockFlowInfo)
//	if err != nil {
//		t.Fatal("failed", err)
//	}
//	assert.Equal(t, user.Role, dao.OwnerUser)
//
//	mockFlowInfo.FlowStep++
//	user, err = service.GetNextUserIdInfo(context.Background(), mockFlowInfo)
//	if err != nil {
//		t.Fatal("failed", err)
//	}
//	assert.Equal(t, user.Role, dao.DbaUser)
//
//	mockFlowInfo.FlowStep++
//	user, err = service.GetNextUserIdInfo(context.Background(), mockFlowInfo)
//	if err != nil {
//		t.Fatal("failed", err)
//	}
//	assert.Equal(t, user.Role, dao.AdminUser)
//
//	mockFlowInfo.FlowStep++
//	user, err = service.GetNextUserIdInfo(context.Background(), mockFlowInfo)
//	if err != nil {
//		t.Fatal("failed", err)
//	}
//	assert.Equal(t, user.Role, dao.NormalUser)
//}

func TestIsUserExists(t *testing.T) {
	service := getService()

	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).IsUserExists).Return(true, nil).Build()
	defer mock1.UnPatch()

	_, err := service.IsUserExists(context.Background(), "1", "1")
	if err != nil {
		t.Fatal("failed", err)
	}
}

func TestGetPreCheckResult(t *testing.T) {
	service := getService()

	mockRes := []*dao.TicketPreCheckResult{{TicketId: 123, Item: PreCheckSyntax, Status: 1}}
	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).GetPreCheckResult).Return(mockRes, nil).Build()
	defer mock1.UnPatch()
	i18nMock := mockey.Mock((*mocks.MockI18nServiceInterface).GetLanguage).Return("VolcanoEngine").Build()
	defer i18nMock.UnPatch()

	resp, err := service.getPreCheckResult(context.Background(), 123)
	if err != nil {
		t.Fatal("failed", err)
	}
	assert.Equal(t, resp.AllPass, true)
	assert.Equal(t, resp.CheckItems[0].Item, PreCheckSyntaxCn)
}

func TestGetUserRole(t *testing.T) {
	service := getService()

	mockRes := []string{"user1"}
	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).GetUserRoles).Return(&mockRes, fmt.Errorf("test")).Build()
	_, err := service.getUserRole(context.Background(), "123", "123", "instanceId")
	if err == nil {
		t.Fatal("failed", err)
	}

	mock1.UnPatch()
	time.Sleep(300 * time.Millisecond)
	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).GetUserRoles).Return(&mockRes, nil).Build()
	defer mock2.UnPatch()

	res, err := service.getUserRole(context.Background(), "123", "123", "instanceId")
	if err != nil {
		t.Fatal("failed", err)
	}
	assert.Equal(t, res, "user1")
}

//func TestChangePreCheckTicketStatusAndOperator(t *testing.T) {
//	service := getService()
//
//	mock1 := mockey.Mock((*ticketService).GetNextUserIdInfo).Return(&dao.UserRole{Id: "1", Role: dao.OwnerUser}, nil).Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).GetTicketStatus).Return(0, nil).Build()
//	defer mock2.UnPatch()
//	mock3 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatusAndOperator).Return(nil).Build()
//	msgMock := mockey.Mock((*mocks.MockMsgService).SendMessageByEvent).Return(nil).Build()
//	defer msgMock.UnPatch()
//	err := service.ChangePreCheckTicketStatusAndOperator(context.Background(), &shared.Ticket{TicketId: 1, WorkflowId: 1}, 1, &dao.BpmFlowInfo{})
//	if err != nil {
//		t.Fatal("failed", err)
//	}
//	mock3.UnPatch()
//	time.Sleep(300 * time.Millisecond)
//	mock4 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatusAndOperator).Return(fmt.Errorf("test")).Build()
//	defer mock4.UnPatch()
//	err = service.ChangePreCheckTicketStatusAndOperator(context.Background(), &shared.Ticket{TicketId: 1, WorkflowId: 1}, 1, &dao.BpmFlowInfo{})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//}

func TestCheckPermissions(t *testing.T) {
	service := getService()

	mock1 := mockey.Mock((*ticketService).getUserAllInstancePrivilege).Return(&UserPrivilege{}, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).IsUpperAccount).Return(true, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*ticketService).checkSqlPermission).Return(nil).Build()

	resp := service.initCreateTicketResp(model.PreCheckStatus_Undo)
	res, _, _ := parser.New().Parse("update t_test set a = 1", "", "")
	parserStruct := &TicketSqlTextParseResult{
		stmts:   res,
		pgStmts: nil,
		sqls:    []string{"update t_test set a = 1"},
	}
	service.checkPermissions(context.Background(), &shared.Ticket{SqlText: "update t_test set a = 1"}, resp, parserStruct)
	mock3.UnPatch()
	time.Sleep(300 * time.Millisecond)
	mock4 := mockey.Mock((*ticketService).checkSqlPermission).Return(fmt.Errorf("test")).Build()
	defer mock4.UnPatch()

	service.checkPermissions(context.Background(), &shared.Ticket{SqlText: "update t_test set a = 1"}, resp, parserStruct)
}

func TestGetUserAllInstancePrivilege(t *testing.T) {
	service := getService()

	var mockInstancePrivilege []*dao.UserInstancePrivilege
	mockInstancePrivilege = append(mockInstancePrivilege, &dao.UserInstancePrivilege{InstanceId: "InstanceId"})
	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).GetUserInstancePrivilege).Return(&mockInstancePrivilege, nil).Build()
	defer mock1.UnPatch()
	var mockDatabasePrivilege []*dao.UserDatabasePrivilege
	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).GetUserDatabasePrivilege).Return(&mockDatabasePrivilege, nil).Build()
	defer mock2.UnPatch()
	var mockTablePrivilege []*dao.UserTablePrivilege
	mock3 := mockey.Mock((*mocks.MockWorkflowDAL).GetUserTablePrivilege).Return(&mockTablePrivilege, nil).Build()
	defer mock3.UnPatch()
	var mockColumnPrivilege []*dao.UserColumnPrivilege
	mock4 := mockey.Mock((*mocks.MockWorkflowDAL).GetUserColumnPrivilege).Return(&mockColumnPrivilege, nil).Build()
	defer mock4.UnPatch()

	res, err := service.getUserAllInstancePrivilege(context.Background(), "1", "instanceId", "tenantId", model.DbwPrivilegeType_CORRECT.String())
	if err != nil {
		t.Fatal("failed", err)
	}
	assert.Equal(t, len(*res.instancePrivilege), 1)
}

func TestCheckOneSqlPermission(t *testing.T) {
	service := getService()
	ctx := context.Background()
	var mockInstancePrivilege []*dao.UserInstancePrivilege
	var mockDatabasePrivilege []*dao.UserDatabasePrivilege
	var mockTablePrivilege []*dao.UserTablePrivilege
	var mockColumnPrivilege []*dao.UserColumnPrivilege

	userPrivilege := &UserPrivilege{
		instancePrivilege: &mockInstancePrivilege,
		databasePrivilege: &mockDatabasePrivilege,
		tablePrivilege:    &mockTablePrivilege,
		columnPrivilege:   &mockColumnPrivilege,
	}
	res, _, _ := parser.New().Parse("update t_test set a = 1", "", "")
	parserStruct := &TicketSqlTextParseResult{
		stmts:   res,
		pgStmts: nil,
		sqls:    []string{"update t_test set a = 1"},
	}
	mock0 := mockey.Mock((*ticketService).getUserAllInstancePrivilege).Return(userPrivilege, nil).Build()
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "update t_test set a = 1",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	//assert.Equal(t, res, nil)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "delete from t_test",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	//assert.Equal(t, res, nil)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "insert into t_test values(1)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	//assert.Equal(t, res, nil)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "update db.t_test set a = 1",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	//assert.NotEqual(t, res, nil)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "delete from db.t_test",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	//assert.NotEqual(t, res, nil)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "insert into db.t_test values(1)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	//assert.NotEqual(t, res, nil)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "update t_test set a = 1",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	assert.NotEqual(t, res, nil)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "delete from t_test",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	assert.NotEqual(t, res, nil)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "insert into t_test values(1)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	assert.NotEqual(t, res, nil)

	*userPrivilege.instancePrivilege = append(*userPrivilege.instancePrivilege, &dao.UserInstancePrivilege{InstanceId: "instanceId"})
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "update t_test set a = 1",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "delete from t_test",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "insert into t_test values(1)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	*userPrivilege.instancePrivilege = (*userPrivilege.instancePrivilege)[0:0]

	*userPrivilege.databasePrivilege = append(*userPrivilege.databasePrivilege, &dao.UserDatabasePrivilege{DbName: "test"})
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "update t_test set a = 1",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "delete from t_test",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "insert into t_test values(1)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	*userPrivilege.databasePrivilege = (*userPrivilege.databasePrivilege)[0:0]

	*userPrivilege.tablePrivilege = append(*userPrivilege.tablePrivilege, &dao.UserTablePrivilege{DbName: "test", TbName: "t_test"})
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "update t_test set a = 1",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "delete from t_test",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "insert into t_test values(1)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	*userPrivilege.tablePrivilege = (*userPrivilege.tablePrivilege)[0:0]

	*userPrivilege.columnPrivilege = append(*userPrivilege.columnPrivilege, &dao.UserColumnPrivilege{DbName: "test", TbName: "t_test", ColumnName: "a"})
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "update t_test set a = 1",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)

	*userPrivilege.instancePrivilege = append(*userPrivilege.instancePrivilege, &dao.UserInstancePrivilege{InstanceId: "instanceId"})
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "create table t_test(a int)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "drop table t_test(a int)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "truncate table t_test",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "drop database a",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "create database a",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)

	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "rename table a.t to a.t1 ",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)

	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "repair table a ",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)

	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "create index on a (id)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)

	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "drop index idx_id on a ",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)

	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "Alter database a charset=utf8 ",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)

	*userPrivilege.instancePrivilege = (*userPrivilege.instancePrivilege)[0:0]

	*userPrivilege.databasePrivilege = append(*userPrivilege.databasePrivilege, &dao.UserDatabasePrivilege{DbName: "test"})
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "create table test(id int)  ",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	mock0.UnPatch()

	mock1 := mockey.Mock((*ticketService).getUserAllInstancePrivilege).Return(nil, fmt.Errorf("error")).Build()
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "update t_test set a = 1",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "delete from t_test",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "insert into t_test values(1)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "update db.t_test set a = 1",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "delete from db.t_test",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "insert into db.t_test values(1)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "update t_test set a = 1",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "delete from t_test",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "insert into t_test values(1)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)

	*userPrivilege.instancePrivilege = append(*userPrivilege.instancePrivilege, &dao.UserInstancePrivilege{InstanceId: "instanceId"})
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "update t_test set a = 1",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "delete from t_test",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "insert into t_test values(1)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	*userPrivilege.instancePrivilege = (*userPrivilege.instancePrivilege)[0:0]

	*userPrivilege.databasePrivilege = append(*userPrivilege.databasePrivilege, &dao.UserDatabasePrivilege{DbName: "test"})
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "update t_test set a = 1",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "delete from t_test",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "insert into t_test values(1)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	*userPrivilege.databasePrivilege = (*userPrivilege.databasePrivilege)[0:0]

	*userPrivilege.tablePrivilege = append(*userPrivilege.tablePrivilege, &dao.UserTablePrivilege{DbName: "test", TbName: "t_test"})
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "update t_test set a = 1",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "delete from t_test",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "insert into t_test values(1)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	*userPrivilege.tablePrivilege = (*userPrivilege.tablePrivilege)[0:0]

	*userPrivilege.columnPrivilege = append(*userPrivilege.columnPrivilege, &dao.UserColumnPrivilege{DbName: "test", TbName: "t_test", ColumnName: "a"})
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "update t_test set a = 1",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)

	*userPrivilege.instancePrivilege = append(*userPrivilege.instancePrivilege, &dao.UserInstancePrivilege{InstanceId: "instanceId"})
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "create table t_test(a int)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "drop table t_test(a int)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "truncate table t_test",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "drop database a",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "create database a",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)

	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "rename table a.t to a.t1 ",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)

	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "repair table a ",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)

	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "create index on a (id)",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)

	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "drop index idx_id on a ",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)

	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "Alter database a charset=utf8 ",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)

	*userPrivilege.instancePrivilege = (*userPrivilege.instancePrivilege)[0:0]

	*userPrivilege.databasePrivilege = append(*userPrivilege.databasePrivilege, &dao.UserDatabasePrivilege{DbName: "test"})
	service.checkSqlPermission(ctx,
		&shared.Ticket{
			SqlText:      "create table test(id int)  ",
			DbName:       "test",
			InstanceId:   "instanceId",
			InstanceType: shared.MySQL,
		}, parserStruct)
	mock1.UnPatch()

}

func TestCheckUserTenant(t *testing.T) {
	service := getService()
	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).IsUserInTenant).Return(false, fmt.Errorf("test")).Build()
	err := service.checkUserTenant(context.Background(), "1", 1)
	if err == nil {
		t.Fatal("failed", err)
	}
	mock1.UnPatch()
	time.Sleep(300 * time.Millisecond)
	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).IsUserInTenant).Return(false, nil).Build()
	defer mock2.UnPatch()
	if err == nil {
		t.Fatal("failed", err)
	}
}

func TestCheckTicketUserInfo(t *testing.T) {
	service := getService()
	ctx := context.Background()
	mock1 := mockey.Mock(fwctx.GetTenantID).Return("123").Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock(fwctx.GetUserID).Return("").Build()
	defer mock2.UnPatch()

	mock3 := mockey.Mock((*ticketService).IsUserExists).Return(false, fmt.Errorf("test")).Build()

	err := service.CheckTicketUserInfo(ctx, 1)
	if err == nil {
		t.Fatal("failed", err)
	}
	mock3.UnPatch()
	time.Sleep(300 * time.Millisecond)
	mock4 := mockey.Mock((*ticketService).IsUserExists).Return(true, nil).Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock((*ticketService).checkUserTenant).Return(nil).Build()
	defer mock5.UnPatch()

	service.CheckTicketUserInfo(ctx, 1)
}

func TestGetUpdateTables(t *testing.T) {
	service := getService()
	sqlStr1 := "update db.tb set a = 1" // ignore_security_alert
	sqlStr2 := "UPDATE mytable1 t1\nINNER JOIN mytable2 t2 ON t1.id = t2.id\nSET t1.name = 'new_name', t2.age = 20\nWHERE t2.age > 18;"
	sqlStr3 := "UPDATE mydatabase1.mytable1 t1\nINNER JOIN mydatabase2.mytable2 t2 ON t1.id = t2.id\nINNER JOIN mydatabase3.mytable3 t3 ON t1.id = t3.id\nSET t1.name = 'new_name', t2.age = 20, t3.address = 'new_address'\nWHERE t2.age > 18 AND t3.city = 'Beijing';"
	sqlStr4 := "UPDATE mydatabase1.mytable1 t1\nINNER JOIN mydatabase2.mytable2 t2 ON t1.id = t2.id\nINNER JOIN mydatabase3.mytable3 t3 ON t1.id = t3.id\nINNER JOIN mydatabase4.mytable4 t4 ON t1.id = t4.id\nSET t1.name = 'new_name', t2.age = 20, t3.address = 'new_address', t4.salary = 10000\nWHERE t2.age > 18 AND t3.city = 'Beijing' AND t4.department = 'Sales';"
	p := parser.New()
	var nodes []ast.StmtNode

	nodes, _, _ = p.Parse(sqlStr1, "", "")
	for _, statementNode := range nodes {
		tables := service.getTables(statementNode)
		assert.Equal(t, len(tables), 1)
	}

	nodes, _, _ = p.Parse(sqlStr2, "", "")
	for _, statementNode := range nodes {
		tables := service.getTables(statementNode)
		assert.Equal(t, len(tables), 2)
	}

	nodes, _, _ = p.Parse(sqlStr3, "", "")
	for _, statementNode := range nodes {
		tables := service.getTables(statementNode)
		assert.Equal(t, len(tables), 3)
	}

	nodes, _, _ = p.Parse(sqlStr4, "", "")
	for _, statementNode := range nodes {
		tables := service.getTables(statementNode)
		assert.Equal(t, len(tables), 4)
	}
}

func TestGetDeleteTables(t *testing.T) {
	service := getService()
	sqlStr1 := "DELETE table1 WHERE 1=1;"
	sqlStr2 := "DELETE table1, table2\nFROM table1\nJOIN table2 ON table1.column1 = table2.column1\nWHERE 1=1;"
	sqlStr3 := "DELETE table1, table2, table3\nFROM table1\nJOIN table2 ON table1.column1 = table2.column1\nJOIN table3 ON table2.column2 = table3.column2\nWHERE 1=1;"
	sqlStr4 := "DELETE table1, table2, table3, table4\nFROM table1\nJOIN table2 ON table1.column1 = table2.column1\nJOIN table3 ON table2.column2 = table3.column2\nJOIN table4 ON table3.column3 = table4.column3\nWHERE 1=1;"
	p := parser.New()
	var nodes []ast.StmtNode

	nodes, _, _ = p.Parse(sqlStr1, "", "")
	for _, statementNode := range nodes {
		tables := service.getTables(statementNode)
		assert.Equal(t, len(tables), 1)
	}

	nodes, _, _ = p.Parse(sqlStr2, "", "")
	for _, statementNode := range nodes {
		tables := service.getTables(statementNode)
		assert.Equal(t, len(tables), 2)
	}

	nodes, _, _ = p.Parse(sqlStr3, "", "")
	for _, statementNode := range nodes {
		tables := service.getTables(statementNode)
		assert.Equal(t, len(tables), 3)
	}

	nodes, _, _ = p.Parse(sqlStr4, "", "")
	for _, statementNode := range nodes {
		tables := service.getTables(statementNode)
		assert.Equal(t, len(tables), 4)
	}
}

func TestGetCreateTables(t *testing.T) {
	service := getService()
	sqlStr1 := "create table t_1 (id int)"
	sqlStr2 := "create table t_1 like t_2;"
	p := parser.New()
	var nodes []ast.StmtNode

	nodes, _, _ = p.Parse(sqlStr1, "", "")
	for _, statementNode := range nodes {
		tables := service.getTables(statementNode)
		assert.Equal(t, len(tables), 1)
	}

	nodes, _, _ = p.Parse(sqlStr2, "", "")
	for _, statementNode := range nodes {
		tables := service.getTables(statementNode)
		assert.Equal(t, len(tables), 2)
	}
}

func TestPreCheckUnsuportedDDL(t *testing.T) {
	service := getService()
	res := service.PreCheckMySQLUnsuportedDDL(context.Background(), "add column age varchar(10)")
	fmt.Println("res is ", res)
	if res == true {
		t.Fatal("error")
	}

	res0 := service.PreCheckMySQLUnsuportedDDL(context.Background(), "change  age nianling varchar(10)")
	fmt.Println("res0 is ", res0)
	if res0 != true {
		t.Fatal("error")
	}

	res1 := service.PreCheckMySQLUnsuportedDDL(context.Background(), "change column age nianling varchar(10)")
	fmt.Println("res1 is ", res)
	if res1 != true {
		t.Fatal("error")
	}

	res2 := service.PreCheckMySQLUnsuportedDDL(context.Background(), " RENAME TO new_column ")
	if res2 != true {
		t.Fatal("error")
	}

	res3 := service.PreCheckMySQLUnsuportedDDL(context.Background(), "encryption column age nianling varchar(10)")
	fmt.Println("res3 is ", res3)
	if res3 != true {
		t.Fatal("error")
	}

	res4 := service.PreCheckMySQLUnsuportedDDL(context.Background(), "optimize column age nianling varchar(10)")
	fmt.Println("res3 is ", res4)
	if res3 != true {
		t.Fatal("error")
	}

	res5 := service.PreCheckMySQLUnsuportedDDL(context.Background(), "remove partition")
	fmt.Println("res3 is ", res5)
	if res5 != true {
		t.Fatal("error")
	}
}

func TestIsInstanceRunning(t *testing.T) {
	service := getService()
	mock1 := mockey.Mock((*mocks.MockDataSourceService).ListInstance).Return(&datasource.ListInstanceResp{
		Total: 1,
		InstanceList: []*model.InstanceInfo{
			{
				InstanceId:     utils.StringRef("123"),
				InstanceStatus: v2.InstanceStatus_Running.String(),
			},
		},
	}, errors.New("error")).Build()
	flag := service.IsInstanceRunning(context.Background(), "123", shared.MySQL)
	if flag {
		t.Fatal("error")
	}
	mock1.UnPatch()

	mock2 := mockey.Mock((*mocks.MockDataSourceService).ListInstance).Return(&datasource.ListInstanceResp{
		Total: 1,
		InstanceList: []*model.InstanceInfo{
			{
				InstanceId:     utils.StringRef("123"),
				InstanceStatus: v2.InstanceStatus_Running.String(),
			},
		},
	}, nil).Build()
	flag2 := service.IsInstanceRunning(context.Background(), "123", shared.MySQL)
	if !flag2 {
		t.Fatal("error")
	}
	mock2.UnPatch()

	mock3 := mockey.Mock((*mocks.MockDataSourceService).ListInstance).Return(&datasource.ListInstanceResp{
		Total: 1,
		InstanceList: []*model.InstanceInfo{
			{
				InstanceId:     utils.StringRef("123"),
				InstanceStatus: v2.InstanceStatus_Running.String(),
			},
		},
	}, nil).Build()
	flag3 := service.IsInstanceRunning(context.Background(), "123", shared.MySQL)
	if !flag3 {
		t.Fatal("error")
	}
	mock3.UnPatch()
}

func TestSendMessageToVeDBDDLTicketActor(t *testing.T) {
	service := getService()
	ctx := context.Background()
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockActorClient).KindOf).Return(mocks.NewMockKindClient(&gomock.Controller{})).Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("error")).Build()
	defer baseMock3.UnPatch()
	baseMock0000 := mockey.Mock(fwctx.GetTenantID).Return("1").Build()
	defer baseMock0000.UnPatch()
	baseMock0001 := mockey.Mock(fwctx.GetUserID).Return("1").Build()
	defer baseMock0001.UnPatch()

	service.sendMessageToVeDBDDLTicketActor(ctx, &shared.Ticket{}, &shared.DataSource{})
}

func TestDescribeTicketsForOperateRecord(t *testing.T) {

	service := getService()
	ctx := context.Background()

	name := "test"
	param := model.TicketRecordSearchParam{
		CreateUserName: &name,
	}
	PageNumber := int32(1)
	PageSize := int32(10)
	req := &model.DescribeTicketRecordListReq{
		PageNumber:              &PageNumber,
		PageSize:                &PageSize,
		TicketRecordSearchParam: &param,
	}

	tickets := []*dao.Ticket{}

	ticket1 := dao.Ticket{
		TicketId: 1,
	}
	ticket2 := dao.Ticket{
		TicketId: 2,
	}
	tickets = append(tickets, &ticket1)
	tickets = append(tickets, &ticket2)

	user := dao.DbwUser{
		ID:              1,
		TenantID:        "12343223",
		UserID:          "123",
		UserName:        "cx",
		MaxExecuteCount: 100,
		CurExecuteCount: 1,
	}

	users := []*dao.DbwUser{}
	users = append(users, &user)

	mock4 := mockey.Mock(fwctx.GetTenantID).Return("12343223").Build()
	defer mock4.UnPatch()
	mock5 := mockey.Mock(fwctx.GetUserID).Return("123").Build()
	defer mock5.UnPatch()
	mock1 := mockey.Mock((*mocks.MockDbwUserDAL).Get).Return(&user, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeTicketsForOperateRecord).Return(tickets, 2, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockDbwUserDAL).GetUserByName).Return(users, nil).Build()
	defer mock3.UnPatch()
	var mockRes []*dao.TicketUserInfo
	info := &dao.TicketUserInfo{
		UserId:   "123",
		UserName: "cx",
	}
	mockRes = append(mockRes, info)
	mock6 := mockey.Mock((*mocks.MockWorkflowDAL).GetUserNameByIds).Return(&mockRes, nil).Build()
	defer mock6.UnPatch()

	record, err := service.DescribeTicketsForOperateRecord(ctx, req)
	if err != nil {
		t.Fatal("DescribeTicketsForOperateRecord failed!", err)
	}

	ticketRecord := record.TicketRecordList[0]
	if ticketRecord.TicketId != "1" {
		t.Fatal("DescribeTicketsForOperateRecord failed!", err)
	}
}

func TestCreateOnlineDDLSqlTaskDryRun(t *testing.T) {
	service := getService()
	ctx := context.Background()

	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()

	mock0 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(
		nil, fmt.Errorf("InstanceNotFound")).Build()
	service.CreateOnlineDDLSqlTaskDryRun(ctx, &datasource.CreateFreeLockCorrectOrderDryRunReq{})
	mock0.UnPatch()

	mock1 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(
		nil, fmt.Errorf("error")).Build()
	service.CreateOnlineDDLSqlTaskDryRun(ctx, &datasource.CreateFreeLockCorrectOrderDryRunReq{})
	mock1.UnPatch()

	mock2 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(
		nil, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockDataSourceService).CreateFreeLockCorrectOrderDryRun).Return(
		nil, fmt.Errorf("error")).Build()
	service.CreateOnlineDDLSqlTaskDryRun(ctx, &datasource.CreateFreeLockCorrectOrderDryRunReq{})
	mock3.UnPatch()

	mock4 := mockey.Mock((*mocks.MockDataSourceService).CreateFreeLockCorrectOrderDryRun).Return(
		&datasource.CreateFreeLockCorrectOrderDryRunResp{
			DryRunSuccess: false,
			ErrorCode:     "",
			Reason:        "",
		}, nil).Build()
	service.CreateOnlineDDLSqlTaskDryRun(ctx, &datasource.CreateFreeLockCorrectOrderDryRunReq{})
	mock4.UnPatch()

}

func TestSubmitTicket(t *testing.T) {
	//service := getService()
	//ctx := context.Background()
	//err := fmt.Errorf("error")
	//
	//baseMock1 := mockey.Mock(log.Log).Return().Build()
	//defer baseMock1.UnPatch()
	//
	//mockx := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{}, err).Build()
	//defer mockx.UnPatch()
	//
	//mock0 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateTicketSubmitFlag).Return(err).Build()
	//service.SubmitTicket(ctx, &model.SubmitTicketReq{
	//	TicketId: "1",
	//})
	//mock0.UnPatch()
	//
	//mock01 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateTicketSubmitFlag).Return(nil).Build()
	//defer mock01.UnPatch()
	//mock1 := mockey.Mock((*ticketService).GetTicket).Return(&shared.Ticket{}, err).Build()
	//service.SubmitTicket(ctx, &model.SubmitTicketReq{
	//	TicketId: "1",
	//})
	//mock1.UnPatch()
	//
	//mock11 := mockey.Mock((*ticketService).GetTicket).Return(&shared.Ticket{}, nil).Build()
	//defer mock11.UnPatch()
	//mock2 := mockey.Mock((*ticketService).ChangePreCheckTicketStatusAndOperator).Return(nil).Build()
	//defer mock2.UnPatch()
	//service.SubmitTicket(ctx, &model.SubmitTicketReq{
	//	TicketId: "1",
	//})
}

func TestFormatOnlineDDlTicketConfig(t *testing.T) {
	service := getService()

	mock1 := mockey.Mock((*mocks.MockConfigProvider).Get).Return(&config.Config{OnlineDDlDmlBatchSize: 100}).Build()
	defer mock1.UnPatch()

	ticket := &shared.Ticket{TicketType: int32(model.TicketType_NormalSqlChange)}
	_, err := service.formatOnlineDDlTicketConfig(context.Background(), nil, ticket)
	assert.Equal(t, nil, err)

	ticket = &shared.Ticket{TicketType: int32(model.TicketType_FreeLockStructChange)}
	_, err = service.formatOnlineDDlTicketConfig(context.Background(), &model.SubmitTicketReq{OnlineDDlConfig: &model.OnlineDDlConfig{}}, ticket)
	assert.Equal(t, nil, err)
}

func TestDescribePreCheckDetail(t *testing.T) {
	//service := getService()
	//ctx := context.Background()
	//err := fmt.Errorf("error")
	//
	//baseMock1 := mockey.Mock(log.Log).Return().Build()
	//defer baseMock1.UnPatch()
	//
	//mock0 := mockey.Mock((*mocks.MockPreCheckDetailDAL).DescribeByTicketID).Return(nil, err).Build()
	//service.DescribePreCheckDetail(ctx, &model.DescribePreCheckDetailReq{
	//	TicketId: "1",
	//})
	//mock0.UnPatch()
	//
	//mock01 := mockey.Mock((*mocks.MockPreCheckDetailDAL).DescribeByTicketID).Return(
	//	[]*dao.TicketPreCheckDetail{
	//		{
	//			TicketId:              0,
	//			TenantId:              "",
	//			SqlText:               "",
	//			ExplainResult:         "",
	//			SyntaxCheckResult:     "",
	//			PermissionCheckResult: "",
	//			SecurityCheckResult:   "",
	//		},
	//	}, nil).Build()
	//defer mock01.UnPatch()
	//service.DescribePreCheckDetail(ctx, &model.DescribePreCheckDetailReq{
	//	TicketId: "1",
	//})
}

func TestNoPreCheck(t *testing.T) {
	service := getService()
	mock1 := mockey.Mock((*ticketService).ChangePreCheckTicketStatusAndOperator).Return(nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*ticketService).AutoApproval).Return(fmt.Errorf("test")).Build()
	defer mock2.UnPatch()

	_, _ = service.NoPreCheck(context.Background(), &shared.Ticket{})
}

func TestExplainCommand(t *testing.T) {
	service := getService()
	ctx := context.Background()
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()

	mock1 := mockey.Mock((*mocks.MockDataSourceService).ExplainCommand).Return(&datasource.ExplainCommandResp{}, nil).Build()
	service.ExplainCommand(ctx, &shared.DataSource{}, "select 1")
	mock1.UnPatch()

	mock11 := mockey.Mock((*mocks.MockDataSourceService).ExplainCommand).Return(nil, fmt.Errorf("")).Build()
	service.ExplainCommand(ctx, &shared.DataSource{}, "select 1")
	mock11.UnPatch()
}

// Mock_DbwInstanceDAL_executeCommands is a mock structure for the dal.DbwInstanceDAL interface.
type Mock_DbwInstanceDAL_executeCommands struct {
	dal.DbwInstanceDAL
}

// Get is a mock method.
func (m *Mock_DbwInstanceDAL_executeCommands) Get(ctx context.Context, InstanceId string, InstanceType string, Source string, TenantId string, Mode string) (*dao.DbwInstance, error) {
	// This method body is not used, as it's mocked by mockey.
	return nil, nil
}

// Mock_DataSourceService_executeCommands is a mock structure for the datasource.DataSourceService interface.
type Mock_DataSourceService_executeCommands struct {
	datasource.DataSourceService
}

// GetDatasourceAddress is a mock method.
func (m *Mock_DataSourceService_executeCommands) GetDatasourceAddress(ctx context.Context, ds *shared.DataSource) error {
	// This method body is not used, as it's mocked by mockey.
	return nil
}

// Mock_ActorClient_executeCommands is a mock structure for the cli.ActorClient interface.
type Mock_ActorClient_executeCommands struct {
	cli.ActorClient
}

// KindOf is a mock method.
func (m *Mock_ActorClient_executeCommands) KindOf(kind string) cli.KindClient {
	// This method body is not used, as it's mocked by mockey.
	return nil
}

// Mock_KindClient_executeCommands is a mock structure for the cli.KindClient interface.
type Mock_KindClient_executeCommands struct {
	cli.KindClient
}

// Call is a mock method.
func (m *Mock_KindClient_executeCommands) Call(ctx context.Context, name string, msg interface{}, callopts ...*cluster.GrainCallOptions) (interface{}, error) {
	// This method body is not used, as it's mocked by mockey.
	return nil, nil
}

// MockStandardError implements consts.StandardError for testing.
type MockStandardError struct {
	consts.StandardError
	code int32
	msg  string
}

func (e *MockStandardError) Error() string      { return e.msg }
func (e *MockStandardError) GetCode() int32     { return e.code }
func (e *MockStandardError) GetMsg() string     { return e.msg }
func (e *MockStandardError) GetStatus() string  { return "STATUS_MOCK" }
func (e *MockStandardError) GetHttpCode() int64 { return 500 }
func (e *MockStandardError) GetDetail() string  { return e.msg }
func (e *MockStandardError) GetReason() string  { return "" }
func Test_ticketService_executeCommands_BitsUTGen(t *testing.T) {
	// Common mock setup
	mockDbwInstanceDAL := &Mock_DbwInstanceDAL_executeCommands{}
	mockDsService := &Mock_DataSourceService_executeCommands{}
	mockActorClient := &Mock_ActorClient_executeCommands{}
	mockKindClient := &Mock_KindClient_executeCommands{}

	selfService := &ticketService{
		dbwInstance: mockDbwInstanceDAL,
		ds:          mockDsService,
		actorClient: mockActorClient,
	}

	ctx := context.Background()

	mockey.PatchConvey("Test executeCommands", t, func() {
		// Mock common functions for all test cases
		mockey.Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
		mockey.Mock(log.Warn).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
		mockey.Mock(fwctx.GetTenantID).Return("test-tenant").Build()
		mockey.Mock(fwctx.GetUserID).Return("test-user").Build()
		mockey.Mock(utils001.DecryptData).Return("decrypted-password").Build()
		mockey.Mock(consts.ErrorOf).To(func(e model.ErrorCode) consts.StandardError {
			return &MockStandardError{msg: e.String(), code: int32(e)}
		}).Build()
		mockey.Mock(consts.ErrorWithParam).To(func(e model.ErrorCode, params ...interface{}) consts.StandardError {
			return &MockStandardError{msg: fmt.Sprintf("%s: %v", e.String(), params), code: int32(e)}
		}).Build()

		mockey.PatchConvey("失败场景 - 获取DB账号失败", func() {
			// 场景描述：当获取数据库账号信息失败时，函数应返回ListAccountFail错误。
			// 数据构造：
			// - ds: 一个基本的DataSource对象。
			// - ticket: 一个基本的Ticket对象。
			// 逻辑链路：
			// 1. Mock dbwInstance.Get 方法返回一个错误。
			// 2. 调用 executeCommands。
			// 3. 断言返回的错误为 ListAccountFail。
			ds := &shared.DataSource{InstanceId: "inst-1"}
			ticket := &shared.Ticket{TicketId: 1}
			expectedErr := errors.New("get instance error")

			mockey.Mock(mockey.GetMethod(mockDbwInstanceDAL, "Get")).Return(nil, expectedErr).Build()

			msg, err := selfService.executeCommands(ctx, ds, ticket)

			convey.So(msg, convey.ShouldBeEmpty)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, model.ErrorCode_ListAccountFail.String())
		})

		mockey.PatchConvey("失败场景 - 获取DB地址失败", func() {
			// 场景描述：当获取数据库连接地址失败时，函数应返回InternalError。
			// 数据构造：
			// - ds: 一个基本的DataSource对象。
			// - ticket: 一个基本的Ticket对象，其类型不为MySQLSharding的无锁结构变更。
			// 逻辑链路：
			// 1. Mock dbwInstance.Get 方法成功返回。
			// 2. Mock ds.GetDatasourceAddress 方法返回一个错误。
			// 3. 调用 executeCommands。
			// 4. 断言返回的错误为 InternalError。
			ds := &shared.DataSource{InstanceId: "inst-1"}
			ticket := &shared.Ticket{TicketId: 1, InstanceType: shared.MySQL}
			expectedErr := errors.New("get address error")

			mockey.Mock(mockey.GetMethod(mockDbwInstanceDAL, "Get")).Return(&dao.DbwInstance{}, nil).Build()
			mockey.Mock(mockey.GetMethod(mockDsService, "GetDatasourceAddress")).Return(expectedErr).Build()

			msg, err := selfService.executeCommands(ctx, ds, ticket)

			convey.So(msg, convey.ShouldBeEmpty)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, model.ErrorCode_InternalError.String())
		})

		// A helper function to set up successful pre-checks
		setupSuccessfulPreChecks := func() {
			mockey.Mock(mockey.GetMethod(mockDbwInstanceDAL, "Get")).Return(&dao.DbwInstance{}, nil).Build()
			mockey.Mock(mockey.GetMethod(mockActorClient, "KindOf")).Return(mockKindClient).Build()
		}

		testcases := []struct {
			name                   string
			instanceType           shared.DataSourceType
			ticketType             model.TicketType
			isDirectDDL            bool
			actorKind              string
			actorShouldBeCalled    bool
			shouldGetAddress       bool
			expectedSuccessMessage string
		}{
			{
				name:                   "成功场景 - MySQL 普通SQL变更",
				instanceType:           shared.MySQL,
				ticketType:             model.TicketType_NormalSqlChange,
				actorKind:              consts.ExecTicketActorKind,
				actorShouldBeCalled:    true,
				shouldGetAddress:       true,
				expectedSuccessMessage: "MySQL NormalSqlChange Started",
			},
			{
				name:                   "成功场景 - MySQL 无锁数据变更",
				instanceType:           shared.MySQL,
				ticketType:             model.TicketType_FreeLockSqlChange,
				actorKind:              consts.FreeLockDMLActorKind,
				actorShouldBeCalled:    true,
				shouldGetAddress:       true,
				expectedSuccessMessage: "MySQL FreeLockSqlChange Started",
			},
			{
				name:                   "成功场景 - MySQL 无锁结构变更 (普通DDL)",
				instanceType:           shared.MySQL,
				ticketType:             model.TicketType_FreeLockStructChange,
				isDirectDDL:            true,
				actorKind:              consts.ExecTicketActorKind,
				actorShouldBeCalled:    true,
				shouldGetAddress:       true,
				expectedSuccessMessage: "MySQL FreeLockStructChange (Normal DDL) Started",
			},
			{
				name:                   "成功场景 - MySQL 无锁结构变更 (Online DDL)",
				instanceType:           shared.MySQL,
				ticketType:             model.TicketType_FreeLockStructChange,
				isDirectDDL:            false,
				actorKind:              consts.OnlineDDLTicketActorKind,
				actorShouldBeCalled:    true,
				shouldGetAddress:       true,
				expectedSuccessMessage: "MySQL FreeLockStructChange (Online DDL) Started",
			},
			{
				name:                   "成功场景 - VeDB 无锁结构变更 (普通DDL)",
				instanceType:           shared.VeDBMySQL,
				ticketType:             model.TicketType_FreeLockStructChange,
				isDirectDDL:            true,
				actorKind:              consts.ExecTicketActorKind,
				actorShouldBeCalled:    true,
				shouldGetAddress:       true,
				expectedSuccessMessage: "VeDB FreeLockStructChange (Normal DDL) Started",
			},
			{
				name:                   "成功场景 - VeDB 无锁结构变更 (VeDB DDL)",
				instanceType:           shared.VeDBMySQL,
				ticketType:             model.TicketType_FreeLockStructChange,
				isDirectDDL:            false,
				actorKind:              consts.VeDBDDLTicketActorKind,
				actorShouldBeCalled:    true,
				shouldGetAddress:       true,
				expectedSuccessMessage: "VeDB FreeLockStructChange (VeDB DDL) Started",
			},
			{
				name:                   "成功场景 - Sharding 无锁结构变更",
				instanceType:           shared.MySQLSharding,
				ticketType:             model.TicketType_FreeLockStructChange,
				actorKind:              consts.ShardingFreeLockDDLActorKind,
				actorShouldBeCalled:    true,
				shouldGetAddress:       false,
				expectedSuccessMessage: "Sharding FreeLockStructChange Started",
			},
			{
				name:                "失败场景 - Sharding 无锁数据变更 (无对应actor)",
				instanceType:        shared.MySQLSharding,
				ticketType:          model.TicketType_FreeLockSqlChange,
				actorShouldBeCalled: false,
				shouldGetAddress:    true,
			},
		}

		for _, tc := range testcases {
			mockey.PatchConvey(tc.name, func() {
				mockey.PatchConvey("发送消息成功", func() {
					// 场景描述：针对特定工单类型，验证其是否能成功调用正确的actor并启动任务。
					// 数据构造：
					// - ds: 一个基本的DataSource对象。
					// - ticket: 根据测试用例构造的Ticket对象。
					// 逻辑链路：
					// 1. Mock 数据库账号和地址获取成功。
					// 2. 如果是DDL，Mock IsEnableDDLDirectExecCommandsType 的返回值。
					// 3. Mock actorClient.KindOf(..).Call(..) 成功返回任务启动信息。
					// 4. 调用 executeCommands。
					// 5. 断言返回成功消息，且无错误。
					setupSuccessfulPreChecks()
					ds := &shared.DataSource{InstanceId: "inst-1"}
					ticket := &shared.Ticket{
						TicketId:     1,
						InstanceType: tc.instanceType,
						TicketType:   int32(tc.ticketType),
					}

					if tc.shouldGetAddress {
						mockey.Mock(mockey.GetMethod(mockDsService, "GetDatasourceAddress")).Return(nil).Build()
					}

					if tc.ticketType == model.TicketType_FreeLockStructChange {
						mockey.Mock((*ticketService).IsEnableDDLDirectExecCommandsType).Return(tc.isDirectDDL).Build()
					}

					if tc.actorShouldBeCalled {
						mockey.Mock(mockey.GetMethod(mockKindClient, "Call")).Return(&shared.TicketExecuted{
							Code:    shared.ExecutedStart,
							Message: tc.expectedSuccessMessage,
						}, nil).Build()
					}

					msg, err := selfService.executeCommands(ctx, ds, ticket)

					if tc.actorShouldBeCalled {
						convey.So(err, convey.ShouldBeNil)
						convey.So(msg, convey.ShouldEqual, tc.expectedSuccessMessage)
					} else {
						// This branch has no actor call, and will fall through to the default switch case, which returns an error.
						convey.So(msg, convey.ShouldEqual, "execute ticket fail")
						convey.So(err, convey.ShouldNotBeNil)
						convey.So(err.Error(), convey.ShouldContainSubstring, model.ErrorCode_TicketStartError.String())
					}
				})

				if tc.actorShouldBeCalled {
					mockey.PatchConvey("发送消息失败", func() {
						// 场景描述：验证当actor调用返回错误时，函数能正确处理并返回InternalError。
						// 逻辑链路：
						// 1. Mock actorClient.KindOf(..).Call(..) 返回一个错误。
						// 2. 调用 executeCommands。
						// 3. 断言返回 InternalError。
						setupSuccessfulPreChecks()
						ds := &shared.DataSource{InstanceId: "inst-1"}
						ticket := &shared.Ticket{InstanceType: tc.instanceType, TicketType: int32(tc.ticketType)}
						expectedErr := errors.New("actor call failed")

						if tc.shouldGetAddress {
							mockey.Mock(mockey.GetMethod(mockDsService, "GetDatasourceAddress")).Return(nil).Build()
						}
						if tc.ticketType == model.TicketType_FreeLockStructChange {
							mockey.Mock((*ticketService).IsEnableDDLDirectExecCommandsType).Return(tc.isDirectDDL).Build()
						}
						mockey.Mock(mockey.GetMethod(mockKindClient, "Call")).Return(nil, expectedErr).Build()

						msg, err := selfService.executeCommands(ctx, ds, ticket)

						convey.So(msg, convey.ShouldBeEmpty)
						convey.So(err, convey.ShouldNotBeNil)
						convey.So(err.Error(), convey.ShouldContainSubstring, model.ErrorCode_InternalError.String())
					})

					mockey.PatchConvey("任务启动失败", func() {
						// 场景描述：验证当actor返回任务启动失败的状态时，函数能正确处理并返回TicketStartError。
						// 逻辑链路：
						// 1. Mock actorClient.KindOf(..).Call(..) 返回一个表示失败的TicketExecuted对象。
						// 2. 调用 executeCommands。
						// 3. 断言返回 TicketStartError。
						setupSuccessfulPreChecks()
						ds := &shared.DataSource{InstanceId: "inst-1"}
						ticket := &shared.Ticket{InstanceType: tc.instanceType, TicketType: int32(tc.ticketType)}

						if tc.shouldGetAddress {
							mockey.Mock(mockey.GetMethod(mockDsService, "GetDatasourceAddress")).Return(nil).Build()
						}
						if tc.ticketType == model.TicketType_FreeLockStructChange {
							mockey.Mock((*ticketService).IsEnableDDLDirectExecCommandsType).Return(tc.isDirectDDL).Build()
						}
						mockey.Mock(mockey.GetMethod(mockKindClient, "Call")).Return(&shared.TicketExecuted{
							Code:    shared.ExecutedFail,
							Message: "failed to start",
						}, nil).Build()

						msg, err := selfService.executeCommands(ctx, ds, ticket)

						convey.So(msg, convey.ShouldEqual, "failed to start")
						convey.So(err, convey.ShouldNotBeNil)
						convey.So(err.Error(), convey.ShouldContainSubstring, model.ErrorCode_TicketStartError.String())
					})

					mockey.PatchConvey("响应类型错误", func() {
						// 场景描述：验证当actor返回非预期的响应类型时，函数能正确处理并返回TicketStartError。
						// 逻辑链路：
						// 1. Mock actorClient.KindOf(..).Call(..) 返回一个错误的类型。
						// 2. 调用 executeCommands。
						// 3. 断言返回 TicketStartError。
						setupSuccessfulPreChecks()
						ds := &shared.DataSource{InstanceId: "inst-1"}
						ticket := &shared.Ticket{InstanceType: tc.instanceType, TicketType: int32(tc.ticketType)}

						if tc.shouldGetAddress {
							mockey.Mock(mockey.GetMethod(mockDsService, "GetDatasourceAddress")).Return(nil).Build()
						}
						if tc.ticketType == model.TicketType_FreeLockStructChange {
							mockey.Mock((*ticketService).IsEnableDDLDirectExecCommandsType).Return(tc.isDirectDDL).Build()
						}
						mockey.Mock(mockey.GetMethod(mockKindClient, "Call")).Return("invalid response type", nil).Build()

						msg, err := selfService.executeCommands(ctx, ds, ticket)

						convey.So(msg, convey.ShouldEqual, "execute ticket fail")
						convey.So(err, convey.ShouldNotBeNil)
						convey.So(err.Error(), convey.ShouldContainSubstring, model.ErrorCode_TicketStartError.String())
					})
				}
			})
		}
	})
}
