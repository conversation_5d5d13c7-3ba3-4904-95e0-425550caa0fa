package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/tenant"
	"context"
	"fmt"
	"reflect"
	"regexp"
	"strings"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/mysql"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	parser "code.byted.org/infcs/ds-sql-parser"
	"code.byted.org/infcs/ds-sql-parser/ast"
	pgparse "github.com/pganalyze/pg_query_go/v6"
)

func (selfService *ticketService) checkSqlFormat(ctx context.Context, ticket *shared.Ticket, resp *model.PreCheckTicketResp,
	parseStruct *TicketSqlTextParseResult) (*PrecheckTicketSytaxResult, bool) {
	ds, err := selfService.getDBDataSource(ctx, ticket.InstanceId, ticket.DbName, ticket.InstanceType)
	if err != nil {
		log.Warn(ctx, "get datasource error %v", err)
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = fmt.Sprintf("get datasource error %v", err)
		res := make([]string, len(parseStruct.sqls))
		for i := range res {
			res[i] = fmt.Sprintf("get datasource error %v", err)
		}
		return &PrecheckTicketSytaxResult{
			checkResult: res,
		}, false
	}
	// veDB不支持所有的htap语法,这里排查一下
	if ticket.InstanceType == shared.VeDBMySQL {
		res, IsValidSQLs := selfService.IsSQLTableContainsHTAPEngine(ctx, parseStruct, ds)
		if !IsValidSQLs {
			resp.CheckItems[0].Status = model.PreCheckStatus_Error
			resp.CheckItems[0].Memo = "veDB not support the HTAP syntax"
			return res, false
		}
	}
	// 普通SQL变更 && 普通结构变更
	if ticket.TicketType == int32(model.TicketType_NormalSqlChange) {
		// NOTE 这里针对字节云 && 小基架的普通SQL变更,需要单独处理,只支持insert,update,delete
		if tenant.IsRDSMultiCloudPlatform(ctx, selfService.conf) {
			return selfService.checkNormalSQLFormatForMultiCloudDML(ctx, resp, parseStruct)
		}
		//return selfService.CheckNormalSqlFormat(ctx, ticket, resp, parseStruct)
		//NOTE 普通SQL变更支持所有的语法,create ticket的时候已经校验过了
		resp.CheckItems[0].Status = model.PreCheckStatus_Pass
		resp.CheckItems[0].Memo = "Pass"
		res := make([]string, len(parseStruct.sqls))
		for i := range res {
			res[i] = "Pass"
		}
		return &PrecheckTicketSytaxResult{
			checkResult: res,
		}, true
	}
	// 无锁结构变更
	if ticket.TicketType == int32(model.TicketType_FreeLockStructChange) {
		return selfService.CheckFreeLockDDLSqlFormat(ctx, ticket, resp, parseStruct)
	}
	// 无锁DML变更
	if ticket.TicketType == int32(model.TicketType_FreeLockSqlChange) {
		return selfService.CheckFreeLockDMLSqlFormat(ctx, ticket, resp, parseStruct)
	}

	resp.CheckItems[0].Status = model.PreCheckStatus_Error
	resp.CheckItems[0].Memo = "wrong ticket type"
	return &PrecheckTicketSytaxResult{
		checkResult: []string{
			"wrong ticket type",
		},
	}, false
}

func (selfService *ticketService) sendMessageToFreeLockDMLActor(ctx context.Context, ticket *shared.Ticket, ds *shared.DataSource) (interface{}, error) {
	return selfService.actorClient.KindOf(consts.FreeLockDMLActorKind).
		Call(ctx, conv.Int64ToStr(ticket.TicketId), &shared.ExecFreeLockDMLTicket{
			TenantID:            fwctx.GetTenantID(ctx),
			UserID:              fwctx.GetUserID(ctx),
			Source:              ds,
			TicketType:          ticket.TicketType,
			ExecuteType:         ticket.ExecuteType,
			SqlText:             ticket.SqlText,
			ExecutableStartTime: ticket.ExecStartTime,
			ExecutableEndTime:   ticket.ExecEndTime,
		})
}

func (selfService *ticketService) sendMessageToTicketActor(ctx context.Context, ticket *shared.Ticket,
	ds *shared.DataSource) (interface{}, error) {
	return selfService.actorClient.KindOf(consts.ExecTicketActorKind).
		Call(ctx, conv.Int64ToStr(ticket.TicketId), &shared.ExecTicket{
			TenantID:            fwctx.GetTenantID(ctx),
			UserID:              fwctx.GetUserID(ctx),
			Source:              ds,
			TicketType:          ticket.TicketType,
			ExecuteType:         ticket.ExecuteType,
			ExecutableStartTime: ticket.ExecStartTime,
			ExecutableEndTime:   ticket.ExecEndTime,
		})
}

func (selfService *ticketService) sendMessageToOnlineDDLTicketActor(ctx context.Context, ticket *shared.Ticket,
	ds *shared.DataSource) (interface{}, error) {
	return selfService.actorClient.KindOf(consts.OnlineDDLTicketActorKind).
		Call(ctx, conv.Int64ToStr(ticket.TicketId), &shared.ExecTicket{
			TenantID:            fwctx.GetTenantID(ctx),
			UserID:              fwctx.GetUserID(ctx),
			Source:              ds,
			TicketType:          ticket.TicketType,
			ExecuteType:         ticket.ExecuteType,
			ExecutableStartTime: ticket.ExecStartTime,
			ExecutableEndTime:   ticket.ExecEndTime,
		})
}

func (selfService *ticketService) sendMessageToVeDBDDLTicketActor(ctx context.Context, ticket *shared.Ticket,
	ds *shared.DataSource) (interface{}, error) {
	return selfService.actorClient.KindOf(consts.VeDBDDLTicketActorKind).
		Call(ctx, conv.Int64ToStr(ticket.TicketId), &shared.ExecVeDBDDLTicket{
			TenantID:            fwctx.GetTenantID(ctx),
			UserID:              fwctx.GetUserID(ctx),
			Source:              ds,
			TicketType:          ticket.TicketType,
			ExecuteType:         ticket.ExecuteType,
			ExecutableStartTime: ticket.ExecStartTime,
			ExecutableEndTime:   ticket.ExecEndTime,
		})
}

func (selfService *ticketService) sendMessageToShardingFreeLockDMLActor(ctx context.Context, ticket *shared.Ticket, ds *shared.DataSource) (interface{}, error) {
	return selfService.actorClient.KindOf(consts.ShardingFreeLockDMLActorKind).
		Call(ctx, conv.Int64ToStr(ticket.TicketId), &shared.ExecShardingFreeLockDMLTicket{
			Source:  ds,
			SqlText: ticket.SqlText,
		})
}

func (selfService *ticketService) sendMessageToShardingFreeLockDDLActor(ctx context.Context, ticket *shared.Ticket, ds *shared.DataSource) (interface{}, error) {
	return selfService.actorClient.KindOf(consts.ShardingFreeLockDDLActorKind).
		Call(ctx, conv.Int64ToStr(ticket.TicketId), &shared.ExecShardingFreeLockDDLTicket{
			SqlText:             ticket.SqlText,
			TenantID:            fwctx.GetTenantID(ctx),
			UserID:              fwctx.GetUserID(ctx),
			Source:              ds,
			TicketType:          ticket.TicketType,
			ExecuteType:         ticket.ExecuteType,
			ExecutableStartTime: ticket.ExecStartTime,
			ExecutableEndTime:   ticket.ExecEndTime,
		})
}

func (selfService *ticketService) initCreateTicketResp(status model.PreCheckStatus) *model.PreCheckTicketResp {
	resp := &model.PreCheckTicketResp{}
	for idx, value := range CheckItemNames {
		checkItem := &model.CheckItem{
			Item:     value,
			ItemType: CheckItemTypes[idx],
			Status:   status}
		resp.CheckItems = append(resp.GetCheckItems(), checkItem)
	}
	resp.AllPass = true // 默认都通过,检查项里面有一项不通过,就置为false
	resp.EnableSubmitTicket = true
	return resp
}

func (selfService *ticketService) changItemNameEnToCn(resp *model.PreCheckTicketResp) {
	itemMap := map[string]string{
		PreCheckSyntax:       PreCheckSyntaxCn,
		PreCheckPermission:   PreCheckPermissionCn,
		PreCheckExplain:      PreCheckExplainCn,
		PreCheckSecurityRule: PreCheckSecurityRuleCn,
	}
	for _, item := range resp.CheckItems {
		item.Item = itemMap[item.Item]
	}
}

// PreCheckMySQLUnsuportedDDL Note 已经没人使用,后面会删掉
func (selfService *ticketService) PreCheckMySQLUnsuportedDDL(ctx context.Context, execSql string) bool {
	// 不支持添加主键操作
	r, _ := regexp.Compile("(?i).*add.*primary\\s+key")
	findStr := r.FindString(execSql)
	if findStr != "" {
		return true
	}

	// 不支持重命名列,pt-osc重命名列--dry-run可以执行，--execute会告警失败
	r, _ = regexp.Compile("(?i)change") // 不区分大小写
	findStr = r.FindString(execSql)
	if findStr != "" {
		// 重命名列的SQL语法是change column c1 c2 col_define, c1 != c2 认为是重命名列
		colList := strings.Split(execSql, " ")
		if len(colList) < 4 {
			log.Error(ctx, "Online DDL invalid exec_sql(%s)", execSql)
			return true
		}
		var originCol string
		var changeCol string

		// change [column] <originCol> <changeCol>,这里column关键字是可选项，所以要处理一下
		if strings.ToLower(colList[1]) == "column" {
			originCol = colList[2]
			changeCol = colList[3]
		} else {
			originCol = colList[1]
			changeCol = colList[2]
		}
		if originCol != changeCol {
			log.Warn(ctx, "Online DDL unsupport change column name sql(%s)", execSql)
			return true
		}
	}

	// pt-osc不支持表重命名, 表重命名语法：ALTER TABLE old_tbl_name RENAME TO new_tbl_name;
	r, _ = regexp.Compile("(?i)rename\\s+to")
	findStr = r.FindString(execSql)
	if findStr != "" {
		log.Warn(ctx, "Online DDL unsupport table rename sql(%s)", execSql)
		return true
	}

	// 表空间操作不支持
	r, _ = regexp.Compile("(?i)encryption")
	findStr = r.FindString(execSql)
	if findStr != "" {
		log.Warn(ctx, "Online DDL unsupport encryption sql(%s)", execSql)
		return true
	}
	// OPTIMIZE table
	r, _ = regexp.Compile("^(?i)optimize")
	findStr = r.FindString(execSql)
	if findStr != "" {
		log.Warn(ctx, "Online DDL unsupport optimize sql(%s)", execSql)
		return true
	}
	// 分区表操作
	partitionOpsList := []string{
		"partition by",
		"add partition",
		"drop partition",
		"discard partition",
		"import partition",
		"truncate partition",
		"coalesce partition",
		"reorgnize partition",
		"exchange partition",
		"analyze partition",
		"check partition",
		"optimize partition",
		"rebuild partition",
		"repair partition",
		"remove partition",
	}
	for _, partitionSql := range partitionOpsList {
		r, _ = regexp.Compile(fmt.Sprintf("(?i)%s", partitionSql))
		findStr = r.FindString(execSql)
		if findStr != "" {
			log.Warn(ctx, "Online DDL unsupport optimize sql(%s)", execSql)
			return true
		}
	}
	return false
}

func (selfService *ticketService) checkNormalSQLFormat(ctx context.Context, resp *model.PreCheckTicketResp, parseStruct *TicketSqlTextParseResult) bool {
	for _, statementNode := range parseStruct.stmts {
		// nodeType is *ast.UpdateStmt
		nodeType := reflect.TypeOf(statementNode)
		// nodeTypeStrs is ["*ast","UpdateStmt"] , nodeType.String() is "*ast.UpdateStmt"
		nodeTypeStrs := strings.Split(nodeType.String(), ".")
		var typeStr string
		// 这里防止panic,所以先判断,在取值
		if len(nodeTypeStrs) > 1 {
			typeStr = nodeTypeStrs[len(nodeTypeStrs)-1]
		}
		_, exist := EnabledNormalCommandsType[typeStr]
		if !exist {
			log.Warn(ctx, fmt.Sprintf("Normal DML ticket not support %s sql type", typeStr))
			resp.CheckItems[0].Status = model.PreCheckStatus_Error
			resp.CheckItems[0].Memo = fmt.Sprintf("Normal DML ticket not support %s sql type", typeStr)
			return false
		}
	}
	resp.CheckItems[0].Status = model.PreCheckStatus_Pass
	resp.CheckItems[0].Memo = "Pass"
	return true
}

// 如果是多云的租户,普通DML就不让执行DDL
func (selfService *ticketService) checkNormalSQLFormatForMultiCloudDML(ctx context.Context, resp *model.PreCheckTicketResp, parseStruct *TicketSqlTextParseResult) (*PrecheckTicketSytaxResult, bool) {
	checkRes := make([]string, len(parseStruct.sqls))
	var isPass = true
	for idx, statementNode := range parseStruct.stmts {
		// nodeType is *ast.UpdateStmt
		nodeType := reflect.TypeOf(statementNode)
		// nodeTypeStrs is ["*ast","UpdateStmt"] , nodeType.String() is "*ast.UpdateStmt"
		nodeTypeStrs := strings.Split(nodeType.String(), ".")
		var typeStr string
		// 这里防止panic,所以先判断,在取值
		if len(nodeTypeStrs) > 1 {
			typeStr = nodeTypeStrs[len(nodeTypeStrs)-1]
		}
		_, exist := EnabledNormalCommandsTypeForMultiCloud[typeStr]
		if !exist {
			isPass = false
			log.Warn(ctx, fmt.Sprintf("Normal DML ticket not support %s sql type", typeStr))
			resp.CheckItems[0].Status = model.PreCheckStatus_Error
			resp.CheckItems[0].Memo = fmt.Sprintf("Normal DML ticket not support %s sql type", typeStr)
			checkRes[idx] = fmt.Sprintf("Normal DML ticket not support %s sql type", typeStr)
			continue
		} else {
			checkRes[idx] = fmt.Sprintf("Pass")
		}
	}
	if !isPass {
		log.Warn(ctx, "isPass is %v,checkRes is %v", isPass, checkRes)
		return &PrecheckTicketSytaxResult{
			checkResult: checkRes,
		}, false
	}
	resp.CheckItems[0].Status = model.PreCheckStatus_Pass
	resp.CheckItems[0].Memo = "Pass"
	return &PrecheckTicketSytaxResult{
		checkResult: checkRes,
	}, true
}

func (selfService *ticketService) checkPgNormalSQLFormat(ctx context.Context, ticket *shared.Ticket, resp *model.PreCheckTicketResp, parseStruct *TicketSqlTextParseResult) bool {
	ds := &shared.DataSource{Type: shared.Postgres}
	cmds, err := selfService.ps.Explain(ctx, ds, ticket.SqlText, model.SqlExecuteType_Ticket)
	if err != nil || len(cmds) == 0 {
		log.Warn(ctx, "explain postgresql error %v", err)
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = fmt.Sprintf("Split ticket sqlText error:%v", err)
		return false
	}
	for _, cmd := range cmds {
		if _, err = pgparse.Parse(cmd.Text()); err != nil {
			log.Warn(ctx, "parse postgresql error %v", err)
			resp.CheckItems[0].Status = model.PreCheckStatus_Error
			resp.CheckItems[0].Memo = "Parse postgresql error"
			return false
		}
		// 只检查语法即可，不检查权限
		//res := utils.GetCommandTypeBySourceType(ds, cmd.Text())
		//if res.CommandType == utils.None && res.Granularity == utils.None && res.PrivilegeType == utils.None {
		//	log.Warn(ctx, "postgresql type unsupported")
		//	resp.CheckItems[0].Status = model.PreCheckStatus_Error
		//	resp.CheckItems[0].Memo = "Unsupported ticket sql command type"
		//	return false
		//}
	}
	resp.CheckItems[0].Status = model.PreCheckStatus_Pass
	resp.CheckItems[0].Memo = "Pass"
	return true
}
func (selfService *ticketService) checkFreeLockDDLSQLFormat(ctx context.Context, ticket *shared.Ticket, resp *model.PreCheckTicketResp,
	parseStruct *TicketSqlTextParseResult) (*PrecheckTicketSytaxResult, bool) {
	//1、判断是否多条语句
	if len(parseStruct.sqls) > 1 {
		log.Warn(ctx, "SQL format is illegal，normal DDL ticket %v only support single SQL", ticket.TicketId)
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = "SQL format is illegal，normal DDL  ticket only support single SQL"
		checkRes := make([]string, len(parseStruct.sqls))
		for idx := 0; idx < len(parseStruct.sqls); idx++ {
			checkRes[idx] = "SQL format is illegal，normal DDL  ticket only support single SQL"
		}
		return &PrecheckTicketSytaxResult{
			checkResult: checkRes,
		}, false
	}
	// 2、判断是否是合法语句
	// nodeType is *ast.AlterTableStmt
	nodeType := reflect.TypeOf(parseStruct.stmts[0])
	// nodeTypeStrs is ["*ast","AlterTableStmt"] , nodeType.String() is "*ast.AlterTableStmt"
	nodeTypeStrs := strings.Split(nodeType.String(), ".")
	var typeStr string
	if len(nodeTypeStrs) > 1 {
		typeStr = nodeTypeStrs[len(nodeTypeStrs)-1]
	}
	// 这些里面是需要直接执行的语句
	if _, directExecFlag := EnabledFreeLockDDLDirectExecCommandsType[typeStr]; directExecFlag {
		resp.CheckItems[0].Status = model.PreCheckStatus_Pass
		resp.CheckItems[0].Memo = "Pass"
		return &PrecheckTicketSytaxResult{
			checkResult: []string{
				"pass",
			},
		}, true
	}
	_, exist := EnabledFreeLockDDLCommandsType[typeStr]
	if !exist {
		log.Warn(ctx, fmt.Sprintf("normal DDL ticket %v not support %s sql type", ticket.TicketId, typeStr))
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = fmt.Sprintf("Normal DDL ticket %v not support %s sql type，only support alter table ,create index,drop index", ticket.TicketId, typeStr)
		return &PrecheckTicketSytaxResult{
			checkResult: []string{
				fmt.Sprintf("Normal DDL ticket %v not support %s sql type，only support alter table ,create index,drop index", ticket.TicketId, typeStr),
			},
		}, false
	}

	// 3、VeDB和MySQL都使用对应的方法来判断是否支持DryRun，vedb的现在是手写的
	dryRunResp, err := selfService.CreateOnlineDDLSqlTaskDryRun(ctx, &datasource.CreateFreeLockCorrectOrderDryRunReq{
		InstanceId:   ticket.InstanceId,
		InstanceType: ticket.InstanceType,
		DBName:       ticket.DbName,
		ExecSQL:      ticket.SqlText,
	})
	if err != nil {
		log.Warn(ctx, "ticket %v OnlineDDLSqlTaskDryRun (%s) fail %v", ticket.TicketId, ticket.SqlText, err)
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = fmt.Sprintf("pre check online ddl ticket %v error:%v", ticket.TicketId, err)
		return &PrecheckTicketSytaxResult{
			checkResult: []string{
				fmt.Sprintf("pre check online ddl ticket %v error:%v", ticket.TicketId, err),
			},
		}, false
	}
	if dryRunResp != nil && !dryRunResp.DryRunSuccess {
		log.Warn(ctx, "get ticket %v dryRun result is false", ticket.TicketId)
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = fmt.Sprintf("ticket %v: %v,%v", ticket.TicketId, dryRunResp.ErrorCode, dryRunResp.Reason)
		return &PrecheckTicketSytaxResult{
			checkResult: []string{
				fmt.Sprintf("ticket %v: %v,%v", ticket.TicketId, dryRunResp.ErrorCode, dryRunResp.Reason),
			},
		}, false
	}
	resp.CheckItems[0].Status = model.PreCheckStatus_Pass
	resp.CheckItems[0].Memo = "Pass"
	return &PrecheckTicketSytaxResult{
		checkResult: []string{
			"pass",
		},
	}, true
}

func (selfService *ticketService) checkPgFreeLockDDLSQLFormat(ctx context.Context, ticket *shared.Ticket, resp *model.PreCheckTicketResp,
	parseStruct *TicketSqlTextParseResult) (*PrecheckTicketSytaxResult, bool) {
	// TODO Pg的无锁DDL语法检查
	return &PrecheckTicketSytaxResult{
		checkResult: []string{
			"pass",
		},
	}, true
}

func (selfService *ticketService) CheckShardingFreeLockDMLSQLFormat(ctx context.Context, ticket *shared.Ticket, resp *model.PreCheckTicketResp) (string, bool) {
	// 1.检查sql语法
	stmts, isError := selfService.getSqlStmts(ctx, ticket, resp)
	if isError {
		return "", false
	}
	// 2.检查sql条数
	if selfService.checkSqlNum(stmts, resp) {
		return "", false
	}
	// 3.检查sql类型
	if selfService.checkSqlType(stmts, resp) {
		return "", false
	}
	// 4.检查delete限制
	tables, isError := selfService.checkDeleteSql(stmts, resp)
	tableName := ""
	if len(tables) > 0 {
		tableName = tables[0]
	}
	return tableName, !isError
	//// 5.检查主键，由下游子任务检查
}

func (selfService *ticketService) checkUniqueKey(ctx context.Context, ticket *shared.Ticket, resp *model.PreCheckTicketResp, tables []string) (isError bool) {
	// 4、判断是否有非空唯一键或者主键
	linkType := shared.Volc
	if ticket.InstanceType.String() == shared.MetaMySQL.String() {
		linkType = shared.Public
	}
	ds := &shared.DataSource{Type: ticket.InstanceType, LinkType: linkType, InstanceId: ticket.InstanceId, Db: ticket.DbName}
	_, _ = selfService.getDBAccount(ctx, ds)

	getAddressResp, err := selfService.ds.GetDBInnerAddress(ctx, &datasource.GetDBInnerAddressReq{Source: ds})
	if err != nil {
		errMsg := fmt.Sprintf("get instance:%s inner address error:%s", ticket.InstanceId, err.Error())
		log.Warn(ctx, errMsg)
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = errMsg
		return true
	}
	ds.Address = getAddressResp.Source.Address
	//_, _ = selfService.getDBAddress(ctx, ds)
	command := fmt.Sprintf("SELECT DISTINCT TABLE_NAME, INDEX_NAME,NULLABLE,SEQ_IN_INDEX, IF(INDEX_NAME='PRIMARY','Primary',"+
		"IF(NON_UNIQUE=1,IF(INDEX_TYPE='FULLTEXT','FullText',IF(INDEX_TYPE='SPATIAL','Spatial','Normal')),'Unique')) "+
		"as INDEX_TYPE, COLUMN_NAME, SUB_PART,INDEX_COMMENT FROM INFORMATION_SCHEMA.STATISTICS "+
		"WHERE TABLE_SCHEMA='%s' and TABLE_NAME in ('%s') %s;", ticket.DbName, tables[0], mysql.DBW_CONSOLE_DEFAULT_HINT)
	// 这里写tables[0]是因为insert...select语句有可能有2个表,取第0个就是select后面的表，而不是insert后面的表
	req := &datasource.GetTableIndexInfoReq{
		TableName: tables[0],
		Source:    ds,
		Command:   command,
	}

	getTableResult, err := selfService.ds.GetTableIndexInfo(ctx, req)
	// rresp, err := selfService.GetTableIndexInfo(ctx, ds, req)

	if err != nil {
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = "error in obtaining table primary key or non empty unique index"
		return true
	}
	log.Info(ctx, "getTableResult is %v", getTableResult)
	/* eg:
	+------------+------------+----------+------------+-------------+----------+---------------+
	| table_name | index_name | NULLABLE | index_type | column_name | sub_part | index_comment |
	+------------+------------+----------+------------+-------------+----------+---------------+
	| students_4 | PRIMARY    |          | Primary    | id          |     NULL |               |
	| students_4 | name_sex   |          | Unique     | name        |     NULL |               |
	| students_4 | name_sex   |          | Unique     | sex         |     NULL |               |
	| students_4 | age        |          | Normal     | age         |     NULL |               |
	+------------+------------+----------+------------+-------------+----------+---------------+
	4 <USER> <GROUP> set (0.00 sec)
	*/
	_, isContainPkOrUniqueKey := selfService.GetTablePKOrUniqKey(ctx, getTableResult)
	if !isContainPkOrUniqueKey { // 如果不包含,直接报错
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = "get empty primary key or a non-null unique index,Please check if the database contains the table or if the table contains a primary key or a non-null unique index "
		return true
	}
	return false
}

func (selfService *ticketService) checkDeleteSql(stmts []ast.StmtNode, resp *model.PreCheckTicketResp) (tables []string, isError bool) {
	// 4、判断delete语句的限制
	tables, err := utils.CheckFreeLockDMLLimit(stmts[0])
	if err != nil {
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = err.Error()
		return tables, true
	}
	return tables, false
}

func (selfService *ticketService) getSqlStmts(ctx context.Context, ticket *shared.Ticket, resp *model.PreCheckTicketResp) (stmt []ast.StmtNode, isError bool) {
	p := parser.New()
	stmts, _, err := p.Parse(ticket.SqlText, "utf8", "")
	if err != nil || len(stmts) == 0 {
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = fmt.Sprintf("SQL statement error or unsupported, reason: %v", err)
		return stmts, true
	}
	return stmts, false
}

func (selfService *ticketService) checkSqlNum(stmts []ast.StmtNode, resp *model.PreCheckTicketResp) (isError bool) {
	//1、判断是否多条语句
	if len(stmts) > 1 {
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = "SQL format is illegal，free lock DML ticket only support single SQL"
		return true
	}
	return false
}

func (selfService *ticketService) checkSqlType(stmts []ast.StmtNode, resp *model.PreCheckTicketResp) (isError bool) {
	// 2、判断是否是insert...select,update,delete语句
	var whiteSet = map[string]bool{
		"DeleteStmt": true,
	}
	nodeType := reflect.TypeOf(stmts[0])
	nodeTypeStrs := strings.Split(nodeType.String(), ".")
	var nodeTypeStr = nodeType.String()
	if len(nodeTypeStrs) > 1 {
		nodeTypeStr = nodeTypeStrs[len(nodeTypeStrs)-1]
	}
	if _, exist := whiteSet[nodeTypeStr]; !exist {
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = fmt.Sprintf("free lock DML ticket not support %s sql type，only support delete", nodeTypeStr)
		return true
	}
	return false
}

func (selfService *ticketService) checkFreeLockDMLSQLFormat(ctx context.Context, ticket *shared.Ticket, resp *model.PreCheckTicketResp,
	parseStruct *TicketSqlTextParseResult) (*PrecheckTicketSytaxResult, bool) {
	//1、判断是否多条语句
	if len(parseStruct.sqls) > 1 {
		log.Warn(ctx, "ticket %v contains more than one sql", ticket.TicketId)
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = "SQL format is illegal，free lock DML ticket only support single SQL"
		checkResult := make([]string, len(parseStruct.sqls))
		for i := 0; i < len(parseStruct.sqls); i++ {
			checkResult[i] = "SQL format is illegal，free lock DML ticket only support single SQL"
		}
		return &PrecheckTicketSytaxResult{
			checkResult: checkResult,
		}, false
	}

	// 2、判断是否是insert...select,update,delete语句
	var whiteSet = map[string]bool{
		"UpdateStmt": true,
		"DeleteStmt": true,
		"InsertStmt": true,
	}
	nodeType := reflect.TypeOf(parseStruct.stmts[0])
	nodeTypeStrs := strings.Split(nodeType.String(), ".")
	var nodeTypeStr = nodeType.String()
	if len(nodeTypeStrs) > 1 {
		nodeTypeStr = nodeTypeStrs[len(nodeTypeStrs)-1]
	}
	if _, exist := whiteSet[nodeTypeStr]; !exist {
		log.Warn(ctx, "ticket %v not support %s sql type", ticket.TicketId, nodeTypeStr)
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = fmt.Sprintf("free lock DML ticket not support %s sql type，only support insert...select,update,delete", nodeTypeStr)
		checkResult := make([]string, len(parseStruct.sqls))
		for i := 0; i < len(parseStruct.sqls); i++ {
			checkResult[i] = fmt.Sprintf("free lock DML ticket not support %s sql type，only support insert...select,update,delete", nodeTypeStr)
		}
		return &PrecheckTicketSytaxResult{
			checkResult: checkResult,
		}, false
	}

	// 3、判断每种语句的限制
	tables, err := utils.CheckFreeLockDMLLimit(parseStruct.stmts[0])
	if err != nil {
		log.Warn(ctx, "ticket %v check free lock dml limit error:%v", ticket.TicketId, err)
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = err.Error()
		checkResult := make([]string, len(parseStruct.sqls))
		for i := 0; i < len(parseStruct.sqls); i++ {
			checkResult[i] = fmt.Sprintf("check free lock dml limit error:%v", err)
		}
		return &PrecheckTicketSytaxResult{
			checkResult: checkResult,
		}, false
	}

	// 4、判断是否有非空唯一键或者主键
	ds, err := selfService.getDBDataSource(ctx, ticket.InstanceId, ticket.DbName, ticket.InstanceType)
	if err != nil {
		log.Warn(ctx, "get db datasource error:%v", err)
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = err.Error()
		checkResult := make([]string, len(parseStruct.sqls))
		for i := 0; i < len(parseStruct.sqls); i++ {
			checkResult[i] = fmt.Sprintf("check free lock dml limit error:%v", err)
		}
		return &PrecheckTicketSytaxResult{
			checkResult: checkResult,
		}, false
	}
	//_, _ = selfService.getDBAddress(ctx, ds)
	command := fmt.Sprintf("SELECT DISTINCT TABLE_NAME, INDEX_NAME,NULLABLE,SEQ_IN_INDEX, IF(INDEX_NAME='PRIMARY','Primary',"+
		"IF(NON_UNIQUE=1,IF(INDEX_TYPE='FULLTEXT','FullText',IF(INDEX_TYPE='SPATIAL','Spatial','Normal')),'Unique')) "+
		"as INDEX_TYPE, COLUMN_NAME, SUB_PART,INDEX_COMMENT FROM INFORMATION_SCHEMA.STATISTICS "+
		"WHERE TABLE_SCHEMA='%s' and TABLE_NAME in ('%s') %s;", ticket.DbName, tables[0], mysql.DBW_CONSOLE_DEFAULT_HINT)
	// 这里写tables[0]是因为insert...select语句有可能有2个表,取第0个就是select后面的表，而不是insert后面的表
	req := &datasource.GetTableIndexInfoReq{
		TableName: tables[0],
		Source:    ds,
		Command:   command,
	}
	rresp, err := selfService.GetTableIndexInfo(ctx, ds, req)
	if err != nil {
		log.Warn(ctx, "ticket %v get table index info error %v", ticket.TicketId, err)
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = fmt.Sprintf("error in obtaining table primary key or non empty unique index:%v", err)
		checkResult := make([]string, len(parseStruct.sqls))
		for i := 0; i < len(parseStruct.sqls); i++ {
			checkResult[i] = fmt.Sprintf("error in obtaining table primary key or non empty unique index:%v", err.Error())
		}
		return &PrecheckTicketSytaxResult{
			checkResult: checkResult,
		}, false
	}
	log.Info(ctx, "rresp is %v", rresp)
	/* eg:
	+------------+------------+----------+------------+-------------+----------+---------------+
	| table_name | index_name | NULLABLE | index_type | column_name | sub_part | index_comment |
	+------------+------------+----------+------------+-------------+----------+---------------+
	| students_4 | PRIMARY    |          | Primary    | id          |     NULL |               |
	| students_4 | name_sex   |          | Unique     | name        |     NULL |               |
	| students_4 | name_sex   |          | Unique     | sex         |     NULL |               |
	| students_4 | age        |          | Normal     | age         |     NULL |               |
	+------------+------------+----------+------------+-------------+----------+---------------+
	4 <USER> <GROUP> set (0.00 sec)
	*/
	pkOrUniqKey, isContainPkOrUniqkey := selfService.GetTablePKOrUniqKey(ctx, rresp)
	if !isContainPkOrUniqkey { // 如果不包含,直接报错
		log.Warn(ctx, "ticket %v not contain pk or unique key", ticket.TicketId)
		resp.CheckItems[0].Status = model.PreCheckStatus_Error
		resp.CheckItems[0].Memo = "get empty primary key or a non-null unique index,Please check if the database contains the table or if the table contains a primary key or a non-null unique index "
		checkResult := make([]string, len(parseStruct.sqls))
		for i := 0; i < len(parseStruct.sqls); i++ {
			checkResult[i] = "get empty primary key or a non-null unique index,Please check if the database contains the table or if the table contains a primary key or a non-null unique index "
		}
		return &PrecheckTicketSytaxResult{
			checkResult: checkResult,
		}, false
	}

	// 5、判断如果是update 语句,还需要判断更新的列里面是否包含主键或者非空唯一键
	var cols []string
	if utils.IsSQLTypeUpdate(parseStruct.stmts[0]) {
		switch parseStruct.stmts[0].(type) {
		case *ast.UpdateStmt:
			cols = utils.GetAffectedColumn(parseStruct.stmts[0].(*ast.UpdateStmt))
		default:
			log.Warn(ctx, "wrong stmts type")
		}
		for _, val := range cols {
			if selfService.IsColInPk(val, pkOrUniqKey) {
				log.Warn(ctx, "ticket %v update cols contain pk or unique key", ticket.TicketId)
				resp.CheckItems[0].Status = model.PreCheckStatus_Error
				resp.CheckItems[0].Memo = "the Update statement contains a primary key or a non empty unique index field, which is not allowed to be executed"
				checkResult := make([]string, len(parseStruct.sqls))
				for i := 0; i < len(parseStruct.sqls); i++ {
					checkResult[i] = "the Update statement contains a primary key or a non empty unique index field, which is not allowed to be executed"
				}
				return &PrecheckTicketSytaxResult{
					checkResult: checkResult,
				}, false
			}
		}
	}
	log.Info(ctx, "ticket %v pkOrUniqKey is %s", ticket.TicketId, utils.Show(pkOrUniqKey))
	resp.CheckItems[0].Status = model.PreCheckStatus_Pass
	resp.CheckItems[0].Memo = "Pass"
	checkResult := make([]string, len(parseStruct.sqls))
	for i := 0; i < len(parseStruct.sqls); i++ {
		checkResult[i] = "Pass"
	}
	return &PrecheckTicketSytaxResult{
		checkResult: checkResult,
	}, true
}

func (selfService *ticketService) checkPgFreeLockDMLSQLFormat(ctx context.Context, ticket *shared.Ticket, resp *model.PreCheckTicketResp,
	parseStruct *TicketSqlTextParseResult) (*PrecheckTicketSytaxResult, bool) {
	// TODO Pg的无锁DML语法检查
	return &PrecheckTicketSytaxResult{
		checkResult: []string{
			"Pass",
		},
	}, true
}

func (selfService *ticketService) checkNormalSQLPermission(ctx context.Context, ticket *shared.Ticket, parseStruct *TicketSqlTextParseResult) (*PrecheckTicketPermissionResult, error) {
	var res = &PrecheckTicketPermissionResult{
		checkResult: GetSliceAllSameString(len(parseStruct.sqls), "Pass"),
	}
	// 这块这一期需要进行一些改造
	for idx, statementNode := range parseStruct.stmts {
		switch stmt := statementNode.(type) {
		case *ast.UpdateStmt:
			allUserPrivilege, err := selfService.getUserAllInstancePrivilege(ctx, ticket.CreateUserId, ticket.InstanceId, ticket.TenantId,
				model.DbwPrivilegeType_CORRECT.String())
			if err != nil {
				log.Warn(ctx, "获取用户权限失败，err：%v", err)
				res.checkResult[idx] = fmt.Sprintf("failed to get user permissions %v", err)
				continue
			}
			if err = selfService.checkUpdateStmt(stmt, allUserPrivilege, ticket.DbName, ticket.InstanceId); err != nil {
				res.checkResult[idx] = fmt.Sprintf("failed to check user permissions %v", err)
			}
		case *ast.DeleteStmt:
			allUserPrivilege, err := selfService.getUserAllInstancePrivilege(ctx, ticket.CreateUserId, ticket.InstanceId, ticket.TenantId,
				model.DbwPrivilegeType_CORRECT.String())
			if err != nil {
				log.Warn(ctx, "获取用户权限失败，err：%v", err)
				res.checkResult[idx] = fmt.Sprintf("failed to get user permissions %v", err)
				continue
			}
			if err = selfService.checkDeleteStmt(stmt, allUserPrivilege, ticket.DbName, ticket.InstanceId); err != nil {
				res.checkResult[idx] = fmt.Sprintf("failed to check user permissions %v", err)
			}
		case *ast.InsertStmt:
			allUserPrivilege, err := selfService.getUserAllInstancePrivilege(ctx, ticket.CreateUserId, ticket.InstanceId, ticket.TenantId,
				model.DbwPrivilegeType_CORRECT.String())
			if err != nil {
				log.Warn(ctx, "获取用户权限失败，err：%v", err)
				res.checkResult[idx] = fmt.Sprintf("failed to get user permissions %v", err)
				continue
			}
			if err = selfService.checkInsertStmt(stmt, allUserPrivilege, ticket.DbName, ticket.InstanceId); err != nil {
				res.checkResult[idx] = fmt.Sprintf("failed to check user permissions %v", err)
			}
		case *ast.AlterTableStmt:
			allUserPrivilege, err := selfService.getUserAllInstancePrivilege(ctx, ticket.CreateUserId, ticket.InstanceId, ticket.TenantId,
				model.DbwPrivilegeType_STRUCTURE_CHANGE.String())
			if err != nil {
				log.Warn(ctx, "获取用户权限失败，err：%v", err)
				res.checkResult[idx] = fmt.Sprintf("failed to get user permissions %v", err)
				continue
			}
			if err = selfService.checkAlterTableStmt(stmt, allUserPrivilege, ticket.DbName, ticket.InstanceId); err != nil {
				res.checkResult[idx] = fmt.Sprintf("failed to check user permissions %v", err)
			}
		case *ast.CreateTableStmt:
			allUserPrivilege, err := selfService.getUserAllInstancePrivilege(ctx, ticket.CreateUserId, ticket.InstanceId, ticket.TenantId,
				model.DbwPrivilegeType_STRUCTURE_CHANGE.String())
			if err != nil {
				log.Warn(ctx, "获取用户权限失败，err：%v", err)
				res.checkResult[idx] = fmt.Sprintf("failed to get user permissions %v", err)
				continue
			}
			if err = selfService.checkCreateTableStmt(stmt, allUserPrivilege, ticket.DbName, ticket.InstanceId); err != nil {
				res.checkResult[idx] = fmt.Sprintf("failed to check user permissions %v", err)
			}
		case *ast.DropTableStmt:
			allUserPrivilege, err := selfService.getUserAllInstancePrivilege(ctx, ticket.CreateUserId, ticket.InstanceId, ticket.TenantId,
				model.DbwPrivilegeType_STRUCTURE_CHANGE.String())
			if err != nil {
				log.Warn(ctx, "获取用户权限失败，err：%v", err)
				res.checkResult[idx] = fmt.Sprintf("failed to get user permissions %v", err)
				continue
			}
			if err = selfService.checkDropTableStmt(stmt, allUserPrivilege, ticket.DbName, ticket.InstanceId); err != nil {
				res.checkResult[idx] = fmt.Sprintf("failed to check user permissions %v", err)
			}
		case *ast.TruncateTableStmt:
			allUserPrivilege, err := selfService.getUserAllInstancePrivilege(ctx, ticket.CreateUserId, ticket.InstanceId, ticket.TenantId,
				model.DbwPrivilegeType_STRUCTURE_CHANGE.String())
			if err != nil {
				log.Warn(ctx, "获取用户权限失败，err：%v", err)
				res.checkResult[idx] = fmt.Sprintf("failed to get user permissions %v", err)
				continue
			}
			if err = selfService.checkTruncateTableStmt(stmt, allUserPrivilege, ticket.DbName, ticket.InstanceId); err != nil {
				res.checkResult[idx] = fmt.Sprintf("failed to check user permissions %v", err)
			}
		case *ast.RepairTableStmt:
			allUserPrivilege, err := selfService.getUserAllInstancePrivilege(ctx, ticket.CreateUserId, ticket.InstanceId, ticket.TenantId,
				model.DbwPrivilegeType_STRUCTURE_CHANGE.String())
			if err != nil {
				log.Warn(ctx, "获取用户权限失败，err：%v", err)
				res.checkResult[idx] = fmt.Sprintf("failed to get user permissions %v", err)
				continue
			}
			if err = selfService.checkRepairTableStmt(stmt, allUserPrivilege, ticket.DbName, ticket.InstanceId); err != nil {
				res.checkResult[idx] = fmt.Sprintf("failed to check user permissions %v", err)
			}
		case *ast.RenameTableStmt:
			allUserPrivilege, err := selfService.getUserAllInstancePrivilege(ctx, ticket.CreateUserId, ticket.InstanceId, ticket.TenantId,
				model.DbwPrivilegeType_STRUCTURE_CHANGE.String())
			if err != nil {
				log.Warn(ctx, "获取用户权限失败，err：%v", err)
				res.checkResult[idx] = fmt.Sprintf("failed to get user permissions %v", err)
				continue
			}
			if err = selfService.checkRenameTableStmt(stmt, allUserPrivilege, ticket.DbName, ticket.InstanceId); err != nil {
				res.checkResult[idx] = fmt.Sprintf("failed to check user permissions %v", err)
			}
		case *ast.CreateIndexStmt:
			allUserPrivilege, err := selfService.getUserAllInstancePrivilege(ctx, ticket.CreateUserId, ticket.InstanceId, ticket.TenantId,
				model.DbwPrivilegeType_STRUCTURE_CHANGE.String())
			if err != nil {
				log.Warn(ctx, "获取用户权限失败，err：%v", err)
				res.checkResult[idx] = fmt.Sprintf("failed to get user permissions %v", err)
				continue
			}
			if err = selfService.checkCreateIndexStmt(stmt, allUserPrivilege, ticket.DbName, ticket.InstanceId); err != nil {
				res.checkResult[idx] = fmt.Sprintf("failed to check user permissions %v", err)
			}
		case *ast.DropIndexStmt:
			allUserPrivilege, err := selfService.getUserAllInstancePrivilege(ctx, ticket.CreateUserId, ticket.InstanceId, ticket.TenantId,
				model.DbwPrivilegeType_STRUCTURE_CHANGE.String())
			if err != nil {
				log.Warn(ctx, "获取用户权限失败，err：%v", err)
				res.checkResult[idx] = fmt.Sprintf("failed to get user permissions %v", err)
				continue
			}
			if err = selfService.checkDropIndexStmt(stmt, allUserPrivilege, ticket.DbName, ticket.InstanceId); err != nil {
				res.checkResult[idx] = fmt.Sprintf("failed to check user permissions %v", err)
			}
		case *ast.CreateDatabaseStmt:
			allUserPrivilege, err := selfService.getUserAllInstancePrivilege(ctx, ticket.CreateUserId, ticket.InstanceId, ticket.TenantId,
				model.DbwPrivilegeType_STRUCTURE_CHANGE.String())
			if err != nil {
				log.Warn(ctx, "获取用户权限失败，err：%v", err)
				res.checkResult[idx] = fmt.Sprintf("failed to get user permissions %v", err)
				continue
			}
			if err = selfService.checkCreateDatabaseStmt(stmt, allUserPrivilege, ticket.DbName, ticket.InstanceId); err != nil {
				res.checkResult[idx] = fmt.Sprintf("failed to check user permissions %v", err)
			}
		case *ast.DropDatabaseStmt:
			allUserPrivilege, err := selfService.getUserAllInstancePrivilege(ctx, ticket.CreateUserId, ticket.InstanceId, ticket.TenantId,
				model.DbwPrivilegeType_STRUCTURE_CHANGE.String())
			if err != nil {
				log.Warn(ctx, "获取用户权限失败，err：%v", err)
				res.checkResult[idx] = fmt.Sprintf("failed to get user permissions %v", err)
				continue
			}
			if err = selfService.checkDropDatabaseStmt(stmt, allUserPrivilege, ticket.DbName, ticket.InstanceId); err != nil {
				res.checkResult[idx] = fmt.Sprintf("failed to check user permissions %v", err)
			}
		case *ast.AlterDatabaseStmt:
			allUserPrivilege, err := selfService.getUserAllInstancePrivilege(ctx, ticket.CreateUserId, ticket.InstanceId, ticket.TenantId,
				model.DbwPrivilegeType_STRUCTURE_CHANGE.String())
			if err != nil {
				log.Warn(ctx, "获取用户权限失败，err：%v", err)
				res.checkResult[idx] = fmt.Sprintf("failed to get user permissions %v", err)
				continue
			}
			if err = selfService.checkAlterDatabaseStmt(stmt, allUserPrivilege, ticket.DbName, ticket.InstanceId); err != nil {
				res.checkResult[idx] = fmt.Sprintf("failed to check user permissions %v", err)
			}
		case *ast.CreateFunctionStmt:
		case *ast.DropFunctionStmt:

		case *ast.CreateTriggerStmt:

		//case *ast.DropTriggerStmt:
		//	return nil
		case *ast.CreateViewStmt:

		//case *ast.DropViewStmt:
		//	return nil
		case *ast.AlterViewStmt:

		case *ast.CreateProcedureStmt:

		case *ast.DropProcedureStmt:

		default:

		}
	}
	return res, nil
}

func (selfService *ticketService) checkPgSQLPermission(ctx context.Context, ticket *shared.Ticket, parseStruct *TicketSqlTextParseResult) (*PrecheckTicketPermissionResult, error) {
	ds := &shared.DataSource{Type: shared.Postgres, Db: ticket.DbName, InstanceId: ticket.InstanceId}
	var res = &PrecheckTicketPermissionResult{
		checkResult: GetSliceAllSameString(len(parseStruct.sqls), "Pass"),
	}

	for idx, sql := range parseStruct.sqls {
		parseType := utils.GetCommandTypeBySourceType(ds, sql)
		if parseType.PrivilegeType == utils.None {
			res.checkResult[idx] = "security control instance not support this type of SQL"
			continue
		}
		// 如果有instance权限，则直接通过
		instancePrivilege, _ := selfService.workflowDal.GetUserInstancePrivilege(ctx, ticket.CreateUserId, ticket.InstanceId, ticket.TenantId, parseType.PrivilegeType)
		if instancePrivilege != nil && len(*instancePrivilege) > 0 && (*instancePrivilege)[0].InstanceId == ticket.InstanceId {
			continue
		}
		pri, parseErr := selfService.pgPriSvc.CheckByCommandTypeForPostgres(ctx, sql, ds, parseType)
		// 不为空,说明sql中存在库表对象缺失权限
		if pri != "" {
			res.checkResult[idx] = consts.ErrorWithParam(model.ErrorCode_SqlPrivilegeCheckFailed, pri).Error()
			continue
		}
		if parseErr != nil {
			log.Warn(ctx, "get pg sql permission error %v", parseErr)
			res.checkResult[idx] = parseErr.Error()
			continue
		}
	}
	return res, nil
}

func (selfService *ticketService) getCountSQL(ctx context.Context, ticket *shared.Ticket, parseStruct *TicketSqlTextParseResult) []string {
	var sqlText []string
	// 循环所有的语句执行命令,求explain的语句
	for _, val := range parseStruct.stmts {
		nodeType := reflect.TypeOf(val)                       // nodeType is *ast.UpdateStmt
		nodeTypeStrs := strings.Split(nodeType.String(), ".") // nodeTypeStrs is ["*ast","UpdateStmt"] , nodeType.String() is "*ast.UpdateStmt"
		var nodeTypeStr = nodeType.String()
		if len(nodeTypeStrs) > 1 {
			nodeTypeStr = nodeTypeStrs[len(nodeTypeStrs)-1]
		}
		// 如果是AlterTableStmt\TruncateTableStmt\RenameTableStmt\RepairTableStmt语句, 则需要计算一下影响的数据记录行数
		// Drop语句由于有可能出现If Exists,所以不能直接用表名字来计算行数,表有可能不存在
		switch nodeTypeStr {
		case "AlterTableStmt", "TruncateTableStmt", "RenameTableStmt", "RepairTableStmt":
			tables := selfService.getTables(val)
			tableName := tables[0].Name.String()
			dbName := tables[0].Schema.String()
			// 如果dbName为空,则使用ticket的dbName,否则直接使用解析出来的dbName
			if strings.TrimSpace(dbName) == "" {
				dbName = ticket.DbName
			}
			sqlText = append(sqlText, fmt.Sprintf("select count(*) from %s.%s", dbName, tableName))
		case "UpdateStmt", "DeleteStmt", "InsertStmt":
			sqlText = append(sqlText, val.Text())
		default:
			sqlText = append(sqlText, "select 1;")
		}
	}
	log.Info(ctx, "ticket %v get count sql text is %v", ticket.TicketId, utils.Show(sqlText))
	return sqlText
}

func (selfService *ticketService) getPgCountSQL(ctx context.Context, ticket *shared.Ticket, parseStruct *TicketSqlTextParseResult) []string {
	var sqlText []string
	for idx, stmt := range parseStruct.pgStmts {
		res := utils.GetCommandTypeForPostgresql(stmt)
		if res.CommandType == "InsertStmt" || res.CommandType == "DeleteStmt" || res.CommandType == "UpdateStmt" {
			sqlText = append(sqlText, parseStruct.sqls[idx])
		} else {
			sqlText = append(sqlText, "select 1;")
		}
		// pg Alter语句不好搞,先取0吧
		//} else if (res.CommandType == "DropStmt" || res.CommandType == "AlterTableStmt" || res.CommandType == "RenameStmt") &&
		//	res.Granularity == utils.Table {
		//	// 这里获取一下表
		//	getTablesFromPostgres
		//	sqlText = append(sqlText, cmd.Text())
		//}
	}
	return sqlText
}

func (selfService *ticketService) IsEnableDDLDirectExecCommandsType(sql string) bool {
	// 0、解析SQL
	p := parser.New()
	stmts, _, err := p.Parse(sql, "utf8", "")
	if err != nil || len(stmts) != 1 {
		return false
	}
	// 2、判断是否是合法语句
	// nodeType is *ast.AlterTableStmt
	nodeType := reflect.TypeOf(stmts[0])
	// nodeTypeStrs is ["*ast","AlterTableStmt"] , nodeType.String() is "*ast.AlterTableStmt"
	nodeTypeStrs := strings.Split(nodeType.String(), ".")
	var typeStr string
	if len(nodeTypeStrs) > 1 {
		typeStr = nodeTypeStrs[len(nodeTypeStrs)-1]
	}
	// 这些里面是需要直接执行的语句
	if _, directExecFlag := EnabledFreeLockDDLDirectExecCommandsType[typeStr]; directExecFlag {
		return true
	}
	return false
}

func (selfService *ticketService) IsSQLTableContainsHTAPEngine(ctx context.Context, parseStruct *TicketSqlTextParseResult,
	ds *shared.DataSource) (*PrecheckTicketSytaxResult, bool) {
	res := make([]string, len(parseStruct.sqls))
	var IsValidSQLs = true
	for idx, val := range parseStruct.stmts {
		res[idx] = "Pass"
		tbls := selfService.getTables(val)
		if len(tbls) > 0 {
			tableName := tbls[0].Name.String()
			if parseStruct.sqls[idx] == "show tables;" {

			}
			info, _ := selfService.ds.GetCreateTableInfo(ctx, ds, fmt.Sprintf("%v.%v", ds.Db, tableName))
			//if err != nil {
			//	log.Warn(ctx, "get create table info error %v", err)
			//	res[idx] = fmt.Sprintf("this sql contains htap table %v,unsupport execute", tableName)
			//	IsValidSQLs = false
			//}
			// 如果包含SECONDARY_ENGINE=HTAP,则说明是HTAP表,则不允许执行SQL语句,提示预检查失败
			if strings.Contains(info, strings.ToLower("SECONDARY_ENGINE=HTAP")) ||
				strings.Contains(parseStruct.sqls[idx], strings.ToLower("SECONDARY_ENGINE=HTAP")) {
				log.Warn(ctx, "this sql contains htap table %v,unsupported execute", tableName)
				res[idx] = fmt.Sprintf("this sql contains htap table %v,unsupport execute", tableName)
				IsValidSQLs = false
			}
		}
	}
	return &PrecheckTicketSytaxResult{
		checkResult: res,
	}, IsValidSQLs
}

func (selfService *ticketService) IsTicketCanEdit(ctx context.Context, ticket *dao.Ticket) bool {
	// 1.获取审批节点信息
	flowNodes, err := selfService.approvalFlowService.DescribeApprovalFlowLogs(ctx, ticket.FlowConfigId, ticket.ApprovalFlowId, ticket.InstanceId, ticket.TenantId, ticket.CreateUserId)
	if err != nil {
		log.Warn(ctx, "DescribeApprovalFlowLogs error: %s", err.Error())
		return false
	}
	for _, flowNode := range flowNodes {
		if flowNode.Status == model.FlowNodeStatus_Pass {
			// 如果有通过的节点,则不允许编辑
			return false
		}
	}
	return true
}
