package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-sql-parser/ast"

	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
)

const (
	TicketUndo          = 0
	TicketPreCheck      = 1
	TicketPreCheckError = 2
	TicketExamine       = 3
	TicketCancel        = 4
	TicketReject        = 5
	TicketWaitExecute   = 6
	TicketExecute       = 7
	TicketFinished      = 8
	TicketError         = 9
)

const OnlineDDlCheckNum = 10

// 预检查项，英文名
const (
	PreCheckSyntax       = "Syntax"
	PreCheckPermission   = "Permission"
	PreCheckExplain      = "Explain"
	PreCheckSecurityRule = "SecurityRule"
)

var CheckItemNames = []string{PreCheckSyntax, PreCheckPermission, PreCheckExplain, PreCheckSecurityRule}
var CheckItemTypes = []model.ItemType{model.ItemType_PreCheckSyntax, model.ItemType_PreCheckPermission, model.ItemType_PreCheckExplain, model.ItemType_PreCheckSecurityRule}

// 预检查项，中文名
const (
	PreCheckSyntaxCn       = "语法检查"
	PreCheckPermissionCn   = "权限检查"
	PreCheckExplainCn      = "影响行数(系统统计值,实际影响行数仍然以SQL执行为准!)"
	PreCheckSecurityRuleCn = "安全规则检查"
)

const (
	PUT  = "PUT"
	POST = "POST"
	GET  = "GET"
)

const (
	reject = 0
	pass   = 1
	//cancel = 2
)

const (
	BPMAuthTypeBasicAuth = "BasicAuth"
	BPMAuthTypeJWT       = "Jwt"
)

type BpmData struct {
	Id       int64  `json:"id"`
	Creator  string `json:"creator"`
	Status   string `json:"status"`
	Ctime    string `json:"ctime"`
	Utime    string `json:"utime"`
	Finished int    `json:"finished"`
	Content  string `json:"content"`
}

type BpmLogResponse struct {
	Data    []*BpmData `json:"data"`
	Message string     `json:"message"`
	Code    int        `json:"code"`
}

type BpmConfigData struct {
	Assignee           []string `json:"assignee"`
	InstanceId         string   `json:"instance_id"`
	ExecSQL            string   `json:"exec_sql"`
	DBName             string   `json:"db_name"`
	Background         string   `json:"background"`
	JwtToken           string   `json:"jwt_token"`
	TicketID           string   `json:"ticket_id"`
	TicketType         string   `json:"ticket_type"`
	TenantID           string   `json:"tenant_id"`
	XVolcRegion        string   `json:"x_volc_region"`        // 字节云服务的region
	XBytecloudEndpoint string   `json:"x_bytecloud_endpoint"` // 字节云服务账号的endpoint
	XBytecloudEnv      string   `json:"x_bytecloud_env"`      // 字节云服务账号的环境变量
}

type BpmResponse struct {
	Data    BpmData `json:"data"`
	Message string  `json:"message"`
	Code    int     `json:"code"`
}

type RequestRecord struct {
	action    int
	step      int
	totalStep int
	approval  string
	comment   string
}

type UserPrivilege struct {
	instancePrivilege *[]*dao.UserInstancePrivilege
	databasePrivilege *[]*dao.UserDatabasePrivilege
	tablePrivilege    *[]*dao.UserTablePrivilege
	columnPrivilege   *[]*dao.UserColumnPrivilege
}

func ChangeTicketType(ticket *dao.Ticket) *shared.Ticket {
	var (
		batchSize            int64
		isReplicaDelayEnable bool
		replicaDelaySeconds  int32
		sleepTimeMs          int32
	)
	if ticket.Extra != nil && ticket.Extra["BatchSize"] != nil {
		batchSize = int64(ticket.Extra["BatchSize"].(float64))
	}
	if ticket.Extra != nil && ticket.Extra["IsEnableDelayCheck"] != nil {
		isReplicaDelayEnable = ticket.Extra["IsEnableDelayCheck"].(bool)
	}
	if ticket.Extra != nil && ticket.Extra["ReplicaDelaySeconds"] != nil {
		replicaDelaySeconds = int32(ticket.Extra["ReplicaDelaySeconds"].(float64))
	}
	if ticket.Extra != nil && ticket.Extra["SleepTimeMs"] != nil {
		sleepTimeMs = int32(ticket.Extra["SleepTimeMs"].(float64))
	}

	return &shared.Ticket{
		TicketId:             ticket.TicketId,
		FlowConfigId:         ticket.FlowConfigId,
		ApprovalFlowId:       ticket.ApprovalFlowId,
		WorkflowId:           ticket.WorkflowId,
		TicketType:           int32(ticket.TicketType),
		TicketStatus:         int32(ticket.TicketStatus),
		FlowStep:             ticket.FlowStep,
		ExecuteType:          int32(ticket.ExecuteType),
		CreateTime:           ticket.CreateTime,
		UpdateTime:           ticket.UpdateTime,
		CreateUserId:         ticket.CreateUserId,
		TenantId:             ticket.TenantId,
		CurrentUserIds:       ticket.CurrentUserIds,
		InstanceId:           ticket.InstanceId,
		InstanceType:         shared.DataSourceType(shared.DataSourceType_value[ticket.InstanceType]),
		SqlText:              ticket.SqlText,
		Description:          ticket.Description,
		DbName:               ticket.DbName,
		TaskId:               ticket.TaskId,
		ExecStartTime:        int32(ticket.ExecutableStartTime),
		ExecEndTime:          int32(ticket.ExecutableEndTime),
		BatchSize:            batchSize,
		IsReplicaDelayEnable: isReplicaDelayEnable,
		ReplicaDelaySeconds:  replicaDelaySeconds,
		SleepTimeMs:          sleepTimeMs,
		CreateFrom:           ticket.CreatedFrom,
		ArchiveConfig:        ticket.DataArchiveConfig,
		Memo:                 ticket.Memo,
		AffectedRows:         ticket.AffectedRows,
		Submitted:            int32(ticket.Submitted),
	}
}

func AnalyzeBpmResponse(bpmRes BpmResponse) error {
	// 为了增强健壮性，对于调用bpm的结果，如果bpm工单已经完成，那么认为这次访问成功
	if bpmRes.Code == -1 && !isFinished(bpmRes.Message) {
		errMsg := "请求bpm失败：" + bpmRes.Message
		return fmt.Errorf(errMsg)
	}
	return nil
}

func isFinished(message string) bool {
	return strings.Contains(message, "finished") ||
		strings.Contains(message, "canceled") ||
		strings.Contains(message, "not found next status step") ||
		strings.Contains(message, "has been changed")
}

func AnalyzeBpmLogResponse(bpmRes BpmLogResponse) error {
	if bpmRes.Code == -1 {
		errMsg := "请求bpm失败：" + bpmRes.Message
		return fmt.Errorf(errMsg)
	}
	return nil
}

func GetBpmLogResponse(ctx context.Context, resp *http.Response) (BpmLogResponse, error) {
	var bpmRes BpmLogResponse
	resBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Warn(ctx, "ioutil readAll : "+err.Error())
		return bpmRes, err
	}
	if err = json.Unmarshal(resBody, &bpmRes); err != nil {
		errMsg := string(resBody) + " json反序列化失败，原因是：" + err.Error()
		return bpmRes, fmt.Errorf(errMsg)
	}
	return bpmRes, nil
}

func GetBpmResponse(ctx context.Context, resp *http.Response) (BpmResponse, error) {
	var bpmRes BpmResponse
	resBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Warn(ctx, "ioutil readAll : "+err.Error())
		return bpmRes, err
	}
	if err = json.Unmarshal(resBody, &bpmRes); err != nil {
		errMsg := string(resBody) + " json反序列化失败，原因是：" + err.Error()
		return bpmRes, fmt.Errorf(errMsg)
	}
	return bpmRes, nil
}

func formatActionRecordBody(rr RequestRecord) map[string]interface{} {
	switch rr.action {
	case reject:
		return formatRejectRecordBody(rr.approval)
	case pass:
		return formatPassRecordBody(rr.step, rr.totalStep, rr.approval)
	}
	return make(map[string]interface{})
}

func formatPassRecordBody(step int, totalStep int, approval string) map[string]interface{} {
	reqBody := make(map[string]interface{})
	if step == totalStep {
		reqBody["status"] = "end"
	} else {
		reqBody["status"] = fmt.Sprintf("step%d", step+1)
	}
	reqBody["op_key"] = "next"
	opData := make(map[string]interface{})
	opData["approval"] = approval
	reqBody["op_data"] = opData
	return reqBody
}

func formatRejectRecordBody(approval string) map[string]interface{} {
	reqBody := make(map[string]interface{})
	opData := make(map[string]interface{})
	opData["approval"] = approval
	reqBody["status"] = "reject"
	reqBody["op_key"] = "reject"
	reqBody["op_data"] = opData
	return reqBody
}

func formatCreateRecordBody(configId int64) map[string]interface{} {
	reqBody := make(map[string]interface{})
	configBody := make(map[string]interface{})
	configBody["approval_assignee"] = "<EMAIL>"
	configBody["approval2_assignee"] = "<EMAIL>"

	reqBody["workflow_config_id"] = configId
	reqBody["config"] = configBody
	return reqBody
}

func formatCreateRecordBodyForJWT(configId int64, configData BpmConfigData) map[string]interface{} {
	reqBody := make(map[string]interface{})
	configBody := make(map[string]interface{})
	// workflow_config_id参数和config参数必选
	// 参考:https://cloud.bytedance.net/docs/bpm/docs/63c0be2b5433bd022b9cb751/640ae4e59b8c08021e972a5a?x-resource-account=public#f9757e3d：
	configBody["safe_transfer_assignees"] = configData.Assignee // 只有在这里加了的审批人，才能在下面的reviewing_assignee中审批
	configBody["reviewing_assignee"] = configData.Assignee

	configBody["assignee"] = configData.Assignee
	//configBody["assignee"] = "<EMAIL>"
	configBody["instance_id"] = configData.InstanceId
	configBody["exec_sql"] = configData.ExecSQL
	configBody["background"] = configData.Background
	configBody["db_name"] = configData.DBName
	configBody["jwt_token"] = configData.JwtToken
	configBody["ticket_id"] = configData.TicketID
	configBody["tenant_id"] = configData.TenantID

	configBody["volc_region"] = configData.XVolcRegion
	configBody["bytecloud_endpoint"] = configData.XBytecloudEndpoint
	configBody["bytecloud_env"] = configData.XBytecloudEnv

	reqBody["workflow_config_id"] = configId
	reqBody["config"] = configBody
	return reqBody
}

func arrToMap(strs *[]string) *map[string]bool {
	res := make(map[string]bool)
	if strs == nil {
		return &res
	}
	for _, value := range *strs {
		res[value] = true
	}
	return &res
}

func dealRows(rows string) string {
	if rows == "" || strings.ToUpper(rows) == "NULL" {
		rows = "0"
		return rows
	}
	return rows
}

func GenerateExecWindow(start int64, end int64) string {
	// 将毫秒转换为 Duration
	durationStart := time.Duration(start) * time.Millisecond
	// 将 Duration 转换为时分秒
	hoursStart := int(durationStart.Hours())
	minutesStart := int(durationStart.Minutes()) % 60

	durationEnd := time.Duration(end) * time.Millisecond
	hoursEnd := int(durationEnd.Hours())
	minutesEnd := int(durationEnd.Minutes()) % 60

	// 格式化为时分秒字符串
	return fmt.Sprintf("%02d:%02d-%02d:%02d", hoursStart, minutesStart, hoursEnd, minutesEnd)
}

func StructToMap(obj interface{}) map[string]interface{} {
	var data = make(map[string]interface{}, 0)
	if obj == nil {
		return data
	}
	jsonData, _ := json.Marshal(obj)
	json.Unmarshal(jsonData, &data)

	return data
}

func StructToJson(obj interface{}) string {
	jsonData, _ := json.Marshal(obj)
	return string(jsonData)
}

func GetIndexInfoRespSharedToDatasource(resp *shared.GetIndexInfoResp) *datasource.GetTableInfoIndexResp {
	var res []*datasource.TableIndexInfo
	for _, val := range resp.TableIndexInfo {
		item := &datasource.TableIndexInfo{
			TableName:    val.TableName,
			IndexName:    val.IndexName,
			Nullable:     val.Nullable,
			SeqInIndex:   int(val.SeqInIndex),
			IndexType:    val.IndexType,
			ColumnName:   val.ColumnName,
			SubPart:      val.SubPart,
			IndexComment: val.IndexComment,
		}
		res = append(res, item)
	}
	return &datasource.GetTableInfoIndexResp{
		TableIndexInfo: res,
	}
}

func GetIndexValueRespSharedToDatasource(resp *shared.GetIndexValueResp) *datasource.GetIndexValueResp {
	var res []*datasource.TableIndexValue
	for _, val := range resp.TableIndexValue {
		item := &datasource.TableIndexValue{
			IndexValue: val.IndexValue,
			IndexName:  val.IndexName,
		}
		res = append(res, item)
	}
	return &datasource.GetIndexValueResp{
		TableIndexValue: res,
	}
}

var EnabledNormalDDLCommandsType = map[string]bool{
	"CreateDatabaseStmt": true,
	"AlterDatabaseStmt":  true,
	"DropDatabaseStmt":   true,

	"CreateFunctionStmt": true,
	"DropFunctionStmt":   true,

	"CreateTriggerStmt": true,
	"DropTriggerStmt":   true,

	"CreateProcedureStmt": true,
	"DropProcedureStmt":   true,

	"CreateIndexStmt": true,
	"DropIndexStmt":   true,

	"CreateTableStmt":   true,
	"AlterTableStmt":    true,
	"RenameTableStmt":   true,
	"DropTableStmt":     true,
	"RepairTableStmt":   true,
	"TruncateTableStmt": true,

	"CreateViewStmt": true,
	"AlterViewStmt":  true,
	"DropViewStmt":   true,
}

var EnabledNormalCommandsType = map[string]bool{
	// 普通DML语句
	"UpdateStmt": true,
	"DeleteStmt": true,
	"InsertStmt": true,

	// 普通DDL 语句
	"CreateDatabaseStmt": true,
	"AlterDatabaseStmt":  true,
	"DropDatabaseStmt":   true,

	"CreateFunctionStmt": true,
	"DropFunctionStmt":   true,

	"CreateTriggerStmt": true,
	"DropTriggerStmt":   true,

	"CreateProcedureStmt": true,
	"DropProcedureStmt":   true,

	"CreateIndexStmt": true,
	"DropIndexStmt":   true,

	"CreateTableStmt":   true,
	"AlterTableStmt":    true,
	"RenameTableStmt":   true,
	"DropTableStmt":     true,
	"RepairTableStmt":   true,
	"TruncateTableStmt": true,

	"CreateViewStmt": true,
	"AlterViewStmt":  true,
	"DropViewStmt":   true,
}

var EnabledNormalCommandsTypeForMultiCloud = map[string]bool{
	// 普通DML语句
	"UpdateStmt": true,
	"DeleteStmt": true,
	"InsertStmt": true,
}

var EnabledFreeLockDDLCommandsType = map[string]bool{
	"AlterTableStmt":  true,
	"CreateIndexStmt": true,
	"DropIndexStmt":   true,
}

// EnabledFreeLockDDLDirectExecCommandsType 可以直接执行的DDL语句
var EnabledFreeLockDDLDirectExecCommandsType = map[string]bool{
	"CreateDatabaseStmt": true,
	"AlterDatabaseStmt":  true,
	"DropDatabaseStmt":   true,

	"CreateFunctionStmt": true,
	"DropFunctionStmt":   true,

	"CreateTriggerStmt": true,
	"DropTriggerStmt":   true,

	"CreateProcedureStmt": true,
	"DropProcedureStmt":   true,

	"CreateTableStmt": true,
	//"AlterTableStmt":    true,
	"RenameTableStmt":   true,
	"DropTableStmt":     true,
	"RepairTableStmt":   true,
	"TruncateTableStmt": true,

	"CreateViewStmt": true,
	"AlterViewStmt":  true,
	"DropViewStmt":   true,
}

var EnabledExplainCommandsType = map[string]bool{
	// 普通DML语句
	"UpdateStmt": true,
	"DeleteStmt": true,
	"InsertStmt": true,
}

func IsEnableNormalSQLChangeTicket(dsType shared.DataSourceType) bool {
	return dsType == shared.MySQL || dsType == shared.VeDBMySQL || dsType == shared.Postgres || dsType == shared.MetaMySQL || dsType == shared.MySQLSharding
}

func IsEnableFreeLockSQLChangeTicket(dsType shared.DataSourceType) bool {
	return dsType == shared.MySQL || dsType == shared.VeDBMySQL || dsType == shared.MetaMySQL
}

func IsEnableOnlineDDLTicket(dsType shared.DataSourceType) bool {
	return dsType == shared.MySQL || dsType == shared.VeDBMySQL
}

// IsEnableDataCleanTicket 是否支持数据清理工单,先支持mysql和vedb
func IsEnableDataCleanTicket(dsType shared.DataSourceType) bool {
	return dsType == shared.MySQL || dsType == shared.VeDBMySQL
}

func GetJwtToken(ctx context.Context) string {
	var (
		extra    = fwctx.GetExtra(ctx)
		jwtToken string
	)
	if _, ok := extra["X-Jwt-Token"]; ok {
		jwtToken = strings.Split(extra["X-Jwt-Token"], ",")[0] // 这里取第一个
		log.Info(ctx, "jwt token from extra is %v", jwtToken)
	}
	return jwtToken
}

func GetJwtTokenForMultiCloud(ctx context.Context) string {
	var (
		extra    = fwctx.GetExtra(ctx)
		jwtToken string
	)
	// 小基架的直接从这个header里面获取,多云的也可以从这个里面取,取不到的话，再从下面取
	if _, ok := extra["X-Jwt-Token"]; ok {
		jwtToken = strings.Split(extra["X-Jwt-Token"], ",")[0] // 这里取第一个
		log.Info(ctx, "jwt token from extra is %v", jwtToken)
	}
	// 多云的,暂时不太清楚为什么header里面有时候没有x-jwt-token,重新从X-Jwt-Dbw获取一下jwt-token
	log.Info(ctx, "extra is %v", extra)
	if jwtToken == "" {
		log.Info(ctx, "jwt token is empty,this is a multi cloud env,not sinf env,try to get jwt token from extra x-jwt-dbw")
		if _, ok := extra["X-Jwt-Dbw"]; ok {
			jwtToken = strings.Split(extra["X-Jwt-Dbw"], ",")[0] // 这里取第一个
			log.Info(ctx, "jwt token from extra X-Jwt-Dbw is %v", jwtToken)
		}
	}
	return jwtToken
}

func GetByteCloudEnv(ctx context.Context) string {
	var (
		extra        = fwctx.GetExtra(ctx)
		byteCloudEnv string
	)
	// 小基架的工单取这个值
	if _, ok := extra["X-Bytecloud-Env"]; ok {
		byteCloudEnv = strings.Split(extra["X-Bytecloud-Env"], ",")[0] // 这里取第一个
		log.Info(ctx, "bytecloud env from extra is %v", byteCloudEnv)
	}
	// 对于多云的工单，如果上面没有取到，就取
	return byteCloudEnv
}

func GetRequestSrc(ctx context.Context) string {
	var (
		extra       = fwctx.GetExtra(ctx)
		requestFrom string
	)
	// 小基架的工单取这个值
	if _, ok := extra["X-Request-Ticket-From"]; ok {
		requestFrom = extra["X-Request-Ticket-From"]
		log.Info(ctx, "X-Request-Ticket-From from extra is %v", requestFrom)
	}
	// 对于多云的工单，如果上面没有取到，就取
	return requestFrom
}

func GetPreCheckMemo(ret *model.PreCheckTicketResp) string {
	var result string
	if len(ret.CheckItems) > 0 {
		for _, val := range ret.CheckItems {
			if val != nil {
				result += val.GetMemo()
			}
		}
	}
	return result
}

// GetTableNameVisitor 获取表名
type GetTableNameVisitor struct {
	Tables []*ast.TableName
}

func (d *GetTableNameVisitor) Enter(in ast.Node) (out ast.Node, skipChildren bool) {
	switch node := in.(type) {
	case *ast.TableName: // 所有表的集合
		d.Tables = append(d.Tables, node)
	}
	return in, false
}

func (d *GetTableNameVisitor) Leave(in ast.Node) (out ast.Node, ok bool) {
	return in, true
}

func GetSliceAllSameString(length int, item string) []string {
	s := make([]string, length)
	for i := range s {
		s[i] = item
	}
	return s
}
