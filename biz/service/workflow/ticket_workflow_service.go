package workflow

import (
	"bytes"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/dig"
	"net/http"
	"os"
)

const (
	EnvBoe = "boe"
	//EnvOnline = "online"
	//EnvI18n   = "i18n"

	requestSrcMultiCloud = "multi_cloud"
)

type ticketWorkflowService struct {
	workflowDal dal.WorkflowDAL
	cnf         config.ConfigProvider
}

type NewTicketWorkflowServiceIn struct {
	dig.In
	WorkflowDal dal.WorkflowDAL
	Conf        config.ConfigProvider
}

func NewTicketWorkflowService(d NewTicketWorkflowServiceIn) TicketWorkflowService {
	h := &ticketWorkflowService{
		workflowDal: d.WorkflowDal,
		cnf:         d.Conf,
	}
	return h
}

type TicketWorkflowService interface {
	CreateBpmWorkflowRecord(ctx context.Context, BpmType string, configData BpmConfigData) (int64, error)
	PassWorkflowRecord(ctx context.Context, bpmFlowInfo *dao.BpmFlowInfo, totalStep int, operatorId string) error
	RejectWorkflowRecord(ctx context.Context, bpmFlowInfo *dao.BpmFlowInfo, operatorId string) error
	CancelWorkflowRecord(ctx context.Context, workflowId int64, operatorId string) error
	DescribeWorkflowLogs(ctx context.Context, workflowId int64, bpmType string) (bpm BpmLogResponse, err error)
}

func (selfService *ticketWorkflowService) getBpmRecordUrl(ctx context.Context) string {
	cnf := selfService.cnf.Get(ctx)
	return cnf.BpmFlowUrl + "/record"
}

func (selfService *ticketWorkflowService) getBpmRecordUrlBOE(ctx context.Context) string {
	cnf := selfService.cnf.Get(ctx)
	return cnf.BpmFlowUrlBOE + "/record"
}

func (selfService *ticketWorkflowService) getBpmConfigUrl(ctx context.Context) string {
	cnf := selfService.cnf.Get(ctx)
	return cnf.BpmFlowUrl + "/config"
}

func (selfService *ticketWorkflowService) getBpmBasicUser(ctx context.Context) string {
	cnf := selfService.cnf.Get(ctx)
	return cnf.BpmBasicAuthUser
}

func (selfService *ticketWorkflowService) getBpmBasicPassword(ctx context.Context) string {
	cnf := selfService.cnf.Get(ctx)
	return cnf.BpmBasicAuthPassword
}

func (selfService *ticketWorkflowService) getDefaultApprovalConfigId(ctx context.Context) int64 {
	cnf := selfService.cnf.Get(ctx)
	return cnf.BpmDefaultConfigId
}

func (selfService *ticketWorkflowService) GetMultiCloudDMLConfigId(ctx context.Context) int64 {
	cnf := selfService.cnf.Get(ctx)
	return cnf.BpmMultiCloudDMLConfigId
}

func (selfService *ticketWorkflowService) GetMultiCloudDDLConfigId(ctx context.Context) int64 {
	cnf := selfService.cnf.Get(ctx)
	return cnf.BpmMultiCloudDDLConfigId
}

func (selfService *ticketWorkflowService) GetMultiCloudByteCloudDMLConfigId(ctx context.Context) int64 {
	cnf := selfService.cnf.Get(ctx)
	return cnf.BpmMultiCloudByteCloudDMLConfigId
}

func (selfService *ticketWorkflowService) GetMultiCloudByteCloudDDLConfigId(ctx context.Context) int64 {
	cnf := selfService.cnf.Get(ctx)
	return cnf.BpmMultiCloudByteCloudDDLConfigId
}

func (selfService *ticketWorkflowService) GetMultiCloudDMLConfigIdBOE(ctx context.Context) int64 {
	cnf := selfService.cnf.Get(ctx)
	return cnf.BpmMultiCloudDMLConfigIdBOE
}

func (selfService *ticketWorkflowService) GetMultiCloudDDLConfigIdBOE(ctx context.Context) int64 {
	cnf := selfService.cnf.Get(ctx)
	return cnf.BpmMultiCloudDDLConfigIdBOE
}

func (selfService *ticketWorkflowService) GetApprovalConfigId(ctx context.Context, ticketType string) int64 {
	// 获取header中的X-Bytecloud-Env环境变量
	// 默认走当前环境的configID, 廊坊环境特殊处理，如果是廊坊环境，并且是字节云的boe，走boe的configID )
	requestSrc := GetRequestSrc(ctx)
	xBytecloudEnv := GetByteCloudEnv(ctx)
	log.Info(ctx, "request src is %v,byte cloud env is %v ticketType is %v", requestSrc, xBytecloudEnv, ticketType)
	var ApprovalConfigId int64
	switch ticketType {
	case model.TicketType_NormalSqlChange.String():
		ApprovalConfigId = selfService.GetMultiCloudDMLConfigId(ctx)
		if requestSrc == requestSrcMultiCloud {
			ApprovalConfigId = selfService.GetMultiCloudByteCloudDMLConfigId(ctx)
		}
		if xBytecloudEnv == EnvBoe {
			ApprovalConfigId = selfService.GetMultiCloudDMLConfigIdBOE(ctx)
		}
	case model.TicketType_FreeLockStructChange.String():
		ApprovalConfigId = selfService.GetMultiCloudDDLConfigId(ctx)
		if requestSrc == requestSrcMultiCloud {
			ApprovalConfigId = selfService.GetMultiCloudByteCloudDDLConfigId(ctx)
		}
		if xBytecloudEnv == EnvBoe {
			ApprovalConfigId = selfService.GetMultiCloudDDLConfigIdBOE(ctx)
		}
	default: // 审批先默认绑定一个DML的,反正也没什么不同
		ApprovalConfigId = selfService.GetMultiCloudDMLConfigId(ctx)
		if requestSrc == requestSrcMultiCloud {
			ApprovalConfigId = selfService.GetMultiCloudByteCloudDMLConfigId(ctx)
		}
		if xBytecloudEnv == EnvBoe {
			ApprovalConfigId = selfService.GetMultiCloudDMLConfigIdBOE(ctx)
		}
	}
	return ApprovalConfigId
}

func (selfService *ticketWorkflowService) GetByteCloudServiceRegionBOE(ctx context.Context) string {
	cnf := selfService.cnf.Get(ctx)
	return cnf.ByteCloudServiceRegionBOE
}

func (selfService *ticketWorkflowService) GetByteCloudServiceEndPointBOE(ctx context.Context) string {
	cnf := selfService.cnf.Get(ctx)
	return cnf.ByteCloudServiceEndPointBOE
}

func (selfService *ticketWorkflowService) GetByteCloudServiceEndPoint(ctx context.Context) string {
	cnf := selfService.cnf.Get(ctx)
	return cnf.ByteCloudServiceEndPoint
}

func (selfService *ticketWorkflowService) GetByteCloudRegion(ctx context.Context) string {
	xBytecloudEnv := GetByteCloudEnv(ctx)
	if xBytecloudEnv == EnvBoe {
		return selfService.GetByteCloudServiceRegionBOE(ctx)
	}
	return os.Getenv(`BDC_REGION_ID`)
}

func (selfService *ticketWorkflowService) GetByteCloudEndpoint(ctx context.Context) string {
	xBytecloudEnv := GetByteCloudEnv(ctx)
	if xBytecloudEnv == EnvBoe {
		return selfService.GetByteCloudServiceEndPointBOE(ctx)
	}
	return selfService.GetByteCloudServiceEndPoint(ctx)
}

func (selfService *ticketWorkflowService) CreateBpmWorkflowRecord(ctx context.Context, BpmType string, configData BpmConfigData) (int64, error) {
	client := &http.Client{}
	ApprovalConfigId := selfService.GetApprovalConfigId(ctx, configData.TicketType)
	log.Info(ctx, "get approval config id is %v", ApprovalConfigId)
	var reqBody = make(map[string]interface{}, 0)

	configData.JwtToken = GetJwtToken(ctx)
	if configData.JwtToken == "" {
		log.Info(ctx, "jwt token from x-jwt-token is empty,this is a multi cloud request,not sinf")
		configData.JwtToken = GetJwtTokenForMultiCloud(ctx)
	}
	configData.TenantID = fwctx.GetTenantID(ctx)
	configData.XVolcRegion = selfService.GetByteCloudRegion(ctx)
	configData.XBytecloudEndpoint = selfService.GetByteCloudEndpoint(ctx)
	configData.XBytecloudEnv = GetByteCloudEnv(ctx)

	reqBody = formatCreateRecordBodyForJWT(ApprovalConfigId, configData)
	reqBodyBytes, _ := json.Marshal(reqBody)
	/* NOTE 这里需要判断是走BOE的BPM还是当前Region下的BPM,默认是走当前Region下的BPM
	但是有一个特殊情况:
		当用户在廊坊环境下创建工单时，需要根据传入的X-Bytecloud-Env参数来区分走BOE的BPM还是走线上的BPM,
		如果用户是在廊坊BOE环境下创建工单时,X-Bytecloud-Env的值是boe,
		如果用户是在廊坊线上环境下创建工单时,X-Bytecloud-Env的值是online,
		如果用户是在新加坡环境下创建工单时,X-Bytecloud-Env的值是i18n,
	*/
	// 获取请求体header中的X-Bytecloud-Env的值
	xBytecloudEnv := GetByteCloudEnv(ctx)
	log.Info(ctx, "xBytecloudEnv is %v", xBytecloudEnv)
	// 默认是走cn的bpm
	var bpmUrl = selfService.getBpmRecordUrl(ctx)
	if xBytecloudEnv == EnvBoe {
		bpmUrl = selfService.getBpmRecordUrlBOE(ctx)
	}
	req, err := http.NewRequest(POST, bpmUrl, bytes.NewReader(reqBodyBytes))
	if err != nil {
		errMsg := fmt.Sprintf("CreateBpmWorkflowRecord NewRequest error : %s", err.Error())
		log.Warn(ctx, errMsg)
		return -1, fmt.Errorf(errMsg)
	}
	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	// 确定请求是来自小基架,还是来自字节云
	requestSrc := GetRequestSrc(ctx)
	log.Info(ctx, "requestSrc is %v", requestSrc)
	switch requestSrc {
	case requestSrcMultiCloud:
		// TODO 认证方式改为JWT
		switch BpmType {
		case BPMAuthTypeJWT:
			req.Header.Set("X-JWT-Token", GetJwtTokenForMultiCloud(ctx))
		default:
			req.SetBasicAuth(selfService.getBpmBasicUser(ctx), selfService.getBpmBasicPassword(ctx))
		}
	default:
		// TODO 认证方式改为JWT
		switch BpmType {
		case BPMAuthTypeJWT:
			req.Header.Set("X-JWT-Token", GetJwtToken(ctx))
		default:
			req.SetBasicAuth(selfService.getBpmBasicUser(ctx), selfService.getBpmBasicPassword(ctx))
		}
	}
	resp, err := client.Do(req)
	if err != nil {
		errMsg := fmt.Sprintf("client do create workflow record error : %v", err)
		log.Warn(ctx, errMsg)
		return -1, fmt.Errorf(errMsg)
	}
	bpm, err := GetBpmResponse(ctx, resp)
	if err != nil {
		errMsg := fmt.Sprintf("client do get bpm response error : %v", err)
		log.Warn(ctx, errMsg)
		return -1, err
	}
	err = AnalyzeBpmResponse(bpm)
	if err != nil {
		errMsg := fmt.Sprintf("client do get bpm response error : %v", err)
		log.Warn(ctx, errMsg)
		return -1, err
	}
	return bpm.Data.Id, nil

}

func (selfService *ticketWorkflowService) PassWorkflowRecord(ctx context.Context, bpmFlowInfo *dao.BpmFlowInfo, totalStep int, operatorId string) error {
	client := &http.Client{}
	reqBodyBytes, _ := json.Marshal(formatActionRecordBody(RequestRecord{action: pass, step: int(bpmFlowInfo.FlowStep), totalStep: totalStep, approval: operatorId}))
	requestUrl := fmt.Sprintf("%s/%d/status", selfService.getBpmRecordUrl(ctx), bpmFlowInfo.WorkflowId)
	req, err := http.NewRequest(PUT, requestUrl, bytes.NewReader(reqBodyBytes))
	if err != nil {
		errMsg := fmt.Sprintf("passWorkflowRecord NewRequest error : %s", err.Error())
		log.Warn(ctx, errMsg)
		return fmt.Errorf(errMsg)
	}
	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.SetBasicAuth(selfService.getBpmBasicUser(ctx), selfService.getBpmBasicPassword(ctx))
	resp, err := client.Do(req)
	if err != nil {
		errMsg := fmt.Sprintf("client do passWorkflowRecord error : %s", err.Error())
		log.Warn(ctx, errMsg)
		return fmt.Errorf(errMsg)
	}
	bpm, err := GetBpmResponse(ctx, resp)
	if err != nil {
		return err
	}
	return AnalyzeBpmResponse(bpm)
}

func (selfService *ticketWorkflowService) RejectWorkflowRecord(ctx context.Context, bpmFlowInfo *dao.BpmFlowInfo, operatorId string) error {
	client := &http.Client{}
	reqBodyBytes, _ := json.Marshal(formatActionRecordBody(RequestRecord{action: reject, approval: operatorId}))
	requestUrl := fmt.Sprintf("%s/%d/status", selfService.getBpmRecordUrl(ctx), bpmFlowInfo.WorkflowId)
	req, err := http.NewRequest(PUT, requestUrl, bytes.NewReader(reqBodyBytes))
	if err != nil {
		errMsg := fmt.Sprintf("rejectWorkflowRecord NewRequest error : %s", err.Error())
		log.Warn(ctx, errMsg)
		return fmt.Errorf(errMsg)
	}
	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.SetBasicAuth(selfService.getBpmBasicUser(ctx), selfService.getBpmBasicPassword(ctx))
	resp, err := client.Do(req)
	if err != nil {
		errMsg := fmt.Sprintf("client do rejectWorkflowRecord error : %s", err.Error())
		log.Warn(ctx, errMsg)
		return fmt.Errorf(errMsg)
	}
	bpm, err := GetBpmResponse(ctx, resp)
	if err != nil {
		return err
	}
	return AnalyzeBpmResponse(bpm)
}

func (selfService *ticketWorkflowService) CancelWorkflowRecord(ctx context.Context, workflowId int64, operatorId string) error {
	client := &http.Client{}
	requestUrl := fmt.Sprintf("%s/%d/cancel", selfService.getBpmRecordUrl(ctx), workflowId)
	req, err := http.NewRequest(POST, requestUrl, nil)
	if err != nil {
		errMsg := fmt.Sprintf("cancelWorkflowRecord NewRequest error : %s", err.Error())
		log.Warn(ctx, errMsg)
		return fmt.Errorf(errMsg)
	}
	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.SetBasicAuth(selfService.getBpmBasicUser(ctx), selfService.getBpmBasicPassword(ctx))
	resp, err := client.Do(req)
	if err != nil {
		errMsg := fmt.Sprintf("client do cancelWorkflowRecord error : %s", err.Error())
		log.Warn(ctx, errMsg)
		return fmt.Errorf(errMsg)
	}
	bpm, err := GetBpmResponse(ctx, resp)
	if err != nil {
		return err
	}
	return AnalyzeBpmResponse(bpm)
}

func (selfService *ticketWorkflowService) DescribeWorkflowLogs(ctx context.Context, workflowId int64, bpmType string) (bpm BpmLogResponse, err error) {
	client := &http.Client{}
	requestUrl := fmt.Sprintf("%s/%d/logs", selfService.getBpmRecordUrl(ctx), workflowId)
	req, err := http.NewRequest(GET, requestUrl, nil)
	if err != nil {
		errMsg := fmt.Sprintf("cancelWorkflowRecord NewRequest error : %s", err.Error())
		log.Warn(ctx, errMsg)
		err = fmt.Errorf(errMsg)
		return
	}
	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	switch bpmType {
	case BPMAuthTypeJWT:
		req.Header.Set("X-JWT-Token", GetJwtToken(ctx))
	default:
		req.SetBasicAuth(selfService.getBpmBasicUser(ctx), selfService.getBpmBasicPassword(ctx))
	}
	resp, err := client.Do(req)
	if err != nil {
		errMsg := fmt.Sprintf("client do cancelWorkflowRecord error : %s", err.Error())
		log.Warn(ctx, errMsg)
		err = fmt.Errorf(errMsg)
		return
	}

	bpm, err = GetBpmLogResponse(ctx, resp)
	if err != nil {
		return
	}
	return bpm, AnalyzeBpmLogResponse(bpm)
}
