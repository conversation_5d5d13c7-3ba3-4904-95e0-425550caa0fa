package workflow

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	crossauth "code.byted.org/infcs/dbw-mgr/biz/service/cross_service_authorization"
	dbw_ticket_service "code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket"

	json_tool "code.byted.org/bcc/tools/json"
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	biz_model "code.byted.org/infcs/dbw-mgr/biz/model"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/i18n"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/message"
	parserService "code.byted.org/infcs/dbw-mgr/biz/service/parser"
	"code.byted.org/infcs/dbw-mgr/biz/service/usermgmt"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	dslibutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/ds-sql-parser/ast"
	_ "github.com/pingcap/tidb/types/parser_driver"
	"github.com/qjpcpu/fp"
	"github.com/volcengine/volc-sdk-golang/service/tls"
	"go.uber.org/dig"
)

const (
	Charset = "UTF-8"

	TicketFromWeb         = "ticket"
	TicketFromDataArchive = "data_archive"

	DefaultMemoForExplain = "0"

	Submitted   = 1
	UnSubmitted = 0
)

type ticketService struct {
	dbwInstance         dal.DbwInstanceDAL
	dbwUser             dal.DbwUserDAL
	workflowDal         dal.WorkflowDAL
	preCheckDetailDal   dal.PreCheckDetailDAL
	executeRecordDal    dal.SecRuleExecuteRecordDAL
	idg                 idgen.Service
	ds                  datasource.DataSourceService
	actorClient         cli.ActorClient
	conf                config.ConfigProvider
	priSvc              usermgmt.PrivilegeServiceInterface
	pgPriSvc            usermgmt.PostgresPrivilegeServiceInterface
	c3ConfProvider      c3.ConfigProvider
	location            location.Location
	i18nSvc             i18n.I18nServiceInterface
	ps                  parserService.CommandParser
	flowRepo            repository.ApprovalFlowRepo
	archiveRepo         repository.DataArchiveRepo
	msgService          message.MsgService
	ticketRepo          repository.TicketRepo
	ticketService       dbw_ticket_service.DbwTicketService
	ticketCommonService dbw_ticket_service.TicketCommonService
	secRuleDal          dal.SecRuleDAL
	approvalFlowService approval_flow.ApprovalFlowService
	crossAuthSvc        crossauth.CrossServiceAuthorizationService
}

type TicketService interface {
	GetTicket(ctx context.Context, workId int64) (*shared.Ticket, error)
	GetTicketUnfinished(ctx context.Context, createTime int64) ([]*shared.Ticket, error)
	IsNeedCheck(ctx context.Context, workId int64) (bool, *shared.Ticket, error)
	ChangeTicketStatus(ctx context.Context, workId int64, status int, desc string) error
	ChangePreCheckTicketStatusAndOperator(ctx context.Context, ticket *shared.Ticket, status int, flowInfo *dao.BpmFlowInfo) error
	UpdatePreCheckResult(ctx context.Context, ticketId int64, preCheckResult *model.PreCheckTicketResp) error
	CreateTicket(ctx context.Context, req *model.CreateTicketReq, approvalFlowId int64, approvalTemplateId int64, workFlowId int64, ticketId int64, tenantId string, userId string) (string, error)
	DescribeTickets(ctx context.Context, req *model.DescribeTicketsReq) (*model.DescribeTicketsResp, error)
	DescribeTicketDetail(ctx context.Context, req *model.DescribeTicketDetailReq) (*model.DescribeTicketDetailResp, error)
	StopTicket(ctx context.Context, req *model.StopTicketReq) (*model.StopTicketResp, error)
	ModifyTicket(ctx context.Context, req *model.ModifyTicketReq) error
	ModifyTicketFlow(ctx context.Context, flowInfo *dao.FlowInfo) error
	IsAutoExecute(ctx context.Context, ticketId int64) (bool, error)
	GetAndCheckTicketFlow(ctx context.Context, ticketId int64, tenantId string, currentOperatorId string) (*dao.BpmFlowInfo, error)
	GetBpmWorkflow(ctx context.Context, ticketId int64) (*dao.BpmFlowInfo, error)
	ExecuteTicket(ctx context.Context, req *model.ExecuteTicketReq) (*model.ExecuteTicketResp, error)
	GetInstanceOwnerIds(ctx context.Context, tenantId string, instanceId string) (string, error)
	GetInstanceDbaIds(ctx context.Context, tenantId string, instanceId string) (string, error)
	GetTenantAdminIds(ctx context.Context, tenantId string) (*[]string, error)
	GetUserNameByIds(ctx context.Context, userIds string, tenantId string) (*map[string]string, error)
	PreCheck(ctx context.Context, req *model.PreCheckTicketReq) (ret *model.PreCheckTicketResp, err error)
	// Deprecated: 已不再使用
	GetNextUserIdInfo(ctx context.Context, flowInfo *dao.BpmFlowInfo) (*dao.UserRole, error)
	IsUserExists(ctx context.Context, userId string, tenantId string) (bool, error)
	IsInstanceAvailable(ctx context.Context, tenantId string, instanceId string) (bool, error)
	IsInstanceRunning(ctx context.Context, instanceId string, instanceType shared.DataSourceType) bool
	CheckTicketUserInfo(ctx context.Context, ticketId int64) error
	DescribeTicketLogDetail(ctx context.Context, req *model.DescribeTicketLogDetailReq) (*model.DescribeTicketLogDetailResp, error)
	GetTableIndexInfo(ctx context.Context, ds *shared.DataSource, req *datasource.GetTableIndexInfoReq) (*datasource.GetTableInfoIndexResp, error)
	GetIndexValue(ctx context.Context, req *datasource.GetIndexValueReq) (*datasource.GetIndexValueResp, error)
	DescribeTicketsForOperateRecord(ctx context.Context, req *model.DescribeTicketRecordListReq) (*model.DescribeTicketRecordListResp, error)
	UpdateTicketCurrentUser(ctx context.Context, approvalNodeId int64, approvalUserIds string, approvalNodeName string) error
	IsTemplateInTicket(ctx context.Context, tenantId string, approvalTemplateId int64) (bool, error)
	ExplainCommand(ctx context.Context, ds *shared.DataSource, command string) (*datasource.ExplainCommandResp, error)
	GetDBAccount(ctx context.Context, ds *shared.DataSource) (*shared.DataSource, error)
	GetDBAddress(ctx context.Context, ds *shared.DataSource) (*shared.DataSource, error)

	CheckShardingFreeLockDMLSQLFormat(ctx context.Context, ticket *shared.Ticket, resp *model.PreCheckTicketResp) (string, bool)
	SubmitTicket(ctx context.Context, req *model.SubmitTicketReq) (*model.SubmitTicketResp, error)
	DescribePreCheckDetail(ctx context.Context, req *model.DescribePreCheckDetailReq) (*model.DescribePreCheckDetailResp, error)

	ExecuteMigrationTicket(ctx context.Context, req *model.ExecuteMigrationTicketReq) (*model.ExecuteMigrationTicketResp, error)
	NewPassWorkflow(ctx context.Context, approvalFlow *entity.ApprovalFlow, ticket *shared.Ticket, userId string) (*model.WorkflowActionResp, error)
	CreateSession(ctx context.Context, req *model.CreateSessionReq) (*model.CreateSessionResp, error)

	GetKubeCluster(ctx context.Context, instanceId string, dsType model.DSType) (string, error)
	DealCreateDbExportTaskResponse(ctx context.Context, resp interface{}) (*model.CreateDbExportTaskResp, error)
}

type NewTicketServiceIn struct {
	dig.In
	DbwInstance         dal.DbwInstanceDAL
	DbwUser             dal.DbwUserDAL
	WorkflowDal         dal.WorkflowDAL
	PreCheckDetailDal   dal.PreCheckDetailDAL
	ExecuteRecordDal    dal.SecRuleExecuteRecordDAL
	Idg                 idgen.Service
	Ds                  datasource.DataSourceService
	ActorClient         cli.ActorClient
	Conf                config.ConfigProvider
	PriSvc              usermgmt.PrivilegeServiceInterface
	PgPriSvc            usermgmt.PostgresPrivilegeServiceInterface
	C3ConfProvider      c3.ConfigProvider
	Location            location.Location
	I18nSvc             i18n.I18nServiceInterface
	Ps                  parserService.CommandParser
	FlowRepo            repository.ApprovalFlowRepo
	ArchiveRepo         repository.DataArchiveRepo
	MsgService          message.MsgService
	TicketRepo          repository.TicketRepo
	TicketService       dbw_ticket_service.DbwTicketService
	TicketCommonService dbw_ticket_service.TicketCommonService
	SecRuleDal          dal.SecRuleDAL
	ApprovalFlowService approval_flow.ApprovalFlowService
	CrossAuthSvc        crossauth.CrossServiceAuthorizationService
}

func NewTicketService(d NewTicketServiceIn) TicketService {
	h := &ticketService{
		dbwInstance:         d.DbwInstance,
		dbwUser:             d.DbwUser,
		workflowDal:         d.WorkflowDal,
		preCheckDetailDal:   d.PreCheckDetailDal,
		executeRecordDal:    d.ExecuteRecordDal,
		idg:                 d.Idg,
		ds:                  d.Ds,
		actorClient:         d.ActorClient,
		conf:                d.Conf,
		priSvc:              d.PriSvc,
		pgPriSvc:            d.PgPriSvc,
		c3ConfProvider:      d.C3ConfProvider,
		location:            d.Location,
		i18nSvc:             d.I18nSvc,
		ps:                  d.Ps,
		flowRepo:            d.FlowRepo,
		archiveRepo:         d.ArchiveRepo,
		msgService:          d.MsgService,
		ticketRepo:          d.TicketRepo,
		ticketService:       d.TicketService,
		ticketCommonService: d.TicketCommonService,
		secRuleDal:          d.SecRuleDal,
		approvalFlowService: d.ApprovalFlowService,
		crossAuthSvc:        d.CrossAuthSvc,
	}
	return h
}

func (selfService *ticketService) NoPreCheck(ctx context.Context, ticket *shared.Ticket) (ret *model.PreCheckTicketResp, err error) {
	flowInfo := &dao.BpmFlowInfo{
		TicketId:     ticket.TicketId,
		TenantId:     ticket.TenantId,
		InstanceId:   ticket.InstanceId,
		FlowStep:     0,
		CreateUserId: ticket.CreateUserId,
	}
	err = selfService.ChangePreCheckTicketStatusAndOperator(ctx, ticket, TicketExamine, flowInfo)
	if err != nil {
		log.Warn(ctx, "ticket: %d,modify ticket status error:%s", ticket.TicketId, err.Error())
		return ret, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	err = selfService.AutoApproval(ctx, ticket.TicketId)
	if err != nil {
		return nil, err
	}
	return &model.PreCheckTicketResp{AllPass: true}, nil
}

func (selfService *ticketService) CreateTicket(ctx context.Context, req *model.CreateTicketReq, approvalFlowId int64,
	approvalTemplateId int64, workFlowId int64, ticketId int64, tenantId string, userId string) (string, error) {
	// 获取工单ID
	createTime := time.Now().UnixMilli()
	// 拿用户角色
	userRole, err := selfService.getUserRole(ctx, userId, tenantId, req.InstanceId)
	if err != nil {
		log.Warn(ctx, "get user role err:%v", err)
		return "", fmt.Errorf("create ticket failed: failed to get user role")
	}
	// 写库
	createFrom := TicketFromWeb
	// 周期性归档,自动执行
	if req.GetCreateFrom() == TicketFromDataArchive && req.ArchiveConfig != nil && req.ArchiveConfig.ArchiveType == model.ArchiveType_Cycle {
		eType := model.ExecuteType_Auto
		req.TicketExecuteType = &eType
		createFrom = TicketFromDataArchive
	}
	// 单次归档,手动执行(2025-06-12改为自动执行)
	if req.GetCreateFrom() == TicketFromDataArchive && req.ArchiveConfig != nil && req.ArchiveConfig.ArchiveType == model.ArchiveType_Once {
		eType := model.ExecuteType_Auto
		req.TicketExecuteType = &eType
		createFrom = TicketFromDataArchive
	}
	// 对于归档工单,不需要预检查,直接提交
	var submitted int8
	if req.GetCreateFrom() == TicketFromDataArchive {
		submitted = 1
	}

	ticket := &dao.Ticket{
		TicketId:            ticketId,
		FlowConfigId:        approvalTemplateId,
		WorkflowId:          workFlowId,
		ApprovalFlowId:      approvalFlowId,
		TicketType:          int8(req.TicketType),
		TicketStatus:        TicketUndo,
		FlowStep:            0,
		ExecuteType:         int8(req.GetTicketExecuteType()),
		CreateTime:          createTime,
		UpdateTime:          createTime,
		CreateUserName:      req.GetCreateUserName(),
		CreateUserId:        userId,
		TenantId:            tenantId,
		CreatedFrom:         createFrom,
		CurrentUserIds:      userId,
		CurrentUserRole:     userRole,
		AllOperatorId:       userId,
		InstanceType:        req.InstanceType.String(),
		InstanceId:          req.InstanceId,
		SqlText:             req.GetSqlText(),
		DbName:              req.DatabaseName,
		ExecutableStartTime: int64(*req.ExecStartTime),
		ExecutableEndTime:   int64(*req.ExecEndTime),
		Extra:               StructToMap(req.BatchConfig),
		DataArchiveConfig:   StructToJson(req.ArchiveConfig),
		Memo:                req.GetMemo(),
		Title:               req.GetTitle(),
		Submitted:           submitted,
	}
	if err = selfService.workflowDal.CreateTicket(ctx, ticket); err != nil {
		errMsg := fmt.Sprintf("create ticket error，ticketId:%d, err:%v", ticketId, err)
		log.Warn(ctx, errMsg)
		return "", fmt.Errorf("failed to create ticket: internal error")
	}
	return strconv.FormatInt(ticketId, 10), nil
}

func (selfService *ticketService) getUserRole(ctx context.Context, userId string, tenantId string, instanceId string) (string, error) {
	// 1.拿到用户所有的角色
	roles, err := selfService.workflowDal.GetUserRoles(ctx, userId, tenantId, instanceId)
	if err != nil {
		errMsg := fmt.Sprintf("failed to get user role:%s", err.Error())
		log.Warn(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}
	return (*roles)[0], nil
}

func (selfService *ticketService) ChangeTicketStatus(ctx context.Context, ticketId int64, status int, desc string) error {
	if err := selfService.workflowDal.UpdateWorkStatus(ctx, &dao.Ticket{TicketId: ticketId, TicketStatus: int8(status), Description: desc}); err != nil {
		errMsg := fmt.Sprintf("修改工单状态失败，ticketId:%d, err:%v", ticketId, err)
		log.Warn(ctx, errMsg)
		return fmt.Errorf("failed to modify ticket status")
	}
	return nil
}

func (selfService *ticketService) ChangePreCheckTicketStatusAndOperator(ctx context.Context, ticket *shared.Ticket, status int, flowInfo *dao.BpmFlowInfo) error {
	// 获取下个状态的负责人
	var userRole *dao.UserRole
	var err error
	if ticket == nil {
		return nil
	}
	userRole, err = selfService.GetNewNextUserIdInfo(ctx, ticket)

	if err != nil {
		errMsg := fmt.Sprintf("failed to get the next handler，ticketId:%d, err:%v", ticket.TicketId, err)
		log.Warn(ctx, errMsg)
		return fmt.Errorf(errMsg)
	}
	//// 拉ticket节点状态，如果过了预检查就不更新了，因为预检查可能有多个请求过来，避免节点已经被审批又被改回去
	//currentStatus, err := selfService.workflowDal.GetTicketStatus(ctx, ticket.TicketId)
	//if err != nil {
	//	errMsg := fmt.Sprintf("failed to get ticket status，ticketId:%d, err:%v", ticket.TicketId, err)
	//	log.Warn(ctx, errMsg)
	//	return fmt.Errorf(errMsg)
	//}
	//if !selfService.isNeedPreCheck(int8(currentStatus)) {
	//	log.Warn(ctx,"")
	//	return nil
	//}
	if err := selfService.workflowDal.UpdateWorkStatusAndOperator(ctx, ticket.TicketId, status, userRole); err != nil {
		errMsg := fmt.Sprintf("failed to modify ticket status/responsible person，ticketId:%d, err:%v", ticket.TicketId, err)
		log.Warn(ctx, errMsg)
		return fmt.Errorf(errMsg)
	}
	if ticket.Submitted == 1 {
		err = selfService.msgService.SendMessageByEvent(ctx, &biz_model.SendMessageByEventReq{
			EventName: message.TicketWaitApproveEvent,
			EventDetail: biz_model.EventDetail{
				TicketWaitApprove: biz_model.TicketWaitApprove{
					TicketType:     utils.TicketTypeTrans(model.TicketType(ticket.TicketType)),
					TicketId:       strconv.FormatInt(ticket.TicketId, 10),
					SubmitterId:    ticket.CreateUserId,
					ApproveUserIds: strings.Split(userRole.Id, ","),
					FlowStep:       1,
					InstanceType:   ticket.InstanceType.String(),
				}},
			ReceiverList: strings.Split(userRole.Id, ","),
		})
		if err != nil {
			log.Warn(ctx, "ticket %d send message error:%s", ticket.TicketId, err.Error())
		}
	}
	return nil
}

func (selfService *ticketService) isNeedPreCheck(ticketStatus int8) bool {
	// 如果工单状态已经在预检查之后，则认为不需要在进行预检查
	return ticketStatus < TicketPreCheckError
}

func (selfService *ticketService) getTables(node ast.StmtNode) []*ast.TableName {
	var visitor = &GetTableNameVisitor{
		Tables: []*ast.TableName{},
	}
	// 先把右侧取到，因为sql解析的leftNode是靠右侧的点，之后我们一直往左侧走即可
	switch stmt := node.(type) {
	default:
		stmt.Accept(visitor)
		// 去重一下
		var res []*ast.TableName
		var resMap = make(map[string]bool, 0)
		for _, val := range visitor.Tables {
			if _, ok := resMap[fmt.Sprintf(val.Schema.String(), val.Name.String())]; !ok {
				res = append(res, val)
				resMap[fmt.Sprintf(val.Schema.String(), val.Name.String())] = true
			}
		}
		for _, val := range res {
			log.Info(context.Background(), fmt.Sprintf("get db from sql is %v,get table from sql is %v",
				val.Schema.String(), val.Name.String()))
		}
		return res
	}
}

func (selfService *ticketService) checkTablePermission(table *ast.TableName, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	//// 如果sql存在库名且库名和执行db不一致，则不允许执行
	//if table.Schema.String() != "" && table.Schema.String() != executeDbName {
	//	return fmt.Errorf("执行db:%s, sql db:%s 不一致，不允许执行", executeDbName, table.Schema.String())
	//}
	// 按从高到低的权限进行检查，如果前面通过了，后面则在不需要进行检查
	if selfService.checkInstancePrivilege(instanceId, allUserPrivilege.instancePrivilege) {
		return nil
	}
	if selfService.checkDatabasePrivilege(executeDbName, allUserPrivilege.databasePrivilege) {
		return nil
	}
	if selfService.checkTablePrivilege(executeDbName, table.Name.String(), allUserPrivilege.tablePrivilege) {
		return nil
	}
	return fmt.Errorf("do not have database/table：%s.%s permission", executeDbName, table.Name.String())
}

func (selfService *ticketService) checkDatabasePermission(db string, allUserPrivilege *UserPrivilege, executeDbName string, instanceId string) error {
	// 如果sql存在库名且库名和执行db不一致，则不允许执行
	//if db != executeDbName {
	//	return fmt.Errorf("执行db:%s, sql db:%s 不一致，不允许执行", executeDbName, db)
	//}
	// 按从高到低的权限进行检查，如果前面通过了，后面则在不需要进行检查
	if selfService.checkInstancePrivilege(instanceId, allUserPrivilege.instancePrivilege) {
		return nil
	}
	if selfService.checkDatabasePrivilege(executeDbName, allUserPrivilege.databasePrivilege) {
		return nil
	}
	return fmt.Errorf("do not have database/table：%s.%s permission", executeDbName, db)
}

func (selfService *ticketService) checkDatabasePrivilege(dbName string, databasePrivilege *[]*dao.UserDatabasePrivilege) bool {
	return fp.StreamOf(*databasePrivilege).Filter(func(dbPrivilege *dao.UserDatabasePrivilege) bool {
		return strings.EqualFold(dbName, dbPrivilege.DbName)
	}).Exists()
}

func (selfService *ticketService) checkTablePrivilege(dbName string, tbName string, tablePrivilege *[]*dao.UserTablePrivilege) bool {
	return fp.StreamOf(*tablePrivilege).Filter(func(tbPrivilege *dao.UserTablePrivilege) bool {
		return strings.EqualFold(dbName, tbPrivilege.DbName) && strings.EqualFold(tbName, tbPrivilege.TbName)
	}).Exists()
}

func (selfService *ticketService) checkColumnPrivilege(dbName string, tbName string, columns []*ast.Assignment, columnPrivilege *[]*dao.UserColumnPrivilege) error {
	for _, columnAssignment := range columns {
		isPass := fp.StreamOf(*columnPrivilege).Filter(func(colPrivilege *dao.UserColumnPrivilege) bool {
			return strings.EqualFold(dbName, colPrivilege.DbName) && strings.EqualFold(tbName, colPrivilege.TbName) &&
				strings.EqualFold(columnAssignment.Column.Name.String(), colPrivilege.ColumnName)
		}).Exists()
		if !isPass {
			return fmt.Errorf("there are columns in SQL that do not have permissions")
		}
	}
	return nil
}

func (selfService *ticketService) GetTablePKOrUniqKey(ctx context.Context, rresp *datasource.GetTableInfoIndexResp) ([]*datasource.TableIndexInfo, bool) {
	var resPk = make([]*datasource.TableIndexInfo, 0)
	var resUniq = make([]*datasource.TableIndexInfo, 0)
	for _, val := range rresp.TableIndexInfo {
		if val.IndexType == "Primary" { // 有主键
			resPk = append(resPk, val)
		}
		if val.IndexType == "Unique" && val.Nullable == "" { // Nullable为Yes代表以为null，为空表示非null
			resUniq = append(resUniq, val)
		}
	}
	// 有主键,使用主键，无主键，使用非空唯一索引
	if len(resPk) != 0 { // 有主键,直接返回主键,不考虑非空唯一索引
		log.Info(ctx, "the current table has a primary key")
		return resPk, true
	}
	if len(resUniq) == 0 { // 如果没有唯一索引和主键，直接返回false
		log.Warn(ctx, "the current table does not contain a primary key or a non empty unique index")
		return nil, false
	}
	// 判断唯一索引是否为非空
	var uniqMap = make(map[string][]*datasource.TableIndexInfo, 0)
	for _, item := range resUniq {
		_, ok := uniqMap[item.IndexName]
		if !ok {
			uniqMap[item.IndexName] = []*datasource.TableIndexInfo{}
			uniqMap[item.IndexName] = append(uniqMap[item.IndexName], item)
		} else {
			uniqMap[item.IndexName] = append(uniqMap[item.IndexName], item)
		}
	}
	for _, value := range uniqMap {
		var isItemValid = true
		for _, j := range value {
			// 只要包含
			if j.Nullable == "Yes" { // 只要有一个是Yes,说明这个唯一索引不是非空,不可用
				isItemValid = false
				break
			}
		}
		if isItemValid {
			log.Info(ctx, "the current table has a non empty unique index")
			return value, true
		}
	}
	log.Warn(ctx, "the current table does not have a non empty unique index")
	return nil, false
}

func (selfService *ticketService) getDBDataSource(ctx context.Context, instanceId string, db string, dsType shared.DataSourceType) (*shared.DataSource, error) {
	cnf := selfService.conf.Get(ctx)
	linkType := shared.Volc
	if dsType == shared.MetaMySQL {
		linkType = shared.Public
	}
	ds := &shared.DataSource{
		Type:             dsType,
		LinkType:         linkType,
		ConnectTimeoutMs: cnf.TicketConnectTimeout * 1000,
		ReadTimeoutMs:    cnf.TicketReadTimeout * 1000,
		WriteTimeoutMs:   cnf.TicketWriteTimeout * 1000,
		IdleTimeoutMs:    cnf.TicketIdleTimeout * 1000,
		InstanceId:       instanceId,
		Db:               db,
	}
	// 这里获取安全管控的账号密码
	_, err := selfService.getDBAccount(ctx, ds)
	if err != nil {
		log.Warn(ctx, "get db account error:%s", err.Error())
		return nil, err
	}
	// 获取默认终端的连接地址
	//_, err = selfService.getDBAddress(ctx, ds)
	err = selfService.ds.GetDatasourceAddress(ctx, ds)
	if err != nil {
		log.Warn(ctx, "get db address error:%s", err.Error())
		return nil, err
	}
	return ds, nil
}

func (selfService *ticketService) ModifyTicket(ctx context.Context, req *model.ModifyTicketReq) error {
	ticket, err := selfService.GetTicket(ctx, dslibutils.MustStrToInt64(req.TicketId))
	if err != nil || ticket == nil {
		log.Info(ctx, "ticket: get ticket entry err:", err.Error())
		return consts.ErrorOf(model.ErrorCode_TicketIsNotExisted)
	}
	// 如果工单的租户ID不等于ctx的租户ID,直接报错
	if ticket.TenantId != fwctx.GetTenantID(ctx) {
		log.Warn(ctx, "ticket %v tenant %v is not matched with ctx tenant %v ", ticket.TicketId, ticket.TenantId, fwctx.GetTenantID(ctx))
		return consts.ErrorOf(model.ErrorCode_TicketIsNotExisted)
	}
	if ticket.TicketStatus != int32(model.TicketStatus_TicketExamine) {
		log.Info(ctx, "ticket: %d is not preCheck error can't modify:", ticket.TicketId)
		return consts.ErrorOf(model.ErrorCode_TicketStatusNotSatisfy)
	}
	// 这里新增一个逻辑，只能执行那些instance状态为非delete的实例
	isInstanceAvailable, err := selfService.workflowDal.IsInstanceAvailable(ctx, ticket.TenantId, ticket.InstanceId)
	if err != nil || !isInstanceAvailable {
		log.Info(ctx, "ticket: failed to obtain user information:%v, or the instance has been taken offline and cannot be executed:%v", err, isInstanceAvailable)
		return consts.ErrorOf(model.ErrorCode_InstanceStatusNotSatisfy)
	}

	ticketForUpdate := &dao.Ticket{
		TicketId:          dslibutils.MustStrToInt64(req.TicketId),
		TenantId:          fwctx.GetTenantID(ctx),
		SqlText:           req.SqlText,
		DataArchiveConfig: StructToJson(req.ArchiveConfig),
	}
	log.Info(ctx, "modify ticket dal:%s", ticket.TicketId)
	return selfService.workflowDal.UpdateById(ctx, ticketForUpdate)
}

func (selfService *ticketService) GetTicket(ctx context.Context, workId int64) (*shared.Ticket, error) {
	ticket, err := selfService.workflowDal.DescribeByTicketID(ctx, workId)
	if err != nil {
		errMsg := fmt.Sprintf("ticket: failed to obtain ticket details，workId：%d, err:%v", workId, err)
		log.Warn(ctx, errMsg)
		return nil, err
	}
	return ChangeTicketType(ticket), nil
}

func (selfService *ticketService) GetTicketUnfinished(ctx context.Context, createTime int64) ([]*shared.Ticket, error) {
	tickets, err := selfService.workflowDal.DescribeTicketsByCreateTime(ctx, createTime)
	if err != nil {
		errMsg := fmt.Sprintf("ticket: failed to obtain details of unprocessed tickets，createTime：%d, err:%v", createTime, err)
		log.Warn(ctx, errMsg)
		return nil, err
	}
	var res []*shared.Ticket
	for _, val := range tickets {
		res = append(res, ChangeTicketType(val))
	}
	return res, nil
}

func (selfService *ticketService) ExecuteTicket(ctx context.Context, req *model.ExecuteTicketReq) (*model.ExecuteTicketResp, error) {
	ticket, err := selfService.GetTicket(ctx, dslibutils.MustStrToInt64(req.TicketId))
	if err != nil || ticket == nil {
		log.Warn(ctx, "ticket %v: get ticket entry err: %v,ticket is %v", req.TicketId, err, ticket)
		return &model.ExecuteTicketResp{
			AllPass:    false,
			ErrMessage: model.ErrorCode_TicketIsNotExisted.String(),
		}, consts.ErrorOf(model.ErrorCode_TicketIsNotExisted)
	}
	// 执行工单的条件：
	// 1、只有等待执行的工单，才可以执行
	if ticket.TicketStatus != int32(model.TicketStatus_TicketWaitExecute) {
		log.Warn(ctx, "ticket %v: ticket status is %v not satisfy", req.TicketId, ticket.TicketStatus)
		return nil, consts.ErrorOf(model.ErrorCode_TicketStatusNotSatisfy)
	}

	// 2、只有纳入管控、并且是当前租户的instance的工单，才可以执行
	isInstanceAvailable, err := selfService.workflowDal.IsInstanceAvailable(ctx, ticket.TenantId, ticket.InstanceId)
	if err != nil || !isInstanceAvailable {
		log.Warn(ctx, "ticket %s: failed to obtain user information:%v, or the instance is not in security control mode: %v", req.TicketId, err, isInstanceAvailable)
		selfService.workflowDal.UpdateWorkStatus(ctx, &dao.Ticket{
			TicketStatus: int8(model.TicketStatus_TicketError),
			Description:  fmt.Sprintf("ticket %s: failed to obtain user information:%v, or the instance is not in security control mode: %v", req.TicketId, err, isInstanceAvailable),
			TicketId:     ticket.TicketId,
			TenantId:     ticket.TenantId})
		return nil, consts.ErrorOf(model.ErrorCode_InstanceStatusNotSatisfy)
	}

	// 3、只有实例状态是Running的工单,才可以执行
	if !selfService.IsInstanceRunning(ctx, ticket.InstanceId, ticket.InstanceType) {
		log.Warn(ctx, "ticket %s: the instance status is not running or the instance does not exist", req.TicketId)
		selfService.workflowDal.UpdateWorkStatus(ctx, &dao.Ticket{
			TicketStatus: int8(model.TicketStatus_TicketError),
			Description:  "the instance status is not running or the instance does not exist",
			TicketId:     ticket.TicketId,
			TenantId:     ticket.TenantId})
		return nil, consts.ErrorOf(model.ErrorCode_CheckInstanceError)
	}
	// 如果是归档工单,这块就走归档工单的逻辑了
	if ticket.CreateFrom == TicketFromDataArchive {
		// 走归档逻辑
		return selfService.ExecuteDataArchiveTicket(ctx, ticket)
	}
	cnf := selfService.conf.Get(ctx)
	ds := &shared.DataSource{
		LinkType:         shared.Volc,
		Type:             ticket.InstanceType,
		InstanceId:       ticket.InstanceId,
		Db:               ticket.DbName,
		ConnectTimeoutMs: cnf.TicketConnectTimeout * 1000,
		ReadTimeoutMs:    cnf.TicketReadTimeout * 1000,
		WriteTimeoutMs:   cnf.TicketWriteTimeout * 1000,
	}
	// sharding的ddl工单,暂时不支持新的工单
	if ticket.InstanceType != shared.MySQLSharding && ticket.TicketType == int32(model.TicketType_FreeLockStructChange) && selfService.ticketCommonService.IsInGhostDDLWhite(ctx, fwctx.GetTenantID(ctx), ticket.InstanceType.String()) {
		// online ddl 走其他流程
		return selfService.ExecuteOnlineDDlTicket(ctx, ticket, ds)
	}
	if ticket.InstanceType == shared.MetaMySQL {
		ds.LinkType = shared.Public
	}

	commandRes, err := selfService.executeCommands(ctx, ds, ticket)
	if err != nil {
		return &model.ExecuteTicketResp{
			AllPass:    false,
			ErrMessage: commandRes,
		}, err
	}
	return &model.ExecuteTicketResp{AllPass: true, ErrMessage: commandRes}, nil
}

func (selfService *ticketService) ExecuteDataArchiveTicket(ctx context.Context, ticket *shared.Ticket) (*model.ExecuteTicketResp, error) {
	archiveConfig := &model.DataArchiveConfig{}
	err := json.Unmarshal([]byte(ticket.ArchiveConfig), archiveConfig)
	if err != nil {
		log.Warn(ctx, "Unmarshal archiveConfig error:%s archiveConfig:%s", err.Error(), ticket.ArchiveConfig)
		// 把工单转为失败
		selfService.changeTicketError(ctx, ticket.TicketId, "Unmarshal archiveConfig error, contact administrator to solve it")
		return &model.ExecuteTicketResp{AllPass: false}, err
	}
	return selfService.createCronArchive(ctx, ticket, archiveConfig)
}

func (selfService *ticketService) ExecuteOnlineDDlTicket(ctx context.Context, ticket *shared.Ticket, ds *shared.DataSource) (*model.ExecuteTicketResp, error) {

	if ticket.ExecuteType == int32(model.ExecuteType_Cron) {
		// 如果是cron，直接改为执行中，等调度就可以了
		err := selfService.ticketRepo.UpdateRunningInfo(ctx, &entity.TicketRunningInfo{TicketId: fmt.Sprintf("%d", ticket.TicketId), TicketStatus: int(model.TicketStatus_TicketWaitExecute), Description: ""})
		if err != nil {
			log.Warn(ctx, "UpdateRunningInfo error:%s", err.Error())
			return &model.ExecuteTicketResp{AllPass: false}, consts.ErrorOf(model.ErrorCode_InternalError)
		}
		return &model.ExecuteTicketResp{AllPass: true}, nil
	}

	err := selfService.actorClient.KindOf(consts.DbwInstanceTicketActorKind).Send(ctx, ticket.InstanceId, &shared.ExecuteDbwTicket{TicketId: fmt.Sprintf("%d", ticket.TicketId)})
	if err != nil {
		log.Warn(ctx, "send msg to execute online ddl error:%v", err)
		// selfService.changeTicketError(ctx, ticket.TicketId, "execute online ddl task error, contact administrator to solve it")
		return &model.ExecuteTicketResp{AllPass: false}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return &model.ExecuteTicketResp{AllPass: true}, nil
}

func (selfService *ticketService) changeTicketError(ctx context.Context, ticketId int64, errMsg string) {
	daoTicket := &dao.Ticket{TicketId: ticketId, TicketStatus: int8(model.TicketStatus_TicketError), Description: errMsg}
	err := selfService.workflowDal.UpdateWorkStatus(ctx, daoTicket)
	if err != nil {
		log.Warn(ctx, "ticketId: %d UpdateWorkStatus error :%s", ticketId, err.Error())
	}
}

func (selfService *ticketService) createArchiveTask(ctx context.Context, ticket *shared.Ticket, archiveConfig *model.DataArchiveConfig) (int64, error) {
	archiveTaskId, err := selfService.idg.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "createArchiveTask error, can't get id: %s", err.Error())
		return 0, fmt.Errorf("reate ticket failed, generate ticket ID failed")
	}

	archiveTask := &entity.ArchiveTask{
		TaskId:          archiveTaskId,
		ArchiveConfigId: -1,
		IsBackUp:        archiveConfig.IsBackUp,
		InstanceId:      ticket.InstanceId,
		InstanceType:    ticket.InstanceType.String(),
		Database:        ticket.DbName,
		Table:           archiveConfig.TableName,
		TenantId:        ticket.TenantId,
		CreateTime:      time.Now().Unix(),
		UpdateTime:      time.Now().Unix(),
		TicketId:        ticket.TicketId,
	}
	err = selfService.archiveRepo.CreateArchiveTask(ctx, archiveTask)
	if err != nil {
		log.Warn(ctx, "createArchiveTask error: %s", err.Error())
		return 0, fmt.Errorf("create createArchive task failed")
	}
	return archiveTaskId, nil
}

func (selfService *ticketService) createCronArchive(ctx context.Context, ticket *shared.Ticket, archiveConfig *model.DataArchiveConfig) (*model.ExecuteTicketResp, error) {
	// 1.插入配置记录
	archiveConfigId, err := selfService.createArchiveConfig(ctx, ticket, archiveConfig)
	if err != nil {
		selfService.changeTicketError(ctx, ticket.TicketId, "create archiveConfig error, contact administrator to solve it")
		return &model.ExecuteTicketResp{AllPass: false}, err
	}
	// 2.创建config，如果失败了，需要删除配置
	err = selfService.actorClient.KindOf(consts.DataArchiveConfigActorKind).
		Send(ctx, fmt.Sprintf("%d", archiveConfigId), &shared.CreateArchiveConfig{ArchiveConfigId: archiveConfigId})
	if err != nil {
		selfService.changeTicketError(ctx, ticket.TicketId, "create archive cron task error, contact administrator to solve it")
		_ = selfService.archiveRepo.DeleteArchiveConfig(ctx, archiveConfigId)
		return &model.ExecuteTicketResp{AllPass: false}, err
	}
	if err = selfService.UpdateTicketRepo(ctx, &dao.Ticket{TicketId: ticket.TicketId, TicketStatus: int8(model.TicketStatus_TicketExecute)}); err != nil {
		log.Warn(ctx, "ticket: update ticket %s execute err:%s", ticket.TicketId, err.Error())
		return &model.ExecuteTicketResp{AllPass: false}, err
	}
	return &model.ExecuteTicketResp{AllPass: true}, nil
}

func (selfService *ticketService) UpdateTicketRepo(ctx context.Context, ticket *dao.Ticket) error {
	log.Info(ctx, "update ticket status to %v", ticket.TicketStatus)
	err := selfService.workflowDal.UpdateWorkStatus(ctx, ticket)
	if err != nil {
		log.Warn(ctx, "update ticket status to %v error", ticket.TicketStatus)
		return err
	}
	return nil
}

func (selfService *ticketService) createArchiveConfig(ctx context.Context, ticket *shared.Ticket, archiveConfig *model.DataArchiveConfig) (int64, error) {
	archiveConfigId, err := selfService.idg.NextID(ctx)
	if err != nil {
		log.Warn(ctx, "createArchiveTask error, can't get id: %s", err.Error())
		return 0, fmt.Errorf("reate ticket failed, generate ticket ID failed")
	}
	// CronStr
	cronStr := ""
	if archiveConfig.ArchiveType == model.ArchiveType_Cycle {
		cronStr = archiveConfig.GetArchiveCycleInfo().GetCronStr()
	}
	createUser, err := selfService.getUserNameListFromMap(ctx, ticket.CreateUserId, ticket.TenantId)
	if err != nil {
		log.Warn(ctx, "ticket:get createUser err:%s", err.Error())
		return 0, consts.ErrorWithParam(model.ErrorCode_InputParamError, fmt.Sprintf("failed to obtain creator information, check again"))
	}

	if archiveConfig.BackUpConfig == nil {
		archiveConfig.BackUpConfig = &model.BackUpConfig{}
	}

	dataArchiveConfig := &entity.ArchiveConfig{
		ConfigId:     archiveConfigId,
		TicketId:     ticket.TicketId,
		IsBackUp:     archiveConfig.IsBackUp,
		InstanceId:   ticket.InstanceId,
		InstanceType: ticket.InstanceType.String(),
		Database:     ticket.DbName,
		TableName:    archiveConfig.TableName,
		TenantId:     ticket.TenantId,
		CreateTime:   time.Now().Unix(),
		ArchiveType:  archiveConfig.ArchiveType,
		BizTimes:     archiveConfig.TimeInfos,
		OtherCase:    archiveConfig.GetOtherCase(),
		UpdateTime:   time.Now().Unix(),
		IsOpen:       true,
		CronStr:      cronStr,
		Deleted:      0,
		CreateUserId: ticket.CreateUserId,
		CreateUser:   createUser,
		BackUpType:   archiveConfig.BackUpConfig.GetBackUpType(),
	}
	err = selfService.archiveRepo.CreateArchiveConfig(ctx, dataArchiveConfig)
	if err != nil {
		log.Warn(ctx, "CreateArchiveConfig error: %s", err.Error())
		return 0, fmt.Errorf("create createArchive config failed")
	}
	return archiveConfigId, nil
}

func (selfService *ticketService) GetDBAccount(ctx context.Context, ds *shared.DataSource) (*shared.DataSource, error) {
	return selfService.getDBAccount(ctx, ds)
}

func (selfService *ticketService) getDBAccount(ctx context.Context, ds *shared.DataSource) (*shared.DataSource, error) {
	// 这块需要实例管理dbw_instance的表记录
	instanceInfo, err := selfService.dbwInstance.Get(ctx, ds.InstanceId, ds.Type.String(), ds.LinkType.String(), fwctx.GetTenantID(ctx), model.ControlMode_Management.String())
	if err != nil {
		log.Info(ctx, "ticket： get Instance %s info error:%s", ds.InstanceId, err.Error())
		return nil, err
	}
	// 这里需要把密码进行解密
	ds.Password = utils.DecryptData(instanceInfo.DatabasePassword, instanceInfo.InstanceId)
	ds.User = instanceInfo.DatabaseUser
	return ds, nil
}

func (selfService *ticketService) GetDBAddress(ctx context.Context, ds *shared.DataSource) (*shared.DataSource, error) {
	return selfService.getDBAddress(ctx, ds)
}

func (selfService *ticketService) getDBAddress(ctx context.Context, ds *shared.DataSource) (*shared.DataSource, error) {
	rreq := &datasource.GetDBInnerAddressReq{
		Source: ds,
	}
	rresp, err := selfService.ds.GetDBInnerAddress(ctx, rreq)
	if err != nil {
		log.Warn(ctx, "ticket: get db address error:", err.Error())
		return nil, err
	}
	if rresp.Source.Address == "" {
		log.Warn(ctx, "ticket: get empty db address ")
		return nil, errors.New("get empty db address")
	}
	return ds, nil
}

// executeCommands 执行命令接口,如果执行失败,返回失败原因
func (selfService *ticketService) executeCommands(ctx context.Context, ds *shared.DataSource, ticket *shared.Ticket) (string, error) {
	// 获取安全管控的账号密码
	ds, err := selfService.getDBAccount(ctx, ds)
	if err != nil {
		log.Warn(ctx, "ticket %v: get db account err: %v", ticket.TicketId, err)
		return "", consts.ErrorOf(model.ErrorCode_ListAccountFail)
	}
	// 获取内网地址
	//ds, err = selfService.getDBAddress(ctx, ds)
	if ticket.InstanceType == shared.MySQLSharding && ticket.TicketType == int32(model.TicketType_FreeLockStructChange) {
		// sharding的无锁DDL不获取连接地址,没啥用
	} else {
		err = selfService.ds.GetDatasourceAddress(ctx, ds)
		if err != nil {
			log.Info(ctx, "ticket %v: get db account err: %v", ticket.TicketId, err)
			return "", consts.ErrorOf(model.ErrorCode_InternalError)
		}
	}

	// MySQL 普通SQL变更工单
	var resp interface{}
	if IsEnableNormalSQLChangeTicket(ticket.InstanceType) && ticket.TicketType == int32(model.TicketType_NormalSqlChange) {
		log.Info(ctx, "ticket: %v begin to send message to sqlchange actor，tenantId is %s", ticket.TicketId, fwctx.GetTenantID(ctx))
		resp, err = selfService.sendMessageToTicketActor(ctx, ticket, ds)
	}
	// MySQL 无锁数据变更工单
	if IsEnableFreeLockSQLChangeTicket(ticket.InstanceType) && ticket.TicketType == int32(model.TicketType_FreeLockSqlChange) {
		log.Info(ctx, "ticket: %v begin to send message to freelock sqlchange actor，tenantId is %s", ticket.TicketId, fwctx.GetTenantID(ctx))
		resp, err = selfService.sendMessageToFreeLockDMLActor(ctx, ticket, ds)
	}
	// MySQL 无锁结构变更工单
	if (ticket.InstanceType == shared.MySQL || ticket.InstanceType == shared.MetaMySQL) && ticket.TicketType == int32(model.TicketType_FreeLockStructChange) {
		if selfService.IsEnableDDLDirectExecCommandsType(ticket.SqlText) {
			log.Info(ctx, "ticket: %v its a normal ddl[%s],begin to send message to mysql normal ddl actor，tenantId is %s", ticket.TicketId, ticket.SqlText, fwctx.GetTenantID(ctx))
			resp, err = selfService.sendMessageToTicketActor(ctx, ticket, ds)
		} else {
			log.Info(ctx, "ticket: %v begin to send message to online ddl actor，tenantId is %s", ticket.TicketId, fwctx.GetTenantID(ctx))
			resp, err = selfService.sendMessageToOnlineDDLTicketActor(ctx, ticket, ds)
		}
	}
	// VeDB 无锁结构变更工单
	if ticket.InstanceType == shared.VeDBMySQL && ticket.TicketType == int32(model.TicketType_FreeLockStructChange) {
		if selfService.IsEnableDDLDirectExecCommandsType(ticket.SqlText) {
			log.Info(ctx, "ticket: %v its a normal ddl[%s],begin to send message to vedb normal ddl actor，tenantId is %s", ticket.TicketId, ticket.SqlText, fwctx.GetTenantID(ctx))
			resp, err = selfService.sendMessageToTicketActor(ctx, ticket, ds)
		} else {
			log.Info(ctx, "ticket: %v begin to send message to vedb ddl actor，tenantId is %s", ticket.TicketId, fwctx.GetTenantID(ctx))
			resp, err = selfService.sendMessageToVeDBDDLTicketActor(ctx, ticket, ds)
		}
	}
	if ticket.InstanceType == shared.MySQLSharding && ticket.TicketType == int32(model.TicketType_FreeLockSqlChange) {
		log.Info(ctx, "ticket: %v begin to send message to sharding freeLock sqlChange actor，tenantId is %s", ticket.TicketId, fwctx.GetTenantID(ctx))
		// resp, err = selfService.sendMessageToShardingFreeLockDMLActor(ctx, ticket, ds)
	}
	// sharding 无锁结构变更工单
	if ticket.InstanceType == shared.MySQLSharding && ticket.TicketType == int32(model.TicketType_FreeLockStructChange) {
		log.Info(ctx, "ticket: %v begin to send message to sharding freeLock struct change actor，tenantId is %s", ticket.TicketId, fwctx.GetTenantID(ctx))
		resp, err = selfService.sendMessageToShardingFreeLockDDLActor(ctx, ticket, ds)
	}
	if err != nil {
		log.Warn(ctx, "send message to ticket actor error:%s", err.Error())
		return "", consts.ErrorWithParam(model.ErrorCode_InternalError, "failed to initiate task execution")
	}
	switch rsp := resp.(type) {
	case *shared.TicketExecuted:
		if rsp.Code == shared.ExecutedStart {
			log.Info(ctx, "execute ticket start! %v", rsp)
			return rsp.Message, nil
		}
		log.Warn(ctx, "execute ticket fail %v", rsp)
		return rsp.Message, consts.ErrorOf(model.ErrorCode_TicketStartError)
	default:
		log.Warn(ctx, "execute ticket fail %v", rsp)
		return "execute ticket fail", consts.ErrorOf(model.ErrorCode_TicketStartError)
	}
}

func (selfService *ticketService) ModifyTicketFlow(ctx context.Context, flowInfo *dao.FlowInfo) error {
	if err := selfService.workflowDal.ModifyTicketFlow(ctx, flowInfo); err != nil {
		errMsg := fmt.Sprintf("failed to modify ticket flow，ticketId:%d, err:%v", flowInfo.TicketId, err)
		log.Warn(ctx, errMsg)
		return fmt.Errorf(errMsg)
	}
	return nil
}

func (selfService *ticketService) GetAndCheckTicketFlow(ctx context.Context, ticketId int64, tenantId string, currentOperatorId string) (*dao.BpmFlowInfo, error) {
	bpmFlowInfo, err := selfService.workflowDal.GetBpmWorkflow(ctx, ticketId)
	if err != nil {
		log.Warn(ctx, "get BpmWorkflow failed, err:%v", err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return bpmFlowInfo, nil
	// FixMe 测试流程,暂时不加用户校验,后续需要增加租户校验
	//CurrentUserIdList := strings.Split(bpmFlowInfo.CurrentUserIds, ",")
	//if bpmFlowInfo.TenantId != tenantId {
	//	return bpmFlowInfo, consts.ErrorOf(model.ErrorCode_UserMgmtPermissionDeny)
	//}
	//for _, userId := range CurrentUserIdList {
	//	if strings.EqualFold(strings.TrimSpace(userId), currentOperatorId) {
	//		return bpmFlowInfo, nil
	//	}
	//}
	//return bpmFlowInfo, consts.ErrorOf(model.ErrorCode_UserMgmtPermissionDeny)
}

func (selfService *ticketService) GetBpmWorkflow(ctx context.Context, ticketId int64) (*dao.BpmFlowInfo, error) {
	bpmWorkflow, err := selfService.workflowDal.GetBpmWorkflow(ctx, ticketId)
	if err != nil {
		errMsg := fmt.Sprintf("get BpmFlowInfo failed, ticketId:%d, err:%v", ticketId, err)
		log.Warn(ctx, errMsg)
		return nil, fmt.Errorf(errMsg)
	}
	return bpmWorkflow, err
}

func (selfService *ticketService) IsAutoExecute(ctx context.Context, ticketId int64) (bool, error) {
	autoExecute, err := selfService.workflowDal.IsAutoExecute(ctx, ticketId)
	if err != nil {
		errMsg := fmt.Sprintf("get AutoExecute failed, ticketId:%d, err:%v", ticketId, err)
		log.Warn(ctx, errMsg)
		return false, fmt.Errorf(errMsg)
	}
	log.Info(ctx, "ticket %v  auto execute is %v", ticketId, autoExecute)
	return autoExecute, err
}

func (selfService *ticketService) ExplainCommand(ctx context.Context, ds *shared.DataSource, command string) (*datasource.ExplainCommandResp, error) {
	explainCommand, err := selfService.ds.ExplainCommand(ctx, &datasource.ExplainCommandReq{
		Source:  ds,
		Command: command,
	})
	if err != nil {
		log.Warn(ctx, "ticket explain command %v error:%v", command, err.Error())
		return nil, err
	}
	log.Info(ctx, "explain res is %s", utils.Show(explainCommand))
	return explainCommand, nil
}

func (selfService *ticketService) DescribeTickets(ctx context.Context, req *model.DescribeTicketsReq) (*model.DescribeTicketsResp, error) {
	// 这里需要做个判断,如果传入了req.SearchParam.CreateUser的信息,代表想要通过创建人过滤，这里填入User的Id到req中。
	if req.SearchParam != nil && req.SearchParam.IsSetCreateUser() {
		dbUser, err := selfService.dbwUser.GetUserByName(ctx, *req.SearchParam.CreateUser, fwctx.GetTenantID(ctx))
		if err != nil {
			return nil, err
		}
		var res []string
		for _, val := range dbUser {
			res = append(res, val.UserID)
		}
		req.SearchParam.CreateUserId = dslibutils.StringRef(strings.Join(res, ","))
	}
	// 这里做一下兜底,如果当前用户不在user表里面,直接返回空的列表
	tenantId := fwctx.GetTenantID(ctx) // 默认主账号
	userId := fwctx.GetUserID(ctx)
	if userId == "" { // 如果是主账号，则UserId用tenantId
		userId = tenantId
	}
	users, err := selfService.dbwUser.Get(ctx, userId, tenantId)
	if err != nil {
		log.Warn(ctx, "ticket: get users err:%s", err.Error())
		return nil, err
	}
	if users == nil || (users != nil && users.ID == 0) {
		return &model.DescribeTicketsResp{Total: 0, Tickets: []*model.TicketItem{}}, nil
	}
	log.Info(ctx, "ticket: get user result is %v", users)
	tickets, total, err := selfService.workflowDal.DescribeTickets(ctx, req, users.Role)
	if err != nil {
		return nil, err
	}
	ticketList := make([]*model.TicketItem, 0)
	for _, val := range tickets {
		createUser, err := selfService.getUserNameListFromMap(ctx, val.CreateUserId, val.TenantId)
		if err != nil {
			return nil, err
		}
		currentUser, err := selfService.getUserNameListFromMap(ctx, val.CurrentUserIds, val.TenantId)
		if err != nil {
			return nil, err
		}
		instanceType, err := model.InstanceTypeFromString(val.InstanceType)
		if err != nil {
			return nil, err
		}
		var dataArchiveConfig = &model.DataArchiveConfig{}
		if err := json.Unmarshal([]byte(val.DataArchiveConfig), dataArchiveConfig); err != nil {
			log.Warn(ctx, "ticket %v: unmarshal dataArchiveConfig err:%s", val.TicketId, err.Error())
		}
		ti := &model.TicketItem{
			TicketId:          strconv.Itoa(int(val.TicketId)),
			TicketType:        model.TicketType(val.TicketType),
			TicketStatus:      model.TicketStatus(val.TicketStatus),
			TicketExecuteType: model.ExecuteType(val.ExecuteType),
			InstanceType:      instanceType,
			InstanceId:        val.InstanceId,
			CreateUser:        &model.CreateUserInfo{CreateUserName: createUser, CreateUserId: val.CreateUserId},
			CurrentUserRole:   selfService.convertRoleFromDalToModel(val.CurrentUserRole),
			CurrentUser:       &model.CurrentUserInfo{CurrentUserName: currentUser, CurrentUserId: val.CurrentUserIds},
			CreateTime:        time.UnixMilli(val.CreateTime).UTC().Format(time.RFC3339),
			UpdateTime:        time.UnixMilli(val.UpdateTime).UTC().Format(time.RFC3339),
			Description:       val.Description,
			Memo:              val.Memo,
			Title:             val.Title,
			DBName:            val.DbName,
			DataArchiveConfig: dataArchiveConfig,
			AffectedRows:      val.AffectedRows,
		}
		ticketList = append(ticketList, ti)
	}

	return &model.DescribeTicketsResp{
		Total:   int32(total),
		Tickets: ticketList,
	}, nil
}

func (selfService *ticketService) DescribeTicketDetail(ctx context.Context, req *model.DescribeTicketDetailReq) (*model.DescribeTicketDetailResp, error) {
	log.Info(ctx, "ticket: get ticket id is [%v] ,tenant id is [%v],use id is [%v]",
		dslibutils.MustStrToInt64(req.TicketId), fwctx.GetTenantID(ctx), fwctx.GetUserID(ctx))
	ticket, err := selfService.workflowDal.DescribeByTicketID(ctx, dslibutils.MustStrToInt64(req.TicketId))
	if err != nil {
		log.Warn(ctx, "ticket: get ticket err:%s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_InputParamError, fmt.Sprintf("no corresponding ticket found%s, check!!!", req.TicketId))
	}
	createUser, err := selfService.getUserNameListFromMap(ctx, ticket.CreateUserId, ticket.TenantId)
	if err != nil {
		log.Warn(ctx, "ticket:get createUser err:%s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_InputParamError, fmt.Sprintf("failed to obtain creator information, check again"))
	}
	currentUser, err := selfService.getUserNameListFromMap(ctx, ticket.CurrentUserIds, ticket.TenantId)
	if err != nil {
		log.Warn(ctx, "ticket:get currentUser err:%s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_InputParamError, fmt.Sprintf("failed to obtain current processor information, check again"))
	}
	log.Info(ctx, "ticket:get ticket success")
	instanceType, err := model.InstanceTypeFromString(ticket.InstanceType)
	if err != nil {
		log.Info(ctx, "ticket:get InstanceType error")
		return nil, consts.ErrorWithParam(model.ErrorCode_InputParamError, fmt.Sprintf("failed to obtain database type, check again"))
	}

	var (
		batchSize            int64
		isReplicaDelayEnable bool
		replicaDelaySeconds  int32
		sleepTimeMs          int32
		dbBatchNum           int32
	)
	if ticket.Extra != nil && ticket.Extra["BatchSize"] != nil {
		batchSize = int64(ticket.Extra["BatchSize"].(float64))
	}
	if ticket.Extra != nil && ticket.Extra["IsEnableDelayCheck"] != nil {
		isReplicaDelayEnable = ticket.Extra["IsEnableDelayCheck"].(bool)
	}
	if ticket.Extra != nil && ticket.Extra["ReplicaDelaySeconds"] != nil {
		replicaDelaySeconds = int32(ticket.Extra["ReplicaDelaySeconds"].(float64))
	}
	if ticket.Extra != nil && ticket.Extra["SleepTimeMs"] != nil {
		sleepTimeMs = int32(ticket.Extra["SleepTimeMs"].(float64))
	}
	if ticket.Extra != nil && ticket.Extra["DBBatchNum"] != nil {
		dbBatchNum = int32(ticket.Extra["DBBatchNum"].(float64))
	}
	batchConfig := &model.BatchConfig{
		SleepTimeMs:         dslibutils.Int32Ref(sleepTimeMs),
		IsEnableDelayCheck:  dslibutils.BoolRef(isReplicaDelayEnable),
		ReplicaDelaySeconds: dslibutils.Int32Ref(replicaDelaySeconds),
		BatchSize:           dslibutils.Int32Ref(int32(batchSize)),
		DBBatchNum:          dslibutils.Int32Ref(dbBatchNum),
	}
	archiveConfig := &model.DataArchiveConfig{}
	if strings.TrimSpace(ticket.DataArchiveConfig) != "" {
		err = json.Unmarshal([]byte(ticket.DataArchiveConfig), &archiveConfig)
		if err != nil {
			log.Warn(ctx, "ticket:%d Unmarshal  DataArchiveConfig error: %s", ticket.TicketId, err.Error())
		}
	}
	entityTicket, err := selfService.ticketRepo.GetByID(ctx, req.TicketId)
	if err != nil {
		log.Warn(ctx, "ticket:%d GetByID error: %s", req.TicketId, err.Error())
		entityTicket = &entity.Ticket{}
	}

	result := &model.DescribeTicketDetailResp{
		TicketId:          req.TicketId,
		TicketType:        model.TicketType(ticket.TicketType),
		TicketStatus:      model.TicketStatus(ticket.TicketStatus),
		TicketExecuteType: model.ExecuteType(ticket.ExecuteType),
		CreateUser: &model.CreateUserInfo{
			CreateUserName: createUser,
			CreateUserId:   ticket.CreateUserId,
		},
		CurrentUser: &model.CurrentUserInfo{
			CurrentUserName: currentUser,
			CurrentUserId:   ticket.CurrentUserIds,
		},
		CreateTime:    time.UnixMilli(ticket.CreateTime).UTC().Format(time.RFC3339),
		UpdateTime:    time.UnixMilli(ticket.UpdateTime).UTC().Format(time.RFC3339),
		InstanceType:  instanceType,
		InstanceId:    ticket.InstanceId,
		DbName:        ticket.DbName,
		SqlText:       ticket.SqlText,
		Description:   ticket.Description,
		ExecStartTime: int32(ticket.ExecutableStartTime),
		ExecEndTime:   int32(ticket.ExecutableEndTime),
		Progress:      ticket.Progress,
		BatchConfig:   batchConfig,
		ArchiveConfig: archiveConfig,
		Memo:          dslibutils.StringRef(ticket.Memo),
		WorkflowId:    conv.Int64ToStr(ticket.WorkflowId),
		Title:         dslibutils.StringRef(ticket.Title),
		TicketConfig:  ticket.TicketConfig,
		TaskId:        ticket.TaskId,
		Submitted:     int32(ticket.Submitted),
		ProgressInfo: &model.ProgressInfo{
			TotalSubTask:       int32(entityTicket.RunningDetail.TotalSubTask),
			CurrentStep:        int32(entityTicket.RunningDetail.CurrentStep),
			CurrentTaskSql:     entityTicket.RunningDetail.CurrentTaskSql,
			CurrentTaskPercent: entityTicket.RunningDetail.CurrentTaskPercent,
			ProgressMemo:       entityTicket.RunningDetail.ProgressMemo,
		},
	}
	log.Info(ctx, "describe ticket detail is %v", utils.Show(result))
	return result, nil
}

func (selfService *ticketService) StopTicket(ctx context.Context, req *model.StopTicketReq) (*model.StopTicketResp, error) {
	ticket, err := selfService.workflowDal.DescribeByTicketID(ctx, dslibutils.MustStrToInt64(req.TicketId))
	if err != nil || ticket == nil {
		log.Warn(ctx, "stopTicket: get ticketId failed")
		return nil, consts.ErrorOf(model.ErrorCode_TicketIsNotExisted)
	}
	var kind string
	switch ticket.TicketType {
	case int8(model.TicketType_NormalSqlChange):
		kind = consts.ExecTicketActorKind
	case int8(model.TicketType_FreeLockSqlChange):
		kind = consts.FreeLockDMLActorKind
	case int8(model.TicketType_FreeLockStructChange):
		// 无锁DDL工单需要单独处理,部分无锁DDL工单是使用了ExecuteTicketActor直接执行的
		if selfService.IsEnableDDLDirectExecCommandsType(ticket.SqlText) {
			kind = consts.ExecTicketActorKind
		} else {
			kind = consts.OnlineDDLTicketActorKind
			if ticket.InstanceType == model.InstanceType_VeDBMySQL.String() {
				kind = consts.VeDBDDLTicketActorKind
			}
		}
	default:
		log.Warn(ctx, "ticket %v got wrong ticket type %v", ticket.TicketId, ticket.TicketType)
		return nil, fmt.Errorf("ticket %v got wrong ticket type %v", ticket.TicketId, ticket.TicketType)
	}

	if ticket.TicketType == int8(model.TicketType_FreeLockStructChange) && selfService.ticketCommonService.IsInGhostDDLWhite(ctx, fwctx.GetTenantID(ctx), ticket.InstanceType) {
		resp, err := selfService.actorClient.KindOf(consts.DbwInstanceTicketActorKind).
			Call(ctx, ticket.InstanceId, &shared.StopDbwTicket{
				TicketId:   req.TicketId,
				TicketType: shared.DbwTicketType(ticket.TicketType),
			})
		if err != nil {
			log.Warn(ctx, "send stop error:%s", err.Error())
			return &model.StopTicketResp{}, err
		}
		switch rsp := resp.(type) {
		case *shared.DbwTicketResponse:
			if rsp.Success {
				return &model.StopTicketResp{}, nil
			}
			log.Warn(ctx, "stopTicket: execute ticket failed %v", rsp.ErrMsg)
		default:
			log.Error(ctx, "stopTicket: stop ticket failed %v", rsp)
		}
		return &model.StopTicketResp{}, fmt.Errorf("ticket stop failed")
	}

	resp, err := selfService.actorClient.KindOf(kind).
		Call(ctx, conv.Int64ToStr(ticket.TicketId), &shared.StopTicket{
			TicketId:   ticket.TicketId,
			TaskId:     ticket.TaskId,
			TenantId:   ticket.TenantId,
			InstanceId: ticket.InstanceId,
		})
	if err != nil {
		log.Warn(ctx, "stopTicket: send message to %v exec ticket actor error:%s", kind, err.Error())
		return nil, err
	}
	switch rsp := resp.(type) {
	case *shared.StopTicketResp:
		if rsp.Status == consts.StatusSuccess {
			log.Info(ctx, "stopTicket: stop ticket success! %v", rsp)
			selfService.workflowDal.UpdateWorkStatus(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError),
				Description: "the ticket was manually terminated successfully",
				TicketId:    ticket.TicketId,
				TenantId:    ticket.TenantId})
			return &model.StopTicketResp{Code: model.ErrCode_Success, Message: "the ticket was manually terminated successfully"}, nil
		}
		if rsp.Status == consts.StatusFailed {
			log.Error(ctx, "stopTicket: stop ticket failed! %v", rsp)
			selfService.workflowDal.UpdateWorkStatus(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError),
				Description: fmt.Sprintf("manual terminate ticket failed, %v, contact the administrator for processing!!!", rsp.ErrMessage),
				TicketId:    ticket.TicketId,
				TenantId:    ticket.TenantId})
			return &model.StopTicketResp{Code: model.ErrCode_Error, Message: "manual termination of ticket failed, contact the administrator for processing!!!"}, fmt.Errorf("termination of ticket failed, contact the administrator for processing")
		}
		log.Error(ctx, "stopTicket: execute ticket failed %v", rsp)
		return nil, fmt.Errorf("ticket stop failed")
	default:
		log.Error(ctx, "stopTicket: stop ticket failed %v", rsp)
		return nil, fmt.Errorf("ticket stop failed")
	}
}

func (selfService *ticketService) GetInstanceOwnerIds(ctx context.Context, tenantId string, instanceId string) (string, error) {
	InstanceOwnerId, err := selfService.workflowDal.GetInstanceOwnerIds(ctx, tenantId, instanceId)
	if err != nil {
		errMsg := fmt.Sprintf("failed to obtain the instance owner, instance ID：%s, err:%v", instanceId, err)
		log.Warn(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}
	return InstanceOwnerId, err
}

func (selfService *ticketService) GetInstanceDbaIds(ctx context.Context, tenantId string, instanceId string) (string, error) {
	InstanceDbaId, err := selfService.workflowDal.GetInstanceDbaIds(ctx, tenantId, instanceId)
	if err != nil {
		errMsg := fmt.Sprintf("failed to obtain the instance DBA, instance ID：%s, err:%v", instanceId, err)
		log.Warn(ctx, errMsg)
		return "", fmt.Errorf(errMsg)
	}
	return InstanceDbaId, err
}

func (selfService *ticketService) GetTenantAdminIds(ctx context.Context, tenantId string) (*[]string, error) {
	tenantAdminIds, err := selfService.workflowDal.GetTenantAdminIds(ctx, tenantId)
	if err != nil {
		errMsg := fmt.Sprintf("failed to obtain administrator, tenantId：%s, err:%v", tenantId, err)
		log.Warn(ctx, errMsg)
		return nil, fmt.Errorf(errMsg)
	}
	return tenantAdminIds, err
}

func (selfService *ticketService) GetUserNameByIds(ctx context.Context, userIds string, tenantId string) (*map[string]string, error) {
	spaceRe, _ := regexp.Compile(`\s*,\s*`)
	idList := spaceRe.Split(userIds, -1)
	ticketUserInfos, err := selfService.workflowDal.GetUserNameByIds(ctx, &idList, tenantId)
	res := make(map[string]string)
	if err != nil || ticketUserInfos == nil {
		errMsg := fmt.Sprintf("failed to obtain administrator, tenantId：%s, err:%v", tenantId, err)
		log.Warn(ctx, errMsg)
		return &res, fmt.Errorf(errMsg)
	}
	for _, value := range *ticketUserInfos {
		res[value.UserId] = value.UserName
	}
	return &res, nil
}

func (selfService *ticketService) getUserNameListFromMap(ctx context.Context, userIds string, tenantId string) (string, error) {
	userMap, err := selfService.GetUserNameByIds(ctx, userIds, tenantId)
	if err != nil {
		return "", err
	}
	userName := make([]string, 0, len(*userMap))
	for _, v := range *userMap {
		userName = append(userName, v)
	}
	createUser := strings.Join(userName, ",")
	return createUser, nil
}

func (selfService *ticketService) UpdatePreCheckResult(ctx context.Context, ticketId int64, preCheckResult *model.PreCheckTicketResp) error {
	err := selfService.workflowDal.ReplaceIntoPreCheckResult(ctx, ticketId, preCheckResult.CheckItems)
	if err != nil {
		log.Warn(ctx, "Insert/update pre check result failed, err:%v", err)
	}
	return err
}

// Deprecated: 已不再使用
func (selfService *ticketService) GetNextUserIdInfo(ctx context.Context, flowInfo *dao.BpmFlowInfo) (*dao.UserRole, error) {
	// TODO 现在默认，实例owner， dba，管理员  后续从配置表里捞出对应流程
	//switch flowInfo.FlowStep + 1 {
	//case 1:
	//	return selfService.getInstanceOwnerRole(ctx, flowInfo)
	//case 2:
	//	return selfService.getInstanceDbaRole(ctx, flowInfo)
	//case 3:
	//	return selfService.getManagerRole(ctx, flowInfo)
	//}
	// 全部审批完了，就转回给创建人
	userRole := dao.UserRole{}
	var err error
	userRole.Id = flowInfo.CreateUserId
	userRole.TenantId = flowInfo.TenantId
	userRole.Role, err = selfService.getUserRole(ctx, flowInfo.CreateUserId, flowInfo.TenantId, flowInfo.InstanceId)
	if err != nil {
		return nil, err
	}
	return &userRole, nil
}

func (selfService *ticketService) IsUserExists(ctx context.Context, userId string, tenantId string) (bool, error) {
	isUserExists, err := selfService.workflowDal.IsUserExists(ctx, userId, tenantId)
	if err != nil {
		log.Info(ctx, "failed to obtain user information: err：%v", err)
		return false, err
	}
	return isUserExists, nil
}

func (selfService *ticketService) checkUserTenant(ctx context.Context, tenantId string, ticketId int64) error {
	isUserInTenant, err := selfService.workflowDal.IsUserInTenant(ctx, tenantId, ticketId)
	if err != nil {
		log.Warn(ctx, "failed to obtain ticket information，ticket ID:%d, err：%v", ticketId, err)
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if !isUserInTenant {
		return consts.ErrorOf(model.ErrorCode_UserMgmtPermissionDeny)
	}
	return nil
}

func (selfService *ticketService) CheckTicketUserInfo(ctx context.Context, ticketId int64) error {
	// 拿租户ID和用户ID
	tenantId := fwctx.GetTenantID(ctx)
	userId := fwctx.GetUserID(ctx)
	if tenantId == "" {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "not get tenantId")
	}
	if userId == "" {
		userId = tenantId
	}
	// 检查用户是不是存在，不存在/已删除/禁用
	isExist, err := selfService.IsUserExists(ctx, userId, tenantId)
	if err != nil {
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if !isExist {
		return consts.ErrorOf(model.ErrorCode_UserNotJoinUserMgmt)
	}
	// 检查用户租户id是否和工单的租户id一致
	return selfService.checkUserTenant(ctx, tenantId, ticketId)
}

func (selfService *ticketService) convertRoleFromDalToModel(dalRole string) string {
	switch {
	case dalRole == dao.AdminUser:
		return model.CurrentUserRole_InstanceManager.String()
	case dalRole == dao.OwnerUser:
		return model.CurrentUserRole_InstanceOwner.String()
	case dalRole == dao.DbaUser:
		return model.CurrentUserRole_InstanceDBA.String()
	default:
		return dalRole
	}
}

func (selfService *ticketService) createTlsClient(ctx context.Context) tls.Client {
	// TODO 这块等工单优化的时候,统一打开,现在暂时没用到
	c3Cfg := selfService.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	Ak := c3Cfg.TLSServiceAccessKey
	Sk := c3Cfg.TLSServiceSecretKey
	regionId := selfService.location.RegionID()
	tlsEndpoint := fmt.Sprintf(consts.TLSEndpointTemplate, regionId)
	//log.Info(ctx, "tls endpoint is %s,%s,%s,%v", tlsEndpoint, c3Cfg.TLSServiceAccessKey, c3Cfg.TLSServiceSecretKey)

	return tls.NewClient(tlsEndpoint, Ak, Sk, "", regionId)
}

func (selfService *ticketService) getTicketDetailFromTls(ctx context.Context, ticket *dao.Ticket) (*tls.SearchLogsResponse, error) {
	client := selfService.createTlsClient(ctx)
	// TODO 这块填写最终的Topic
	//cnf := selfService.conf.Get(ctx)
	c3Cfg := selfService.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	retLog, err := client.SearchLogs(&tls.SearchLogsRequest{
		TopicID:   c3Cfg.TicketLogTopic,
		Query:     conv.Int64ToStr(ticket.TicketId),
		StartTime: ticket.CreateTime/1000 - 60,
		EndTime:   time.Now().Unix() + 60,
		Limit:     1000,
		HighLight: false,
		Context:   "",
		Sort:      "",
	})
	if err != nil {
		log.Warn(ctx, "ticketId:%s, 读取TLS数据失败：%s", conv.Int64ToStr(ticket.TicketId), utils.Show(err))
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if retLog == nil || retLog.Logs == nil || len(retLog.Logs) == 0 {
		log.Info(ctx, "retLog 为空，未找到ticketId: %d 对应报告", conv.Int64ToStr(ticket.TicketId))
		return nil, nil
	}
	return retLog, nil
}

func (selfService *ticketService) DescribeTicketLogDetail(ctx context.Context, req *model.DescribeTicketLogDetailReq) (*model.DescribeTicketLogDetailResp, error) {
	// TODO 这里给freelockdmlticket_actor发消息
	ticket, err := selfService.workflowDal.DescribeByTicketID(ctx, dslibutils.MustStrToInt64(req.TicketId))
	if err != nil || ticket == nil {
		log.Warn(ctx, "DescribeTicketLogDetail: 获取ticket %s 失败", req.TicketId)
		return nil, consts.ErrorOf(model.ErrorCode_TicketIsNotExisted)
	}
	fromTls, err := selfService.getTicketDetailFromTls(ctx, ticket)
	if err != nil {
		log.Warn(ctx, "DescribeTicketLogDetail: 获取工单 %s tls日志失败：%s", req.TicketId, err.Error())
		return nil, err
	}
	log.Info(ctx, "ticket log from tls is %v", fromTls)
	if fromTls == nil || fromTls.Logs == nil {
		return &model.DescribeTicketLogDetailResp{}, nil
	}
	var res []*model.DescribeTicketLogItem
	for _, val := range fromTls.Logs {
		res = append(res, &model.DescribeTicketLogItem{
			Message:    val["Message"].(string),
			CreateTime: val["CreateTime"].(string),
			Progress:   val["Progress"].(string),
			TenantId:   val["TenantId"].(string),
			InstanceId: val["InstanceId"].(string),
			TicketId:   val["TicketId"].(string),
		})
	}
	return &model.DescribeTicketLogDetailResp{TicketExecLogs: res}, nil
}

func (selfService *ticketService) IsColInPk(target string, cols []*datasource.TableIndexInfo) bool {
	for _, val := range cols {
		if val.ColumnName == target {
			return true
		}
	}
	return false
}

func (selfService *ticketService) GetTableIndexInfo(ctx context.Context, ds *shared.DataSource, req *datasource.GetTableIndexInfoReq) (
	*datasource.GetTableInfoIndexResp, error) {
	info, err := selfService.ds.GetTableIndexInfo(ctx, &datasource.GetTableIndexInfoReq{
		TableName: req.TableName,
		Source:    ds,
		Command:   req.Command,
	})
	if err != nil {
		log.Warn(ctx, "get ticket table index info error:%v ", err)
		return nil, err
	}
	return info, nil
}

func (selfService *ticketService) GetIndexValue(ctx context.Context, req *datasource.GetIndexValueReq) (*datasource.GetIndexValueResp, error) {
	// 从连接池获取链接
	indexValue, err := selfService.ds.GetTableIndexValue(ctx, &datasource.GetIndexValueReq{
		TableName: req.TableName,
		Source:    req.Source,
		Command:   req.Command,
		Columns:   req.Columns,
	})
	if err != nil {
		log.Warn(ctx, "get ticket table index value error:%s ", err.Error())
		return nil, fmt.Errorf("failed to obtain ticket table index boundary information,%v", err)
	}
	return indexValue, nil
}

func (selfService *ticketService) CreateOnlineDDLSqlTaskDryRun(ctx context.Context, req *datasource.CreateFreeLockCorrectOrderDryRunReq) (*datasource.CreateFreeLockCorrectOrderDryRunResp, error) {
	_, err := selfService.ds.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
		Type:       req.InstanceType,
	})
	if err != nil {
		log.Warn(ctx, "describe instance fail. %s", err)
		if strings.Contains(err.Error(), "InstanceNotFound") {
			return &datasource.CreateFreeLockCorrectOrderDryRunResp{
				DryRunSuccess: false,
				ErrorCode:     model.ErrorCode_InstanceNotFound.String(),
				Reason:        fmt.Sprintf("instance %v not found", req.InstanceId),
			}, consts.ErrorOf(model.ErrorCode_InstanceNotFound)
		}
		return &datasource.CreateFreeLockCorrectOrderDryRunResp{
			DryRunSuccess: false,
			ErrorCode:     model.ErrorCode_InstanceStatusNotSatisfy.String(),
			Reason:        fmt.Sprintf("describe instance %v err %v", req.InstanceId, err),
		}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	resp, err := selfService.ds.CreateFreeLockCorrectOrderDryRun(ctx, &datasource.CreateFreeLockCorrectOrderReq{
		InstanceId: req.InstanceId,
		Type:       req.InstanceType,
		TableName:  req.TableName,
		DBName:     req.DBName,
		ExecSQL:    req.ExecSQL,
		Comment:    req.Comment,
	})
	if err != nil {
		log.Warn(ctx, "create dry run task error %v ", err)
		return &datasource.CreateFreeLockCorrectOrderDryRunResp{
			DryRunSuccess: false,
			ErrorCode:     model.ErrorCode_InternalError.String(),
			Reason:        fmt.Sprintf("instance %v create dry run task err %v", req.InstanceId, err),
		}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return &datasource.CreateFreeLockCorrectOrderDryRunResp{
		DryRunSuccess: resp.DryRunSuccess,
		ErrorCode:     resp.ErrorCode,
		Reason:        resp.Reason,
	}, nil
}

func (selfService *ticketService) UpdateTicketCurrentUser(ctx context.Context, approvalNodeId int64, approvalUserIds string, approvalNodeName string) error {
	return selfService.workflowDal.UpdateTicketCurrentUser(ctx, approvalNodeId, approvalUserIds, approvalNodeName)
}
func (selfService *ticketService) IsTemplateInTicket(ctx context.Context, tenantId string, approvalTemplateId int64) (bool, error) {
	return selfService.workflowDal.IsTemplateInTicket(ctx, tenantId, approvalTemplateId)
}

func (selfService *ticketService) DescribeTicketsForOperateRecord(ctx context.Context, req *model.DescribeTicketRecordListReq) (*model.DescribeTicketRecordListResp, error) {
	// 这里需要做个判断,如果传入了req.SearchParam.CreateUserName的信息,代表想要通过创建人过滤，这里填入User的Id到req中。
	if req.IsSetTicketRecordSearchParam() && req.GetTicketRecordSearchParam().IsSetCreateUserName() {
		dbUser, err := selfService.dbwUser.GetUserByName(ctx, *req.GetTicketRecordSearchParam().CreateUserName, fwctx.GetTenantID(ctx))
		if err != nil {
			return nil, err
		}
		var res []string
		for _, val := range dbUser {
			res = append(res, val.UserID)
		}
		req.TicketRecordSearchParam.CreateUserId = dslibutils.StringRef(strings.Join(res, ","))
	}

	// 这里做一下兜底,如果当前用户不在user表里面,直接返回空的列表
	tenantId := fwctx.GetTenantID(ctx) // 默认主账号
	userId := fwctx.GetUserID(ctx)
	if userId == "" { // 如果是主账号，则UserId用tenantId
		userId = tenantId
	}
	users, err := selfService.dbwUser.Get(ctx, userId, tenantId)
	if err != nil {
		log.Warn(ctx, "ticket: get users err:%s", err.Error())
		return nil, err
	}
	if users == nil || (users != nil && users.ID == 0) {
		return &model.DescribeTicketRecordListResp{Total: 0, TicketRecordList: []*model.TicketRecord{}}, nil
	}

	tickets, total, err := selfService.workflowDal.DescribeTicketsForOperateRecord(ctx, req)
	if err != nil {
		return nil, err
	}
	ticketList := make([]*model.TicketRecord, 0)
	for _, val := range tickets {
		createUser, err := selfService.getUserNameListFromMap(ctx, val.CreateUserId, val.TenantId)
		if err != nil {
			return nil, err
		}
		ticketType := model.TicketType(val.TicketType)
		record := &model.TicketRecord{
			TicketId:       strconv.Itoa(int(val.TicketId)),
			TicketStatus:   model.TicketStatus(val.TicketStatus),
			CreateUserName: createUser,
			CreateUserId:   val.CreateUserId,
			DbName:         &val.DbName,
			InstanceID:     &val.InstanceId,
			UpdateTime:     time.UnixMilli(val.UpdateTime).UTC().UnixMilli(),
		}
		if req.GetCreateFrom() != TicketFromDataArchive {
			record.TicketType = &ticketType
		}
		ticketList = append(ticketList, record)
	}

	return &model.DescribeTicketRecordListResp{
		Total:            int64(total),
		TicketRecordList: ticketList,
	}, nil
}

// SubmitTicket 提交工单做2件事情
// 1. 把工单的提交字段改为已提交
// 2. 把工单的状态改为审批中
func (selfService *ticketService) SubmitTicket(ctx context.Context, req *model.SubmitTicketReq) (*model.SubmitTicketResp, error) {
	ticket, err := selfService.GetTicket(ctx, dslibutils.MustStrToInt64(req.TicketId))
	if err != nil {
		log.Warn(ctx, "ticketId: %d GetTicket error :%v", req.TicketId, err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}

	if ticket.TicketType == int32(model.TicketType_FreeLockStructChange) && selfService.ticketCommonService.IsInGhostDDLWhite(ctx, fwctx.GetTenantID(ctx), ticket.InstanceType.String()) {
		// online ddl 走其他流程
		ticketConfig, err := selfService.formatTicketConfig(ctx, req, ticket)
		if err != nil {
			return nil, err
		}
		err = selfService.workflowDal.UpdateTicketConfig(ctx, req.TicketId, StructToJson(ticketConfig))
		if err != nil {
			log.Warn(ctx, "ticketId: %d UpdateTicketConfig error :%v", req.TicketId, err)
			return nil, consts.ErrorOf(model.ErrorCode_InternalError)
		}
		return selfService.SubmitOnlineDDlTicket(ctx, ticket, req.TicketId)
	}
	err = selfService.workflowDal.UpdateTicketSubmitFlag(ctx, req.TicketId, Submitted)
	if err != nil {
		log.Warn(ctx, "ticketId: %d UpdateTicketSubmitFlag error :%v", req.TicketId, err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	// 如果工单是预检查错误,这属于强制提交,则需要把工单状态改为审批中，这时候要变更审批流的状态
	// 否则工单是预检查成功的,不需要变更审批流的状态,因为在预检查的最后一步,已经变更过了
	log.Info(ctx, "ticket is %v", utils.Show(ticket))
	if ticket.TicketStatus == int32(model.TicketStatus_TicketPreCheckError) {
		log.Warn(ctx, "ticket %v is precheck error,need force submit", ticket.TicketId)
		selfService.ChangePreCheckTicketStatusAndOperator(ctx, ticket, TicketExamine, &dao.BpmFlowInfo{})
	}
	err = selfService.AutoApproval(ctx, ticket.TicketId)
	if err != nil {
		return nil, err
	}
	return &model.SubmitTicketResp{
		Code:   model.ErrCode_Success,
		ErrMsg: fmt.Sprintf("Submit Ticket %v Success", req.TicketId),
	}, nil
}

func (selfService *ticketService) formatTicketConfig(ctx context.Context, req *model.SubmitTicketReq, ticket *shared.Ticket) (*entity.TicketConfig, error) {
	onlineDDlConfig, err := selfService.formatOnlineDDlTicketConfig(ctx, req, ticket)
	if err != nil {
		return nil, err
	}
	return &entity.TicketConfig{
		OnlineDDlTicketConfig: onlineDDlConfig,
	}, nil
}

func (selfService *ticketService) formatOnlineDDlTicketConfig(ctx context.Context, req *model.SubmitTicketReq, ticket *shared.Ticket) (entity.OnlineDDlTicketConfig, error) {
	//resp := entity.OnlineDDlTicketConfig{}
	//resp.UseLock = !req.IsUnlock
	if ticket.TicketType != int32(model.TicketType_FreeLockStructChange) {
		return entity.OnlineDDlTicketConfig{}, nil
	}

	if req.OnlineDDlConfig == nil {
		defaultOnlineDDl, err := selfService.getDefaultOnlineDDLValue(ctx, ticket.InstanceId, ticket.InstanceType.String())
		if err != nil {
			return entity.OnlineDDlTicketConfig{}, consts.ErrorOf(model.ErrorCode_ErrSelectOnlineDDLRuleFailed)
		}
		req.OnlineDDlConfig = defaultOnlineDDl
	}

	dmlBatchSize := int32(100)
	cnf := selfService.conf.Get(ctx)
	if cnf.OnlineDDlDmlBatchSize > 0 {
		dmlBatchSize = cnf.OnlineDDlDmlBatchSize
	}

	return entity.OnlineDDlTicketConfig{
		MaxLagMillis:               5000,
		ChangeTableLockTimeout:     req.OnlineDDlConfig.ChangeTableLockTimeout,
		ChangeTableRetryCount:      req.OnlineDDlConfig.ChangeTableRetryCount,
		IsTableCopyDMSAuto:         req.OnlineDDlConfig.IsTableCopyDMSAuto,
		ChunkSize:                  req.OnlineDDlConfig.GetTableCopySize(),
		TableCopySize:              req.OnlineDDlConfig.GetTableCopySize(),
		ChangeTableStrategy:        int64(req.OnlineDDlConfig.ChangeTableStrategy),
		IsExecuteImmediately:       req.OnlineDDlConfig.IsExecuteImmediately,
		ExecuteTime:                req.OnlineDDlConfig.GetExecuteTime(),
		UseLock:                    !req.IsUnlock,
		DMLBatchSize:               dmlBatchSize,
		IsOpenKillLongTransaction:  req.OnlineDDlConfig.IsOpenKillLongTransaction,
		KillLongTransactionTimeout: req.OnlineDDlConfig.KillLongTransactionTimeout,
	}, nil
}

func (selfService *ticketService) getDefaultOnlineDDLValue(ctx context.Context, instanceId string, instanceType string) (*model.OnlineDDlConfig, error) {
	//首先，先根据实例ID去dbw_instance表内，查询到该实例绑定的安全规则集ID
	dbwInstance, err := selfService.dbwInstance.Get(ctx, instanceId, instanceType, shared.Volc.String(), fwctx.GetTenantID(ctx), model.ControlMode_Management.String())
	if err != nil || dbwInstance == nil || dbwInstance.SecurityGroupId == 0 {
		log.Error(ctx, "DescribeOnlineDDLSecurityRule get instance failed, err is:", err)
		return nil, consts.ErrorOf(model.ErrorCode_InstanceNotInSecureMode)
	}

	//查询到安全管控实例信息后，获取到安全规则集ID，然后用安全规则名称查询安全规则
	groupId := dbwInstance.SecurityGroupId
	condition := &dal.SecRuleParam{
		SecGroupID: conv.Int64ToStr(groupId),
		Category:   model.SqlExecutionType_SqlTicket.String(),
		RuleFunc:   "OnlineDDLConfiguration",
		OrderBy:    "created_at",
		SortBy:     model.SortBy_ASC.String(),
	}
	ruleList, err := selfService.secRuleDal.List(ctx, fwctx.GetTenantID(ctx), condition)
	if err != nil {
		log.Warn(ctx, "DescribeOnlineDDLSecurityRule list failed, err is: %v", err)
		return nil, consts.ErrorOf(model.ErrorCode_ErrSelectOnlineDDLRuleFailed)
	}

	if ruleList == nil || len(ruleList.SecurityRules) == 0 {
		return model.NewOnlineDDlConfig(), nil
	}

	rules := ruleList.SecurityRules
	securityRule := rules[0]
	//接下来，如果查询到了安全规则的信息,判断当前安全规则是否启用，如果没有启用，就返回默认值
	if securityRule.State == 0 {
		return model.NewOnlineDDlConfig(), nil
	}

	var OnlineDDLConfig model.OnlineDDlConfig
	log.Info(ctx, "DescribeOnlineDDLSecurityRule securityRule info is:%v", utils.Show(securityRule))
	err = json_tool.JsonUnmarshal([]byte(securityRule.RuleValue), &OnlineDDLConfig)
	if err != nil {
		log.Warn(ctx, "DescribeOnlineDDLSecurityRule JsonUnmarshal error:%s ", err)
		return nil, consts.ErrorOf(model.ErrorCode_ErrSelectOnlineDDLRuleFailed)
	}
	return &OnlineDDLConfig, nil
}

func (selfService *ticketService) SubmitOnlineDDlTicket(ctx context.Context, ticket *shared.Ticket, ticketId string) (*model.SubmitTicketResp, error) {
	// TODO 这期只做online
	msg := &shared.SubmitTicket{TicketId: ticketId, TicketType: shared.OnlineDDL}
	actorResp, err := selfService.actorClient.KindOf(consts.DbwInstanceTicketActorKind).Call(ctx, ticket.InstanceId, msg)
	if err != nil {
		log.Warn(ctx, "submit ticket error:%v", err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	resp, ok := actorResp.(*shared.DbwTicketResponse)
	if !ok || !resp.Success {
		log.Warn(ctx, "resp error, resp:%s", actorResp)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	err = selfService.AutoApproval(ctx, ticket.TicketId)
	if err != nil {
		return nil, err
	}
	return &model.SubmitTicketResp{Code: model.ErrCode_Success, ErrMsg: fmt.Sprintf("Submit Ticket %s Success", ticketId)}, nil
}

func (selfService *ticketService) DescribePreCheckDetail(ctx context.Context, req *model.DescribePreCheckDetailReq) (*model.DescribePreCheckDetailResp, error) {
	ticket, err := selfService.ticketRepo.GetByID(ctx, req.TicketId)
	if err != nil {
		log.Warn(ctx, "ticketId: %d getTicket error :%v", req.TicketId, err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	// 0、解析SQL语句,查看是否包含DDL
	parseStruct, _ := selfService.ParseSqlTextToStruct(ctx, &shared.Ticket{
		TicketId:     conv.StrToInt64(ticket.TicketId, 0),
		InstanceType: shared.DataSourceType(shared.DataSourceType_value[ticket.InstanceType]),
		SqlText:      ticket.SqlText,
	})
	if parseStruct == nil {
		parseStruct = &TicketSqlTextParseResult{}
	}
	userId := ticket.CreateUserId
	tenantId := ticket.TenantId
	isUpperAccount, err := selfService.workflowDal.IsUpperAccount(ctx, tenantId, userId, ticket.InstanceId)
	if err != nil {
		log.Warn(ctx, "check super account error %s", err)
	}
	log.Info(ctx, "ticket %v isUpperAccount is %v", ticket.TicketId, isUpperAccount)
	// 走新online ddl 逻辑
	if ticket.TicketType == int8(model.TicketType_FreeLockStructChange) && selfService.ticketCommonService.IsInGhostDDLWhite(ctx, fwctx.GetTenantID(ctx), ticket.InstanceType) {
		log.Info(ctx, "ticket %v is online ddl ticket", ticket.TicketId)
		detail, err := selfService.DescribeOnlineDDlPreCheckDetail(ctx, req, ticket.TenantId)
		if detail != nil {
			detail.IsContainDDL = true
		}
		return detail, err
	}

	result := &model.DescribePreCheckDetailResp{}
	flagResult, err := selfService.getPreCheckEnableSubmitFlag(ctx, dslibutils.MustStrToInt64(req.TicketId), isUpperAccount)
	if err != nil {
		log.Warn(ctx, "ticketId: %v getPreCheckEnableSubmitFlag error :%v", req.TicketId, err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	log.Info(ctx, "ticketId: %v getPreCheckEnableSubmitFlag result :%v", req.TicketId, utils.Show(flagResult))
	result.AllPass = flagResult.AllPass
	result.EnableSubmitTicket = flagResult.EnableSubmitTicket
	result.Step = flagResult.Step
	result.TotalNeedCheck = int32(len(flagResult.CheckItems))
	result.PrecheckFinished = flagResult.PrecheckFinished
	result.IsContainDDL = parseStruct.isContainDDL
	result.PreCheckDetails = []*model.PreCheckDetail{}
	if !result.PrecheckFinished {
		log.Warn(ctx, "ticket %v precheck not finished", ticket.TicketId)
		return result, nil
	}

	res, err := selfService.preCheckDetailDal.DescribeByTicketID(ctx, dslibutils.MustStrToInt64(req.TicketId), fwctx.GetTenantID(ctx))
	if err != nil {
		log.Warn(ctx, "ticketId: %v DescribePreCheckDetail error :%v", req.TicketId, err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if len(res) == 0 {
		// 没有detail详情，但是finished了，说明在写入detail前，就读取了result结果，中间产生了并发问题。
		// 修改finished为false 我们让前端重新读即可
		result.PrecheckFinished = false
		return result, nil
	}

	log.Info(ctx, "ticketId: %v DescribePreCheckDetail result :%v", req.TicketId, utils.Show(res))
	ExplainDetail := &model.PreCheckDetail{ItemName: PreCheckExplainCn, PreCheckItem: model.PreCheckItem_Explain, ItemDetails: []*model.ItemDetail{}}
	selfService.setItemResult(flagResult, ExplainDetail)
	SyntaxDetail := &model.PreCheckDetail{ItemName: PreCheckSyntaxCn, PreCheckItem: model.PreCheckItem_Syntax, ItemDetails: []*model.ItemDetail{}}
	selfService.setItemResult(flagResult, SyntaxDetail)
	PermissionDetail := &model.PreCheckDetail{ItemName: PreCheckPermissionCn, PreCheckItem: model.PreCheckItem_Permission, ItemDetails: []*model.ItemDetail{}}
	selfService.setItemResult(flagResult, PermissionDetail)
	SecurityDetail := &model.PreCheckDetail{ItemName: PreCheckSecurityRuleCn, PreCheckItem: model.PreCheckItem_SecurityRule, ItemDetails: []*model.ItemDetail{}}
	selfService.setItemResult(flagResult, SecurityDetail)
	for _, val := range res {
		ExplainDetail.ItemDetails = append(ExplainDetail.ItemDetails, &model.ItemDetail{SQL: val.SqlText, Result_: val.ExplainResult})
		SyntaxDetail.ItemDetails = append(SyntaxDetail.ItemDetails, &model.ItemDetail{SQL: val.SqlText, Result_: val.SyntaxCheckResult})
		PermissionDetail.ItemDetails = append(PermissionDetail.ItemDetails, &model.ItemDetail{SQL: val.SqlText, Result_: val.PermissionCheckResult})
		SecurityDetail.ItemDetails = append(SecurityDetail.ItemDetails, &model.ItemDetail{SQL: val.SqlText, Result_: val.SecurityCheckResult})
	}
	result.TicketId = req.TicketId
	result.PreCheckDetails = append(result.PreCheckDetails, ExplainDetail, SyntaxDetail, PermissionDetail, SecurityDetail)
	log.Info(ctx, "ticketId: %v DescribePreCheckDetail result :%v", req.TicketId, utils.Show(result))
	return result, nil
}

func (selfService *ticketService) setItemResult(ret *model.PreCheckTicketResp, result *model.PreCheckDetail) {
	log.Warn(context.Background(), "pos-xxx result:%s,  %s", result.PreCheckItem.String(), ret.CheckItems)
	for _, val := range ret.CheckItems {
		if val.Item == result.ItemName {
			result.Result_ = val.Memo
			if val.Status == model.PreCheckStatus_Error {
				result.PreCheckState = model.PreCheckState_Error
			}
			if val.Status == model.PreCheckStatus_Pass {
				result.PreCheckState = model.PreCheckState_Success
			}
		}
	}
}

func (selfService *ticketService) DescribeOnlineDDlPreCheckDetail(ctx context.Context, req *model.DescribePreCheckDetailReq, tenantId string) (*model.DescribePreCheckDetailResp, error) {
	res, err := selfService.ticketRepo.GetPreCheckResult(ctx, req.TicketId, tenantId)
	if err != nil {
		log.Warn(ctx, "get preCheck result error:%s", err.Error())
		return nil, err
	}
	resp := &model.DescribePreCheckDetailResp{TicketId: req.TicketId, PreCheckDetails: []*model.PreCheckDetail{}, PrecheckFinished: true}
	resp.TotalNeedCheck = OnlineDDlCheckNum
	resp.Step = int32(len(res))
	isAllPass := true
	isSubmit := true
	for _, value := range res {
		sqlRes, err := selfService.ticketRepo.GetPreCheckSqlResult(ctx, value.Id)
		if err != nil {
			log.Warn(ctx, "get preCheck sql result error:%s", err.Error())
			return nil, err
		}
		if value.PreCheckState == model.PreCheckState_Error {
			isAllPass = false
			isSubmit = false
		}

		preCheckItem, _ := model.PreCheckItemFromString(value.ItemNameEN)
		//if preCheckItem == model.PreCheckItem_UniqueKey && value.PreCheckState == model.PreCheckState_Warn {
		//	isSubmit = true
		//}
		preCheckDetail := &model.PreCheckDetail{
			ItemName:      value.ItemNameCN,
			PreCheckItem:  preCheckItem,
			PreCheckState: value.PreCheckState,
			Result_:       value.Result_,
			ItemDetails:   sqlRes,
		}
		resp.PreCheckDetails = append(resp.PreCheckDetails, preCheckDetail)
	}
	preCheckDetail := &model.PreCheckDetail{
		ItemName:      "无锁结构变更运行参数",
		PreCheckItem:  model.PreCheckItem_FreeLockParam,
		PreCheckState: model.PreCheckState_Success,
		Result_:       "",
		ItemDetails:   nil,
	}
	if resp.Step < OnlineDDlCheckNum {
		resp.PrecheckFinished = false
	}
	resp.AllPass = isAllPass
	resp.EnableSubmitTicket = isSubmit
	resp.PreCheckDetails = append(resp.PreCheckDetails, preCheckDetail)
	return resp, nil
}
