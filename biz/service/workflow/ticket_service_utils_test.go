package workflow

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	model2 "code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	parser "code.byted.org/infcs/ds-sql-parser"
	"code.byted.org/infcs/ds-sql-parser/ast"
	"context"
	"errors"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/smartystreets/goconvey/convey"
	"testing"
	"time"
)

//func TestCheckSqlFormat(t *testing.T) {
//	service := getService()
//
//	mock1 := mockey.Mock((*ticketService).SecurityRuleCheck).Return(true, nil).Build()
//	defer mock1.UnPatch()

//resp := service.initCreateTicketResp(model.PreCheckStatus_Undo)
//ds := &shared.DataSource{}
//res := service.checkSqlFormat(context.Background(),
//	&shared.Ticket{InstanceType: shared.MySQL, TicketType: int32(model.TicketType_NormalSqlChange), SqlText: "update t_test set a = 1; delete from t_test"}, resp, ds)
//assert.Equal(t, res, true)
//assert.Equal(t, resp.CheckItems[0].Status, model.PreCheckStatus_Pass)
//
//res = service.checkSqlFormat(context.Background(), &shared.Ticket{InstanceType: shared.MySQL, TicketType: int32(model.TicketType_NormalSqlChange), SqlText: "update t_test a = 1; delete from t_test"}, resp, ds)
//assert.Equal(t, res, false)
//assert.Equal(t, resp.CheckItems[0].Status, model.PreCheckStatus_Error)
//
//res = service.checkSqlFormat(context.Background(), &shared.Ticket{InstanceType: shared.MySQL, TicketType: int32(model.TicketType_FreeLockStructChange), SqlText: "update t_test a = 1; delete from t_test"}, resp, ds)
//assert.Equal(t, res, false)
//assert.Equal(t, resp.CheckItems[0].Status, model.PreCheckStatus_Error)
//
//res = service.checkSqlFormat(context.Background(), &shared.Ticket{InstanceType: shared.MySQL, TicketType: int32(model.TicketType_FreeLockSqlChange), SqlText: "update t_test a = 1; delete from t_test"}, resp, ds)
//assert.Equal(t, res, false)
//assert.Equal(t, resp.CheckItems[0].Status, model.PreCheckStatus_Error)
//
//res = service.checkSqlFormat(context.Background(), &shared.Ticket{InstanceType: shared.MySQL, TicketType: int32(100), SqlText: "update t_test a = 1; delete from t_test"}, resp, ds)
//assert.Equal(t, res, false)
//assert.Equal(t, resp.CheckItems[0].Status, model.PreCheckStatus_Error)
//}

func TestSendMessageToFreeLockDMLActor(t *testing.T) {
	ctx := context.Background()
	service := getService()
	baseMock2 := mockey.Mock((*mocks.MockActorClient).KindOf).Return(mocks.NewMockKindClient(&gomock.Controller{})).Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("error")).Build()
	service.sendMessageToFreeLockDMLActor(ctx, &shared.Ticket{}, &shared.DataSource{})
	baseMock3.UnPatch()
}

func TestSendMessageToOnlineDDLTicketActor(t *testing.T) {
	ctx := context.Background()
	service := getService()
	baseMock2 := mockey.Mock((*mocks.MockActorClient).KindOf).Return(mocks.NewMockKindClient(&gomock.Controller{})).Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("error")).Build()
	service.sendMessageToOnlineDDLTicketActor(ctx, &shared.Ticket{}, &shared.DataSource{})
	baseMock3.UnPatch()
}
func TestSendMessageToTicketActor(t *testing.T) {
	ctx := context.Background()
	service := getService()
	baseMock2 := mockey.Mock((*mocks.MockActorClient).KindOf).Return(mocks.NewMockKindClient(&gomock.Controller{})).Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock((*mocks.MockKindClient).Call).Return(nil, fmt.Errorf("error")).Build()
	service.sendMessageToTicketActor(ctx, &shared.Ticket{}, &shared.DataSource{})
	baseMock3.UnPatch()
}

//	func TestCheckPgNormalSQLFormat(t *testing.T) {
//		ctx := context.Background()
//		service := getService()
//		mock1 := mockey.Mock((*ticketService).SecurityRuleCheck).Return(true, nil).Build()
//		defer mock1.UnPatch()
//
//		mock2 := mockey.Mock((*mocks.MockCommandParser).Explain).Return(nil, fmt.Errorf("error")).Build()
//		service.checkPgNormalSQLFormat(ctx, &shared.Ticket{}, &model.PreCheckTicketResp{
//			CheckItems: []*model.CheckItem{
//				{
//					Status: model.PreCheckStatus_Undo,
//				},
//			},
//		})
//		mock2.UnPatch()
//
//		mock3 := mockey.Mock((*mocks.MockCommandParser).Explain).Return([]*parser.Command{
//			{},
//			{},
//		}, nil).Build()
//		service.checkPgNormalSQLFormat(ctx, &shared.Ticket{}, &model.PreCheckTicketResp{
//			CheckItems: []*model.CheckItem{
//				{
//					Status: model.PreCheckStatus_Undo,
//				},
//			},
//		})
//		mock3.UnPatch()
//	}
//
//	func TestCheckFreeLockDMLSQLFormat(t *testing.T) {
//		ctx := context.Background()
//		service := getService()
//
//		service.checkFreeLockDMLSQLFormat(ctx, &shared.Ticket{
//			InstanceType: shared.MySQL,
//			TicketType:   int32(model.TicketType_NormalSqlChange),
//			SqlText:      "update t_test set a = 1; delete from t_test"},
//			&model.PreCheckTicketResp{
//				CheckItems: []*model.CheckItem{
//					{
//						Status: model.PreCheckStatus_Undo,
//					},
//				}})
//		service.checkFreeLockDMLSQLFormat(ctx, &shared.Ticket{
//			InstanceType: shared.MySQL,
//			TicketType:   int32(model.TicketType_NormalSqlChange),
//			SqlText:      "alter table t_test add age int;"},
//			&model.PreCheckTicketResp{
//				CheckItems: []*model.CheckItem{
//					{
//						Status: model.PreCheckStatus_Undo,
//					},
//				}})
//		mock1 := mockey.Mock(utils.CheckFreeLockDMLLimit).Return([]string{}, fmt.Errorf("error")).Build()
//		service.checkFreeLockDMLSQLFormat(ctx, &shared.Ticket{
//			InstanceType: shared.MySQL,
//			TicketType:   int32(model.TicketType_NormalSqlChange),
//			SqlText:      "update t_test set a = 1;"},
//			&model.PreCheckTicketResp{
//				CheckItems: []*model.CheckItem{
//					{
//						Status: model.PreCheckStatus_Undo,
//					},
//				}})
//		mock1.UnPatch()
//		mock2 := mockey.Mock(utils.CheckFreeLockDMLLimit).Return([]string{"test"}, nil).Build()
//		defer mock2.UnPatch()
//		mock3 := mockey.Mock((*ticketService).getDBAccount).Return(nil, nil).Build()
//		defer mock3.UnPatch()
//		mock4 := mockey.Mock((*ticketService).GetTableIndexInfo).Return(nil, fmt.Errorf("error")).Build()
//		defer mock4.UnPatch()
//		service.checkFreeLockDMLSQLFormat(ctx, &shared.Ticket{
//			InstanceType: shared.MySQL,
//			TicketType:   int32(model.TicketType_NormalSqlChange),
//			SqlText:      "update t_test set a = 1;"},
//			&model.PreCheckTicketResp{
//				CheckItems: []*model.CheckItem{
//					{
//						Status: model.PreCheckStatus_Undo,
//					},
//				}})
//
// }
//
//	func TestGetCountSQL(t *testing.T) {
//		ctx := context.Background()
//		service := getService()
//		service.getCountSQL(ctx, &shared.Ticket{
//			DbName:   "test",
//			TicketId: 123,
//			SqlText:  "delete from t where id=2;update t set id=1;",
//		})
//
//		service.getCountSQL(ctx, &shared.Ticket{
//			DbName:   "test",
//			TicketId: 123,
//			SqlText:  "drop table t ;truncate table t;",
//		})
//	}
func TestGetCountSQL(t *testing.T) {
	ctx := context.Background()
	service := getService()

	sql := "alter table t add id int;"
	p := parser.New()
	stmts, _, _ := p.Parse(sql, "", "")
	service.getCountSQL(ctx, &shared.Ticket{
		SqlText: "alter table t add id int;",
	}, &TicketSqlTextParseResult{
		stmts: stmts,
	})
}

//func TestCheckFreeLockDDLSQLFormat(t *testing.T) {
//	ctx := context.Background()
//	service := getService()
//
//	sql := "alter table t add id int;alter table t add id1 int"
//	p := parser.New()
//	stmts, _, _ := p.Parse(sql, "", "")
//	service.checkFreeLockDDLSQLFormat(ctx, &shared.Ticket{
//		SqlText: sql,
//	}, &model2.PreCheckTicketResp{
//		CheckItems: []*model2.CheckItem{
//			{},
//		},
//	}, &TicketSqlTextParseResult{
//		stmts: stmts,
//	})
//}

func TestCheckSqlFormat(t *testing.T) {
	ctx := context.Background()
	service := getService()
	sql := "alter table t add id int;alter table t add id1 int"
	p := parser.New()
	stmts, _, _ := p.Parse(sql, "", "")

	m1 := mockey.Mock((*ticketService).getDBDataSource).Return(nil, fmt.Errorf("error")).Build()
	service.checkSqlFormat(ctx, &shared.Ticket{
		SqlText: sql,
	}, &model2.PreCheckTicketResp{
		CheckItems: []*model2.CheckItem{
			{},
		},
	}, &TicketSqlTextParseResult{
		stmts: stmts,
	})
	m1.UnPatch()

	m2 := mockey.Mock((*ticketService).getDBDataSource).Return(&shared.DataSource{
		Type: shared.VeDBMySQL,
	}, nil).Build()
	m3 := mockey.Mock((*ticketService).IsSQLTableContainsHTAPEngine).Return(&PrecheckTicketSytaxResult{}, false).Build()
	service.checkSqlFormat(ctx, &shared.Ticket{
		SqlText:      sql,
		InstanceType: shared.VeDBMySQL,
	}, &model2.PreCheckTicketResp{
		CheckItems: []*model2.CheckItem{
			{},
		},
	}, &TicketSqlTextParseResult{
		stmts: stmts,
	})
	m2.UnPatch()
	m3.UnPatch()
}

func TestIsSQLTableContainsHTAPEngine(t *testing.T) {
	ctx := context.Background()
	service := getService()
	sql := "alter table t add id int;alter table t add id1 int"
	p := parser.New()
	stmts, _, _ := p.Parse(sql, "", "")
	m1 := mockey.Mock((*ticketService).getDBDataSource).Return(nil, fmt.Errorf("error")).Build()
	m2 := mockey.Mock((*mocks.MockDataSourceService).GetCreateTableInfo).Return("SECONDARY_ENGINE=HTAP", fmt.Errorf("error")).Build()
	service.IsSQLTableContainsHTAPEngine(ctx, &TicketSqlTextParseResult{
		stmts: stmts,
		sqls:  []string{"alter table t add id int", "alter table t add id1 int"},
	}, &shared.DataSource{})
	m1.UnPatch()
	m2.UnPatch()

}

func Test_ticketService_checkNormalSQLFormatForMultiCloudDML_BitsUTGen(t *testing.T) {
	// 构造一个ticketService实例，因为目标函数不依赖其内部字段，所以可以直接构造
	svc := &ticketService{}

	mockey.PatchConvey("Test_ticketService_checkNormalSQLFormatForMultiCloudDML", t, func() {
		ctx := context.Background()

		mockey.PatchConvey("成功场景: 所有SQL均为支持的DML类型", func() {
			// 场景描述：
			// 当传入的SQL语句都是`EnabledNormalCommandsTypeForMultiCloud`中允许的类型时（Insert, Update, Delete），函数应返回通过。
			// 数据构造：
			// - resp: 一个包含初始CheckItem的PreCheckTicketResp。
			// - parseStruct: 包含多个ast.StmtNode，其类型分别为InsertStmt, UpdateStmt, DeleteStmt。
			// 逻辑链路：
			// 1. 遍历所有SQL语句节点。
			// 2. 对每个节点，类型检查都通过。
			// 3. isPass标志始终为true。
			// 4. 函数最终更新resp状态为Pass，并返回true。

			// 数据构造
			resp := &model2.PreCheckTicketResp{
				CheckItems: []*model2.CheckItem{
					{},
				},
			}
			parseStruct := &TicketSqlTextParseResult{
				sqls: []string{"INSERT ...", "UPDATE ...", "DELETE ..."},
				stmts: []ast.StmtNode{
					&ast.InsertStmt{},
					&ast.UpdateStmt{},
					&ast.DeleteStmt{},
				},
			}

			// 调用
			result, isPass := svc.checkNormalSQLFormatForMultiCloudDML(ctx, resp, parseStruct)

			// 断言
			convey.So(isPass, convey.ShouldBeTrue)
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(len(result.checkResult), convey.ShouldEqual, 3)
			convey.So(result.checkResult, convey.ShouldResemble, []string{"Pass", "Pass", "Pass"})
			convey.So(resp.CheckItems[0].Status, convey.ShouldEqual, model2.PreCheckStatus_Pass)
			convey.So(resp.CheckItems[0].Memo, convey.ShouldEqual, "Pass")
		})

		mockey.PatchConvey("失败场景: 包含不支持的SQL类型", func() {
			// 场景描述：
			// 当传入的SQL语句中包含`EnabledNormalCommandsTypeForMultiCloud`中不允许的类型时（例如SelectStmt），函数应返回不通过，并指出错误类型。
			// 数据构造：
			// - resp: 一个包含初始CheckItem的PreCheckTicketResp。
			// - parseStruct: 包含一个允许的UpdateStmt和一个不允许的SelectStmt。
			// 逻辑链路：
			// 1. 遍历到UpdateStmt，检查通过。
			// 2. 遍历到SelectStmt，检查失败。
			// 3. isPass标志被设为false。
			// 4. log.Warn被调用，resp的状态被更新为Error。
			// 5. 循环结束后，函数返回false和包含错误信息的checkResult。

			// Mock
			mockey.Mock(log.Warn).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()

			// 数据构造
			resp := &model2.PreCheckTicketResp{
				CheckItems: []*model2.CheckItem{
					{},
				},
			}
			parseStruct := &TicketSqlTextParseResult{
				sqls: []string{"UPDATE ...", "SELECT ..."},
				stmts: []ast.StmtNode{
					&ast.UpdateStmt{},
					&ast.SelectStmt{}, // 不支持的类型
				},
			}

			// 调用
			result, isPass := svc.checkNormalSQLFormatForMultiCloudDML(ctx, resp, parseStruct)

			// 断言
			convey.So(isPass, convey.ShouldBeFalse)
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(len(result.checkResult), convey.ShouldEqual, 2)
			expectedErr := "Normal DML ticket not support SelectStmt sql type"
			convey.So(result.checkResult, convey.ShouldResemble, []string{"Pass", expectedErr})
			convey.So(resp.CheckItems[0].Status, convey.ShouldEqual, model2.PreCheckStatus_Error)
			convey.So(resp.CheckItems[0].Memo, convey.ShouldEqual, expectedErr)
		})

		mockey.PatchConvey("边界场景: 输入的SQL列表为空", func() {
			// 场景描述：
			// 当传入的解析结果中不包含任何SQL语句时，函数应默认检查通过。
			// 数据构造：
			// - resp: 一个包含初始CheckItem的PreCheckTicketResp。
			// - parseStruct: sqls和stmts切片均为空。
			// 逻辑链路：
			// 1. 循环体不执行。
			// 2. isPass标志保持为true。
			// 3. 函数最终更新resp状态为Pass，并返回true。

			// 数据构造
			resp := &model2.PreCheckTicketResp{
				CheckItems: []*model2.CheckItem{
					{},
				},
			}
			parseStruct := &TicketSqlTextParseResult{
				sqls:  []string{},
				stmts: []ast.StmtNode{},
			}

			// 调用
			result, isPass := svc.checkNormalSQLFormatForMultiCloudDML(ctx, resp, parseStruct)

			// 断言
			convey.So(isPass, convey.ShouldBeTrue)
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(len(result.checkResult), convey.ShouldEqual, 0)
			convey.So(resp.CheckItems[0].Status, convey.ShouldEqual, model2.PreCheckStatus_Pass)
			convey.So(resp.CheckItems[0].Memo, convey.ShouldEqual, "Pass")
		})

		mockey.PatchConvey("失败场景: 包含多个不支持的SQL类型", func() {
			// 场景描述：
			// 当传入的SQL语句中包含多个不支持的类型时，函数应标记每个错误，并将resp的Memo设置为第一个遇到的错误。
			// 数据构造：
			// - resp: 一个包含初始CheckItem的PreCheckTicketResp。
			// - parseStruct: 包含一个CreateTableStmt和一个SelectStmt。
			// 逻辑链路：
			// 1. 遍历到CreateTableStmt，检查失败，isPass=false，更新resp，记录错误。
			// 2. 遍历到SelectStmt，检查失败，isPass保持false，更新resp（覆盖上一次的Memo），记录错误。
			// 3. 函数返回false和包含所有错误信息的checkResult。

			// Mock
			mockey.Mock(log.Warn).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()

			// 数据构造
			resp := &model2.PreCheckTicketResp{
				CheckItems: []*model2.CheckItem{
					{},
				},
			}
			parseStruct := &TicketSqlTextParseResult{
				sqls: []string{"CREATE TABLE ...", "SELECT ..."},
				stmts: []ast.StmtNode{
					&ast.CreateTableStmt{}, // 不支持
					&ast.SelectStmt{},      // 也不支持
				},
			}

			// 调用
			result, isPass := svc.checkNormalSQLFormatForMultiCloudDML(ctx, resp, parseStruct)

			// 断言
			convey.So(isPass, convey.ShouldBeFalse)
			convey.So(result, convey.ShouldNotBeNil)
			expectedErr1 := "Normal DML ticket not support CreateTableStmt sql type"
			expectedErr2 := "Normal DML ticket not support SelectStmt sql type"
			convey.So(result.checkResult, convey.ShouldResemble, []string{expectedErr1, expectedErr2})
			convey.So(resp.CheckItems[0].Status, convey.ShouldEqual, model2.PreCheckStatus_Error)
			// Memo应为最后一个遇到的错误，因为会被覆盖
			convey.So(resp.CheckItems[0].Memo, convey.ShouldEqual, expectedErr2)
		})
	})
}

// Mock_KindClient_sendMessageToShardingFreeLockDDLActor is a mock for the cli.KindClient interface.
type Mock_KindClient_sendMessageToShardingFreeLockDDLActor struct {
	cli.KindClient
}

// Call is a stub for mocking.
func (m *Mock_KindClient_sendMessageToShardingFreeLockDDLActor) Call(ctx context.Context, name string, msg interface{}, callopts ...*cli.GrainCallOptions) (interface{}, error) {
	// This is a stub for mocking. The mocked function will be called instead.
	return nil, nil
}

// Mock_ActorClient_sendMessageToShardingFreeLockDDLActor is a mock for the cli.ActorClient interface.
type Mock_ActorClient_sendMessageToShardingFreeLockDDLActor struct {
	cli.ActorClient
}

// KindOf is a stub for mocking.
func (m *Mock_ActorClient_sendMessageToShardingFreeLockDDLActor) KindOf(kind string) cli.KindClient {
	// This is a stub for mocking. The mocked function will be called instead.
	return nil
}
func Test_ticketService_sendMessageToShardingFreeLockDDLActor_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("Test_ticketService_sendMessageToShardingFreeLockDDLActor", t, func() {
		// 数据构造
		ctx := context.Background()
		ticket := &shared.Ticket{
			TicketId:      12345,
			SqlText:       "ALTER TABLE t1 ADD COLUMN c1 INT",
			TicketType:    1,
			ExecuteType:   2,
			ExecStartTime: int32(time.Now().Unix()),
			ExecEndTime:   int32(time.Now().Add(1 * time.Hour).Unix()),
		}
		ds := &shared.DataSource{
			InstanceId: "test-instance",
			Db:         "test_db",
		}

		mockActorClient := &Mock_ActorClient_sendMessageToShardingFreeLockDDLActor{}
		mockKindClient := &Mock_KindClient_sendMessageToShardingFreeLockDDLActor{}

		selfService := &ticketService{
			actorClient: mockActorClient,
		}

		mockey.PatchConvey("成功场景 - Actor调用成功", func() {
			// 场景描述：
			// 当所有依赖都正常返回，actor调用成功时，函数应返回actor调用的结果且无错误。
			// 数据构造：
			// - ticket: 一个包含必要信息的有效工单对象。
			// - ds: 一个有效的数据源对象。
			// - ctx: 一个后台上下文。
			// 逻辑链路：
			// 1. Mock fwctx.GetTenantID 和 fwctx.GetUserID 返回测试值。
			// 2. Mock actorClient.KindOf 返回一个 mockKindClient 实例。
			// 3. Mock mockKindClient.Call 返回一个成功响应和 nil 错误，并对传入的参数进行断言。
			// 4. 调用目标函数。
			// 5. 验证返回结果与 Mock 结果一致，且错误为 nil。

			// Mock
			mockey.Mock(fwctx.GetTenantID).Return("test-tenant-id").Build()
			mockey.Mock(fwctx.GetUserID).Return("test-user-id").Build()
			mockey.Mock((*Mock_ActorClient_sendMessageToShardingFreeLockDDLActor).KindOf).To(func(_ *Mock_ActorClient_sendMessageToShardingFreeLockDDLActor, kind string) cli.KindClient {
				convey.So(kind, convey.ShouldEqual, consts.ShardingFreeLockDDLActorKind)
				return mockKindClient
			}).Build()
			mockey.Mock((*Mock_KindClient_sendMessageToShardingFreeLockDDLActor).Call).To(func(_ *Mock_KindClient_sendMessageToShardingFreeLockDDLActor, ctx context.Context, name string, msg interface{}, callopts ...*cli.GrainCallOptions) (interface{}, error) {
				execMsg, ok := msg.(*shared.ExecShardingFreeLockDDLTicket)
				convey.So(ok, convey.ShouldBeTrue)
				convey.So(name, convey.ShouldEqual, conv.Int64ToStr(ticket.TicketId))
				convey.So(execMsg.SqlText, convey.ShouldEqual, ticket.SqlText)
				convey.So(execMsg.TenantID, convey.ShouldEqual, "test-tenant-id")
				convey.So(execMsg.UserID, convey.ShouldEqual, "test-user-id")
				convey.So(execMsg.Source, convey.ShouldEqual, ds)
				convey.So(execMsg.TicketType, convey.ShouldEqual, ticket.TicketType)
				convey.So(execMsg.ExecuteType, convey.ShouldEqual, ticket.ExecuteType)
				convey.So(execMsg.ExecutableStartTime, convey.ShouldEqual, ticket.ExecStartTime)
				convey.So(execMsg.ExecutableEndTime, convey.ShouldEqual, ticket.ExecEndTime)
				return "success_response", nil
			}).Build()

			// 调用
			result, err := selfService.sendMessageToShardingFreeLockDDLActor(ctx, ticket, ds)

			// 断言
			convey.So(err, convey.ShouldBeNil)
			convey.So(result, convey.ShouldEqual, "success_response")
		})

		mockey.PatchConvey("失败场景 - Actor调用返回错误", func() {
			// 场景描述：
			// 当actor调用失败并返回一个错误时，函数应将此错误向上传递。
			// 数据构造：
			// - ticket: 一个包含必要信息的有效工单对象。
			// - ds: 一个有效的数据源对象。
			// - ctx: 一个后台上下文。
			// 逻辑链路：
			// 1. Mock fwctx.GetTenantID 和 fwctx.GetUserID 返回测试值。
			// 2. Mock actorClient.KindOf 返回一个 mockKindClient 实例。
			// 3. Mock mockKindClient.Call 返回 nil 和一个指定的错误。
			// 4. 调用目标函数。
			// 5. 验证返回结果为 nil，且错误与 Mock 返回的错误一致。

			// Mock
			expectedErr := errors.New("actor call failed")
			mockey.Mock(fwctx.GetTenantID).Return("test-tenant-id").Build()
			mockey.Mock(fwctx.GetUserID).Return("test-user-id").Build()
			mockey.Mock((*Mock_ActorClient_sendMessageToShardingFreeLockDDLActor).KindOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_sendMessageToShardingFreeLockDDLActor).Call).Return(nil, expectedErr).Build()

			// 调用
			result, err := selfService.sendMessageToShardingFreeLockDDLActor(ctx, ticket, ds)

			// 断言
			convey.So(result, convey.ShouldBeNil)
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "actor call failed")
		})
	})
}
