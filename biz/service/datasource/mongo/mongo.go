package mongo

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"sync"

	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/service/infra"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/shuttle"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	mongoModel "code.byted.org/infcs/dbw-mgr/gen/mongo-mgr/2022-01-01/kitex_gen/infcs/mongodb/v2"
	innerMongoModel "code.byted.org/infcs/dbw-mgr/gen/mongo-mgr/inner/kitex_gen/infcs/mongodb/inner"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/mgr/client"

	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
)

type NewMongoNoSQLDataSourceIn struct {
	dig.In
	Conf           config.ConfigProvider
	MongoMgr       mgr.Provider `name:"mongo"`
	ShuttleSvc     shuttle.PGWShuttleService
	C3ConfProvider c3.ConfigProvider
	Loc            location.Location
	ActorClient    cli.ActorClient
	InfraMgmt      infra.Service
	DbwInstanceDal dal.DbwInstanceDAL
}

type NewMongoNoSQLDataSourceout struct {
	dig.Out
	Source datasource.DataSourceService `group:"datasources"`
}

func NewMongoNoSQLDataSource(p NewMongoNoSQLDataSourceIn) NewMongoNoSQLDataSourceout {
	return NewMongoNoSQLDataSourceout{
		Source: retryIfWhiteListNotReady(&mongoImpl{
			DataSourceService: datasource.NewDataSourceServiceDecorator(nil).Export(),
			cnf:               p.Conf,
			mongomgr:          p.MongoMgr,
			ShuttleSvc:        p.ShuttleSvc,
			C3ConfProvider:    p.C3ConfProvider,
			Loc:               p.Loc,
			actorClient:       p.ActorClient,
			InfraMgmt:         p.InfraMgmt,
			dbwInstanceDal:    p.DbwInstanceDal,
		}),
	}
}

type mongoImpl struct {
	datasource.DataSourceService
	cnf            config.ConfigProvider
	mongomgr       mgr.Provider
	ShuttleSvc     shuttle.PGWShuttleService
	C3ConfProvider c3.ConfigProvider
	Loc            location.Location
	actorClient    cli.ActorClient
	InfraMgmt      infra.Service
	dbwInstanceDal dal.DbwInstanceDAL
}

func (m *mongoImpl) Type() shared.DataSourceType {
	return shared.Mongo
}

func (self *mongoImpl) DescribeDBInstanceSSL(ctx context.Context, req *datasource.DescribeDBInstanceSSLReq) (*datasource.DescribeDBInstanceSSLResp, error) {
	mongoReq := &mongoModel.DescribeDBInstanceSSLReq{InstanceId: req.InstanceId}
	mongoResp := &mongoModel.DescribeDBInstanceSSLResp{}
	log.Info(ctx, "DescribeDBInstanceSSLReq:%s", mongoReq.String())
	if err := self.mongomgr.Get().Call(ctx, mongoModel.Action_DescribeDBInstanceSSL.String(), mongoReq, mongoResp, client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		log.Warn(ctx, "failed to call DescribeDBInstanceSSL, err=%v", err)
		return nil, err
	}
	log.Info(ctx, "DescribeDBInstanceSSLResp:%s", mongoResp.String())
	return &datasource.DescribeDBInstanceSSLResp{
		InstanceId:    mongoResp.InstanceId,
		SSLEnable:     mongoResp.SSLEnable,
		IsValid:       mongoResp.IsValid,
		SSLExpireTime: mongoResp.SSLExpiredTime,
	}, nil
}

func (m *mongoImpl) ListInstance(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	if req.LinkType != shared.Volc {
		return nil, nil
	}
	rreq := &mongoModel.DescribeDBInstancesReq{
		PageNumber: utils.Int32Ref(req.PageNumber),
		PageSize:   utils.Int32Ref(req.PageSize),
		VpcId:      nil,
	}
	if req.InstanceName != "" {
		rreq.InstanceName = utils.StringRef(req.InstanceName)
	}
	if req.InstanceId != "" {
		rreq.InstanceId = utils.StringRef(req.InstanceId)
	}
	if req.InstanceStatus != "" {
		status, err := mongoModel.InstanceStatusFromString(req.InstanceStatus)
		if err != nil {
			log.Warn(ctx, "failed to get InstanceStatus from string %s", req.InstanceStatus)
		}
		rreq.InstanceStatus = &status
	}
	if req.DBEngineVersion != "" {
		engineVersion := convertDBEngineVersionFromString(ctx, req.DBEngineVersion)
		rreq.DBEngineVersion = &engineVersion
	}
	if req.SubInstanceType != "" {
		instanceType := convertMongoTypeFromString(ctx, req.SubInstanceType)
		rreq.InstanceType = &instanceType
	}
	if len(req.Tags) > 0 {
		tagFilter := make([]*mongoModel.TagFilterObject, 0)
		for _, tag := range req.Tags {
			tagFilter = append(tagFilter, &mongoModel.TagFilterObject{
				Key:   tag.Key,
				Value: utils.StringRef(tag.Value),
			})
		}
		rreq.TagFilters = tagFilter
	}
	if req.ProjectName != "" {
		rreq.ProjectName = utils.StringRef(req.ProjectName)
	}
	rresp := &mongoModel.DescribeDBInstancesResp{}
	if err := m.mongomgr.Get().Call(ctx, mongoModel.Action_DescribeDBInstances.String(), rreq, rresp, client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		log.Warn(ctx, "mongoImpl: get mongo instances fail %v", err)
		return nil, err
	}
	resp := &datasource.ListInstanceResp{Total: int64(rresp.Total)}
	var (
		wg               sync.WaitGroup
		instMapEndpoints sync.Map
	)

	// 并发去查DescribeEndpoints
	instanceList := rresp.DBInstances
	instLengths := int32(len(instanceList))
	//instancesEndpointChannel := make(chan *instMapEndpoint, instLengths)
	DefaultPageSize := int32(100)
	totalPages := int32(math.Ceil(float64(instLengths) / float64(DefaultPageSize)))
	for pageNumber := int32(0); pageNumber < totalPages; pageNumber++ {
		for start := int32(0); start < 100; start++ {
			instIndex := pageNumber*DefaultPageSize + start
			if instIndex >= instLengths {
				break
			}
			wg.Add(1)
			go func(instIndex int32) {
				defer wg.Done()
				var mongoNodeInfos []*model.MongoNodeInfo
				instanceId := instanceList[instIndex].GetInstanceId()
				if instanceList[instIndex].InstanceStatus == mongoModel.InstanceStatus_Running {
					respEndpoint, err := m.describeDBEndpoint(ctx, instanceId)
					if err != nil {
						return
					}
					for _, ep := range respEndpoint.DBEndpoints {
						if ep.EndpointType != mongoModel.EndpointType_Mongos &&
							ep.EndpointType != mongoModel.EndpointType_ReplicaSet {
							continue
						}
						for _, addr := range ep.DBAddresses {
							var nodeRole model.NodeRole
							switch addr.AddressType {
							case mongoModel.AddressType_Primary:
								nodeRole = model.NodeRole_Primary
							case mongoModel.AddressType_Secondary:
								nodeRole = model.NodeRole_Secondary
							case mongoModel.AddressType_Hidden:
								// nodeRole = model.NodeRole_Hidden
								continue // Hidden节点不展示
							case mongoModel.AddressType_ReadyOnly:
								nodeRole = model.NodeRole_ReadOnly
							}
							mongoNodeInfos = append(mongoNodeInfos, &model.MongoNodeInfo{
								NodeId:   addr.NodeId,
								NodeRole: &nodeRole,
								NodeIP:   &addr.AddressIP,
								NodePort: &addr.AddressPort,
							})
						}
						instMapEndpoints.Store(instanceId, mongoNodeInfos)
						break
					}
				}
			}(instIndex)
		}
		wg.Wait()
	}
	fp.StreamOf(rresp.DBInstances).
		Map(func(i *mongoModel.DBInstance) *model.InstanceInfo {
			var (
				instanceType   string
				mongoNodeInfos []*model.MongoNodeInfo
			)

			instanceSpec := &model.InstanceSpec{}
			if i.InstanceType == mongoModel.InstanceType_ReplicaSet {
				instanceType = model.MongoInstanceType_Mongo_ReplicaSet.String()
				instanceSpec.NodeNumber = int32(len(i.Nodes))
				if len(i.Nodes) > 0 {
					instanceSpec.CpuNum = int32(*i.Nodes[0].TotalvCPU)
					instanceSpec.MemInGiB = *i.Nodes[0].TotalMemoryGB
				}

			}
			if i.InstanceType == mongoModel.InstanceType_ShardedCluster {
				instanceType = model.MongoInstanceType_Mongo_ShardedCluster.String()
			}
			InstanceStatusStr := i.InstanceStatus.String()
			subType, _ := model.SubInstanceTypeFromString(instanceType)
			zoneIds := strings.Split(i.ZoneId, ",")
			targetTags := make([]*model.TagObject, 0)
			if i.IsSetTags() {
				for _, tag := range i.GetTags() {
					targetTags = append(targetTags, &model.TagObject{
						Key:   tag.Key,
						Value: *tag.Value,
					})
				}
			}
			accountId := req.TenantId
			if i.GetTenant() != "" {
				accountId = i.GetTenant()
			}
			item := &model.InstanceInfo{
				InstanceId:      utils.StringRef(i.InstanceId),
				InstanceName:    utils.StringRef(i.InstanceName),
				SubInstanceType: &subType,
				MongoNodeInfos:  mongoNodeInfos,
				InstanceStatus:  InstanceStatusStr,
				Zone:            zoneIds[0],
				DBEngineVersion: i.DBEngineVersionStr,
				InstanceSpec:    instanceSpec,
				ProjectName:     i.ProjectName,
				Tags:            targetTags,
				AccountId:       utils.StringRef(accountId),
				CreateTime:      utils.StringRef(i.GetCreateTime()),
				InstanceType:    model.InstanceType_Mongo,
				LinkType:        model.LinkType_Volc,
			}
			if mongoNodes, ok := instMapEndpoints.Load(i.InstanceId); ok {
				if mongoNodeList, ok := mongoNodes.([]*model.MongoNodeInfo); ok {
					item.MongoNodeInfos = mongoNodeList
				}
			}
			return item
		}).
		ToSlice(&resp.InstanceList)
	return resp, nil
}
func (m *mongoImpl) ListInstanceLightWeight(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	if req.LinkType != shared.Volc {
		return nil, nil
	}
	ret := &datasource.ListInstanceResp{}
	rreq := &mongoModel.DescribeDBInstancesForDBWReq{
		PageNumber: utils.Int32Ref(req.PageNumber),
		PageSize:   utils.Int32Ref(req.PageSize),
		Filters:    &mongoModel.DescribeDBInstancesForDBWFilter{},
	}
	rresp := &mongoModel.DescribeDBInstancesForDBWResp{}
	// 如果是运维面账号，则不需要传这个值
	if req.TenantId != "0" && req.TenantId != "1" {
		rreq.Filters.AccountID = utils.StringRef(req.TenantId)
	}
	//if req.InstanceStatus != "" {
	//	status, err := mongoModel.InstanceStatusFromString(req.InstanceStatus)
	//	if err != nil {
	//		log.Warn(ctx, "failed to get InstanceStatus from string %s", req.InstanceStatus)
	//	}
	//}
	if req.CreateTimeStart != "" && req.CreateTimeEnd != "" {
		rreq.Filters.CreateStartTime = utils.StringRef(req.CreateTimeStart)
		rreq.Filters.CreateEndTime = utils.StringRef(req.CreateTimeEnd)
	}
	if err := m.mongomgr.Get().Call(ctx, mongoModel.Action_DescribeDBInstancesForDBW.String(), rreq, rresp, client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		log.Warn(ctx, "mongoImpl: get mongo instances fail %v", err)
		return nil, err
	}
	if err := fp.StreamOf(rresp.DBInstances).Map(func(i *mongoModel.DBInstanceInfoForDBW) *model.InstanceInfo {
		var (
			instanceType string
		)
		if i.InstanceType == mongoModel.InstanceType_ReplicaSet {
			instanceType = model.MongoInstanceType_Mongo_ReplicaSet.String()
		}
		instanceSpec := &model.InstanceSpec{}
		if i.InstanceType == mongoModel.InstanceType_ReplicaSet {
			instanceType = model.MongoInstanceType_Mongo_ReplicaSet.String()
			instanceSpec.NodeNumber = int32(len(i.Nodes))
			if len(i.Nodes) > 0 {
				instanceSpec.CpuNum = int32(*i.Nodes[0].TotalvCPU)
				instanceSpec.MemInGiB = *i.Nodes[0].TotalMemoryGB
			}

		}
		if i.InstanceType == mongoModel.InstanceType_ShardedCluster {
			instanceType = model.MongoInstanceType_Mongo_ShardedCluster.String()
		}
		targetTags := make([]*model.TagObject, 0)
		if i.IsSetTags() {
			for _, tag := range i.GetTags() {
				targetTags = append(targetTags, &model.TagObject{
					Key:   tag.Key,
					Value: *tag.Value,
				})
			}
		}
		InstanceStatusStr := i.InstanceStatus.String()
		subType, _ := model.SubInstanceTypeFromString(instanceType)
		accountId := req.TenantId
		if i.GetTenant() != "" {
			accountId = i.GetTenant()
		}
		zoneIds := strings.Split(i.ZoneId, ",")
		var zone string
		if len(zoneIds) > 0 {
			zone = zoneIds[0]
		}
		item := &model.InstanceInfo{
			InstanceId:      utils.StringRef(i.InstanceId),
			InstanceName:    utils.StringRef(i.InstanceName),
			SubInstanceType: &subType,
			InstanceStatus:  InstanceStatusStr,
			DBEngineVersion: i.DBEngineVersionStr,
			Zone:            zone,
			InstanceSpec:    instanceSpec,
			ProjectName:     i.ProjectName,
			Tags:            targetTags,
			AccountId:       utils.StringRef(accountId),
			CreateTime:      utils.StringRef(i.GetCreateTime()),
			InstanceType:    model.InstanceType_Mongo,
			LinkType:        model.LinkType_Volc}
		return item
	}).ToSlice(&ret.InstanceList); err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	ret.Total = int64(rresp.Total)
	return ret, nil
}
func (m *mongoImpl) FillDataSource(ctx context.Context, ds *shared.DataSource) error {
	if ds.LinkType != shared.Volc {
		return nil
	}
	// 只支持售卖区访问
	resp, err := m.describeInstance(ctx, ds.InstanceId)
	if err != nil {
		log.Warn(ctx, "mongoImpl: get mongo [%s] describeInstance fail: %v", ds.InstanceId, err)
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	// 这里用实例的NetworkType字段判断：
	// 1、如果是Private，则获取vpcId，后面通过shuttle来获取Address
	// 2、如果是Private+InnerPLB，则获取vpcId，并且直接传入Address和CadidateAddress
	// 3、如果只有InnerPLB，则直接传入Address地址

	// 不管是私网连接还是公共服务区连接,都先填充AuthDB
	_ = m.fillAuthDB(ctx, ds)

	// 如果不包含公共服务区地址,则获取VpcId
	if m.cnf.Get(ctx).DisableMongoShuttle {
		topo, err := m.getInstanceTopo(ctx, ds.InstanceId)
		if err != nil {
			log.Warn(ctx, "mongoImpl: get mongo [%s] getInstanceTopo fail: %v", ds.InstanceId, err)
			return err
		}
		for _, node := range topo {
			for _, ip := range node.IpList {
				if ip.NodeId == ds.MongoNodeId {
					ds.Address = fmt.Sprintf("%s:%s", ip.IPv4, ip.Port)
					if resp.GetDBInstance() != nil {
						if resp.GetDBInstance().InstanceType == mongoModel.InstanceType_ReplicaSet {
							for _, nodeDetail := range resp.GetDBInstance().Nodes {
								if nodeDetail.NodeId == ds.MongoNodeId {
									role := nodeDetail.GetNodeRole()
									if role == mongoModel.NodeRole_Primary {
										ds.EndpointRole = shared.Primary
									} else if role == mongoModel.NodeRole_Secondary {
										ds.EndpointRole = shared.Secondary
									}
								}
							}
						}
					}

				}
			}
		}
	} else {
		endpoint, err := m.describeDBEndpoint(ctx, ds.InstanceId)
		if err != nil {
			log.Warn(ctx, "mongoImpl: get mongo [%s] describeDBEndpoint fail: %v", ds.InstanceId, err)
			return consts.ErrorOf(model.ErrorCode_InternalError)
		}
		if !strings.Contains(*resp.DBInstance.NetworkType, mongoModel.NetworkType_InnerPLB.String()) {
			for _, val := range endpoint.DBEndpoints {
				if val.EndpointType != mongoModel.EndpointType_Mongos &&
					val.EndpointType != mongoModel.EndpointType_ReplicaSet {
					continue
				}
				if val.NetworkType == mongoModel.NetworkType_Private {
					for _, v := range val.DBAddresses {
						if v.NodeId == ds.MongoNodeId {
							if v.AddressType == mongoModel.AddressType_Secondary {
								ds.EndpointRole = shared.Secondary
							} else {
								ds.EndpointRole = shared.Primary
							}
							break
						}
					}
				}
			}
			log.Info(ctx, "mongoImpl: get VpcID: %s for instance [%s]", ds.VpcID, ds.InstanceId)
		}
		// 如果包含公共服务区地址，直接将IP、Port填入ds.Address中

		for _, val := range endpoint.DBEndpoints {
			if val.EndpointType != mongoModel.EndpointType_Mongos &&
				val.EndpointType != mongoModel.EndpointType_ReplicaSet {
				continue
			}
			if val.NetworkType == mongoModel.NetworkType_InnerPLB {
				for _, v := range val.DBAddresses {
					if v.NodeId == ds.MongoNodeId {
						var address string
						if v.AddressIPV6 != nil {
							address = v.GetAddressIPV6()
						} else {
							address = v.GetAddressIP()
						}
						ds.Address = fmt.Sprintf("%s:%s", datasource.ConvertIPV6(address), v.AddressPort)
						if v.AddressType == mongoModel.AddressType_Secondary {
							ds.EndpointRole = shared.Secondary
						} else {
							ds.EndpointRole = shared.Primary
						}
						return nil
					}
				}
			}
		}
		// use vpc network
		ds.VpcID = resp.DBInstance.VpcId
		//ds.VpcID = resp.DBInstance.VpcId
		log.Info(ctx, "mongoImpl: get VpcID: %s , Address: %s, for instance [%s]", ds.VpcID, ds.Address, ds.InstanceId)
	}
	return nil
}

func (m *mongoImpl) fillAuthDB(ctx context.Context, ds *shared.DataSource) error {
	userName := ds.User
	resp, err := m.DescribeAccounts2(ctx, &datasource.DescribeAccountsReq{
		DSType:     m.Type(),
		InstanceId: ds.InstanceId,
		PageSize:   100,
		PageNumber: 1,
	})
	if err != nil {
		log.Warn(ctx, "mongoImpl: get mongo [%s] describeAccounts fail: %v", ds.InstanceId, err)
		return err
	}
	for _, ac := range resp.AccountsInfo {
		if ac.AccountName == userName {
			ds.AuthDb = ac.AuthDB
			return nil
		}
	}
	return nil
}

/* for get vpc id */
func (m *mongoImpl) describeInstance(ctx context.Context, instanceId string) (*mongoModel.DescribeDBInstanceDetailResp, error) {
	req := &mongoModel.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
	}
	resp := &mongoModel.DescribeDBInstanceDetailResp{}
	return resp, m.mongomgr.Get().Call(ctx, mongoModel.Action_DescribeDBInstanceDetail.String(), req, resp, client.WithVersion(consts.Mongo_Version_V2))
}

/* for get vpc id */
func (m *mongoImpl) describeDBEndpoint(ctx context.Context, instanceId string) (*mongoModel.DescribeDBEndpointResp, error) {
	reqEndpoint := &mongoModel.DescribeDBEndpointReq{
		InstanceId: instanceId,
	}
	respEndpoint := &mongoModel.DescribeDBEndpointResp{}
	if err := m.mongomgr.Get().Call(ctx, mongoModel.Action_DescribeDBEndpoint.String(), reqEndpoint, respEndpoint, client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		msg := fmt.Sprintf("describe instance %s endpoint failed: %v", instanceId, err)
		log.Info(ctx, msg)
		return nil, err
	}
	return respEndpoint, nil
}

func (m *mongoImpl) CreateAccountAndGrant(
	ctx context.Context,
	instanceId, accountName, password, dbName, priv string,
	dsType shared.DataSourceType) error {
	createDBAccountReq := &mongoModel.CreateDBAccountReq{
		AccountName:     accountName,
		InstanceId:      instanceId,
		AccountPassword: password,
		AuthDB:          dbName,
		AccountDBPrivileges: []*mongoModel.AccountDBPrivilegeObject{
			{
				DBName:    dbName,
				RoleNames: []string{priv},
			},
		},
	}
	if err := m.mongomgr.Get().Call(
		ctx,
		mongoModel.Action_CreateDBAccount.String(),
		createDBAccountReq,
		nil,
		client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		return err
	}
	return nil
}

func (m *mongoImpl) DescribeAccounts2(ctx context.Context, req *datasource.DescribeAccountsReq) (*datasource.DescribeAccountResp2, error) {
	rreq := &mongoModel.DescribeDBAccountsReq{
		InstanceId:  req.InstanceId,
		AccountName: utils.StringRef(req.AccountName),
		AuthDB:      req.AuthDB,
	}
	rresp := &mongoModel.DescribeDBAccountsResp{}
	if err := m.mongomgr.Get().Call(
		ctx,
		mongoModel.Action_DescribeDBAccounts.String(),
		rreq,
		rresp,
		client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		return nil, err
	}
	res := &datasource.DescribeAccountResp2{}
	fp.StreamOf(rresp.Accounts).Map(func(ac *mongoModel.Account) *datasource.AccountInfo {
		dai := &datasource.AccountInfo{
			AccountName: ac.AccountName,
			AccountType: ac.AccountType.String(),
			AuthDB:      ac.AuthDB,
		}
		for _, priv := range ac.AccountPrivileges {
			p := &datasource.AccountPrivilege{
				DBName:    priv.DBName,
				Privilege: priv.RoleName,
			}
			dai.Privileges = append(dai.Privileges, p)
		}
		return dai
	}).ToSlice(&res.AccountsInfo)
	return res, nil
}

func (m *mongoImpl) CheckPrivilege(ctx context.Context, instanceId, dbName, accountName, priv string, _ shared.DataSourceType) (bool, error) {
	req := &datasource.DescribeAccountsReq{
		DSType:      m.Type(),
		InstanceId:  instanceId,
		AccountName: accountName,
	}
	describeAccountResp, err := m.DescribeAccounts2(ctx, req)
	if err != nil {
		log.Warn(ctx, "DescribeAccounts error %s", err)
		return false, err
	}
	for _, info := range describeAccountResp.AccountsInfo {
		for _, ap := range info.Privileges {
			if ap.DBName == dbName && ap.Privilege == priv {
				return true, nil
			}
		}
	}
	return false, nil
}
func (m *mongoImpl) IsMyOwnInstance(ctx context.Context, instanceId string, _ shared.DataSourceType) bool {
	if fwctx.GetTenantID(ctx) == "1" || fwctx.GetTenantID(ctx) == "0" {
		return true
	}
	inst, _ := m.dbwInstanceDal.Get(ctx, instanceId, model.InstanceType_Mongo.String(), "", "", "")
	if inst != nil {
		if inst.TenantId != fwctx.GetTenantID(ctx) {
			return false
		} else {
			return true
		}
	}
	rreq := &mongoModel.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
	}
	rresp := &mongoModel.DescribeDBInstanceDetailResp{}
	if err := m.mongomgr.Get().Call(
		ctx,
		mongoModel.Action_DescribeDBInstanceDetail.String(),
		rreq,
		rresp,
		client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		return false
	}
	return true
}
func (m *mongoImpl) CheckInstanceState(ctx context.Context, instanceId string, ds shared.DataSourceType, isConnectedInstance bool) error {
	var (
		dbInstanceStatusBlackList map[string]string
		blackList                 []string
		rawBlackList              string
	)
	rreq := &mongoModel.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
	}
	rresp := &mongoModel.DescribeDBInstanceDetailResp{}
	if err := m.mongomgr.Get().Call(
		ctx,
		mongoModel.Action_DescribeDBInstanceDetail.String(),
		rreq,
		rresp,
		client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		return err
	}
	cfg := m.cnf.Get(ctx)
	if isConnectedInstance {
		rawBlackList = cfg.DBInstanceStateWithConnectionBlackList
	} else {
		rawBlackList = cfg.DBInstanceStateWithoutConnectionBlackList
	}
	err := json.Unmarshal([]byte(rawBlackList), &dbInstanceStatusBlackList)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	} else {
		blackList = strings.Split(dbInstanceStatusBlackList[ds.String()], ",")
	}
	currentStatus := rresp.DBInstance.InstanceStatus.String()
	for _, item := range blackList {
		if item == currentStatus {
			log.Warn(ctx, "instance status is %s, not support", currentStatus)
			return consts.ErrorWithParam(model.ErrorCode_InstanceNotInRunningStatus, currentStatus)
		}
	}
	return nil
}
func (m *mongoImpl) ListInstanceNodes(ctx context.Context, req *datasource.ListInstanceNodesReq) (*datasource.ListInstanceNodesResp, error) {
	log.Info(ctx, "ListInstanceNodes start, req is: %v", utils.Show(req))
	rreq := mongoModel.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	rresp := &mongoModel.DescribeDBInstanceDetailResp{}

	if err := m.mongomgr.Get().Call(
		ctx,
		mongoModel.Action_DescribeDBInstanceDetail.String(),
		rreq,
		rresp,
		client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		log.Warn(ctx, "mongo call DescribeDBInstanceDetail failed %v", err)
		return nil, err
	}
	ret := &datasource.ListInstanceNodesResp{}
	if rresp.GetDBInstance().InstanceType == mongoModel.InstanceType_ShardedCluster {
		fp.StreamOf(rresp.GetDBInstance().GetMongos()).
			Map(func(node *mongoModel.Mongos) *model.NodeInfoObject {
				return &model.NodeInfoObject{
					NodeId:   node.GetMongosNodeId(),
					NodeType: model.NodeType_Primary,
					ZoneId:   node.GetZoneId(),
				}
			}).ToSlice(&ret.Nodes)
	}
	if rresp.GetDBInstance().InstanceType == mongoModel.InstanceType_ReplicaSet {
		fp.StreamOf(rresp.GetDBInstance().Nodes).
			Map(func(node *mongoModel.Node) *model.NodeInfoObject {
				nodeType, _ := model.NodeTypeFromString(node.GetNodeRole().String())
				return &model.NodeInfoObject{
					NodeId:   node.NodeId,
					NodeType: nodeType,
					ZoneId:   node.GetZoneId(),
				}
			}).ToSlice(&ret.Nodes)
	}
	log.Info(ctx, "ListInstanceNodes result is: %v", utils.Show(ret))
	return ret, nil
}
func (m *mongoImpl) FillInnerDataSource(ctx context.Context, ds *shared.DataSource) error {
	panic("not not implemented")
}
func (m *mongoImpl) KillProcess(ctx context.Context, req *datasource.KillProcessReq) (*datasource.KillProcessResp, error) {
	if len(req.ProcessIDs) < 1 {
		return &datasource.KillProcessResp{}, nil
	}
	var (
		failInfoList []*shared.KillFailInfo
		isMongos     bool
	)
	// 获取engine ip: port信息
	topo, err := m.getInstanceTopo(ctx, req.Source.InstanceId)
	if err != nil {
		log.Warn(ctx, "get mongo topo failed:%v", err)
		return nil, err
	}
	if len(topo) < 1 {
		log.Warn(ctx, "get mongo topo failed")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "get mongo topo failed")
	}

	// 获取节点对应的ip
	for _, topoItem := range topo {
		for _, node := range topoItem.IpList {
			if node.NodeId == req.NodeId {
				if node.IPv4 != "" {
					req.Source.Address = fmt.Sprintf("%s:%s", node.IPv4, node.Port)
				} else {
					req.Source.Address = fmt.Sprintf("[%s]:%s", node.IPv6, node.Port)
				}
				// 判断当前节点是否为mongos,mongos的opid为str,其余需转为int
				if topoItem.Component == model.ComponentType_Mongos {
					isMongos = true
				}
				break
			}
		}
	}
	conn, err := m.getMongoConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "mongoImpl: connect to datasource %s fail %v", req.Source.Address, err)
		return nil, consts.ErrorOf(model.ErrorCode_ConnectionFailed)
	}
	defer conn.Close(ctx)
	for _, processId := range req.ProcessIDs {
		var opid interface{}
		if isMongos {
			opid = processId
		} else {
			opid, _ = strconv.ParseInt(processId, 10, 64)
		}
		if err := conn.KillOp(ctx, "admin", opid); err != nil {
			failInfoList = append(failInfoList, &shared.KillFailInfo{
				ProcessId:    processId,
				ErrorMessage: err.Error()})
		}
	}
	return &datasource.KillProcessResp{FailInfoList: failInfoList}, nil

}
func (m *mongoImpl) EnsureAccount(ctx context.Context, req *datasource.EnsureAccountReq) error {
	log.Info(ctx, "Start to check account")
	var ipAddr string
	topo, err := m.getInstanceTopo(ctx, req.Source.InstanceId)
	if err != nil {
		return err
	}
	log.Info(ctx, "mongo topo is %s", utils.Show(topo))
	//任选一个节点测试账号存在性
	if len(topo) < 1 {
		log.Warn(ctx, "get mongo topo failed")
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "get mongo topo failed")
	}
	if len(topo[0].IpList) < 1 {
		log.Info(ctx, "mongo ip list is Null")
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "mongo ip list is Null")
	}
	// 检查DB侧dbw账号是否存在
	if req.Source.Address != "" {
		ipAddr = req.Source.Address
	} else {
		addr := topo[0].IpList[0]
		if addr.IPv4 != "" {
			ip := addr.IPv4
			ipAddr = fmt.Sprintf("%s:%s", ip, addr.Port)
		} else {
			ip := addr.IPv6
			ipAddr = fmt.Sprintf("[%s]:%s", ip, addr.Port)
		}
	}
	rrSource := req.Source
	rrSource.Address = ipAddr
	log.Info(ctx, "mongo ds is %s", utils.Show(rrSource))
	conn, err := m.getMongoConn(ctx, rrSource)
	if err != nil {
		if strings.Contains(err.Error(), "Authentication failed") {
			log.Warn(ctx, "mongo account not exist")
			if err := m.deleteDBWAccount(ctx, &datasource.DeleteAccountReq{
				InstanceId: rrSource.InstanceId,
			}); err != nil {
				return err
			}
			if err := m.createDBWAccount(ctx, &datasource.CreateAccountReq{
				InstanceId:      rrSource.InstanceId,
				AccountPassword: req.Source.GetPassword(),
				AccountName:     req.Source.GetUser(),
			}); err != nil {
				return err
			}
			return nil
		} else {
			log.Warn(ctx, "connect to datasource %s fail %v", rrSource.Address, err)
			return err
		}
	}
	defer conn.Close(ctx)
	if err = conn.Ping(ctx); err != nil {
		if strings.Contains(err.Error(), "Authentication failed") {
			log.Warn(ctx, "mongo account not exist")
			if err := m.deleteDBWAccount(ctx, &datasource.DeleteAccountReq{
				InstanceId: rrSource.InstanceId,
			}); err != nil {
				return err
			}
			if err := m.createDBWAccount(ctx, &datasource.CreateAccountReq{
				InstanceId:      rrSource.InstanceId,
				AccountPassword: req.Source.GetPassword(),
				AccountName:     req.Source.GetUser(),
			}); err != nil {
				return err
			}
			return nil
		} else {
			log.Warn(ctx, "ping %s fail %v", rrSource.Address, err)
			return err
		}
	}
	return nil
}
func (m *mongoImpl) DescribeDBInstanceShardInfos(ctx context.Context, req *datasource.DescribeDBInstanceShardInfosReq) (*datasource.DescribeDBInstanceShardInfosResp, error) {
	var shards []*datasource.ShardInfo
	describeDBInstanceDetailReq := &mongoModel.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	resp := &mongoModel.DescribeDBInstanceDetailResp{}
	if err := m.mongomgr.Get().Call(
		ctx,
		mongoModel.Action_DescribeDBInstanceDetail.String(),
		describeDBInstanceDetailReq,
		resp,
		client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		log.Warn(ctx, "mongo DescribeInstanceDetail err: %v", err)
		return nil, err
	}
	// 提取shard
	if err := fp.StreamOf(resp.GetDBInstance().GetShards()).Map(func(i *mongoModel.Shard) *datasource.ShardInfo {
		var (
			nodes []*model.ShardNodeInfo
		)
		for _, node := range i.Nodes {
			nodes = append(nodes, &model.ShardNodeInfo{
				NodeId:   node.NodeId,
				NodeType: node.NodeRole.String(),
				ZoneId:   node.GetZoneId(),
			})
		}
		return &datasource.ShardInfo{
			ShardId:       i.GetShardId(),
			Nodes:         nodes,
			ComponentType: model.ComponentType_Shards,
		}
	}).ToSlice(&shards); err != nil {
		return nil, err
	}
	// 提取configServer
	var (
		configNodes []*model.ShardNodeInfo
		mongosNodes []*model.ShardNodeInfo
	)
	configShard := &datasource.ShardInfo{
		ShardId:       resp.GetDBInstance().GetConfigServersId(),
		ComponentType: model.ComponentType_ConfigServers,
	}
	for _, node := range resp.GetDBInstance().GetConfigServers() {
		configNodes = append(configNodes, &model.ShardNodeInfo{
			NodeId:   node.GetConfigServerNodeId(),
			NodeType: node.NodeRole.String(),
			ZoneId:   node.GetZoneId(),
		})
	}
	configShard.Nodes = configNodes
	shards = append(shards, configShard)

	// 提取mongos
	mongosShard := &datasource.ShardInfo{
		ShardId:       resp.GetDBInstance().GetMongosId(),
		ComponentType: model.ComponentType_Mongos,
	}
	for _, node := range resp.GetDBInstance().GetMongos() {
		mongosNodes = append(mongosNodes, &model.ShardNodeInfo{
			NodeId:   node.GetMongosNodeId(),
			NodeType: model.NodeRole_None.String(),
			ZoneId:   node.GetZoneId(),
		})
	}
	mongosShard.Nodes = mongosNodes
	shards = append(shards, mongosShard)
	ret := &datasource.DescribeDBInstanceShardInfosResp{
		Shards: shards,
	}
	return ret, nil
}
func (m *mongoImpl) DescribeInstanceAddressList(ctx context.Context, req *datasource.DescribeInstanceAddressReq) ([]*datasource.DescribeInstanceAddressResp, error) {
	// DB侧会话
	// 获取engine ip: port信息
	var ret []*datasource.DescribeInstanceAddressResp
	topo, err := m.getInstanceTopo(ctx, req.InstanceId)
	if err != nil {
		return nil, err
	}
	if len(topo) < 1 {
		log.Warn(ctx, "get mongo topo failed")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "get mongo topo failed")
	}
	for _, instInfo := range topo {
		for _, addr := range instInfo.IpList {
			var (
				ipAddr string
			)
			if addr.IPv4 != "" {
				ipAddr = addr.IPv4
			} else {
				ipAddr = addr.IPv6
			}
			port, _ := strconv.ParseInt(addr.Port, 10, 64)
			ret = append(ret, &datasource.DescribeInstanceAddressResp{
				NodeId: addr.NodeId,
				IP:     ipAddr,
				Port:   int32(port),
			})
		}
	}
	return ret, nil
}
func (m *mongoImpl) DescribeCurrentConn(ctx context.Context, req *datasource.DescribeCurrentConnsReq) (*datasource.DescribeCurrentConnsResp, error) {
	ret := &datasource.DescribeCurrentConnsResp{}
	dialogInfos, err := m.getAllDialogInfos(ctx, &datasource.DescribeDialogInfosReq{
		Source:      req.Source,
		QueryFilter: req.QueryFilter,
	}, true)
	if err != nil {
		return nil, err
	}
	// get details info
	// 从会话中提取连接信息
	ret.ConnDetails = m.filterConnDetails(ctx, dialogInfos, req)
	ret.ConnStatistics = m.getConnStatistics(ctx, dialogInfos, 5)
	return ret, nil

}
func (m *mongoImpl) DescribeDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq) (*datasource.DescribeDialogInfosResp, error) {
	ret := &datasource.DescribeDialogInfosResp{}
	dialogInfos, err := m.getAllDialogInfos(ctx, req, false)
	if err != nil {
		log.Warn(ctx, "mongoImpl DescribeDialogInfos getAllDialogInfos failed, err is: %v", err)
		return nil, err
	}
	//log.Info(ctx, "dialogInfos is %s", utils.Show(dialogInfos))
	// get details info
	ret.DialogDetails = m.filterDialogDetails(ctx, dialogInfos, req)
	return ret, nil
}
func (m *mongoImpl) DescribeDialogStatistics(ctx context.Context, req *datasource.DescribeDialogStatisticsReq) (*datasource.DescribeDialogStatisticsResp, error) {
	dialogInfos, err := m.getAllDialogInfos(ctx, &datasource.DescribeDialogInfosReq{
		Source:        req.Source,
		Component:     req.Component,
		QueryFilter:   req.QueryFilter,
		InternalUsers: req.InternalUsers,
	}, false)
	if err != nil {
		return nil, err
	}
	// get aggregated info
	statistics := m.getDialogStatistics(ctx, dialogInfos, req.TopN)
	ret := &datasource.DescribeDialogStatisticsResp{
		DialogStatistics: statistics,
	}
	return ret, nil
}

func (m *mongoImpl) DescribeDBInstanceDetailForPilot(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (string, error) {
	detailReq := &mongoModel.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	resp := &mongoModel.DescribeDBInstanceDetailResp{}

	err := m.mongomgr.Get().Call(ctx, mongoModel.Action_DescribeDBInstanceDetail.String(), detailReq, resp, client.WithVersion(consts.Mongo_Version_V2))
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceDetailForPilot InstanceNotFound, err=%v", err)
			return "Instance Not Found", nil
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceDetailForPilot, err=%v", err)
			return "", err
		}
	}
	return utils.Show(resp), nil
}

func (m *mongoImpl) DescribeDBInstanceParametersForPilot(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (string, error) {
	detailReq := &mongoModel.DescribeDBInstanceParametersReq{
		InstanceId: req.InstanceId,
	}
	resp := &mongoModel.DescribeDBInstanceParametersResp{}

	err := m.mongomgr.Get().Call(ctx, mongoModel.Action_DescribeDBInstanceParameters.String(), detailReq, resp, client.WithVersion(consts.Mongo_Version_V2))
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Info(ctx, "DescribeDBInstanceParametersForPilot InstanceNotFound, err=%v", err)
			return "Instance Not Found", nil
		} else {
			log.Warn(ctx, "failed to call DescribeDBInstanceParametersForPilot, err=%v", err)
			return "", err
		}
	}
	return utils.Show(resp), nil
}

func (m *mongoImpl) getAllDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq, isAll bool) ([]*datasource.MongoDialogInfo, error) {
	type NodeInfoObj struct {
		NodeId   string
		NodeType string
		Addr     string
	}
	var (
		dialogInfos            []*datasource.MongoDialogInfo
		nodeList, fullAllNodes []*NodeInfoObj
	)
	// DB侧会话
	// 获取engine ip: port信息
	topo, err := m.getInstanceTopo(ctx, req.Source.InstanceId)
	if err != nil {
		return nil, err
	}
	if len(topo) < 1 {
		log.Warn(ctx, "get mongo topo failed")
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "get mongo topo failed")
	}
	for _, instInfo := range topo {
		for _, addr := range instInfo.IpList {
			var (
				ipAddr string
			)
			if addr.IPv4 != "" {
				ipAddr = fmt.Sprintf("%s:%s", addr.IPv4, addr.Port)
			} else {
				ipAddr = fmt.Sprintf("[%s]:%s", addr.IPv6, addr.Port)
			}
			fullAllNodes = append(fullAllNodes, &NodeInfoObj{
				NodeId:   addr.NodeId,
				Addr:     ipAddr,
				NodeType: addr.Role,
			})
		}
	}
	if len(req.QueryFilter.GetNodeIds()) > 0 {
		for _, nodeObj := range fullAllNodes {
			for _, nodeId := range req.QueryFilter.GetNodeIds() {
				if nodeId == nodeObj.NodeId {
					nodeList = append(nodeList, nodeObj)
					break
				}
			}
		}
	} else {
		nodeList = fullAllNodes
	}
	for _, node := range nodeList {
		var (
			tempDialog []*datasource.MongoDialogInfo
		)
		rrSource := req.Source
		rrSource.Address = node.Addr
		log.Info(ctx, "current node is %s ds is %s", node.NodeId, utils.Show(rrSource))
		conn, err := m.getMongoConn(ctx, rrSource)
		if err != nil {
			log.Warn(ctx, "mongoImpl: connect to datasource %s fail %v", rrSource.Address, err)
			// 授权问题，重新初始化账号
			if strings.Contains(err.Error(), "Authentication failed") {
				if err := m.EnsureAccount(ctx, &datasource.EnsureAccountReq{
					Source: req.Source,
				}); err != nil {
					log.Warn(ctx, "mongoImpl: connect to datasource %s fail %v", rrSource.Address, err)
					return nil, err
				}

			}
			if conn != nil {
				_ = conn.Close(ctx)
			}
			return nil, err
		}
		var res []*datasource.RawMongoDialogInfo
		if isAll {
			res, err = conn.CurrentOpAll(ctx)
			if err != nil {
				log.Warn(ctx, "mongo currentOp failed %v", err)
				return nil, consts.ErrorOf(model.ErrorCode_ConnectionFailed)
			}
		} else {
			res, err = conn.CurrentOp(ctx)
			if err != nil {
				log.Warn(ctx, "mongo currentOp failed %v", err)
				return nil, consts.ErrorOf(model.ErrorCode_ConnectionFailed)
			}
		}

		//log.Info(ctx, "res is %s", utils.Show(res))
		_ = conn.Close(ctx)
		fp.StreamOf(res).Map(func(info *datasource.RawMongoDialogInfo) *datasource.MongoDialogInfo {
			var execSql, pId, client string
			if info.Info == nil {
				execSql = "-"
			} else {
				dMap := make(map[string]interface{})
				for _, elem := range info.Info.(primitive.D) {
					dMap[elem.Key] = elem.Value
				}
				jsonData, err := json.Marshal(dMap)
				if err != nil {
					execSql = "-"
				} else {
					execSql = string(jsonData)
				}
			}
			if _, ok := info.ProcessID.(string); ok {
				pId = info.ProcessID.(string)
			} else {
				pId = fmt.Sprintf("%d", info.ProcessID)
			}
			if info.ClientV2 != "" {
				client = info.ClientV2
			} else {
				client = info.Client
			}
			return &datasource.MongoDialogInfo{
				ProcessID:    pId,
				Host:         client, // mongo client
				Desc:         info.Desc,
				Time:         fmt.Sprintf("%d", info.Time),
				ConnectionId: fmt.Sprintf("%d", info.ConnectionId),
				Namespace:    info.Namespace,
				PlanSummary:  info.PlanSummary,
				NodeType:     node.NodeType,
				NodeId:       node.NodeId,
				Client:       client, // mongo client
				State:        fmt.Sprintf("%v", info.State),
				Info:         execSql,
				Command:      info.Command,
			}
		}).ToSlice(&tempDialog)
		dialogInfos = append(dialogInfos, tempDialog...)
	}
	log.Info(ctx, "dialogInfos %s ", utils.Show(dialogInfos))
	return dialogInfos, nil
}
func (m *mongoImpl) getDialogStatistics(ctx context.Context, data []*datasource.MongoDialogInfo, topN int32) *shared.DialogStatistics {
	tData := data
	fp.StreamOf(tData).Reject(func(dialog *datasource.MongoDialogInfo) bool {
		return dialog.Namespace == ""
	}).ToSlice(&tData)
	ipInfo := make(map[string]*datasource.IPAggregatedInfo)
	nsInfo := make(map[string]*datasource.NsAggregatedInfo)
	var activeConn, totalConn, longTransactionCount, queryCountWithoutIndex, longestSecsRunning int32 = 0, 0, 0, 0, 0
	// fill NULL column, count active/total conns, aggregate user/ip/db info
	fp.StreamOf(tData).Foreach(func(d *datasource.MongoDialogInfo) {
		totalConn += 1
		//ip := d.Client // 统计client
		addrList := strings.Split(d.Client, ":") // 统计client
		ip := strings.Join(addrList[:len(addrList)-1], ":")
		if _, ok := ipInfo[ip]; !ok {
			ipInfo[ip] = &datasource.IPAggregatedInfo{IP: ip}
		}
		ipInfo[ip].TotalConn += 1

		if _, ok := nsInfo[d.Namespace]; !ok {
			nsInfo[d.Namespace] = &datasource.NsAggregatedInfo{Namespace: d.Namespace}
		}
		nsInfo[d.Namespace].TotalConn += 1

		if strings.ToLower(d.State) == "true" {
			activeConn += 1
			ipInfo[ip].ActiveConn += 1
			nsInfo[d.Namespace].ActiveConn += 1
		}
		// 提取执行时长超过3s的会话
		queryTimeInt, _ := strconv.ParseInt(d.Time, 10, 64)
		if queryTimeInt > 3 {
			longTransactionCount += 1
		}
		// 获取执行时长最长的会话
		if queryTimeInt > int64(longestSecsRunning) {
			longestSecsRunning = int32(queryTimeInt)
		}
		// 提取没有索引的查询(mongo)
		if strings.Contains(d.PlanSummary, "COLLSCAN") {
			queryCountWithoutIndex += 1
		}

	}).Run()
	var ipList []*shared.IPAggregatedInfo
	var nsList []*shared.NsAggregatedInfo
	// sort aggregate info, take top 5
	fp.KVStreamOf(ipInfo).ZipMap(func(k string, v *datasource.IPAggregatedInfo) *shared.IPAggregatedInfo {
		return &shared.IPAggregatedInfo{
			IP:                v.IP,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&ipList)
	fp.KVStreamOf(nsInfo).ZipMap(func(k string, v *datasource.NsAggregatedInfo) *shared.NsAggregatedInfo {
		return &shared.NsAggregatedInfo{
			Namespace:         v.Namespace,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&nsList)

	sort.Slice(ipList, func(i, j int) bool {
		if ipList[i].TotalConnections > ipList[j].TotalConnections {
			return true
		}
		if ipList[i].TotalConnections == ipList[j].TotalConnections &&
			ipList[i].ActiveConnections == ipList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(nsList, func(i, j int) bool {
		if nsList[i].TotalConnections > nsList[j].TotalConnections {
			return true
		}
		if nsList[i].TotalConnections == nsList[j].TotalConnections &&
			nsList[i].ActiveConnections == nsList[j].ActiveConnections {
			return true
		}
		return false
	})
	return &shared.DialogStatistics{
		DialogOver: &shared.DialogOverview{
			ActiveConnections:      activeConn,
			TotalConnections:       totalConn,
			LongestSecsRunning:     longestSecsRunning,
			QueryCountWithoutIndex: queryCountWithoutIndex,
			LongTransactionCount:   longTransactionCount,
		},
		IPAggregatedInfo: ipList[:fp.MinInt(int(topN), len(ipList))],
		NsAggregatedInfo: nsList[:fp.MinInt(int(topN), len(nsList))],
	}
}
func (m *mongoImpl) getConnStatistics(ctx context.Context, data []*datasource.MongoDialogInfo, topN int32) *shared.ConnStatistics {
	ipInfo := make(map[string]*datasource.IPAggregatedInfo)
	var activeConn, totalConn int32 = 0, 0
	// fill NULL column, count active/total conns, aggregate user/ip/db info
	fp.StreamOf(data).Foreach(func(d *datasource.MongoDialogInfo) {
		totalConn += 1
		addrList := strings.Split(d.Host, ":") // 统计client
		ip := strings.Join(addrList[:len(addrList)-1], ":")
		if _, ok := ipInfo[ip]; !ok {
			ipInfo[ip] = &datasource.IPAggregatedInfo{IP: ip}
		}
		ipInfo[ip].TotalConn += 1
		if strings.ToLower(d.State) == "true" {
			activeConn += 1
			ipInfo[ip].ActiveConn += 1
		}
	}).Run()
	var ipList []*shared.IPAggregatedInfo
	// sort aggregate info, take top 5
	fp.KVStreamOf(ipInfo).ZipMap(func(k string, v *datasource.IPAggregatedInfo) *shared.IPAggregatedInfo {
		return &shared.IPAggregatedInfo{
			IP:                v.IP,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&ipList)

	sort.Slice(ipList, func(i, j int) bool {
		if ipList[i].TotalConnections > ipList[j].TotalConnections {
			return true
		}
		if ipList[i].TotalConnections == ipList[j].TotalConnections &&
			ipList[i].ActiveConnections == ipList[j].ActiveConnections {
			return true
		}
		return false
	})
	return &shared.ConnStatistics{
		ConnOverview: &shared.ConnOverview{
			ActiveConnections: activeConn,
			TotalConnections:  totalConn,
		},
		IPAggregatedInfo: ipList[:fp.MinInt(int(topN), len(ipList))],
	}
}
func (m *mongoImpl) filterDialogDetails(ctx context.Context, data []*datasource.MongoDialogInfo, req *datasource.DescribeDialogInfosReq) *shared.DialogDetails {
	queryFilter := req.QueryFilter
	tData := data
	fp.StreamOf(tData).Reject(func(dialog *datasource.MongoDialogInfo) bool {
		return dialog.Namespace == ""
	}).ToSlice(&tData)
	// filter dialog details if desired
	if queryFilter != nil {
		if queryFilter.GetShowSleepConnection() == "false" {
			fp.StreamOf(tData).Reject(func(d *datasource.MongoDialogInfo) bool {
				return strings.ToLower(d.State) == "false"
			}).ToSlice(&tData)
		}
		if pID := queryFilter.ProcessID; pID != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(d.ProcessID, pID)
			}).ToSlice(&tData)
		}
		if host := queryFilter.Host; host != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(d.Host, host)
			}).ToSlice(&tData)
		}
		if command := queryFilter.Command; command != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(strings.ToLower(d.Command), strings.ToLower(command))
			}).ToSlice(&tData)
		}
		if timeLimit := queryFilter.LowerExecTimeLimit; timeLimit != "" {
			//limitInt, er := strconv.Atoi(queryFilter.LowerExecTimeLimit)
			limitFloat, er := strconv.ParseFloat(queryFilter.LowerExecTimeLimit, 64)
			if er == nil {
				fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
					execTime, _ := strconv.ParseFloat(d.Time, 64)
					return execTime >= limitFloat
				}).ToSlice(&tData)
			}
		}
		if info := queryFilter.Info; info != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(strings.ToLower(d.Info), strings.ToLower(info))
			}).ToSlice(&tData)
		}
		if nodeId := queryFilter.NodeId; nodeId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(d.NodeId, nodeId)
			}).ToSlice(&tData)
		}
		if desc := queryFilter.Desc; desc != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(strings.ToLower(d.Desc), strings.ToLower(desc))
			}).ToSlice(&tData)
		}
		if planSummary := queryFilter.PlanSummary; planSummary != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(strings.ToLower(d.PlanSummary), strings.ToLower(planSummary))
			}).ToSlice(&tData)
		}
		if ns := queryFilter.Namespace; ns != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(strings.ToLower(d.Namespace), strings.ToLower(ns))
			}).ToSlice(&tData)
		}
		// 过滤多节点(支持shard)
		if nodeIdList := queryFilter.NodeIds; len(nodeIdList) > 0 {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				var existed bool
				for _, nodeId := range nodeIdList {
					if d.NodeId == nodeId {
						existed = true
						break
					}
				}
				return existed
			}).ToSlice(&tData)
		}
	}
	// 默认按Time倒序
	//datasource.SortMongoDialog(tData, shared.DESC, "Time")
	var details []*shared.DialogDetail
	fp.StreamOf(tData).Map(func(info *datasource.MongoDialogInfo) *shared.DialogDetail {
		return &shared.DialogDetail{
			ProcessID:   info.ProcessID,
			Command:     info.Command,
			Info:        info.Info,
			Time:        info.Time,
			Host:        info.Host,
			PlanSummary: info.PlanSummary,
			NodeId:      info.NodeId,
			NodeType:    info.NodeType,
			State:       info.State,
			Desc:        info.Desc,
			Namespace:   info.Namespace,
		}
	}).ToSlice(&details)
	return &shared.DialogDetails{
		Details: details,
		Total:   int32(len(details)),
	}
}
func (m *mongoImpl) filterConnDetails(ctx context.Context, data []*datasource.MongoDialogInfo, req *datasource.DescribeCurrentConnsReq) *shared.ConnDetails {
	queryFilter := req.QueryFilter
	tData := data
	// filter dialog details if desired
	if queryFilter != nil {
		if queryFilter.GetShowSleepConnection() == "false" {
			fp.StreamOf(tData).Reject(func(d *datasource.MongoDialogInfo) bool {
				return strings.ToLower(d.State) == "false"
			}).ToSlice(&tData)
		}
		if connID := queryFilter.ConnId; connID != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(d.ConnectionId, connID)
			}).ToSlice(&tData)
		}
		if host := queryFilter.Host; host != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(d.Host, host)
			}).ToSlice(&tData)
		}
		if nodeId := queryFilter.NodeId; nodeId != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(d.NodeId, nodeId)
			}).ToSlice(&tData)
		}
		if desc := queryFilter.Desc; desc != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(strings.ToLower(d.Desc), strings.ToLower(desc))
			}).ToSlice(&tData)
		}
		if state := queryFilter.State; state != "" {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				return strings.Contains(strings.ToLower(d.State), strings.ToLower(state))
			}).ToSlice(&tData)
		}
		// 过滤多节点(支持shard)
		if nodeIdList := queryFilter.NodeIds; len(nodeIdList) > 0 {
			fp.StreamOf(tData).Filter(func(d *datasource.MongoDialogInfo) bool {
				var existed bool
				for _, nodeId := range nodeIdList {
					if d.NodeId == nodeId {
						existed = true
						break
					}
				}
				return existed
			}).ToSlice(&tData)
		}
	}
	var details []*shared.ConnDetail
	fp.StreamOf(tData).Map(func(info *datasource.MongoDialogInfo) *shared.ConnDetail {
		return &shared.ConnDetail{
			ConnID: info.ConnectionId,
			Client: info.Host,
			State:  info.State,
			Desc:   info.Desc,
		}
	}).ToSlice(&details)
	return &shared.ConnDetails{
		Details: details,
		Total:   int32(len(details)),
	}
}
func (m *mongoImpl) getInstanceTopo(ctx context.Context, instanceId string) ([]*TopoObject, error) {
	var ret []*TopoObject
	rreq := &innerMongoModel.DescribeDBInnerConnectionReq{
		InstanceId: instanceId,
		Option:     innerMongoModel.RequestTypePtr(innerMongoModel.RequestType_SHARD_ALL),
	}
	rresp := &innerMongoModel.DescribeDBInnerConnectionResp{}
	if err := m.mongomgr.Get().Call(
		ctx,
		innerMongoModel.Action_DescribeDBInnerConnection.String(),
		rreq,
		rresp,
		client.WithVersion(consts.Mongo_Version_V1)); err != nil {
		return nil, err
	}
	log.Info(ctx, "DescribeDBInnerConnectionResp is %s", utils.Show(rresp))
	if !rresp.IsSetShardMongodInfo() {
		//副本集实例
		if rresp.EndPoints != nil && len(rresp.EndPoints) > 0 {
			var ipList []*IpObject
			for _, endPoint := range rresp.EndPoints {
				ipList = append(ipList, &IpObject{
					NodeId: endPoint.GetNodeId(),
					IPv4:   endPoint.Domain,
					Port:   endPoint.Port,
				})
			}
			ret = append(ret, &TopoObject{
				Component: model.ComponentType_ReplicaSets,
				ShardId:   instanceId,
				IpList:    ipList,
			})
		}
	} else {
		//分片集实例
		var mongosTopo, configTopo, shardTopo []*TopoObject
		// 提取mongos
		if rresp.EndPoints != nil && len(rresp.EndPoints) > 0 {
			var ipList []*IpObject
			for _, endPoint := range rresp.EndPoints {
				ipList = append(ipList, &IpObject{
					NodeId: endPoint.GetNodeId(),
					IPv4:   endPoint.Domain,
					Port:   endPoint.Port,
				})
			}
			mongosTopo = append(mongosTopo, &TopoObject{
				Component: model.ComponentType_Mongos,
				ShardId:   instanceId,
				IpList:    ipList,
			})
		}
		ret = append(ret, mongosTopo...)
		// 提取ConfigServer
		if rresp.IsSetConfigInfo() && len(rresp.GetConfigInfo().EndPoints) > 0 {
			var configIpList []*IpObject
			for _, endPoint := range rresp.GetConfigInfo().EndPoints {
				configIpList = append(configIpList, &IpObject{
					NodeId: endPoint.GetNodeId(),
					IPv4:   endPoint.Domain,
					Port:   endPoint.Port,
				})
			}
			configTopo = append(configTopo, &TopoObject{
				Component: model.ComponentType_ConfigServers,
				ShardId:   rresp.GetConfigInfo().InstanceId,
				IpList:    configIpList,
			})
		}
		ret = append(ret, configTopo...)
		// 提取shard
		fp.StreamOf(rresp.GetShardMongodInfo()).Map(func(shard *innerMongoModel.ShardMongodConnectionInfo) *TopoObject {
			var shardIpList []*IpObject
			for _, endPoint := range shard.EndPoints {
				shardIpList = append(shardIpList, &IpObject{
					NodeId: endPoint.GetNodeId(),
					IPv4:   endPoint.Domain,
					Port:   endPoint.Port,
				})
			}
			return &TopoObject{
				Component: model.ComponentType_Shards,
				ShardId:   shard.InstanceId,
				IpList:    shardIpList,
			}
		}).ToSlice(&shardTopo)
		ret = append(ret, shardTopo...)
	}
	return ret, nil
}
func convertDBEngineVersionFromString(ctx context.Context, dbEngineVersion string) mongoModel.DBEngineVersion {
	// MongoDB 4.0  -> MongoDB_4_0
	converted := strings.Replace(strings.Replace(dbEngineVersion, " ", "_", -1), ".", "_", -1)
	engineVersion, err := mongoModel.DBEngineVersionFromString(converted)
	if err != nil {
		log.Warn(ctx, "failed to get DBEngineVersion from string %s", dbEngineVersion)
	}
	return engineVersion
}

func convertMongoTypeFromString(ctx context.Context, mongoInstanceType string) mongoModel.InstanceType {
	// MongoDB 4.0  -> MongoDB_4_0
	switch mongoInstanceType {
	case model.MongoInstanceType_Mongo_ShardedCluster.String():
		return mongoModel.InstanceType_ShardedCluster
	case model.MongoInstanceType_Mongo_ReplicaSet.String():
		return mongoModel.InstanceType_ReplicaSet
	default:
		log.Warn(ctx, "failed to get mongo InstanceType from string %s", mongoInstanceType)
		return mongoModel.InstanceType_ReplicaSet
	}
}
func (m *mongoImpl) createDBWAccount(ctx context.Context, req *datasource.CreateAccountReq) error {
	rreq := &mongoModel.CreateDBWAdminAccountReq{
		InstanceId:      req.InstanceId,
		AccountPassword: req.AccountPassword,
		Roles:           []string{"dbwManageOpRole"},
		AccountDesc:     utils.StringRef("DBW观测诊断专用账号"),
	}
	rresp := &mongoModel.CreateDBWAdminAccountResp{}
	if err := m.mongomgr.Get().Call(
		ctx,
		mongoModel.Action_CreateDBWAdminAccount.String(),
		rreq,
		rresp,
		client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		return err
	}
	return nil
}
func (m *mongoImpl) deleteDBWAccount(ctx context.Context, req *datasource.DeleteAccountReq) error {
	rreq := &mongoModel.DeleteDBWAdminAccountReq{
		InstanceId: req.InstanceId,
	}
	rresp := &mongoModel.DeleteDBWAdminAccountResp{}
	if err := m.mongomgr.Get().Call(
		ctx,
		mongoModel.Action_DeleteDBWAdminAccount.String(),
		rreq,
		rresp,
		client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		return err
	}
	return nil
}

func (m *mongoImpl) DescribeDBInstanceDetail(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (*datasource.DescribeDBInstanceDetailResp, error) {
	if req.InstanceId == "" {
		log.Warn(ctx, "instance param is not set")
		return nil, consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	rreq := &mongoModel.DescribeDBInstanceDetailReq{
		InstanceId: req.InstanceId,
	}
	rresp := &mongoModel.DescribeDBInstanceDetailResp{}
	m.mongomgr.Get().Call(ctx, mongoModel.Action_DescribeDBInstanceDetail.String(), rreq, rresp, client.WithVersion(consts.Mongo_Version_V2))

	if rresp.DBInstance == nil {
		return nil, consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	inst := rresp.DBInstance
	return &datasource.DescribeDBInstanceDetailResp{
		InstanceId:      inst.GetInstanceId(),
		InstanceName:    inst.GetInstanceName(),
		InstanceStatus:  inst.GetInstanceStatus().String(),
		DBEngineVersion: inst.GetDBEngineVersion().String(),
		ProjectName:     inst.GetProjectName(),
	}, nil
}

func (m *mongoImpl) OpenDBInstanceAuditLog(ctx context.Context, req *datasource.OpenDBInstanceAuditLogReq) (*datasource.OpenDBInstanceAuditLogResp, error) {
	rreq := &mongoModel.OpenAuditLogReq{
		InstanceId:         req.InstanceId,
		MongodAuditLogType: convertMongoAuditLogTypesFromString(req.AuditLogTypes),
		OpType:             mongoModel.OpAuditLogType_open,
	}
	rresp := &mongoModel.OpenAuditLogResp{}
	if err := m.mongomgr.Get().Call(ctx, mongoModel.Action_OpenAuditLog.String(), rreq, rresp, client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		log.Warn(ctx, "get redis instances fail %v", err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return nil, nil
}

func convertMongoAuditLogTypesFromString(types []string) []mongoModel.AuditLogType {
	var ret []mongoModel.AuditLogType
	for _, t := range types {
		auditLogType, err := mongoModel.AuditLogTypeFromString(t)
		if err != nil {
			log.Warn(context.Background(), "failed to get AuditLogType from string %s", t)
			continue
		}
		ret = append(ret, auditLogType)
	}
	return ret
}

func (m *mongoImpl) CloseDBInstanceAuditLog(ctx context.Context, req *datasource.CloseDBInstanceAuditLogReq) (*datasource.CloseDBInstanceAuditLogResp, error) {
	rreq := &mongoModel.CloseAuditLogReq{
		InstanceId: req.InstanceId,
		OpType:     mongoModel.OpAuditLogType_close,
	}
	rresp := &mongoModel.CloseAuditLogResp{}
	if err := m.mongomgr.Get().Call(ctx, mongoModel.Action_CloseAuditLog.String(), rreq, rresp, client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		log.Warn(ctx, "close mongo instances fail %v", err)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return nil, nil
}

func (m *mongoImpl) CheckDBInstanceAuditLogStatus(ctx context.Context, req *datasource.CheckDBInstanceAuditLogStatusReq) (*datasource.CheckDBInstanceAuditLogStatusResp, error) {
	rreq := &mongoModel.PreCheckForAuditLogReq{
		InstanceId: req.InstanceId,
	}
	rresp := &mongoModel.PreCheckForAuditLogResp{}
	if err := m.mongomgr.Get().Call(ctx, mongoModel.Action_PreCheckForAuditLog.String(), rreq, rresp, client.WithVersion(consts.Mongo_Version_V2)); err != nil {
		log.Warn(ctx, "get redis instances fail %v", err)
		if strings.Contains(err.Error(), "NotFound") {
			return nil, consts.ErrorOf(model.ErrorCode_InstanceNotFound)
		} else if strings.Contains(err.Error(), "InstanceStatusExclusive") {
			return nil, consts.ErrorOf(model.ErrorCode_InstanceStatusNotSatisfy)
			//} else if strings.Contains(err.Error(), "InvalidInstanceType") {
			//	return nil, consts.ErrorOf(model.ErrorCode_InstanceTypeNotSupport)
		} else if strings.Contains(err.Error(), "UnsupportedDBVersion") {
			return nil, consts.ErrorOf(model.ErrorCode_NotSupportInstanceVersion)
		} else if strings.Contains(err.Error(), "OperationDenied.UnsupportedAccount") {
			return nil, consts.ErrorOf(model.ErrorCode_TenantNotInFunctionWhiteList)
		}
		return nil, err
	}
	return &datasource.CheckDBInstanceAuditLogStatusResp{
		Can:    true,
		Enable: nil,
	}, nil
}

func (m *mongoImpl) DescribeDBInstanceCluster(ctx context.Context, req *datasource.DescribeDBInstanceClusterReq) (*datasource.DescribeDBInstanceClusterResp, error) {
	return &datasource.DescribeDBInstanceClusterResp{
		MultiAZ:          false,
		AzClusterMap:     nil,
		NodePool2Cluster: nil,
	}, nil
}
