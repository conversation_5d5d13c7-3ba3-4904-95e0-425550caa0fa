package mysql

import (
	rdsModel_v2 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/kitex_gen/model/v2"
	"code.byted.org/infcs/ds-lib/framework/mgr/client"
	"context"
	"database/sql"
	"fmt"
	"strings"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	v2 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2022-01-01/kitex_gen/model/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
)

const RDSMySQLContainerName = "mysql"

type VariableValue struct {
	Name  string `gorm:"column:Variable_name"`
	Value string `gorm:"column:Value"`
}

func (m *mysqlImpl) DescribeTableNoPrimaryKey(ctx context.Context, req *datasource.DescribeTableNoPrimaryKeyReq) (ret *datasource.ListTablesResp, err error) {
	ret = &datasource.ListTablesResp{}
	err = m.ConnPool.Call(
		ctx, &datasource.GetConnReq{
			Ds:     req.Source,
			Admin:  true,
		}, func(conn datasource.Conn) error {
			// 查询表是否有主键
			queryPrimaryKey := fmt.Sprintf("/*DBW SQL CONSOLE DEFAULT*/ "+
				"SELECT TABLE_SCHEMA, TABLE_NAME FROM information_schema.TABLES "+
				"WHERE TABLE_TYPE = 'BASE TABLE' "+
				"AND TABLE_SCHEMA NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys', 'byte_rds_meta') "+
				"AND TABLE_NAME NOT IN (SELECT TABLE_NAME FROM information_schema.TABLE_CONSTRAINTS WHERE CONSTRAINT_TYPE = 'PRIMARY KEY') "+
				"LIMIT %d OFFSET %d;", req.Limit, req.Offset)
			type tableItem struct {
				TableSchema string `gorm:"column:TABLE_SCHEMA"`
				TableName   string `gorm:"column:TABLE_NAME"`
			}
			var items []*tableItem
			err = conn.Raw(queryPrimaryKey).Scan(&items)
			if err != nil {
				return err
			}
			for _, item := range items {
				ret.Items = append(ret.Items, item.TableName)
				ret.Tables = append(ret.Tables, &datasource.Table{
					Name:   item.TableName,
					Schema: item.TableSchema,
				})
			}
			totalQuery := fmt.Sprintf("/* DBW SQL CONSOLE DEFAULT*/ "+
				"SELECT COUNT(1) FROM information_schema.TABLES "+
				"WHERE TABLE_TYPE = 'BASE TABLE' "+
				"AND TABLE_SCHEMA NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys', 'byte_rds_meta') "+
				"AND TABLE_NAME NOT IN (SELECT TABLE_NAME FROM information_schema.TABLE_CONSTRAINTS WHERE CONSTRAINT_TYPE = 'PRIMARY KEY') "+
				"LIMIT %d OFFSET %d;", req.Limit, req.Offset)
			var total int64
			err = conn.Raw(totalQuery).Scan(&total)
			if err != nil {
				return err
			}
			ret.Total = total
			return nil
		},
	)
	if err != nil {
		log.Error(ctx, "DescribeTableNoPrimaryKey failed %v", err)
		return
	}
	return
}

func (m *mysqlImpl) DescribeInstanceVariables(ctx context.Context, req *datasource.DescribeInstanceVariablesReq) (
	*datasource.DescribeInstanceVariablesResp, error) {
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "SQLAdvisor get conn error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_ConnectionFailed)
	}
	defer conn.Close()

	// 循环变量取值
	variables := make(map[string]string)
	for _, param := range req.Variables {
		var res = &VariableValue{}
		if err = conn.Raw(fmt.Sprintf(" %s SHOW VARIABLES LIKE \"%s\"", DBW_CONSOLE_DEFAULT_HINT, param)).Scan(res); err != nil {
			log.Warn(ctx, "SQLAdvisor get variable %s error:%s", param, err.Error())
		}
		log.Info(ctx, "SQLAdvisor get variable %v", res)
		variables[param] = res.Value
	}
	return &datasource.DescribeInstanceVariablesResp{
		Variables: variables,
	}, nil
}

func (m *mysqlImpl) DescribeSQLAdvisorTableMeta(ctx context.Context, req *datasource.DescribeSQLAdvisorTableMetaReq) (
	*datasource.DescribeSQLAdvisorTableMetaResp, error) {
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "SQLAdvisor get conn error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_ConnectionFailed)
	}
	defer conn.Close()

	var result []*datasource.SQLAdvisorTableMetaData
	for _, tbl := range req.TableList {
		var res = &datasource.SQLAdvisorTableMetaData{}
		tblName := fmt.Sprintf("%s.%s", req.DBName, tbl)
		if err = conn.Raw(fmt.Sprintf("%s select * from information_schema.tables where table_schema='%s' and table_name='%s'",
			DBW_CONSOLE_DEFAULT_HINT, req.DBName, tbl)).Scan(&res.TableInfo); err != nil {
			log.Warn(ctx, "SQLAdvisor get information_schema.tables %s error:%s", tbl, err.Error())
			continue
		}
		if err = conn.Raw(fmt.Sprintf("%s select * from information_schema.columns  where table_schema='%s' and table_name='%s'",
			DBW_CONSOLE_DEFAULT_HINT, req.DBName, tbl)).Scan(&res.ColumnInfo); err != nil {
			log.Warn(ctx, "SQLAdvisor get mysql.innodb_table_stats %s error:%s", tbl, err.Error())
			continue
		}
		if err = conn.Raw(fmt.Sprintf("%s select * from information_schema.statistics where table_schema='%s' and table_name='%s'",
			DBW_CONSOLE_DEFAULT_HINT, req.DBName, tbl)).Scan(&res.StatisticsInfo); err != nil {
			log.Warn(ctx, "SQLAdvisor get information_schema.statistics %s error:%s", tbl, err.Error())
			continue
		}
		if err = conn.Raw(fmt.Sprintf("%s select * from mysql.innodb_table_stats where database_name='%s' and table_name='%s'",
			DBW_CONSOLE_DEFAULT_HINT, req.DBName, tbl)).Scan(&res.InnodbTableStatsInfo); err != nil {
			log.Warn(ctx, "SQLAdvisor get mysql.innodb_table_stats %s error:%s", tbl, err.Error())
			continue
		}

		if err = conn.Raw(fmt.Sprintf("%s SHOW CREATE TABLE %s", DBW_CONSOLE_DEFAULT_HINT, tblName)).Scan(&res.CreateTableInfo); err != nil {
			log.Warn(ctx, "SQLAdvisor get create table info %s error:%s", tblName, err.Error())
			continue
		}
		log.Info(ctx, "DescribeSQLAdvisorTableMeta res is %v", res)
		res.Name = tbl
		result = append(result, res)
	}
	return &datasource.DescribeSQLAdvisorTableMetaResp{
		Success: true,
		Data:    result,
	}, nil
}

func (m *mysqlImpl) DescribePrimaryKeyRange(ctx context.Context, req *datasource.DescribePrimaryKeyRangeReq) (
	ret *datasource.DescribePrimaryKeyRangeResp, err error) {
	ret = &datasource.DescribePrimaryKeyRangeResp{}
	err = m.ConnPool.Call(
		ctx, &datasource.GetConnReq{
			Admin: true,
			Ds: req.Source,
		}, func(conn datasource.Conn) error {
			minSQL := getMinPrimaryKeySQL(req.Columns, req.DBName, req.TableName)
			maxSQL := getMaxPrimaryKeySQL(req.Columns, req.DBName, req.TableName)
			log.Info(ctx, "SQLAdvisor minSQL is %s,maxSQL is %s", minSQL, maxSQL)
			// TODO 这块可能需要自己实现一个方法来进行查询，Scan的方法无法处理动态的字符串，可以参考GetIndexValue 获取主键或者非空唯一索引的最大值和最小值
			//var minPk []string
			//if err = conn.Raw(minSQL).Scan(&minPk); err != nil {
			//	log.Warn(ctx, "SQLAdvisor get minSQL %s error:%s", minSQL, err.Error())
			//	return nil, err
			//}
			//
			//var maxPk []string
			//if err = conn.Raw(maxSQL).Scan(&maxPk); err != nil {
			//	log.Warn(ctx, "SQLAdvisor get maxSQL %s error:%s", minSQL, err.Error())
			//	return nil, err
			//}
			var colLength = int32(len(req.Columns))
			minSQLResult, err := getSQLResult(conn, minSQL, colLength, req.Columns)
			if err != nil {
				return err
			}
			maxSQLResult, err := getSQLResult(conn, maxSQL, colLength, req.Columns)
			if err != nil {
				return err
			}

			ret.PrimaryKeyInfo = &datasource.PrimaryKeyInfo{}
			for _, val := range req.Columns {
				ret.PrimaryKeyInfo.MinNum = append(ret.PrimaryKeyInfo.MinNum, &datasource.PrimaryKeyValue{
					ColumnName: val,
					Value:      minSQLResult[val],
				})
				ret.PrimaryKeyInfo.MaxNum = append(ret.PrimaryKeyInfo.MaxNum, &datasource.PrimaryKeyValue{
					ColumnName: val,
					Value:      maxSQLResult[val],
				})
			}
			return nil
		},
	)
	if err != nil {
		log.Error(ctx, "SQLAdvisor get conn error:%s", err.Error())
		return
	}
	return
}

func (m *mysqlImpl) DescribeSampleData(ctx context.Context, req *datasource.DescribeSampleDataReq) (
	*datasource.DescribeSampleDataResp, error) {
	var (
		minCondition = getMinColumnCondition(req.MinNum)
		maxCondition = getMaxColumnCondition(req.MaxNum)
		tblName      = fmt.Sprintf("%s.%s", req.DbName, req.TableName)
		columns      = strings.Join(req.Columns, ",")
		colLength    = len(req.Columns)
		orderBy      = getOrderByCondition(req.PrimaryKey, req.OrderBy.String())
	)
	var res []map[string]string
	err := m.ConnPool.Call(ctx, &datasource.GetConnReq{
		Ds:    req.Source,
		Admin: true,
	}, func(conn datasource.Conn) error {
		var command = fmt.Sprintf("%s select %s from %s where (%s) and (%s) order by %s limit %d ",
			DBW_CONSOLE_DEFAULT_HINT, columns, tblName, minCondition, maxCondition, orderBy, req.Limit)
		cursor, err := conn.Rows(command)
		if err != nil {
			log.Warn(ctx, "get definition error %s,sql is [%v]", err, command)
			return err
		}
		values := make([]sql.RawBytes, colLength)
		scanArgs := make([]interface{}, colLength)
		for i := range values {
			scanArgs[i] = &values[i]
		}

		cols, err := cursor.Columns()
		if err != nil {
			log.Warn(ctx, "get columns error:%s", err.Error())
			return consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
		}
		log.Info(ctx, "command is %s,columns is %s , length is %d", command, cols, len(cols))

		for cursor.Next() {
			var item = map[string]string{}
			if err = cursor.Scan(scanArgs...); err != nil {
				log.Warn(ctx, "sql brain sample data scan error:%s", err.Error())
				return consts.ErrorWithParam(model.ErrorCode_SystemError, err.Error())
			}
			for idx, col := range values {
				if col == nil {
					item[req.Columns[idx]] = "NULL"
				} else {
					item[req.Columns[idx]] = string(col) // "2024-04-26 x:xx:xx"
				}
			}
			res = append(res, item)
		}
		return nil
	})
	if err != nil {
		log.Error(ctx, "DescribeSampleData error:%s", err.Error())
		return nil, err
	}
	return &datasource.DescribeSampleDataResp{
		Total:   int32(len(res)),
		Records: res,
	}, nil
}

func (m *mysqlImpl) EnsureAccount(ctx context.Context, req *datasource.EnsureAccountReq) error {
	var err error
	// 1、调用接口获取ds.address
	if err = m.GetDatasourceAddress(ctx, req.Source); err != nil {
		log.Warn(ctx, "EnsureAccount error,getDatasourceAddress error:%s", err.Error())
		return consts.ErrorOf(model.ErrorCode_GetInstanceAddressFailed)
	}
	req.Source.NormalPort = "3306"
	req.Source.AdminPort = "3307"
	//2、在这个环节判断一下DB是否有值，如果DB没值，手动指定访问performance_schema库
	//这里主要是为了防止授权失败后，没有指定DB的情况下，会默认访问information库，这个库是无权限的，所有账号都能访问，从而导致连上但是没权限的问题
	newSource := *req.Source
	if req.Source.Db == "" {
		newSource.Db = "performance_schema"
	}
	conn, err := m.getConnV2(ctx, &newSource)
	if err != nil {
		if strings.Contains(strings.ToLower(err.Error()), consts.MySQLAccountError) ||
			strings.Contains(strings.ToLower(err.Error()), "access denied") {
			log.Warn(ctx, "rds get conn error %s, maybe there is no account", err.Error())
			// 创建一个新的账号,然后去连接
			err = m.resetAccount(ctx, newSource.InstanceId, shared.MySQL)
			if err != nil {
				log.Warn(ctx, "resetAccount err: %v", err)
				return err
			}
		}
	} else {
		// 存量实例高权限账号权限(依赖rds-mgr 562)
		cnf := m.cnf.Get(ctx)
		if cnf.FlushRdsSuperPriv {
			m.GrantSuperPrivFor80(ctx, conn, req.Source.InstanceId)
		}
		m.ConnPool.Put(ctx, conn)
	}
	log.Info(ctx, "EnsureAccount rds conn success, datasource is %v", utils.Show(newSource))
	return nil
}

func (s *mysqlImpl) GetDatasourceAddress(ctx context.Context, ds *shared.DataSource) error {
	rreq := &datasource.ListInstancePodsReq{
		Type:       shared.MySQL,
		LinkType:   shared.Volc,
		InstanceId: ds.InstanceId,
	}
	rresp, err := s.ListInstancePods(ctx, rreq)
	if err != nil {
		log.Warn(ctx, "sqlAdvisor get instance pods error:%s", err.Error())
		return consts.ErrorOf(model.ErrorCode_GetInstanceAddressFailed)
	}
	// 如果没有传, 默认取主节点地址
	if ds.NodeId == "" {
		log.Info(ctx, "instance %v ds.nodeId is empty, try to get primary node", ds.InstanceId)
		for _, pod := range rresp.Data {
			if pod.Role == model.NodeType_Primary.String() {
				for _, container := range pod.Containers {
					if container.Name == RDSMySQLContainerName {
						ds.Address = fmt.Sprintf("%s:%s", pod.PodIP, container.Port)
						log.Info(ctx, "ds.address is %v", ds.Address)
						ds.NodeId = pod.NodeId
						return nil
					}
				}
			}
		}
	} else {
		log.Info(ctx, "instance %v ds.nodeId is %v ,not empty, try to get specific node", ds.InstanceId, ds.NodeId)
		for _, pod := range rresp.Data {
			if pod.NodeId == ds.NodeId {
				for _, container := range pod.Containers {
					if container.Name == RDSMySQLContainerName {
						ds.Address = fmt.Sprintf("%s:%s", pod.PodIP, container.Port)
						log.Info(ctx, "ds.address is %v", ds.Address)
						return nil
					}
				}
			}
		}
	}
	return consts.ErrorOf(model.ErrorCode_ConnectionFailed)
}

func (m *mysqlImpl) resetAccount(ctx context.Context, instanceId string, dsType shared.DataSourceType) error {
	log.Info(ctx, "internal account does not exist or password incorrect, try to create a new one")
	if err := m.deleteAccount(ctx, instanceId, dsType); err != nil {
		return err
	}
	if err := m.createAccount(ctx, instanceId, dsType); err != nil {
		return err
	}
	if err := handler.AddPrivilegeToDB(ctx, m.ActorClient, instanceId, model.DSType(dsType)); err != nil {
		log.Warn(ctx, "AddPrivilegeToDB fail: %v", err.Error())
		return consts.ErrorOf(model.ErrorCode_AddPrivilegeToDBFailed)
	}
	return nil
}

func (m *mysqlImpl) createAccount(ctx context.Context, instanceId string, dsType shared.DataSourceType) error {
	c3cfg := m.C3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	if err := m.CreateAccount(ctx, &datasource.CreateAccountReq{
		DSType:          dsType,
		InstanceId:      instanceId,
		AccountName:     c3cfg.DBWAccountName,
		AccountPassword: getAccountPassword(c3cfg.DbwAccountPasswordGenKey, instanceId),
		AccountType:     v2.AccountType_Normal.String(),
	}); err != nil {
		log.Warn(ctx, "failed to create account, err=%v", err)
		return err
	}
	return nil
}

func (m *mysqlImpl) deleteAccount(ctx context.Context, instanceId string, dsType shared.DataSourceType) error {
	// 我们直接调用删除接口，确保删除掉这个账号后重建
	c3cfg := m.C3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	if err := m.DeleteAccount(ctx, &datasource.DeleteAccountReq{
		DSType:      dsType,
		InstanceId:  instanceId,
		AccountName: c3cfg.DBWAccountName,
	}); err != nil {
		if !strings.Contains(err.Error(), "AccountNotExist") {
			log.Warn(ctx, "failed to delete account, err=%v", err)
			return err
		}
	}
	return nil
}

// 仅限8.0版本
func (m *mysqlImpl) GrantSuperPrivFor80(ctx context.Context, conn datasource.Conn, instanceId string) {
	rdsVersion := getRdsVersion(ctx, conn)
	if strings.Contains(rdsVersion, "MySQL_8_0") {
		// 判断是否具备super权限(SERVICE_CONNECTION_ADMIN)
		hasSuperPriv, err := m.HasSuperPriv(ctx, conn)
		if err != nil {
			log.Warn(ctx, "hasSuperPriv err: %v,skip", err)
			return
		}
		if !hasSuperPriv {
			rreq := rdsModel_v2.GrantDBAccountPrivilegeReq{
				InstanceId:  instanceId,
				AccountName: "dbw_admin",
				AccountPrivileges: []*rdsModel_v2.AccountPrivilegeObject{
					{
						DBName:                 "",
						AccountPrivilege:       rdsModel_v2.PrivilegeType_Global,
						AccountPrivilegeDetail: utils.StringRef("SERVICE_CONNECTION_ADMIN,CONNECTION_ADMIN"),
					},
				},
			}
			if err := m.mysql.Get().Call(
				ctx,
				rdsModel_v2.Action_GrantDBAccountPrivilege.String(),
				rreq,
				nil,
				client.WithTenantID("1"),
				client.WithVersion(RDS_MySQL_Version_V2)); err != nil {
				log.Warn(ctx, "failed to grant account, instanceId=%s, account=%s, err=%v", instanceId, "dbw_admin", err.Error())
				return
			}
		} else {
			log.Info(ctx, "current instance %s has super priv", instanceId)
			return
		}

	} else {
		log.Info(ctx, "current instance %s is not 8.0 version,version is %s", instanceId, rdsVersion)
		return
	}
}
func (m *mysqlImpl) HasSuperPriv(ctx context.Context, conn datasource.Conn) (bool, error) {
	query := "/*+ DBW DAS DEFAULT*/ show grants for dbw_admin"
	var privSet []PrivDetail
	if err := conn.Raw(query).Scan(&privSet); err != nil {
		log.Warn(ctx, "get priv err: %v", err.Error())
		return false, err
	} else {
		for _, priv := range privSet {
			if strings.Contains(priv.Priv, "SERVICE_CONNECTION_ADMIN") {
				log.Info(ctx, "dbw_admin hasSuperPriv: %v", priv.Priv)
				return true, nil
			}
		}
		log.Info(ctx, "dbw_admin need to upper priv")
		return false, nil
	}

}
