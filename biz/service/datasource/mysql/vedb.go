package mysql

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"database/sql"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"os"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/dal"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"

	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/bytebrain"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/ds_utils"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/lib"
	"code.byted.org/infcs/dbw-mgr/biz/service/diagnosis"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/monitor/influxdb"
	"code.byted.org/infcs/dbw-mgr/biz/service/shuttle"
	"code.byted.org/infcs/dbw-mgr/biz/tenant"
	rdsModel "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/mgr/client"
	"k8s.io/utils/strings/slices"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/mgr"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"

	"github.com/volcengine/volc-sdk-golang/service/tls"
	"github.com/volcengine/volc-sdk-golang/service/tls/pb"

	vdbModel "code.byted.org/infcs/dbw-mgr/gen/vedb-mgr/2022-01-01/kitex_gen/infcs/bytendb/model"
	//vdbModel "code.byted.org/infcs/dbw-mgr/gen/vedb-mgr/idl/bytendb-mgr-idl/2022-01-01/kitex_gen/infcs/bytendb/model"

	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/db"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
)

const BYTE_NDB_INTERNAL_SYSTEM = `byte_ndb_system_internal_dbw_`
const DBW_SOLE_BYTE_NDB_GROUP_NAME = `dbw_unique_group_name`
const DialogHintTemplate = "/*force_node=%s*/"
const VeDBDialogAnalysis = " select * from information_schema.processlist "
const CCLSupportSQLTemplateBaseLineVersion = "3.0.7.2"

var sysDatabaseList = []string{"sys", "mysql", "performance_schema", "information_schema"}
var VedbProxyPortsList = []string{"3679", "3680", "3681", "3682", "3683", "3684", "3685"}

type NewVeDBMySQLDataSourceIn struct {
	dig.In
	Conf            config.ConfigProvider
	VeDBMgr         mgr.Provider `name:"vedb"`
	C3ConfProvider  c3.ConfigProvider
	L               location.Location
	MonitorClient   *influxdb.RdsMonitor
	ByteBrainClient bytebrain.ByteBrainService
	ActorClient     cli.ActorClient
	ShuttleSvc      shuttle.PGWShuttleService
	IdSvc           idgen.Service
	DbwInstanceDal  dal.DbwInstanceDAL
}

type NewVeDBMySQLDataSourceOut struct {
	dig.Out
	Source datasource.DataSourceService `group:"datasources"`
}

func NewVeDBMySQLDataSource(p NewVeDBMySQLDataSourceIn) NewVeDBMySQLDataSourceOut {
	mysqlIm := &mysqlImpl{
		DataSourceService: datasource.NewDataSourceServiceDecorator(nil).Export(),
		cnf:               p.Conf,
		mysql:             p.VeDBMgr,
		C3ConfProvider:    p.C3ConfProvider,
		L:                 p.L,
		MonitorClient:     p.MonitorClient,
		ByteBrainClient:   p.ByteBrainClient,
		ShuttleSvc:        p.ShuttleSvc,
		IdSvc:             p.IdSvc,
		ActorClient:       p.ActorClient,
		dbwInstanceDal:    p.DbwInstanceDal,
	}
	mpl := &vedbImpl{
		mysqlImpl: mysqlIm,
		vedbMutex: sync.Mutex{},
	}
	mysqlIm.ConnPool = datasource.NewPool(datasource.NewPoolIn{
		C3:    p.C3ConfProvider,
		Conf:  p.Conf,
		DsSvc: mpl,
	})

	return NewVeDBMySQLDataSourceOut{
		Source: retryIfWhiteListNotReady(mpl),
	}
}

type vedbImpl struct {
	*mysqlImpl
	vedbMutex sync.Mutex
}

// DescribeTLSConnectionInfo DescribeTLSConnectionInfo 已弃用
func (self *vedbImpl) DescribeTLSConnectionInfo(ctx context.Context, req *datasource.DescribeTLSConnectionInfoReq) (*datasource.DescribeTLSConnectionInfoResp, error) {
	var topicId string
	tlsEndpoint := fmt.Sprintf(consts.TLSEndpointTemplate, req.RegionId)
	c3cfg := self.C3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	if self.cnf.Get(ctx).EnableNewVersionSlowLogTopic {
		tlsSlowLogTopic := c3cfg.TLSNdbSlowLogTopicV2
		topicSet := &datasource.TLSSlowLogTopicV2{}
		if err := json.Unmarshal([]byte(tlsSlowLogTopic), topicSet); err != nil {
			log.Warn(ctx, " tlsSlowLogTopic unmarshal failed %v", err)
		}
		instanceId := req.InstanceId
		lastChar := instanceId[len(instanceId)-1:] + "$"
		for _, topic := range topicSet.Topics {
			if topic.InstanceType == req.Type.String() {
				for _, item := range topic.TopicList {
					if item.MatchedRegex == lastChar {
						tlsSlowLogTopic = item.TopicID
						break
					}
				}
			}
		}
	} else {
		tlsSlowLogTopic := c3cfg.TLSSlowLogTopic
		topicSet := &datasource.TLSSlowLogTopic{}
		if err := json.Unmarshal([]byte(tlsSlowLogTopic), topicSet); err != nil {
			log.Warn(ctx, " tlsSlowLogTopic unmarshal failed %v", err)
		}
		for _, topic := range topicSet.Topics {
			if topic.InstanceType == req.Type.String() {
				topicId = topic.TopicID
			}
		}
	}
	resp := &datasource.DescribeTLSConnectionInfoResp{
		Endpoint: tlsEndpoint,
		TopicID:  topicId,
	}
	log.Info(ctx, "DescribeTLSConnectionInfo: get tlsConfig from rds: %#v", resp)
	return resp, nil
}

func (self *vedbImpl) Type() shared.DataSourceType {
	return shared.VeDBMySQL
}

func (self *vedbImpl) AddWhiteList(ctx context.Context, id string, ds *shared.DataSource) (string, error) {
	if ds.LinkType != shared.Volc {
		return "", nil
	}
	log.Info(ctx, "add white list for %v with ip [%s]", ds.InstanceId, self.cnf.Get(ctx).MgrPodCIDR)

	instanceAllowlists, err := self.GetAllowlists(ctx, utils.StringRef(ds.InstanceId))
	if err != nil {
		return "", err
	}
	instanceAllowlistIDs := self.findDBWAllowlist(ctx, instanceAllowlists)
	if len(instanceAllowlistIDs) > 0 {
		return "", nil
	}

	allowlists, err := self.GetAllowlists(ctx, nil)
	if err != nil {
		return "", err
	}
	var wlID string
	dbwAllowList := self.findDBWAllowlist(ctx, allowlists)
	// has allowlist, associate it or update ips of allowlist if necessary
	for _, allowlist := range dbwAllowList {
		if allowlist.AssociatedInstanceNum < 1000 {
			wlID = allowlist.AllowListId
			break
		}
	}
	if wlID != "" {
		req := &vdbModel.AssociateAllowListReq{
			AllowListIds: []string{wlID},
			InstanceIds:  []string{ds.InstanceId},
		}
		if err := self.AssociateAllowList(ctx, req); err != nil {
			return "", err
		}
		return wlID, self.ensureAllowList(ctx, wlID)
	}
	wlID, err = self.CreateAllowList(ctx, &vdbModel.CreateAllowListReq{
		AllowListName: self.genWhiteListName(dbwAllowList),
		AllowListDesc: "DBW internal sole white list",
		AllowListType: vdbModel.AllowListType_IPv4.String(),
		AllowList:     self.cnf.Get(ctx).MgrPodCIDR,
	})
	if err != nil {
		log.Warn(ctx, "create allow list failed %s", err.Error())
		// 忽略白名单已存在错误
		if !strings.Contains(err.Error(), "DuplicateKey") {
			return "", err
		}
		// 获得instance regionId 或者 handler将regionId传过来

		if wlID == "" {
			log.Info(ctx, "get white list id for instance [%s] failed", ds.InstanceId)
			return "", consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, "cannot find white list id for group name: "+DBW_SOLE_BYTE_NDB_GROUP_NAME)
		}
	}

	err = self.AssociateAllowList(ctx, &vdbModel.AssociateAllowListReq{
		AllowListIds: []string{wlID},
		InstanceIds:  []string{ds.InstanceId},
	})
	if err != nil {
		log.Warn(ctx, "associate allow list failed %s", err.Error())
		return "", err
	}
	return wlID, nil
}

func (self *vedbImpl) GetAllowlists(ctx context.Context, instanceId *string) ([]*vdbModel.AllowListInfo, error) {
	req := &vdbModel.DescribeAllowListsReq{
		RegionId:   os.Getenv(consts.REGION_ID),
		InstanceId: instanceId,
	}
	resp := &vdbModel.DescribeAllowListsResp{}
	err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeAllowLists.String(), req, resp)
	if err != nil {
		if strings.Contains(strings.ToLower(err.Error()), "instance does not exist") {
			return nil, consts.ErrorOf(model.ErrorCode_InstanceNotFound)
		}
		return nil, err
	}
	return resp.AllowLists, nil
}

// should return error if not found
func (self *vedbImpl) findDBWAllowlist(ctx context.Context, allowlists []*vdbModel.AllowListInfo) []*vdbModel.AllowListInfo {
	var dbwAllowList []*vdbModel.AllowListInfo
	fp.StreamOf(allowlists).Reject(func(allowList *vdbModel.AllowListInfo) bool {
		return !strings.HasPrefix(allowList.AllowListName, DBW_SOLE_BYTE_NDB_GROUP_NAME)
	}).ToSlice(&dbwAllowList)
	sort.Slice(dbwAllowList, func(i, j int) bool {
		return dbwAllowList[i].AllowListName < dbwAllowList[j].AllowListName
	})
	return dbwAllowList
}

func (self *vedbImpl) genWhiteListName(createdAllowLists []*vdbModel.AllowListInfo) (nextName string) {
	if len(createdAllowLists) == 0 {
		// in order compatible, first name use old group name
		return DBW_SOLE_BYTE_NDB_GROUP_NAME
	}
	lastAllowList := createdAllowLists[len(createdAllowLists)-1]
	if lastAllowList.AllowListName == DBW_SOLE_BYTE_NDB_GROUP_NAME {
		nextName = DBW_SOLE_BYTE_NDB_GROUP_NAME + "_" + "1"
		return
	}
	nameItems := strings.Split(lastAllowList.AllowListName, "_")
	indexNum, _ := strconv.Atoi(nameItems[len(nameItems)-1])
	return fmt.Sprintf("%s_%d", DBW_SOLE_BYTE_NDB_GROUP_NAME, indexNum+1)
}

func (self *vedbImpl) getRegionId(ctx context.Context, instanceId string) (string, error) {
	req := &vdbModel.DescribeDBInstanceDetailReq{
		InstanceId: utils.StringRef(instanceId),
	}
	resp := &vdbModel.DescribeDBInstanceDetailResp{}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDBInstanceDetail.String(), req, resp); err != nil {
		log.Warn(ctx, "vedbImpl.getRegionId: %v", err)
		return "", err
	}
	regionID := resp.InstanceDetail.RegionId
	if regionID == "" {
		msg := fmt.Sprintf("vedbImpl get invalid region [%s] for instance [%s]", regionID, instanceId)
		return "", consts.ErrorWithParam(model.ErrorCode_InputParamError, msg)
	}
	return resp.InstanceDetail.RegionId, nil
}

func (self *vedbImpl) CreateAllowList(ctx context.Context, req *vdbModel.CreateAllowListReq) (id string, err error) {
	log.Info(ctx, "vedbImpl.CreateAllowList req=%s", utils.Show(req))

	originResp := &vdbModel.CreateAllowListResp{}
	err = self.mysql.Get().Call(
		ctx,
		vdbModel.Action_CreateAllowList.String(),
		&req,
		originResp,
	)
	if err != nil {
		return "", err
	}

	id = originResp.AllowListId
	return id, nil
}

func (self *vedbImpl) ensureAllowList(ctx context.Context, allowListId string) (err error) {
	req := &vdbModel.DescribeAllowListDetailReq{
		AllowListId: allowListId,
	}
	resp := &vdbModel.DescribeAllowListDetailResp{}
	err = self.mysql.Get().Call(
		ctx,
		vdbModel.Action_DescribeAllowListDetail.String(),
		&req,
		resp)
	if err != nil {
		log.Warn(ctx, "describe allow list %s failed: %s", allowListId, err.Error())
		return
	}
	mgrPodCIDR := self.cnf.Get(ctx).MgrPodCIDR
	if !self.containsIPList(resp.AllowList, mgrPodCIDR) && len(mgrPodCIDR) > 0 {
		modifyAllowlistReq := &vdbModel.ModifyAllowListReq{
			AllowListId:      allowListId,
			AllowListName:    DBW_SOLE_BYTE_NDB_GROUP_NAME,
			AllowList:        utils.StringRef(mgrPodCIDR),
			ModifyMode:       vdbModel.ModifyModePtr(vdbModel.ModifyMode_Cover),
			ApplyInstanceNum: utils.Int64Ref(int64(len(resp.AssociatedInstances))),
		}
		err = self.mysql.Get().Call(
			ctx,
			vdbModel.Action_ModifyAllowList.String(),
			modifyAllowlistReq,
			nil)
		if err != nil {
			log.Warn(ctx, "modify allowList %s with ip %s failed: %s", allowListId, self.cnf.Get(ctx).MgrPodCIDR, err.Error())
			return
		}
	}
	return
}

func (self *vedbImpl) AssociateAllowList(ctx context.Context, req *vdbModel.AssociateAllowListReq) (err error) {
	log.Info(ctx, "vedbImpl.AssociateAllowList req=%s", utils.Show(req))

	err = self.mysql.Get().Call(
		ctx,
		vdbModel.Action_AssociateAllowList.String(),
		&req,
		nil,
	)

	if err != nil {
		return err
	}

	return nil
}

func (self *vedbImpl) UpdateWhiteList(ctx context.Context, id string, ds *shared.DataSource, ip []string) error {
	if ds.LinkType != shared.Volc {
		return nil
	}
	log.Info(ctx, "update white list")
	log.Info(ctx, "veDBNewWhiteList: do nothing for update op")
	return nil
}

func (self *vedbImpl) RemoveWhiteList(ctx context.Context, id string, ds *shared.DataSource, wlID string) error {
	if ds.LinkType != shared.Volc {
		return nil
	}
	log.Info(ctx, "remove white list [%s] for %v", wlID, ds.InstanceId)
	if !self.cnf.Get(ctx).EnableRemoveWhiteList {
		log.Info(ctx, "Do not Disassociate White List")
		return nil
	}
	err := self.DisassociateAllowList(ctx, &vdbModel.DisassociateAllowListReq{
		AllowListIds: []string{wlID},
		InstanceIds:  []string{ds.InstanceId},
	})
	if err != nil {
		log.Warn(ctx, "Disassociate error %s", err.Error())
		return err
	}

	return nil
}

func (self *vedbImpl) DisassociateAllowList(ctx context.Context, req *vdbModel.DisassociateAllowListReq) (err error) {
	log.Info(ctx, "vedbSvc.DisassociateAllowList req=%s", utils.Show(req))

	err = self.mysql.Get().Call(
		ctx,
		vdbModel.Action_DisassociateAllowList.String(),
		&req,
		nil,
	)
	return err
}

func (self *vedbImpl) FillDataSource(ctx context.Context, ds *shared.DataSource) (err error) {
	if ds.LinkType != shared.Volc {
		return nil
	}
	if ds.NodeId != "" {
		return self.fillNodeAddress(ctx, ds)
	}
	log.Info(ctx, "current address %s", ds.Address)
	return self.fillDefaultEndpointCarmaAddress(ctx, ds)
}

func (self *vedbImpl) fillDefaultEndpointCarmaAddress(ctx context.Context, ds *shared.DataSource) error {
	req := &vdbModel.DescribeDBInstanceConnectionReq{
		InstanceId: ds.InstanceId,
	}
	resp := &vdbModel.DescribeDBInstanceConnectionResp{}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeInstanceProxyInnerConnection.String(), req, resp); err != nil {
		log.Warn(ctx, "get vedb %s connection fail %v", ds.InstanceId, err)
		return err
	}
	if resp.StorageInnerDomain == nil || resp.StorageInnerPort == nil {
		return fmt.Errorf("can't resolve vedb %s address, get empty storage  inner info", ds.InstanceId)
	}
	port := *resp.StorageInnerPort
	if tenant.IsRDSMultiCloudPlatform(ctx, self.cnf) && ds_utils.GetEndpointModeByPSM(ds.Address) == datasource.ReadOnly {
		var err error
		port, err = self.getReadonlyEndpointProxyPort(ctx, ds.InstanceId)
		if err != nil {
			log.Warn(ctx, "get readonly endpoint port error %s", err)
			return err
		}
	}
	address := fmt.Sprintf("%s:%s", *resp.StorageInnerDomain, port)
	log.Info(ctx, "get vedb %s address %s", ds.InstanceId, address)
	ds.Address = address
	return nil
}

func (self *vedbImpl) FillInnerDataSource(ctx context.Context, ds *shared.DataSource) error {
	return self.FillDataSource(ctx, ds)
}

func (self *vedbImpl) CheckDataSource(ctx context.Context, ds *shared.DataSource) error {
	if ds.LinkType != shared.Volc {
		return nil
	}
	conn, err := self.getConn(ctx, ds)
	if err != nil {
		log.Warn(ctx, "failed to check data source, instanceId=%s, err=%v", ds.InstanceId, err)
		return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
	}
	defer conn.Close()
	_, err = conn.Version()
	if err != nil {
		log.Warn(ctx, "get version by fail %v", err)
		return consts.ErrorOf(model.ErrorCode_ConnectionFailed)
	}
	return nil
}
func (self *vedbImpl) CreateAccount(ctx context.Context, req *datasource.CreateAccountReq) error {
	if req.AccountName != "dbw_admin" {
		rreq := &vdbModel.CreateDBAccountReq{
			InstanceId:      utils.StringRef(req.InstanceId),
			AccountName:     utils.StringRef(req.AccountName),
			AccountPassword: utils.StringRef(req.AccountPassword),
			AccountType:     utils.StringRef(req.AccountType),
		}
		if err := self.mysql.Get().Call(ctx, vdbModel.Action_CreateDBAccount.String(), rreq, nil); err != nil {
			log.Warn(ctx, "failed to create account, instanceId=%s, err=%v", req.InstanceId, err.Error())
			return err
		}
	} else {
		rreq := &vdbModel.CreateDBWAccountReq{
			InstanceId:      utils.StringRef(req.InstanceId),
			AccountName:     utils.StringRef(req.AccountName),
			AccountPassword: utils.StringRef(req.AccountPassword),
		}
		if err := self.mysql.Get().Call(ctx, vdbModel.Action_CreateDBWAccount.String(), rreq, nil); err != nil {
			log.Warn(ctx, "failed to create account, instanceId=%s, err=%v", req.InstanceId, err.Error())
			return err
		}
	}
	return nil
}
func (self *vedbImpl) CheckAccountPrivilege(ctx context.Context, req *datasource.CheckDBWAccountReq) (bool, error) {
	rreq := &vdbModel.DescribeDBWAccountReq{
		InstanceId:  utils.StringRef(req.InstanceId),
		AccountName: utils.StringRef(req.AccountName),
	}
	rrep := &vdbModel.DescribeDBWAccountResp{}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDBWAccount.String(), rreq, rrep); err != nil {
		log.Warn(ctx, "failed to get account privilege, instanceId=%s, err=%v", req.InstanceId, err.Error())
		return false, err
	}
	// 确认是否包含SERVICE_CONNECTION_ADMIN权限(admin_port执行权限)
	if rrep.IsSetAccountPrivileges() && strings.Contains(rrep.GetAccountPrivileges(), "SERVICE_CONNECTION_ADMIN") {
		return true, nil
	} else {
		return false, nil
	}
}

func (self *vedbImpl) ListDatabasesWithAccount(ctx context.Context, req *datasource.ListDatabasesWithAccountReq) (*datasource.ListDatabasesWithAccountResp, error) {
	rreq := &vdbModel.DescribeDatabasesReq{
		InstanceId: utils.StringRef(req.InstanceId),
		PageSize:   1000,
		PageNumber: 1,
	}
	rresp := &vdbModel.DescribeDatabasesResp{}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDatabases.String(), rreq, rresp); err != nil {
		log.Warn(ctx, "failed to list databases, instanceId=%s, err=%v", req.InstanceId, err.Error())
		return nil, err
	}
	ret := &datasource.ListDatabasesWithAccountResp{
		Total: rresp.Total,
	}
	fp.StreamOf(rresp.Databases).Map(func(i *vdbModel.DatabaseObject) *rdsModel.DBInfo {
		return &rdsModel.DBInfo{
			DBName:           i.DBName,
			CharacterSetName: i.CharacterSetName,
		}
	}).ToSlice(&ret.DBInfos)

	return ret, nil
}

func (self *vedbImpl) ModifyAccountPrivilege(ctx context.Context, req *datasource.ModifyAccountPrivilegeReq) error {
	rreq := &vdbModel.ModifyDBAccountPrivilegeReq{
		InstanceId:  utils.StringRef(req.InstanceId),
		AccountName: utils.StringRef(req.AccountName),
	}
	fp.StreamOf(req.ModifyAccountPrivilegesInfo).Map(func(i *datasource.ModifyAccountPrivilegeInfo) *vdbModel.ModifyAccountPrivilegesInfoObject {
		return &vdbModel.ModifyAccountPrivilegesInfoObject{
			DBName:     utils.StringRef(i.DBName),
			ActionType: utils.StringRef(i.ActionType),
			Privilege:  utils.StringRef(i.Privilege),
		}
	}).ToSlice(&rreq.ModifyAccountPrivilegesInfo)
	if err := self.mysql.Get().Call(
		ctx,
		vdbModel.Action_ModifyDBAccountPrivilege.String(),
		rreq,
		nil); err != nil {
		log.Warn(ctx, "failed to modify account privilege, instanceId=%s, account=%s, err=%v", req.InstanceId, req.AccountName, err.Error())
		return err
	}
	return nil
}

func (self *vedbImpl) DeleteAccount(ctx context.Context, req *datasource.DeleteAccountReq) error {
	rreq := &vdbModel.DeleteDBAccountReq{
		InstanceId:  utils.StringRef(req.InstanceId),
		AccountName: utils.StringRef(req.AccountName),
	}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DeleteDBAccount.String(), rreq, nil); err != nil {
		log.Warn(ctx, "failed to delete account, instanceId=%s, account=%s, err=%v", req.InstanceId, req.AccountName, err.Error())
		if strings.Contains(err.Error(), "InvalidAccountName") {
			log.Warn(ctx, "Account %s is not existed, ignore it", req.AccountName)
			return nil
		}
		return err
	}
	return nil
}

func (self *vedbImpl) GrantAccountPrivilege(ctx context.Context, req *datasource.GrantAccountPrivilegeReq) error {
	var accountPrivilege = vdbModel.ReadWriteMode_ReadOnly.String()
	if req.AccountPrivilege != "" {
		if _, err := vdbModel.ReadWriteModeFromString(req.AccountPrivilege); err != nil {
			return ErrInvalidPrivilegeType
		}
		accountPrivilege = req.AccountPrivilege
	}
	rreq := vdbModel.GrantDBAccountPrivilegeReq{
		InstanceId:  utils.StringRef(req.InstanceId),
		AccountName: utils.StringRef(req.AccountName),
		AccountPrivileges: []*vdbModel.AccountPrivilegeObject{
			{
				DBName:           utils.StringRef(req.DBName),
				AccountPrivilege: utils.StringRef(accountPrivilege),
			},
		},
	}
	if err := self.mysql.Get().Call(
		ctx,
		vdbModel.Action_GrantDBAccountPrivilege.String(),
		rreq,
		nil,
		client.WithExtra(map[string]string{"dbw": "1"})); err != nil {
		log.Warn(ctx, "failed to grant account, instanceId=%s, account=%s, err=%v", req.InstanceId, req.AccountName, err.Error())
		return err
	}
	return nil
}

func (self *vedbImpl) CheckConn(ctx context.Context, ds *shared.DataSource) error {
	if ds.LinkType == shared.Volc {
		req := &vdbModel.DescribeMasterDBEngineConnectionReq{
			InstanceId: utils.StringRef(ds.InstanceId),
		}
		resp := &vdbModel.DescribeMasterDBEngineConnectionResp{}
		if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeMasterDBEngineConnection.String(), req, resp); err != nil {
			log.Warn(ctx, "get vedb %s connection fail %v", ds.InstanceId, err)
			return err
		}
		if resp.DBEngineDomain == "" {
			log.Info(ctx, "no such instance %s", ds.InstanceId)
			return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, "instance is not found"+ds.InstanceId)
		}
		log.Info(ctx, "get vedb internal address %s", resp.DBEngineDomain)
		nds := new(shared.DataSource)
		*nds = *ds
		nds.Address = fmt.Sprintf("%v:%v", resp.DBEngineDomain, resp.DBEnginePort)
		ds = nds
	}

	conn, err := self.getConn(ctx, ds)
	if err != nil {
		log.Warn(ctx, "check conn by fail %v", err)
		return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
	}
	defer conn.Close()
	_, err = conn.Version()
	if err != nil {
		log.Warn(ctx, "get version by fail %v", err)
		return consts.ErrorOf(model.ErrorCode_ConnectionFailed)
	}
	return nil
}

func (self *vedbImpl) ListInstance(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	if req.LinkType != shared.Volc {
		return nil, nil
	}
	rreq := &vdbModel.DescribeDBInstancesReq{
		PageNumber: req.PageNumber,
		PageSize:   req.PageSize,
	}
	if req.InstanceName != "" {
		rreq.InstanceName = utils.StringRef(req.InstanceName)
	}
	if req.InstanceId != "" {
		rreq.InstanceId = utils.StringRef(req.InstanceId)
	}
	if req.TenantId != "" && req.TenantId != "0" && req.TenantId != "1" {
		rreq.TenantId = utils.StringRef(req.TenantId)
	}
	if req.DBEngineVersion != "" {
		// check DBEngineVersion param
		if req.DBEngineVersion != vdbModel.DBEngineVersionMysql80Camel && req.DBEngineVersion != vdbModel.DBEngineVersionMysql57Camel {
			return nil, consts.ErrorOf(model.ErrorCode_ParamError)
		}
		rreq.DBEngineVersion = utils.StringRef(req.DBEngineVersion)
	}
	if req.InstanceStatus != "" {
		rreq.InstanceStatus = utils.StringRef(req.InstanceStatus)
	}
	if req.CreateTimeStart != "" && req.CreateTimeEnd != "" {
		rreq.CreateTimeStart = utils.StringRef(req.CreateTimeStart)
		rreq.CreateTimeEnd = utils.StringRef(req.CreateTimeEnd)
	}
	if req.ProjectName != "" {
		rreq.ProjectName = utils.StringRef(req.ProjectName)
	}
	if len(req.Tags) > 0 {
		tagFilter := make([]*vdbModel.TagFilterObject, 0)
		for _, tag := range req.Tags {
			tagFilter = append(tagFilter, &vdbModel.TagFilterObject{
				Key:   tag.Key,
				Value: utils.StringRef(tag.Value),
			})
		}
		rreq.TagFilters = tagFilter
	}
	rresp := &vdbModel.DescribeDBInstancesResp{}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDBInstances.String(), rreq, rresp); err != nil {
		log.Warn(ctx, "get vedb instances fail %v", err)
		return nil, err
	}
	resp := &datasource.ListInstanceResp{Total: int64(rresp.Total)}
	err := fp.StreamOf(rresp.Instances).Map(func(i *vdbModel.InstanceObject) *model.InstanceInfo {
		targetTags := make([]*model.TagObject, 0)
		internalAddress := "-"
		zoneIds := strings.Split(i.ZoneIds, ";")
		if i.IsSetTags() {
			for _, tag := range i.GetTags() {
				targetTags = append(targetTags, &model.TagObject{
					Key:   tag.Key,
					Value: *tag.Value,
				})
			}
		}
		var chargeType string
		if i.ChargeDetail != nil {
			chargeType = i.ChargeDetail.ChargeType
		}
		var dbRevisionVersion string
		if i.DBRevisionVersion != nil {
			dbRevisionVersion = *i.DBRevisionVersion
		}
		accountId := req.TenantId
		if i.GetTenantId() != "" {
			accountId = i.GetTenantId()
		}
		var cpuNum int32
		var memInGiB float64
		var zone string
		if len(i.Nodes) > 0 {
			cpuNum = i.Nodes[0].VCPU
			memInGiB = float64(i.Nodes[0].Memory)
		}
		if len(zoneIds) > 0 {
			zone = zoneIds[0]
		}
		return &model.InstanceInfo{
			InstanceId:     utils.StringRef(i.InstanceId),
			InstanceName:   utils.StringRef(i.InstanceName),
			InstanceStatus: i.InstanceStatus,
			InstanceSpec: &model.InstanceSpec{
				CpuNum:     cpuNum,
				MemInGiB:   memInGiB,
				NodeNumber: int32(len(i.Nodes)),
			},
			DBEngineVersion:   i.DBEngineVersion,
			Zone:              zone,
			ProjectName:       utils.StringRef(i.ProjectName),
			AccessSource:      "云数据库 veDB MySQL 版",
			InternalAddress:   internalAddress,
			HasReadOnlyNode:   true,
			Tags:              targetTags,
			ChargeType:        utils.StringRef(chargeType),
			DBRevisionVersion: utils.StringRef(dbRevisionVersion),
			AccountId:         utils.StringRef(accountId),
			CreateTime:        utils.StringRef(i.GetCreateTime()),
			InstanceType:      model.InstanceType_VeDBMySQL,
			LinkType:          model.LinkType_Volc,
		}
	}).ToSlice(&resp.InstanceList)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (self *vedbImpl) ListInstanceLightWeight(ctx context.Context, req *datasource.ListInstanceReq) (*datasource.ListInstanceResp, error) {
	if req.LinkType != shared.Volc {
		return nil, nil
	}
	rreq := &vdbModel.DescribeDBInstancesReq{
		PageNumber: req.PageNumber,
		PageSize:   req.PageSize,
	}
	if req.TenantId != "" && req.TenantId != "0" && req.TenantId != "1" {
		rreq.TenantId = utils.StringRef(req.TenantId)
	}
	if req.InstanceStatus != "" {
		rreq.InstanceStatus = utils.StringRef(req.InstanceStatus)
	}
	if req.CreateTimeStart != "" && req.CreateTimeEnd != "" {
		rreq.CreateTimeStart = utils.StringRef(req.CreateTimeStart)
		rreq.CreateTimeEnd = utils.StringRef(req.CreateTimeEnd)
	}
	rresp := &vdbModel.DescribeDBInstancesResp{}
	if rreq.GetTenantId() != "" {
		if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDBInstances.String(), rreq, rresp, client.WithTenantID(req.TenantId)); err != nil {
			log.Warn(ctx, "get vedb instances fail %v", err)
			return nil, err
		}
	} else {
		if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDBInstances.String(), rreq, rresp); err != nil {
			log.Warn(ctx, "get vedb instances fail %v", err)
			return nil, err
		}
	}
	resp := &datasource.ListInstanceResp{Total: int64(rresp.Total)}
	err := fp.StreamOf(rresp.Instances).Map(func(i *vdbModel.InstanceObject) *model.InstanceInfo {
		accountId := req.TenantId
		if i.GetTenantId() != "" {
			accountId = i.GetTenantId()
		}
		return &model.InstanceInfo{
			InstanceId:      utils.StringRef(i.InstanceId),
			InstanceName:    utils.StringRef(i.InstanceName),
			DBEngineVersion: i.DBEngineVersion,
			AccessSource:    "云数据库 veDB MySQL 版",
			AccountId:       utils.StringRef(accountId),
			CreateTime:      utils.StringRef(i.GetCreateTime()),
			InstanceStatus:  i.InstanceStatus,
		}
	}).ToSlice(&resp.InstanceList)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (self *vedbImpl) ListInstanceNodes(ctx context.Context, req *datasource.ListInstanceNodesReq) (*datasource.ListInstanceNodesResp, error) {
	rreq := &vdbModel.DescribeDBInstanceDetailReq{
		InstanceId: utils.StringRef(req.InstanceId),
	}
	resp := &vdbModel.DescribeDBInstanceDetailResp{}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDBInstanceDetail.String(), rreq, resp); err != nil {
		log.Warn(ctx, "get vedb instance detailed fail %v", err)
		return nil, err
	}
	ret := &datasource.ListInstanceNodesResp{}
	fp.StreamOf(resp.Nodes).
		Reject(func(node *vdbModel.NodeObject) bool {
			return node.NodeType == model.NodeType_APWorkNode.String()
		}).
		Map(func(node *vdbModel.NodeObject) *model.NodeInfoObject {
			nodeType, _ := model.NodeTypeFromString(node.NodeType)
			return &model.NodeInfoObject{
				NodeId:   node.NodeId,
				NodeType: nodeType,
				CpuNum:   node.VCPU,
				MemInGiB: node.Memory,
				ZoneId:   node.ZoneId,
			}
		}).ToSlice(&ret.Nodes)
	return ret, nil
}

func (self *vedbImpl) ListInstanceNodesOri(ctx context.Context, req *datasource.ListInstanceNodesReq) (*datasource.ListInstanceNodesOriResp, error) {
	query := vdbModel.ListInstanceNodesReq{
		InstanceId: req.InstanceId,
	}
	resp := vdbModel.ListInstanceNodesResp{}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_ListInstanceNodes.String(), query, &resp); err != nil {
		log.Warn(ctx, "failed to call DescribeDBInstanceDetail, err=%v", err)
		return nil, err
	}
	ret := &datasource.ListInstanceNodesOriResp{}
	fp.StreamOf(resp.Nodes).
		Map(func(node *vdbModel.ListNode) *datasource.InstanceNodeInfo {
			return &datasource.InstanceNodeInfo{
				NodeID:     node.NodeId,
				PodName:    node.NodeId,
				Role:       node.NodeType,
				Zone:       node.Zone,
				NodeStatus: "",
			}
		}).ToSlice(&ret.NodesInfo)
	return ret, nil
}

func (self *vedbImpl) makeNewGroupID(id string) string {
	return BYTE_NDB_INTERNAL_SYSTEM + id
}

func (self *vedbImpl) getGroupID(id string) string {
	return BYTE_NDB_INTERNAL_SYSTEM + id
}

func (self *vedbImpl) getConn(ctx context.Context, ds *shared.DataSource) (db.Conn, error) {
	parseTimeManualy := false
	if val, ok := ds.ExtraDsn["parseTime"]; ok {
		parseTimeManualy = convertStringToBool(val)
	}
	opt := &db.Options{
		Address:          ds.Address,
		DB:               ds.Db,
		User:             ds.User,
		Password:         ds.Password,
		Driver:           db.MysqlDriver,
		ParseTimeManualy: parseTimeManualy,
	}
	if ds.ConnectTimeoutMs != 0 {
		opt.Timeout = uint(ds.ConnectTimeoutMs)
	}
	if ds.ReadTimeoutMs != 0 {
		opt.ReadTimeout = uint(ds.ReadTimeoutMs)
	}
	if ds.WriteTimeoutMs != 0 {
		opt.WriteTimeout = uint(ds.WriteTimeoutMs)
	}
	return db.NewConn(opt)
}
func (self *vedbImpl) getConnV2(ctx context.Context, ds *shared.DataSource) (datasource.Conn, error) {
	if ds.NodeId == "" {
		return self.ConnPool.GetInstanceAdminConn(ctx, &datasource.GetInstanceAdminConnReq{InstanceId: ds.InstanceId, Type: ds.Type, Ds: ds})
	} else {
		return self.ConnPool.GetNodeAdminConn(ctx, &datasource.GetNodeAdminConnReq{InstanceId: ds.InstanceId, Type: ds.Type, NodeId: ds.NodeId, Ds: ds})
	}
}

func (self *vedbImpl) ListDatabases(ctx context.Context, req *datasource.ListDatabasesReq) (*datasource.ListDatabasesResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, consts.ErrorWithParam(model.ErrorCode_UserDataBaseError, err.Error())
	}
	defer conn.Close()

	ret := &datasource.ListDatabasesResp{}
	var dbList []*DatabaseInfo
	sql := "select SCHEMA_NAME, DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME from information_schema.SCHEMATA where 1 = 1"
	var args []interface{}
	if req.Keyword != "" {
		sql += " and SCHEMA_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if !req.EnableSystemDB {
		sql += " and SCHEMA_NAME not in  (?) "
		systemDB := []string{"information_schema", "performance_schema", "mysql", "sys", "byte_rds_meta", "ndb_meta_info"}
		args = append(args, systemDB)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	//sql += ` LIMIT ? OFFSET ?`
	//args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(sql, args...).Scan(&dbList); err != nil {
		return nil, err
	}

	if req.Keyword == "" {
		if err = conn.Raw("select count(1) from information_schema.SCHEMATA").
			Scan(&ret.Total); err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_UserDataBaseError, err.Error())
		}
	} else {
		if err = conn.Raw("select count(1) from information_schema.SCHEMATA where SCHEMA_NAME like ?", `%`+req.Keyword+`%`).
			Scan(&ret.Total); err != nil {
			return nil, consts.ErrorWithParam(model.ErrorCode_UserDataBaseError, err.Error())
		}
	}
	if err = fp.StreamOf(dbList).Map(func(db *DatabaseInfo) *shared.DatabaseInfo {
		return &shared.DatabaseInfo{
			Name:             db.SchemaName,
			CharacterSetName: db.CharacterSetName,
			CollationName:    db.CollationName,
		}
	}).ToSlice(&ret.Items); err != nil {
		return nil, err
	}
	return ret, nil
}

func (self *vedbImpl) ListTables(ctx context.Context, req *datasource.ListTablesReq) (*datasource.ListTablesResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListTablesResp{}
	sql := "select TABLE_NAME, TABLE_COMMENT from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE IN ('BASE TABLE')"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and TABLE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		sql += genListTableFilter(req.Filters)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	//sql += ` LIMIT ? OFFSET ?`
	//args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(datasource.WithHint(sql, datasource.DbwHint), args...).Scan(&ret.Tables); err != nil {
		return nil, err
	}
	sql = "select TABLE_NAME from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE='BASE TABLE'" // ignore_security_alert
	args = []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and TABLE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		sql += genListTableFilter(req.Filters)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	if err = conn.Raw(datasource.WithHint(sql, datasource.DbwHint), args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	args = []interface{}{req.DB}
	/* get count */
	counterSQL := "/*+ DBW SQL CONSOLE DEFAULT*/ select count(1) from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE='BASE TABLE' " // ignore_security_alert
	if req.Keyword != "" {
		counterSQL += "and TABLE_NAME like ?"
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		counterSQL += genListTableFilter(req.Filters)
	}
	if err = conn.Raw(counterSQL, args...).Scan(&ret.Total); err != nil {
		return nil, err
	}
	return ret, nil
}

func (self *vedbImpl) ListAllTables(ctx context.Context, req *datasource.ListTablesReq) (*datasource.ListTablesResp, error) {
	log.Info(ctx, "ListAllTables-enter %s", utils.Show(req))
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListTablesResp{}
	sql := "select TABLE_NAME from information_schema.TABLES where TABLE_SCHEMA NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys') and TABLE_TYPE IN ('BASE TABLE')"
	args := []interface{}{}
	if req.Keyword != "" {
		sql += " and TABLE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		sql += genListTableFilter(req.Filters)
	}
	if req.Limit != 0 || req.Offset != 0 {
		sql += ` LIMIT ? OFFSET ?`
		args = append(args, req.Limit, req.Offset)
	}
	/*	//sql += ` LIMIT ? OFFSET ?`
		//args = append(args, req.Limit, req.Offset)*/

	log.Info(ctx, "ListAllTables-sql %s args %v", sql, args)
	if err = conn.Raw(datasource.WithHint(sql, datasource.DbwHint), args...).Scan(&ret.Items); err != nil {
		log.Warn(ctx, "Scan to sql %s fail %v", sql, err)
		return nil, err
	}
	args = []interface{}{req.DB}
	/* get count */
	counterSQL := "select count(1) from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE='BASE TABLE' " // ignore_security_alert
	if req.Keyword != "" {
		counterSQL += "and TABLE_NAME like ?"
		args = append(args, `%`+req.Keyword+`%`)
	}
	if req.Filters != nil {
		counterSQL += genListTableFilter(req.Filters)
	}
	log.Info(ctx, "ListAllTables-counterSQL %s args %v", counterSQL, args)
	if err = conn.Raw(datasource.WithHint(counterSQL, datasource.DbwHint), args...).Scan(&ret.Total); err != nil {
		log.Warn(ctx, "Scan to counterSQL %s fail %v", counterSQL, err)
		return nil, err
	}
	return ret, nil
}

func (self *vedbImpl) ListTablesInfo(ctx context.Context, req *datasource.ListTablesInfoReq) (*datasource.ListTablesInfoResp, error) {
	panic("Not Implement")
}

func (self *vedbImpl) DescribeTable(ctx context.Context, req *datasource.DescribeTableReq) (*datasource.DescribeTableResp, error) {
	ret := &datasource.DescribeTableResp{}
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()

	/* get columns info */
	var columns []*DBColumn
	queryCols := fmt.Sprintf("desc `%s`.`%s`", req.DB, req.Table)
	err = conn.Raw(datasource.WithHint(queryCols, datasource.DbwHint)).Scan(&columns)
	if err != nil {
		log.Warn(ctx, "get columns of %s fail %v", req.DB, req.Table, err)
		return nil, err
	}

	ret.Columns = DBColumnToTableInfoColumnInfo(columns)

	/* get index info */
	var indexs []*DBIndex
	queryIndex := fmt.Sprintf("show index from `%s`.`%s`", req.DB, req.Table)
	if err = conn.Raw(datasource.WithHint(queryIndex, datasource.DbwHint)).Scan(&indexs); err != nil {
		log.Warn(ctx, "get index of %s fail %v", req.DB, req.Table, err)
		return nil, err
	}
	var indexString = "sql get  indexs \n"
	for i, _ := range indexs {
		indexString += fmt.Sprintf("%+v", *indexs[i]) + "\n"
	}
	log.Info(ctx, indexString)
	ret.Indexs = getIndexList(indexs)
	log.Info(ctx, "get ret.Indexs %s", fmt.Sprintf("%#v", ret.Indexs))

	/* get foreignKey info */
	row := &struct {
		DDL string `gorm:"column:Create Table"`
	}{}
	if err = conn.Raw(fmt.Sprintf("show create table `%s`.`%s`", req.DB, req.Table)).Scan(row); err != nil {
		log.Warn(ctx, "get foreignKey of %s %s fail %v", req.DB, req.Table, err)
		return nil, err
	}
	log.Info(ctx, "get DDL %s", row.DDL)
	ret.ForeignKeys, err = getForeignKeyInfo(ctx, conn, req.DB, req.Table)
	if err != nil {
		return nil, err
	}
	log.Info(ctx, "get %d foreignKey", len(ret.ForeignKeys))

	/* get tableOption info */
	var tableOption DBOption
	tableOptionQuery := "SELECT ENGINE, ROW_FORMAT, AVG_ROW_LENGTH, AUTO_INCREMENT, TABLE_COLLATION, CREATE_OPTIONS, " +
		"TABLE_COMMENT, TABLE_ROWS, INDEX_LENGTH+DATA_LENGTH+DATA_FREE AS TABLE_SPACE, CREATE_TIME FROM information_schema.tables WHERE TABLE_SCHEMA=? AND TABLE_NAME=?"
	err = conn.Raw(datasource.WithHint(tableOptionQuery, datasource.DbwHint), req.DB, req.Table).Scan(&tableOption)
	if err != nil {
		log.Warn(ctx, "get tableOption from DB of %s fail %v", req.DB, req.Table, err)
		return nil, err
	}
	ret.Option, err = getTableOption(ctx, &tableOption)

	/* get column  Comment、IsAutoIncrement */
	var columnOthers []*DBColumnOther
	queryOther := fmt.Sprintf("select * from information_schema.columns where table_schema='%s' and table_name='%s'", req.DB, req.Table)
	err = conn.Raw(datasource.WithHint(queryOther, datasource.DbwHint)).Scan(&columnOthers)
	if err != nil {
		log.Warn(ctx, "get columnComments from %s of %s fail %v", req.DB, req.Table, err)
		return nil, err
	}
	getColumnOther(ctx, ret, columnOthers)
	getPrimaryOrder(ctx, ret)
	var tableName, stmt string
	var cursor db.Cursor
	var charSet, collationConn string
	definitionSQL := fmt.Sprintf("SHOW CREATE TABLE `%s`.`%s`", req.DB, req.Table)
	if cursor, err = conn.Rows(datasource.WithHint(definitionSQL, datasource.DbwHint)); err != nil {
		log.Warn(ctx, "get definition error %s", err)
		if err != nil {
			return nil, err
		}
	}
	for cursor.Next() {
		if err := cursor.Scan(&tableName, &stmt, &charSet, &collationConn); err != nil {
			if err := cursor.Scan(&tableName, &stmt); err != nil {
				return nil, err
			}
		}
	}
	ret.Definition = stmt

	if !slices.Contains(sysDatabaseList, req.DB) {
		lib.FillTableInfo(ctx, stmt, ret)
	}
	return ret, nil
}

func (self *vedbImpl) ListViews(ctx context.Context, req *datasource.ListViewsReq) (*datasource.ListViewsResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListViewsResp{}
	sql := "select TABLE_NAME from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE IN ('VIEW','SYSTEM VIEW')"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and TABLE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ?`
	args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(datasource.WithHint(sql, datasource.DbwHint), args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("select count(1) from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE IN ('VIEW','SYSTEM VIEW') and TABLE_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("select count(1) from information_schema.TABLES where TABLE_SCHEMA=? and TABLE_TYPE IN ('VIEW','SYSTEM VIEW')", req.DB).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (self *vedbImpl) DescribeView(ctx context.Context, req *datasource.DescribeViewReq) (*datasource.DescribeViewResp, error) {
	ret := &datasource.DescribeViewResp{}
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()

	// show create info
	var viewInfo ViewInfo
	queryView := fmt.Sprintf("SHOW CREATE VIEW `%s`.`%s`", req.DB, req.View)
	err = conn.Raw(datasource.WithHint(queryView, datasource.DbwHint)).Scan(&viewInfo)
	if err != nil {
		log.Warn(ctx, "show create view `%s`.`%s` fail, err=%v", req.DB, req.View, err)
		return nil, err
	}
	ret.ViewInfo = &shared.ViewInfo{
		View:       viewInfo.View,
		CreateView: viewInfo.CreateView,
		ViewMeta:   &shared.ViewInfo_MetaInfo{},
	}

	// viewMeta
	var viewMeta ViewMeta
	queryViewMeta := fmt.Sprintf("select * from information_schema.VIEWS where TABLE_SCHEMA='%s' and TABLE_NAME='%s';", req.DB, req.View)
	err = conn.Raw(datasource.WithHint(queryViewMeta, datasource.DbwHint)).Scan(&viewMeta)
	if err != nil {
		log.Warn(ctx, "select * from information_schema.VIEWS where TABLE_SCHEMA='%s' and TABLE_NAME='%s'; fail, err=%v", req.DB, req.View, err)
		return nil, err
	}
	ret.ViewInfo.ViewMeta.Name = viewMeta.TableName
	ret.ViewInfo.ViewMeta.Definition = viewMeta.ViewDefinition
	ret.ViewInfo.ViewMeta.CheckOption = viewMeta.CheckOption
	ret.ViewInfo.ViewMeta.Definer = viewMeta.DEFINER
	ret.ViewInfo.ViewMeta.Security = viewMeta.SecurityType
	ret.ViewInfo.ViewMeta.Algorithm = getViewAlgorithm(ctx, viewInfo.CreateView)
	return ret, nil
}

func (self *vedbImpl) ListFunctions(ctx context.Context, req *datasource.ListFunctionsReq) (*datasource.ListFunctionsResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListFunctionsResp{}
	sql := "SELECT `ROUTINE_NAME` FROM `information_schema`.`ROUTINES` WHERE ROUTINE_SCHEMA=? AND ROUTINE_TYPE='FUNCTION'"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and ROUTINE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ?`
	args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(datasource.WithHint(sql, datasource.DbwHint), args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("select count(1) from `information_schema`.`ROUTINES` where ROUTINE_SCHEMA=? and ROUTINE_TYPE='FUNCTION' and ROUTINE_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("select count(1) from `information_schema`.`ROUTINES` where ROUTINE_SCHEMA=? and ROUTINE_TYPE='FUNCTION'", req.DB).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (self *vedbImpl) ListProcedures(ctx context.Context, req *datasource.ListProceduresReq) (*datasource.ListProceduresResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListProceduresResp{}
	sql := "SELECT `ROUTINE_NAME` FROM `information_schema`.`ROUTINES` WHERE ROUTINE_SCHEMA=? AND ROUTINE_TYPE='PROCEDURE'"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and ROUTINE_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ?`
	args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(datasource.WithHint(sql, datasource.DbwHint), args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("select count(1) from `information_schema`.`ROUTINES` where ROUTINE_SCHEMA=? and ROUTINE_TYPE='PROCEDURE' and ROUTINE_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("select count(1) from `information_schema`.`ROUTINES` where ROUTINE_SCHEMA=? and ROUTINE_TYPE='PROCEDURE'", req.DB).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (self *vedbImpl) ListTriggers(ctx context.Context, req *datasource.ListTriggersReq) (*datasource.ListTriggersResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListTriggersResp{}
	sql := "select TRIGGER_NAME from information_schema.triggers where TRIGGER_SCHEMA=?"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and TRIGGER_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ?`
	args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(datasource.WithHint(sql, datasource.DbwHint), args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	/* get count */
	if req.Keyword != "" {
		conn.Raw("select count(1) from information_schema.triggers where TRIGGER_SCHEMA=? and TRIGGER_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("select count(1) from information_schema.triggers where TRIGGER_SCHEMA=?", req.DB).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (self *vedbImpl) DescribeEvent(ctx context.Context, req *datasource.DescribeEventReq) (*datasource.DescribeEventResp, error) {
	ret := &datasource.DescribeEventResp{}
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	eventInfo := &DBEvent{}
	sql := fmt.Sprintf("select * from information_schema.EVENTS where EVENT_SCHEMA='%s' and EVENT_NAME='%s';", req.DB, req.Name)
	if err = conn.Raw(datasource.WithHint(sql, datasource.DbwHint)).Scan(eventInfo); err != nil {
		return nil, err
	}
	log.Info(ctx, "eventInfo ret: %v", eventInfo)

	var scheduleMethod shared.ScheduleMethod
	switch eventInfo.ScheduleMethod {
	case "ONE TIME":
		scheduleMethod = shared.FixedTime
	case "RECURRING":
		scheduleMethod = shared.Cycle
	}

	var intervalUnit shared.IntervalUnit
	switch eventInfo.IntervalUnit {
	case "YEAR":
		intervalUnit = shared.Year
	case "MONTH":
		intervalUnit = shared.Month
	case "WEEK":
		intervalUnit = shared.Week
	case "DAY":
		intervalUnit = shared.DAY
	case "HOUR":
		intervalUnit = shared.Hour
	case "MINUTE":
		intervalUnit = shared.Minute
	case "SECOND":
		intervalUnit = shared.Second
	}

	var eventState shared.EventState
	switch eventInfo.EventState {
	case "ENABLED":
		eventState = shared.Enable
	case "DISABLED":
		eventState = shared.Disable
	case "SLAVESIDE_DISABLED":
		eventState = shared.DisableOnSlave
	}

	ret.EventInfo = &shared.EventInfo{
		EventName:      eventInfo.EventName,
		ScheduleMethod: scheduleMethod,
		TimeStamp:      eventInfo.TimeStamp,
		IntervalUnit:   intervalUnit,
		IntervalNumber: eventInfo.IntervalNumber,
		Preserve:       eventInfo.Preserve == "PRESERVE",
		EventState:     eventState,
		Do:             eventInfo.Do,
		Comment:        eventInfo.Comment,
		StartTime:      eventInfo.StartTime,
		EndTime:        eventInfo.EndTime,
	}
	return ret, nil
}

func (self *vedbImpl) DescribeFunction(ctx context.Context, req *datasource.DescribeFunctionReq) (*datasource.DescribeFunctionResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	functionInfo := &DBFunction{}
	ret := &datasource.DescribeFunctionResp{}

	/* get the param list */
	var paramlist []Param
	sql1 := fmt.Sprintf("select PARAMETER_MODE,PARAMETER_NAME,DATA_TYPE,DTD_IDENTIFIER from information_schema.PARAMETERS where specific_schema='%s' and specific_name='%s' and ROUTINE_TYPE='FUNCTION';", req.DB, req.Function)
	if err = conn.Raw(datasource.WithHint(sql1, datasource.DbwHint)).Scan(&paramlist); err != nil {
		return nil, err
	}

	/* get  function info */
	sql2 := fmt.Sprintf("select *from information_schema.ROUTINES where ROUTINE_SCHEMA='%s' and SPECIFIC_NAME='%s' and ROUTINE_TYPE='FUNCTION'", req.DB, req.Function)
	if err = conn.Raw(datasource.WithHint(sql2, datasource.DbwHint)).Scan(functionInfo); err != nil {
		return nil, err
	}

	var paramList2 []*shared.Param
	for _, param := range paramlist {
		paramList2 = append(paramList2, &shared.Param{
			ParamMode: param.ParamMode,
			ParamName: param.ParamName,
			ParamType: param.ParamType,
		})
	}

	var security shared.Security
	switch functionInfo.Security {
	case "DEFINER":
		security = shared.Definer
	case "INVOKER":
		security = shared.Invoker
	default:
		security = shared.Definer
	}

	var sqlDataAccess shared.SQLDataAccess
	switch functionInfo.SQLDataAccess {
	case "CONTAINS SQL":
		sqlDataAccess = shared.ContainsSQL
	case "NO SQL":
		sqlDataAccess = shared.NoSQL
	case "READS SQL DATA":
		sqlDataAccess = shared.ReadsSQLData
	case "MODIFIES SQL DATA":
		sqlDataAccess = shared.ModifiesSQLData
	default:
		sqlDataAccess = shared.ContainsSQL
	}

	var determine shared.Determine
	switch functionInfo.Determine {
	case "YES":
		determine = shared.DETERMINISTIC
	case "NO":
		determine = shared.NOT_DETERMINISTIC
	default:
		determine = shared.DETERMINISTIC
	}

	ret.FunctionInfo = &shared.FunctionInfo{
		FunctionName:     functionInfo.FunctionName,
		Security:         security,
		SQLDataAccess:    sqlDataAccess,
		Definition:       functionInfo.Definition,
		FuncReturnType:   functionInfo.FuncReturnType,
		FuncReturnLength: 0,
		Determine:        determine,
		Comment:          functionInfo.Comment,
		ParamList:        paramList2,
	}
	return ret, nil
}

func (self *vedbImpl) DescribeProcedure(ctx context.Context, req *datasource.DescribeProcedureReq) (*datasource.DescribeProcedureResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	procedureInfo := &DBProcedure{}
	ret := &datasource.DescribeProcedureResp{}

	/* get the param list */
	var paramlist []Param
	sql1 := fmt.Sprintf("select PARAMETER_MODE,PARAMETER_NAME,DATA_TYPE,DTD_IDENTIFIER from information_schema.PARAMETERS where specific_schema=\"%s\" and specific_name=\"%s\";", req.DB, req.Procedure)
	if err = conn.Raw(datasource.WithHint(sql1, datasource.DbwHint)).Scan(&paramlist); err != nil {
		return nil, err
	}

	/* get procedure info */
	sql2 := fmt.Sprintf("select *from information_schema.ROUTINES where ROUTINE_SCHEMA='%s' and SPECIFIC_NAME='%s' and ROUTINE_TYPE='PROCEDURE'", req.DB, req.Procedure)
	if err = conn.Raw(datasource.WithHint(sql2, datasource.DbwHint)).Scan(procedureInfo); err != nil {
		return nil, err
	}

	paramlist2 := make([]*shared.Param, 0)
	for _, param := range paramlist {
		paramlist2 = append(paramlist2, &shared.Param{
			ParamMode: param.ParamMode,
			ParamName: param.ParamName,
			ParamType: param.ParamType,
		})
	}

	var security shared.Security
	switch procedureInfo.Security {
	case "DEFINER":
		security = shared.Definer
	case "INVOKER":
		security = shared.Invoker
	default:
		security = shared.Definer
	}

	var sqlDataAccess shared.SQLDataAccess
	switch procedureInfo.SQLDataAccess {
	case "CONTAINS SQL":
		sqlDataAccess = shared.ContainsSQL
	case "NO SQL":
		sqlDataAccess = shared.NoSQL
	case "READS SQL DATA":
		sqlDataAccess = shared.ReadsSQLData
	case "MODIFIES SQL DATA":
		sqlDataAccess = shared.ModifiesSQLData
	default:
		sqlDataAccess = shared.ContainsSQL
	}

	ret.ProcedureInfo = &shared.ProcedureInfo{
		ProcedureName: procedureInfo.ProcedureName,
		Security:      security,
		SQLDataAccess: sqlDataAccess,
		Definition:    procedureInfo.Definition,
		Comment:       procedureInfo.Comment,
		ParamList:     paramlist2,
	}
	return ret, nil
}

func (self *vedbImpl) DescribeTrigger(ctx context.Context, req *datasource.DescribeTriggerReq) (*datasource.DescribeTriggerResp, error) {
	ret := &datasource.DescribeTriggerResp{}
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()

	/* get trigger info */
	var triggerInfo TriggerInfo
	queryView := fmt.Sprintf("select * from information_schema.TRIGGERS where TRIGGER_SCHEMA='%s' and TRIGGER_NAME='%s';", req.DB, req.Trigger)
	err = conn.Raw(datasource.WithHint(queryView, datasource.DbwHint)).Scan(&triggerInfo)
	if err != nil {
		log.Warn(ctx, "get trigger:`%s`.`%s` info fail, err=%v", req.DB, req.Trigger, err)
		return nil, err
	}

	var triggerTime shared.TriggerTime
	switch triggerInfo.TriggerTime {
	case "BEFORE":
		triggerTime = shared.Before
	case "AFTER":
		triggerTime = shared.After
	}

	var triggerEvent shared.TriggerEvent
	switch triggerInfo.TriggerEvent {
	case "INSERT":
		triggerEvent = shared.Insert
	case "UPDATE":
		triggerEvent = shared.Update
	case "DELETE":
		triggerEvent = shared.Delete
	}

	ret.TriggerInfo = &shared.TriggerInfo{
		TriggerName:  triggerInfo.TriggerName,
		TriggerTime:  triggerTime,
		TriggerEvent: triggerEvent,
		TriggerTable: triggerInfo.TriggerTable,
		Definition:   triggerInfo.Definition,
	}

	return ret, nil
}

func (self *vedbImpl) ListEvents(ctx context.Context, req *datasource.ListEventsReq) (*datasource.ListEventsResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListEventsResp{}
	sql := "select EVENT_NAME from INFORMATION_SCHEMA.EVENTS where EVENT_SCHEMA=?"
	args := []interface{}{req.DB}
	if req.Keyword != "" {
		sql += " and EVENT_NAME like ? "
		args = append(args, `%`+req.Keyword+`%`)
	}
	sql += ` LIMIT ? OFFSET ?`
	args = append(args, req.Limit, req.Offset)
	if err = conn.Raw(datasource.WithHint(sql, datasource.DbwHint), args...).Scan(&ret.Items); err != nil {
		return nil, err
	}
	if req.Keyword != "" {
		conn.Raw("select count(1) from INFORMATION_SCHEMA.EVENTS where EVENT_SCHEMA=? and EVENT_NAME like ?", req.DB, `%`+req.Keyword+`%`).
			Scan(&ret.Total)
	} else {
		conn.Raw("select count(1) from INFORMATION_SCHEMA.EVENTS where EVENT_SCHEMA=?", req.DB).
			Scan(&ret.Total)
	}
	return ret, nil
}

func (self *vedbImpl) ListCharsets(ctx context.Context, req *datasource.ListCharsetsReq) (*datasource.ListCharsetsResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListCharsetsResp{}
	sql := "SELECT character_set_name from information_schema.character_sets"

	if req.Keyword != "" {
		sql += fmt.Sprintf(" WHERE character_set_name LIKE '%%%s%%'", req.Keyword)
	}
	sql += ";"
	log.Info(ctx, sql)
	if err = conn.Raw(datasource.WithHint(sql, datasource.DbwHint)).Scan(&ret.Items); err != nil {
		return nil, err
	}
	ret.Total = int64(len(ret.Items))
	log.Info(ctx, "get Charsets %s", fmt.Sprintf("%#v", ret))
	return ret, nil
}

func (self *vedbImpl) ListCollations(ctx context.Context, req *datasource.ListCollationsReq) (*datasource.ListCollationsResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.ListCollationsResp{}
	sql := "SELECT collation_name from information_schema.collations"

	if req.Keyword != "" {
		sql += fmt.Sprintf(" WHERE collation_name LIKE '%%%s%%'", req.Keyword)
	}
	sql += ";"
	log.Info(ctx, sql)
	if err = conn.Raw(datasource.WithHint(sql, datasource.DbwHint)).Scan(&ret.Items); err != nil {
		return nil, err
	}
	ret.Total = int64(len(ret.Items))
	log.Info(ctx, "get collations %s", fmt.Sprintf("%#v", ret))
	return ret, nil
}

func (self *vedbImpl) setCandidateAddress(ctx context.Context, ds *shared.DataSource) error {
	if ds.LinkType == shared.Volc {
		req := &vdbModel.DescribeMasterDBEngineConnectionReq{
			InstanceId: utils.StringRef(ds.InstanceId),
		}
		resp := &vdbModel.DescribeMasterDBEngineConnectionResp{}
		if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeMasterDBEngineConnection.String(), req, resp); err != nil {
			log.Warn(ctx, "get vedb %s connection fail %v", ds.InstanceId, err)
			return err
		}
		if resp.DBEngineDomain == "" {
			log.Info(ctx, "no such instance %s", ds.InstanceId)
			return consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, "instance not found:"+ds.InstanceId)
		}
		log.Info(ctx, "get vedb internal address %s", resp.DBEngineDomain)
		ds.CadidateAddress = fmt.Sprintf("%v:%v", resp.DBEngineDomain, resp.DBEnginePort)
		return nil
	}
	return nil
}

func (self *vedbImpl) KillQuery(ctx context.Context, req *shared.DataSource, connection *shared.ConnectionInfo) error {
	conn, err := self.getConn(ctx, req)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Address, err)
		return err
	}
	defer conn.Close()
	log.Info(ctx, "kill query %v", connection.OuterConnectionId)
	return conn.Exec(`KILL QUERY ?`, connection.OuterConnectionId)
}

func (self *vedbImpl) GetAdvice(ctx context.Context, req *datasource.GetAdviceReq) (*datasource.GetAdviceResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.GetAdviceResp{}
	// changeDB
	execSql := fmt.Sprintf("use `%s`", req.DB)
	err = conn.Exec(datasource.WithHint(execSql, datasource.DbwHint))
	if err != nil {
		log.Warn(ctx, "Change DB %s fail because %s", req.DB, err)
		return nil, err
	}
	// get Explain
	queryExplain := fmt.Sprintf("explain %s", req.Sql)
	var explainInfos []*DBExplain
	err = conn.Raw(datasource.WithHint(queryExplain, datasource.DbwHint)).Scan(&explainInfos)
	if err != nil {
		log.Warn(ctx, "get explain from DB %s fail, because %v", req.DB, err)
		return nil, err
	}
	err = fp.StreamOf(explainInfos).Map(func(e *DBExplain) *shared.AdviceInfo_ExplainInfo {
		return &shared.AdviceInfo_ExplainInfo{
			Id: e.ID, SelectType: e.SelectType, Table: e.Table,
			Partitions: e.Partitions, Type: e.Type, PossibleKeys: e.PossibleKeys,
			Key: e.Key, KeyLen: e.KeyLen, Ref: e.Ref,
			Rows: e.Rows, Filtered: e.Filtered, Extra: e.Extra}
	}).ToSlice(&ret.Explains)
	if err != nil {
		return nil, err
	}

	return ret, nil
}

func (self *vedbImpl) DescribeTrxAndLocks(ctx context.Context, req *datasource.DescribeTrxAndLocksReq) (*datasource.DescribeTrxAndLocksResp, error) {
	trxAndLockList, err := self.getAllTrxLocks(ctx, req)
	if err != nil {
		return nil, err
	}
	filtedAndLockList := self.filterTrxAndLocks(ctx, trxAndLockList, req.SearchParam, req.NodeIds)
	ret := &datasource.DescribeTrxAndLocksResp{
		Result: &shared.DescribeTrxAndLocksInfo{
			TrxAndLockList: filtedAndLockList,
			Total:          int32(len(filtedAndLockList)),
		},
	}
	return ret, nil
}
func (self *vedbImpl) getAllTrxLocks(ctx context.Context, req *datasource.DescribeTrxAndLocksReq) ([]*shared.TrxAndLock, error) {
	var (
		pods         []*shared.KubePod
		trxLocksList []*shared.TrxAndLock
		querySql     string
	)
	// 获取所有pod节点
	listInstancePodsReq := &datasource.ListInstancePodsReq{
		InstanceId: req.Source.InstanceId,
	}
	listInstancePodsResp, err := self.ListInstancePods(ctx, listInstancePodsReq)
	if err != nil {
		log.Warn(ctx, "get InstancePods fail: %v", err)
		return nil, err
	}
	// 获取所有mysqld节点
	for _, pod := range listInstancePodsResp.Data {
		if pod.Component == "dbengine" && pod.Role == model.NodeType_Primary.String() {
			pods = append(pods, pod)
		}
	}
	if len(pods) < 1 {
		log.Warn(ctx, "No mysqld pod found")
		return nil, consts.ErrorWithParam(model.ErrorCode_GetInstanceAddressFailed, "No mysqld pod found")
	}
	whereConditions, args := generateTrxAndLockSearchWhereConditions(req.SearchParam)
	/* sort */
	whereConditions += ` order by ` + req.SortParam + " " + req.Order
	for _, node := range pods {
		maSource := *req.Source
		//maSource.Address = fmt.Sprintf("%s:%s", pod.PodIP, pod.Containers[0].GetPort())
		// 优先使用adminPort
		if node.Containers[0].GetAdminPort() != "" {
			maSource.Address = fmt.Sprintf("%s:%s", node.PodIP, node.Containers[0].GetAdminPort())
		} else {
			maSource.Address = fmt.Sprintf("%s:%s", node.PodIP, node.Containers[0].GetPort())
		}
		maSource.NodeId = node.NodeId
		conn, err := self.getConnV2(ctx, &maSource)
		if err != nil {
			log.Warn(ctx, "connect to datasource %s fail %v", maSource.Address, err)
			if conn != nil {
				self.ConnPool.Put(ctx, conn)
			}
			continue
		}
		// 确定内核版本
		rdsVersion := getRdsVersion(ctx, conn)
		var TrxAndLocklist []TrxAndLock
		// 查询事务
		querySql = TrxQuery + whereConditions
		log.Info(ctx, "exec sql is %s", querySql)
		if err = conn.Raw(querySql, args...).Scan(&TrxAndLocklist); err != nil {
			self.ConnPool.Put(ctx, conn)
			continue
		}
		log.Info(ctx, "node %s TrxAndLocklist is %s", node.NodeId, utils.Show(TrxAndLocklist))
		// 查询是否存在锁阻塞
		if strings.Contains(rdsVersion, "MySQL_5_7") {
			var waitLocks57 []*datasource.LockWait57Version
			log.Info(ctx, "execute trx 5.7 sql is %s", WaitSql57)
			//获取锁信息
			lockList, err := self.describeLock(ctx, conn)
			if err != nil {
				log.Warn(ctx, "get lock fail: %v", err)
				self.ConnPool.Put(ctx, conn)
				return nil, err
			}
			if err := conn.Raw(WaitSql57).Scan(&waitLocks57); err != nil {
				log.Warn(ctx, "get 5.7 lock wait fail: %v", err)
				self.ConnPool.Put(ctx, conn)
				return nil, err
			}
			self.ConnPool.Put(ctx, conn)
			for _, TL := range TrxAndLocklist {
				var (
					lockStatus       shared.LockStatus
					trxWaitStartTime string
					blockTrxId       string
				)
				if TL.TrxLockStructs > 0 && TL.TrxRequestedLockId != "" {
					lockStatus = shared.LockHoldAndWait
				} else if TL.TrxLockStructs == 0 && TL.TrxRequestedLockId != "" {
					lockStatus = shared.LockWait
				} else if TL.TrxLockStructs > 0 && TL.TrxRequestedLockId == "" {
					lockStatus = shared.LockHold
				} else {
					lockStatus = shared.None
				}
				/* TrxWaitStartTime maybe NULL */
				if TL.TrxWaitStartTime.Format("2006-01-02 15:04:05") == "0001-01-01 00:00:00" {
					trxWaitStartTime = ""
				} else {
					trxWaitStartTime = TL.TrxWaitStartTime.Format("2006-01-02 15:04:05")
				}
				// 匹配事务是否存在锁阻塞
				for _, waitItem := range waitLocks57 {
					if TL.TrxRequestedLockId == waitItem.RequestingLockId {
						blockTrxId = waitItem.BlockTrxId
						break
					}
				}
				tLockList := lockList
				// 匹配事务是否持有锁
				fp.StreamOf(tLockList).Filter(func(d *shared.Lock) bool {
					return d.TrxId == TL.TrxId
				}).ToSlice(&tLockList)
				lockList2 := make([]*shared.Lock, 0)
				lockList2 = append(lockList2, lockList...)
				trxLocksList = append(trxLocksList, &shared.TrxAndLock{
					ProcessId:        TL.ProcessId,
					TrxId:            TL.TrxId,
					TrxStatus:        TL.TrxState,
					TrxIsoLevel:      TL.TrxIsoLevel,
					TrxStartTime:     TL.TrxStartTime.Format("2006-01-02 15:04:05"),
					TrxWaitStartTime: trxWaitStartTime,
					SqlBlocked:       TL.SqlBlocked,
					TrxTablesLocked:  TL.TrxTablesLocked,
					TrxRowsLocked:    TL.TrxRowsLocked,
					TrxRowsModified:  TL.TrxRowsModified,
					LockStatus:       lockStatus,
					LockList:         lockList2,
					TrxExecTime:      TL.TrxExecTime,
					BlockTrxId:       blockTrxId,
					NodeId:           node.NodeId,
				})
			}
		} else {
			//log.Info(ctx, "execute trx 8.0 sql is %s", WaitSql80)
			////获取锁信息
			//lockList, err := self.describeLock(ctx, conn)
			//if err != nil {
			//	log.Warn(ctx, "get lock fail: %v", err)
			//	self.ConnPool.Put(ctx, conn)
			//	return nil, err
			//}
			//var waitLocks80 []*datasource.LockWait80Version
			//if err := conn.Raw(WaitSql80).Scan(&waitLocks80); err != nil {
			//	log.Warn(ctx, "get 5.7 lock wait fail: %v", err)
			//	self.ConnPool.Put(ctx, conn)
			//	return nil, err
			//}
			self.ConnPool.Put(ctx, conn)
			for _, TL := range TrxAndLocklist {
				var (
					lockStatus       shared.LockStatus
					trxWaitStartTime string
					//blockTrxId       string
				)
				if TL.TrxLockStructs > 0 && TL.TrxRequestedLockId != "" {
					lockStatus = shared.LockHoldAndWait
				} else if TL.TrxLockStructs == 0 && TL.TrxRequestedLockId != "" {
					lockStatus = shared.LockWait
				} else if TL.TrxLockStructs > 0 && TL.TrxRequestedLockId == "" {
					lockStatus = shared.LockHold
				} else {
					lockStatus = shared.None
				}
				/* TrxWaitStartTime maybe NULL */
				if TL.TrxWaitStartTime.Format("2006-01-02 15:04:05") == "0001-01-01 00:00:00" {
					trxWaitStartTime = ""
				} else {
					trxWaitStartTime = TL.TrxWaitStartTime.Format("2006-01-02 15:04:05")
				}
				// 匹配事务是否存在锁阻塞
				//for _, waitItem := range waitLocks80 {
				//	if TL.TrxRequestedLockId == waitItem.RequestingLockId {
				//		blockTrxId = waitItem.BlockTrxId
				//		break
				//	}
				//}
				//tLockList := lockList
				// 匹配事务是否持有锁
				//fp.StreamOf(tLockList).Filter(func(d *shared.Lock) bool {
				//	return d.TrxId == TL.TrxId
				//}).ToSlice(&tLockList)
				//lockList2 := make([]*shared.Lock, 0)
				//lockList2 = append(lockList2, tLockList...)
				trxLocksList = append(trxLocksList, &shared.TrxAndLock{
					ProcessId:        TL.ProcessId,
					TrxId:            TL.TrxId,
					TrxStatus:        TL.TrxState,
					TrxIsoLevel:      TL.TrxIsoLevel,
					TrxStartTime:     TL.TrxStartTime.Format("2006-01-02 15:04:05"),
					TrxWaitStartTime: trxWaitStartTime,
					SqlBlocked:       TL.SqlBlocked,
					TrxTablesLocked:  TL.TrxTablesLocked,
					TrxRowsLocked:    TL.TrxRowsLocked,
					TrxRowsModified:  TL.TrxRowsModified,
					LockStatus:       lockStatus,
					//LockList:         lockList2,
					TrxExecTime: TL.TrxExecTime,
					//BlockTrxId:       blockTrxId,
					NodeId: node.NodeId,
				})
			}
		}
	}
	return trxLocksList, nil
}
func (self *vedbImpl) filterTrxAndLocks(ctx context.Context, data []*shared.TrxAndLock, queryFilter *model.TrxQueryFilter, nodeIds []string) []*shared.TrxAndLock {
	tData := data
	// filter dialog details if desired
	if len(nodeIds) > 0 {
		fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
			var existed bool
			for _, nodeId := range nodeIds {
				if d.NodeId == nodeId {
					existed = true
					break
				}
			}
			return existed
		}).ToSlice(&tData)
	}
	if queryFilter != nil {
		if trxId := queryFilter.GetTrxId(); trxId != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.TrxId), strings.ToLower(trxId))
			}).ToSlice(&tData)
		}
		if pId := queryFilter.GetProcessId(); pId != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.ProcessId), strings.ToLower(pId))
			}).ToSlice(&tData)
		}
		if blockTrxId := queryFilter.GetBlockTrxId(); blockTrxId != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.BlockTrxId), strings.ToLower(blockTrxId))
			}).ToSlice(&tData)
		}

		if blockSql := queryFilter.GetSqlBlocked(); blockSql != "" {
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				return strings.Contains(strings.ToLower(d.SqlBlocked), strings.ToLower(blockSql))
			}).ToSlice(&tData)
		}
		if queryFilter.IsSetTrxExecTime() {
			trxExecTime := queryFilter.GetTrxExecTime()
			fp.StreamOf(tData).Filter(func(d *shared.TrxAndLock) bool {
				execTime := d.TrxExecTime
				return execTime > trxExecTime
			}).ToSlice(&tData)

		}

	}
	return tData
}
func (self *vedbImpl) DescribeLockCurrentWaits(ctx context.Context, req *datasource.DescribeLockCurrentWaitsReq) (*datasource.DescribeLockCurrentWaitsResp, error) {
	waitLocks, err := self.getAllWaitLocks(ctx, req)
	if err != nil {
		return nil, err
	}
	// get details info
	ret := self.filterWaitLockDetails(ctx, waitLocks, req.SearchParam, req.NodeIds)
	log.Info(ctx, "lockWaits list is %s", utils.Show(ret))
	return ret, nil
}
func (self *vedbImpl) getAllWaitLocks(ctx context.Context, req *datasource.DescribeLockCurrentWaitsReq) ([]*datasource.LockCurrentWaitsDetail, error) {
	var waitLockList []*datasource.LockCurrentWaitsDetail
	mp := map[string]string{
		"RTrxStarted":      "r_trx_started",
		"RTrxWaitStarted":  "r_trx_wait_started",
		"RTrxRowsLocked":   "r_trx_rows_locked",
		"RTrxRowsModified": "r_trx_rows_modified",
		"RBlockedWaitSecs": "r_blocked_wait_secs",
	}
	// 获取所有pod节点
	listInstancePodsReq := &datasource.ListInstancePodsReq{
		InstanceId: req.Source.InstanceId,
	}
	listInstancePodsResp, err := self.ListInstancePods(ctx, listInstancePodsReq)
	if err != nil {
		log.Warn(ctx, "get InstancePods fail: %v", err)
		return nil, err
	}
	// 获取所有mysqld节点
	var pods []*shared.KubePod
	for _, pod := range listInstancePodsResp.Data {
		if pod.Component == "dbengine" && pod.Role == model.NodeType_Primary.String() {
			pods = append(pods, pod)
		}
	}
	if len(pods) < 1 {
		log.Warn(ctx, "No mysqld pod found")
		return nil, consts.ErrorWithParam(model.ErrorCode_GetInstanceAddressFailed, "No mysqld pod found")
	}
	whereConditions, args := generateLockWaitSearchWhereConditions(req.SearchParam)
	whereConditions += ` order by ` + mp[req.SortParam] + " " + req.Order
	for _, node := range pods {
		var (
			tempLocks []*datasource.LockCurrentWaitsDetail
			querySql  string
		)
		ndbVersion := DBVersion{}
		versionSql := datasource.DbwDasHint + "select version() as version;"
		maSource := *req.Source
		// 优先使用adminPort
		if node.Containers[0].GetAdminPort() != "" {
			maSource.Address = fmt.Sprintf("%s:%s", node.PodIP, node.Containers[0].GetAdminPort())
		} else {
			maSource.Address = fmt.Sprintf("%s:%s", node.PodIP, node.Containers[0].GetPort())
		}
		maSource.NodeId = node.NodeId
		conn, err := self.getConnV2(ctx, &maSource)
		if err != nil {
			log.Warn(ctx, "connect to datasource %s fail %v", maSource.Address, err)
			if conn != nil {
				self.ConnPool.Put(ctx, conn)
			}
			continue
		}
		if err := conn.Raw(versionSql).Scan(&ndbVersion); err != nil {
			log.Warn(ctx, "get rds version fail %v", err)
			return nil, err
		}
		if strings.Contains(ndbVersion.Version, "5.7") {
			querySql = MySQL57WaitLockQuery + whereConditions + " limit 10000" // ignore_security_alert
		} else {
			log.Info(ctx, "skip 8.0 version lockWait")
			self.ConnPool.Put(ctx, conn)
			return waitLockList, nil
		}
		log.Info(ctx, "exec sql is %s", querySql)
		if err = conn.Raw(querySql, args...).Scan(&tempLocks); err != nil {
			self.ConnPool.Put(ctx, conn)
			continue
		}
		self.ConnPool.Put(ctx, conn)
		// fill NULL column
		fp.StreamOf(tempLocks).Foreach(func(d *datasource.LockCurrentWaitsDetail) {
			d.NodeId = node.NodeId
			d.RTrxWaitStarted = convertTimeStr(d.RTrxWaitStarted)
			d.RTrxStarted = convertTimeStr(d.RTrxStarted)
			d.BTrxStarted = convertTimeStr(d.BTrxStarted)
		}).ToSlice(&tempLocks)
		waitLockList = append(waitLockList, tempLocks...)
	}
	return waitLockList, nil
}
func (self *vedbImpl) DescribeDeadlock(ctx context.Context, req *datasource.DescribeDeadlockReq) (*datasource.DescribeDeadlockResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.DescribeDeadlockResp{}
	/* get engine innodb status */
	sql := "show engine innodb status;"
	var statusinfo StatusInfo
	if err = conn.Raw(datasource.WithHint(sql, datasource.DbwDasHint)).Scan(&statusinfo); err != nil {
		return nil, err
	}
	/* DeadLock Info Str */
	reg := regexp.MustCompile(`LATEST DETECTED DEADLOCK\n------------------------\n([\s\S]*)------------\nTRANSACTIONS\n`)
	if len(reg.FindStringSubmatch(statusinfo.Status)) == 0 {
		log.Info(ctx, "there are no new Deadlock")
		retlog := &shared.DescribeDeadlockInfo{}
		retlog, err = self.getDeadLock(ctx, nil, time.Now().Unix(), req.InstanceId, req.TenantId)
		if err != nil {
			log.Warn(ctx, "get Deadlock info fail:%v", err)
			return nil, err
		}
		ret.DescribeDeadlockInfo = retlog
		return ret, nil
	}

	DLInfoStr := reg.FindStringSubmatch(statusinfo.Status)[len(reg.FindStringSubmatch(statusinfo.Status))-1]
	/* get two time */
	DeadlockCollectionTime := time.Now().Format("2006-01-02 15:04:05")
	reg = regexp.MustCompile(`(\d{4}-\d{2}-\d{2}.{9})`)
	DeadlockTime := reg.FindAllString(DLInfoStr, -1)[0]

	/* get every trx and its lock */
	DeadlockList2 := []*shared.Deadlock{}

	reg = regexp.MustCompile(consts.DeadlokcReg)
	trxs := reg.FindAllString(DLInfoStr, -1)

	for i, trx := range trxs {
		dl := &shared.Deadlock{}
		results := GetRegResults(trx, deadLockRegs)

		dl.TrxInfo = strconv.Itoa(i + 1)
		dl.ProcessId = results[0]
		dl.RelateTable = results[1]
		dl.WaitLock = results[2]
		dl.WaitIndex = results[3]
		dl.WaitLockMode = results[4]
		dl.HoldLock = results[5]
		dl.HoldLockIndex = results[6]
		dl.HoldLockMode = results[7]
		dl.Sql = results[8]
		dl.ReqType = GetSqlType(dl.Sql)

		dl.ReqType = GetSqlType(dl.Sql)
		reg = regexp.MustCompile(`WE (.*) TRANSACTION \((\d)\)`)
		if reg.FindStringSubmatch(DLInfoStr) != nil && len(reg.FindStringSubmatch(DLInfoStr)) == 3 {
			if trxnum := reg.FindStringSubmatch(DLInfoStr)[2]; trxnum == strconv.Itoa(i+1) {
				dl.TrxTreat = reg.FindStringSubmatch(DLInfoStr)[1] + " TRANSACTION " + trxnum
			} else {
				dl.TrxTreat = ""
			}
		} else {
			dl.TrxTreat = ""
		}
		DeadlockList2 = append(DeadlockList2, dl)
	}
	DeadlockInfo2 := &shared.DeadlockInfo{
		DeadlockCollectionTime: DeadlockCollectionTime,
		DeadlockTime:           DeadlockTime,
		DeadlockList:           DeadlockList2,
	}
	retlog := &shared.DescribeDeadlockInfo{}
	retlog, err = self.getDeadLock(ctx, DeadlockInfo2, time.Now().Unix(), req.InstanceId, req.TenantId)
	if err != nil {
		log.Warn(ctx, "get Deadlock info fail:%v", err)
		return nil, err
	}
	ret.DescribeDeadlockInfo = retlog
	return ret, nil
}

func (self *vedbImpl) DescribeDeadlockDetect(ctx context.Context, req *datasource.DescribeDeadlockDetectReq) (*datasource.DescribeDeadlockDetectResp, error) {
	conn, err := self.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	defer conn.Close()
	ret := &datasource.DescribeDeadlockDetectResp{}
	type T struct {
		OnOff int `gorm:"column:@@innodb_deadlock_detect"`
	}
	var t T
	sql := "select @@innodb_deadlock_detect;"
	if err = conn.Raw(datasource.WithHint(sql, datasource.DbwDasHint)).Scan(&t); err != nil {
		log.Warn(ctx, "get Deadlock Detect fail %v", err)
		return nil, err
	}
	ret.DescribeDeadlockDetectInfo = &shared.DescribeDeadlockDetectInfo{
		OnOff: t.OnOff == 1,
	}
	return ret, nil
}

func (self *vedbImpl) getDeadLock(ctx context.Context, DeadlockInfo *shared.DeadlockInfo, timestamp int64, InstanceId string, TenantId string) (*shared.DescribeDeadlockInfo, error) {
	/* get tls client */
	var topicId string
	cnf := self.cnf.Get(ctx)
	c3cfg := self.C3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	Ak := c3cfg.TLSServiceAccessKey
	Sk := c3cfg.TLSServiceSecretKey
	//regionId := self.L.RegionID()
	//tlsEndpoint := fmt.Sprintf(consts.TLSEndpointTemplate, regionId)
	regionId := cnf.TlsZone
	tlsEndpoint := cnf.TlsServiceEndpoint
	tlsclient := tls.NewClient(tlsEndpoint, Ak, Sk, "", regionId)

	/* get data from tls */
	EndTime := time.Now().Unix()
	StartTime := EndTime - 86400
	if cnf.EnableNewVersionDeadLockTopic {
		tlsDeadlockTopic := c3cfg.TLSDeadLockTopicV2
		topicSet := &datasource.TLSDeadlockTopic{}
		if err := json.Unmarshal([]byte(tlsDeadlockTopic), topicSet); err != nil {
			log.Warn(ctx, " tlsDeadlockTopic unmarshal failed %v", err)
		}
		for _, topic := range topicSet.Topics {
			if topic.InstanceType == model.DSType_VeDBMySQL.String() {
				topicId = topic.TopicID
			}
		}
	}

	retLog, err := tlsclient.SearchLogsV2(&tls.SearchLogsRequest{
		TopicID:   topicId,
		Query:     fmt.Sprintf("* | select DeadlockTime,  content where InstanceId='%s' and DeadlockCollectionTime>%s order by DeadlockCollectionTime desc limit 1", InstanceId, strconv.FormatInt(timestamp-86400, 10)),
		StartTime: StartTime,
		EndTime:   EndTime,
		Limit:     1000,
		HighLight: false,
		Context:   "",
		Sort:      "",
	})
	if err != nil {
		log.Warn(ctx, "SearchLogs fail:%v", err)
		return nil, err
	}

	/* add data to ret */
	ret := &shared.DescribeDeadlockInfo{}
	DeadlockInfoList2 := []*shared.DeadlockInfo{}

	if retLog != nil && len(retLog.AnalysisResult.Data) != 0 {
		tempmp := map[string]string{}
		for i := 0; i < len(retLog.AnalysisResult.Data); i++ {
			if _, ok := tempmp[retLog.AnalysisResult.Data[i]["DeadlockTime"].(string)]; !ok {
				tempmp[retLog.AnalysisResult.Data[i]["DeadlockTime"].(string)] = "ok"
				DeadlockInfo2 := &shared.DeadlockInfo{}
				err = json.Unmarshal([]byte(retLog.AnalysisResult.Data[i]["content"].(string)), &DeadlockInfo2)
				if err != nil {
					log.Warn(ctx, "Unmarshal fail:%v", err)
					return nil, err
				}
				DeadlockInfoList2 = append(DeadlockInfoList2, DeadlockInfo2)
			}
		}
		if DeadlockInfo != nil {
			if _, ok := tempmp[DeadlockInfo.DeadlockTime]; !ok {
				DeadlockInfoList2 = append(DeadlockInfoList2, DeadlockInfo)
			}
		}
		ret.DeadlockInfoList = DeadlockInfoList2
	} else {
		ret.DeadlockInfoList = DeadlockInfoList2
	}

	/* put log to tls */
	if DeadlockInfo != nil {
		js, _ := json.Marshal(DeadlockInfo)
		log.Info(ctx, " tls")
		_, err = tlsclient.PutLogs(&tls.PutLogsRequest{
			TopicID:      topicId,
			HashKey:      "",
			CompressType: "lz4",
			LogBody: &pb.LogGroupList{
				LogGroups: []*pb.LogGroup{
					{
						Logs: []*pb.Log{
							{
								Contents: []*pb.LogContent{
									{
										Key:   "InstanceId",
										Value: InstanceId,
									},
									{
										Key:   "DeadlockCollectionTime",
										Value: strconv.FormatInt(timestamp, 10),
									},
									{
										Key:   "DeadlockTime",
										Value: DeadlockInfo.DeadlockTime,
									},
									{
										Key:   "content",
										Value: string(js),
									},
									{
										Key:   "TenantId",
										Value: TenantId,
									},
								},
							},
						},
						Source:      "",
						LogTags:     nil,
						FileName:    "",
						ContextFlow: "",
					},
				},
			},
		})
		if err != nil {
			log.Warn(ctx, "put log to tls fail: %s", err)
			return nil, err
		}
	}
	return ret, nil
}

func (self *vedbImpl) describeLock(ctx context.Context, conn datasource.Conn) ([]*shared.Lock, error) {
	ret := make([]*shared.Lock, 0)
	/* get mysql version info */
	var versionSql = "/*+ DBW DAS DEFAULT*/ select version() as version;"
	var MysqlVersion = DBVersion{}
	if err := conn.Raw(versionSql).Scan(&MysqlVersion); err != nil {
		log.Warn(ctx, "get mysql version fail %v", err)
		return nil, err
	}
	//log.Info(ctx, "mysql version is %v", MysqlVersion.Version)
	/* version = 5.7x */
	if strings.Contains(MysqlVersion.Version, "5.7") {
		var Locklist []Lock57
		if err := conn.Raw(LockQuery57).Scan(&Locklist); err != nil {
			return nil, err
		}
		LockList2 := make([]*shared.Lock, 0)
		for _, L := range Locklist {
			var lockProperty string
			var Numlockwait int64
			sql1 := fmt.Sprintf("/*+ DBW DAS DEFAULT*/ select count(1) from information_schema.INNODB_LOCK_WAITS where requested_lock_id ='%s';", L.LockId)
			if err := conn.Raw(sql1).Scan(&Numlockwait); err != nil {
				return nil, err
			}
			if Numlockwait == 0 {
				lockProperty = model.Lockstatus_LockHold.String()
			} else {
				lockProperty = model.Lockstatus_LockWait.String()
			}
			LockList2 = append(LockList2, &shared.Lock{
				LockProperty:       lockProperty,
				LockId:             L.LockId,
				LockAssociateIndex: L.LockAssociateIndex,
				LockAssociateTable: L.LockAssociateTable,
				LockType:           L.LockType,
				LockModel:          L.LockModel,
				TrxId:              L.TrxId,
			})
		}
		ret = LockList2
	}

	/* version = 8.0x */
	if strings.Contains(MysqlVersion.Version, "8.0") {
		var Locklist []Lock80
		if err := conn.Raw(LockQuery80).Scan(&Locklist); err != nil {
			return nil, err
		}
		LockList2 := make([]*shared.Lock, 0)
		for i := 0; i < len(Locklist); i++ {
			if i == len(Locklist)-1 || (Locklist[i].LockBegin != Locklist[i+1].LockBegin) {
				LockList2 = append(LockList2, &shared.Lock{
					LockProperty:       Locklist[i].LockProperty,
					LockId:             Locklist[i].LockId,
					LockAssociateIndex: Locklist[i].LockAssociateIndex,
					LockAssociateTable: Locklist[i].LockAssociateTable,
					LockType:           Locklist[i].LockType,
					LockModel:          Locklist[i].LockModel,
					TrxId:              Locklist[i].TrxId,
				})
			}
		}
		ret = LockList2
	}
	return ret, nil
}

func (self *vedbImpl) fillNodeAddress(ctx context.Context, ds *shared.DataSource) error {
	pods, err := self.ListInstancePods(ctx, &datasource.ListInstancePodsReq{
		InstanceId: ds.InstanceId,
		Type:       self.Type(),
	})
	if err != nil {
		return err
	}
	for _, pod := range pods.Data {
		if pod.Name == ds.NodeId {
			for _, container := range pod.Containers {
				if container.Name == "ndb-dbengine" {
					ds.Address = fmt.Sprintf("%s:%s", pod.PodIP, container.Port)
					return nil
				}
			}
		}
	}
	return errors.New("empty address")
}

func (self *vedbImpl) ListInstancePods(ctx context.Context, req *datasource.ListInstancePodsReq) (*datasource.ListInstancePodsResp, error) {
	rreq := &vdbModel.ListInstancePodsReq{
		InstanceId: conv.StringPtr(req.InstanceId),
	}
	rresp := &vdbModel.ListInstancePodsResp{}
	log.Info(ctx, "veDB instanceId:%s listInstancePodsReq:%s", req.InstanceId, rreq.String())
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_ListInstancePods.String(), rreq, rresp); err != nil {
		log.Warn(ctx, "ListInstancePods failed instanceId=%s, err=%v", req.InstanceId, err.Error())
		return nil, err
	}
	ret := &datasource.ListInstancePodsResp{
		Total: rresp.Total,
	}
	if err := fp.StreamOf(rresp.Datas).Map(func(db *vdbModel.KubePod) *shared.KubePod {
		var (
			convertedRole string
			containerList []*shared.KubeContainer
		)
		if db.Labels["role"] == "leader" {
			convertedRole = model.NodeType_Primary.String()
		} else if db.Labels["role"] == "follower" {
			convertedRole = model.NodeType_Secondary.String()
		} else {
			convertedRole = ""
		}
		for _, item := range db.Containers {
			if item.Name == "ndb-dbengine" || item.Name == "proxy" {
				var (
					port      string
					adminPort string
				)
				containerItem := &shared.KubeContainer{
					Name: item.Name,
					Cpu:  item.Cpu,
					Mem:  item.Mem,
				}
				if item.Name == "ndb-dbengine" {
					port = fmt.Sprintf("%d", item.Ports["dbengine-port"])
					adminPort = fmt.Sprintf("%d", item.Ports["admin-port"])
				} else {
					port = strings.Join(VedbProxyPortsList, ",")
				}
				containerItem.Port = port
				containerItem.AdminPort = adminPort
				containerList = append(containerList, containerItem)
			}
		}
		// 兼容ipv6
		ip := datasource.ConvertIPV6(db.PodIP)
		return &shared.KubePod{
			Name:        db.Name,
			Zone:        db.Zone,
			KubeCluster: db.KubeCluster,
			Region:      db.Region,
			NodeIP:      db.NodeIP,
			PodIP:       ip,
			NodePool:    db.NodePool,
			Role:        convertedRole,
			Component:   db.Labels["component"],
			NodeId:      db.Name,
			Containers:  containerList,
		}
	}).ToSlice(&ret.Data); err != nil {
		return nil, err
	}
	return ret, nil
}

func (self *vedbImpl) DescribeInstancePodAddress(ctx context.Context, req *datasource.DescribeInstanceAddressReq) (*datasource.DescribeInstanceAddressResp, error) {
	listInstancePodsReq := &datasource.ListInstancePodsReq{
		Type:       shared.VeDBMySQL,
		LinkType:   shared.Volc,
		InstanceId: req.InstanceId,
	}
	listInstancePodsResp, err := self.ListInstancePods(ctx, listInstancePodsReq)
	if err != nil {
		log.Warn(ctx, "Get instance %s pods failed: %v", req.InstanceId, err)
		return nil, err
	}
	log.Info(ctx, "DescribeInstanceAddress call ListInstancePods result is %s", utils.Show(listInstancePodsResp))

	leaderZoneId, err := self.getPrimerZoneId(ctx, req.InstanceId)
	if err != nil {
		log.Warn(ctx, "get vedb primer zone id fail %v", err)
		return nil, err
	}

	//获取实例ip port
	ret := &datasource.DescribeInstanceAddressResp{}
	for _, pod := range listInstancePodsResp.Data {
		if pod.Role == req.NodeType && pod.Zone == leaderZoneId {
			ret.IP = pod.PodIP
			for _, container := range pod.Containers {
				if container.Name == "ndb-dbengine" {
					ret.Port = utils.MustStrToInt32(container.Port)
				}
			}
		}
	}
	return ret, nil
}

func (self *vedbImpl) getPrimerZoneId(ctx context.Context, instanceId string) (string, error) {
	rreq := &vdbModel.DescribeDBInstanceDetailReq{
		InstanceId: utils.StringRef(instanceId),
	}
	resp := &vdbModel.DescribeDBInstanceDetailResp{}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDBInstanceDetail.String(), rreq, resp); err != nil {
		log.Warn(ctx, "get vedb instance detailed fail %v", err)
		return "", err
	}
	if resp.InstanceDetail == nil {
		log.Warn(ctx, "get vedb instance detailed detal is null")
		return "", nil
	}

	for _, structure := range resp.InstanceDetail.InstanceStructures {
		if structure != nil && strings.ToLower(structure.SubInstanceType) == "primary" {
			return structure.ZoneIds, nil
		}
	}
	log.Warn(ctx, "there is no primary zone id")
	return "", nil
}
func (self *vedbImpl) DescribeInstanceAddress(ctx context.Context, req *datasource.DescribeInstanceAddressReq) (*datasource.DescribeInstanceAddressResp, error) {
	rreq := &vdbModel.DescribeMasterDBEngineConnectionReq{
		InstanceId: conv.StringPtr(req.InstanceId),
	}
	rresp := &vdbModel.DescribeMasterDBEngineConnectionResp{}
	log.Info(ctx, "veDB instanceId:%s DescribeMasterDBEngineConnectionReq:%s", req.InstanceId, rreq.String())
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeMasterDBEngineConnection.String(), rreq, rresp); err != nil {
		log.Warn(ctx, "DescribeInstanceAddress failed instanceId=%s, err=%v", req.InstanceId, err.Error())
		return nil, err
	}
	ret := &datasource.DescribeInstanceAddressResp{
		IP:   rresp.DBEngineDomain,
		Port: rresp.DBEnginePort,
	}
	return ret, nil
}

func (self *vedbImpl) DescribeInstanceProxyAddress(ctx context.Context, req *datasource.DescribeInstanceAddressReq) (*datasource.DescribeInstanceAddressResp, error) {
	rreq := &vdbModel.DescribeDBInstanceConnectionReq{
		InstanceId: req.InstanceId,
	}
	rresp := &vdbModel.DescribeDBInstanceConnectionResp{}
	log.Info(ctx, "veDB instanceId:%s DescribeDBInstanceConnectionReq:%s", req.InstanceId, rreq.String())

	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeInstanceProxyInnerConnection.String(), req, rresp); err != nil {
		log.Warn(ctx, "get vedb %s connection fail %v", req.InstanceId, err)
		return nil, consts.ErrorOf(model.ErrorCode_InstanceStatusNotSatisfy)
	}
	ret := &datasource.DescribeInstanceAddressResp{
		IP:   rresp.GetStorageInnerDomain(),
		Port: utils.MustStrToInt32(rresp.GetStorageInnerPort()),
	}
	return ret, nil
}

func (self *vedbImpl) DescribeInstanceAddressList(ctx context.Context, req *datasource.DescribeInstanceAddressReq) ([]*datasource.DescribeInstanceAddressResp, error) {
	listInstancePodsReq := &datasource.ListInstancePodsReq{
		Type:       shared.VeDBMySQL,
		LinkType:   shared.Volc,
		InstanceId: req.InstanceId,
	}
	listInstancePodsResp, err := self.ListInstancePods(ctx, listInstancePodsReq)
	if err != nil {
		log.Warn(ctx, "Get instance %s pods failed: %v", req.InstanceId, err)
		return nil, err
	}
	log.Info(ctx, "DescribeInstanceAddressList call ListInstancePods result is %s", utils.Show(listInstancePodsResp))
	//获取实例ip port
	var addressList []*datasource.DescribeInstanceAddressResp
	for _, pod := range listInstancePodsResp.Data {
		if pod.Role == model.NodeType_Primary.String() || pod.Role == model.NodeType_Secondary.String() {
			for _, container := range pod.Containers {
				if container.Name == "ndb-dbengine" {
					ret := &datasource.DescribeInstanceAddressResp{}
					ret.Port = utils.MustStrToInt32(container.Port)
					ret.IP = pod.PodIP
					ret.NodeId = pod.NodeId
					addressList = append(addressList, ret)
				}
			}
		}

	}
	return addressList, nil
}

func (self *vedbImpl) DescribeDBInstanceSSL(ctx context.Context, req *datasource.DescribeDBInstanceSSLReq) (*datasource.DescribeDBInstanceSSLResp, error) {
	return &datasource.DescribeDBInstanceSSLResp{
		SSLEnable: false,
	}, nil
}

func (self *vedbImpl) IsMyOwnInstance(ctx context.Context, instanceId string, _ shared.DataSourceType) bool {
	if fwctx.GetTenantID(ctx) == "1" || fwctx.GetTenantID(ctx) == "0" {
		return true
	}
	inst, _ := self.dbwInstanceDal.Get(ctx, instanceId, model.InstanceType_VeDBMySQL.String(), "", "", "")
	if inst != nil {
		if inst.TenantId != fwctx.GetTenantID(ctx) {
			return false
		} else {
			return true
		}
	}
	rreq := &vdbModel.DescribeDBInstanceDetailReq{
		InstanceId: utils.StringRef(instanceId),
	}
	resp := &vdbModel.DescribeDBInstanceDetailResp{}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDBInstanceDetail.String(), rreq, resp); err != nil {
		log.Warn(ctx, "get vedb instance detailed fail %v", err)
		return false
	}
	return true
}
func (self *vedbImpl) CheckInstanceState(ctx context.Context, instanceId string, ds shared.DataSourceType, isConnectedInstance bool) error {
	var (
		dbInstanceStatusBlackList map[string]string
		blackList                 []string
		rawBlackList              string
	)
	rreq := &vdbModel.DescribeDBInstanceDetailReq{
		InstanceId: utils.StringRef(instanceId),
	}
	resp := &vdbModel.DescribeDBInstanceDetailResp{}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDBInstanceDetail.String(), rreq, resp); err != nil {
		log.Warn(ctx, "get vedb instance detailed fail %v", err)
		return err
	}
	cfg := self.cnf.Get(ctx)
	if isConnectedInstance {
		rawBlackList = cfg.DBInstanceStateWithConnectionBlackList
	} else {
		rawBlackList = cfg.DBInstanceStateWithoutConnectionBlackList
	}
	err := json.Unmarshal([]byte(rawBlackList), &dbInstanceStatusBlackList)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	} else {
		blackList = strings.Split(dbInstanceStatusBlackList[ds.String()], ",")
	}
	currentStatus := resp.InstanceDetail.InstanceStatus
	for _, item := range blackList {
		if item == currentStatus {
			log.Warn(ctx, "instance status is %s, not support", currentStatus)
			return consts.ErrorWithParam(model.ErrorCode_InstanceNotInRunningStatus, currentStatus)
		}
	}
	return nil
}
func (self *vedbImpl) DescribeDBInstanceDetail(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (detailResp *datasource.DescribeDBInstanceDetailResp, err error) {
	detailResp = &datasource.DescribeDBInstanceDetailResp{
		InstanceId: req.InstanceId,
	}
	vreq := &vdbModel.DescribeDBInstanceDetailReq{
		InstanceId: utils.StringRef(req.InstanceId),
	}
	vresp := &vdbModel.DescribeDBInstanceDetailResp{}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDBInstanceDetail.String(), vreq, vresp); err != nil {
		log.Warn(ctx, "vedbImpl.DescribeDBInstanceDetail: %v", err)
		return nil, err
	}
	if IsVeDBInstanceDelete(vresp.InstanceDetail.GetInstanceStatus()) {
		log.Info(ctx, "vedb is delete, instance_status:%s", detailResp.InstanceStatus)
		return nil, errors.New("InvalidInstanceId.NotFound")
	}
	var node int32
	for _, val := range vresp.InstanceDetail.InstanceStructures {
		// 只考虑主AZ,热备节点不考虑
		if val.SubInstanceType == vdbModel.NodeType_Primary.String() {
			node = int32(len(val.Nodes))
			break
		}
	}
	detailResp.InstanceName = vresp.InstanceDetail.GetInstanceName()
	detailResp.InstanceStatus = vresp.InstanceDetail.GetInstanceStatus()
	detailResp.RegionId = vresp.InstanceDetail.GetRegionId()
	detailResp.ZoneId = strings.Split(vresp.InstanceDetail.GetZoneIds(), ";")[0]
	detailResp.DBEngineVersion = vresp.InstanceDetail.GetDBEngineVersion()
	if len(vresp.Nodes) <= 0 {
		return nil, errors.New("InvalidInstance.NoNodes")
	}
	detailResp.VCPU = vresp.Nodes[0].GetVCPU()
	detailResp.Memory = vresp.Nodes[0].GetMemory()
	detailResp.ProjectName = vresp.InstanceDetail.ProjectName
	detailResp.SpecFamily = vresp.InstanceDetail.SpecFamily
	detailResp.ChargeType = vresp.ChargeDetail.ChargeType
	detailResp.NodeNumber = node
	if vresp.InstanceDetail.IsSetMaintenanceWindow() {
		detailResp.DescribeMaintenanceWindow = &datasource.MaintenanceWindow{}
		detailResp.DescribeMaintenanceWindow.MaintenanceTime = vresp.InstanceDetail.MaintenanceWindow.MaintenanceTime
		switch vresp.InstanceDetail.MaintenanceWindow.DayKind {
		case vdbModel.DayKind_Month:
			detailResp.DescribeMaintenanceWindow.DayKind = datasource.DayKind_Month
			detailResp.DescribeMaintenanceWindow.DayOfMonth = vresp.InstanceDetail.MaintenanceWindow.DayOfMonth
		case vdbModel.DayKind_Week:
			detailResp.DescribeMaintenanceWindow.DayKind = datasource.DayKind_Week
			for _, week := range vresp.InstanceDetail.MaintenanceWindow.DayOfWeek {
				switch week {
				case vdbModel.DayOfWeek_Monday:
					detailResp.DescribeMaintenanceWindow.DayOfWeek = append(detailResp.DescribeMaintenanceWindow.DayOfWeek, datasource.DayOfWeek_Monday)
				case vdbModel.DayOfWeek_Tuesday:
					detailResp.DescribeMaintenanceWindow.DayOfWeek = append(detailResp.DescribeMaintenanceWindow.DayOfWeek, datasource.DayOfWeek_Tuesday)
				case vdbModel.DayOfWeek_Wednesday:
					detailResp.DescribeMaintenanceWindow.DayOfWeek = append(detailResp.DescribeMaintenanceWindow.DayOfWeek, datasource.DayOfWeek_Wednesday)
				case vdbModel.DayOfWeek_Thursday:
					detailResp.DescribeMaintenanceWindow.DayOfWeek = append(detailResp.DescribeMaintenanceWindow.DayOfWeek, datasource.DayOfWeek_Thursday)
				case vdbModel.DayOfWeek_Friday:
					detailResp.DescribeMaintenanceWindow.DayOfWeek = append(detailResp.DescribeMaintenanceWindow.DayOfWeek, datasource.DayOfWeek_Friday)
				case vdbModel.DayOfWeek_Saturday:
					detailResp.DescribeMaintenanceWindow.DayOfWeek = append(detailResp.DescribeMaintenanceWindow.DayOfWeek, datasource.DayOfWeek_Saturday)
				case vdbModel.DayOfWeek_Sunday:
					detailResp.DescribeMaintenanceWindow.DayOfWeek = append(detailResp.DescribeMaintenanceWindow.DayOfWeek, datasource.DayOfWeek_Sunday)
				}
			}
		}
	}
	return detailResp, nil
}

func (self *vedbImpl) DescribeDBInstanceEndpoints(ctx context.Context, req *datasource.DescribeDBInstanceEndpointsReq) (*datasource.DescribeDBInstanceEndpointsResp, error) {
	var endpointList []*datasource.EndpointInfo
	detailReq := &vdbModel.DescribeDBInstanceDetailReq{
		InstanceId: utils.StringRef(req.InstanceId),
	}
	detailResp := &vdbModel.DescribeDBInstanceDetailResp{}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDBInstanceDetail.String(), detailReq, detailResp); err != nil {
		log.Warn(ctx, "vedbImpl.DescribeDBInstanceDetail: %v", err)
		return nil, err
	}
	if err := fp.StreamOf(detailResp.Endpoints).Map(func(i *vdbModel.EndpointObject) *datasource.EndpointInfo {
		//TODO 需要增加endpoint port返回
		endpoint := &datasource.EndpointInfo{
			EndpointName:  i.EndpointName,
			EndpointID:    i.EndpointId,
			EndpointType:  i.EndpointType,
			ReadWriteMode: i.GetReadWriteMode(),
			NodeID:        i.NodeIds,
		}
		return endpoint
	}).ToSlice(&endpointList); err != nil {
		return nil, err
	}
	ret := &datasource.DescribeDBInstanceEndpointsResp{
		Endpoints: endpointList,
	}
	return ret, nil
}

func (self *vedbImpl) DescribeDBInstanceCluster(ctx context.Context, req *datasource.DescribeDBInstanceClusterReq) (*datasource.DescribeDBInstanceClusterResp, error) {
	rreq := &vdbModel.ListInstancePodsReq{
		InstanceId: conv.StringPtr(req.InstanceId),
	}
	rresp := &vdbModel.ListInstancePodsResp{}
	log.Info(ctx, "veDB instanceId:%s listInstancePodsReq:%s", req.InstanceId, rreq.String())
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_ListInstancePods.String(), rreq, rresp); err != nil {
		log.Warn(ctx, "ListInstancePods failed instanceId=%s, err=%v", req.InstanceId, err.Error())
		return nil, err
	}
	var (
		kubeClusters = make(map[string]string)
	)
	for _, data := range rresp.Datas {
		if data.Labels["component"] != "proxy" {
			continue
		}
		kubeClusters[data.KubeCluster] = data.KubeCluster
	}

	return &datasource.DescribeDBInstanceClusterResp{
		MultiAZ:      len(kubeClusters) > 1,
		AzClusterMap: kubeClusters,
	}, nil
}

func (self *vedbImpl) DescribeDBInstanceAuditCollectedPod(ctx context.Context, req *datasource.DescribeDBInstanceAuditCollectedPodReq) (*datasource.DescribeDBInstanceAuditCollectedPodResp, error) {
	rreq := &vdbModel.ListInstancePodsReq{
		InstanceId: conv.StringPtr(req.InstanceId),
	}
	rresp := &vdbModel.ListInstancePodsResp{}
	log.Info(ctx, "veDB instanceId:%s listInstancePodsReq:%s", req.InstanceId, rreq.String())
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_ListInstancePods.String(), rreq, rresp); err != nil {
		log.Warn(ctx, "ListInstancePods failed instanceId=%s, err=%v", req.InstanceId, err.Error())
		return nil, err
	}
	var (
		ports      []string
		portmap    = make(map[string]bool)
		cpuRequest string
	)
	for _, data := range rresp.Datas {
		if data.Labels["component"] != "proxy" {
			continue
		}
		for _, container := range data.Containers {
			if container.Name != "proxy" {
				continue
			}

			cpuRequest = container.Cpu
			for k, v := range container.Ports {
				if k == "ndb-proxy-api" || k == "" {
					continue
				}
				portmap[strconv.FormatInt(int64(v), 10)] = true
			}
		}
	}
	for k, _ := range portmap {
		if k != "" {
			ports = append(ports, k)
		}
	}
	return &datasource.DescribeDBInstanceAuditCollectedPodResp{
		Port:       ports,
		CpuRequest: cpuRequest,
	}, nil
}

func IsVeDBInstanceDelete(InstanceStatus string) bool {
	return InstanceStatus == model.VeDBMySQLInstanceStatus_Deleted.String() ||
		InstanceStatus == model.VeDBMySQLInstanceStatus_Deleting.String() ||
		InstanceStatus == model.VeDBMySQLInstanceStatus_CreateFailed.String()
}

func (self *vedbImpl) DescribeDBProxyConfig(ctx context.Context, req *datasource.DescribeDBProxyConfigReq) (*datasource.DescribeDBProxyConfigResp, error) {
	return &datasource.DescribeDBProxyConfigResp{
		IsProxyEnable: true,
	}, nil
}

func (self *vedbImpl) DescribeInstanceVersion(ctx context.Context, req *datasource.DescribeInstanceVersionReq) (*datasource.DescribeInstanceVersionResp, error) {
	describeInstanceVersionReq := &vdbModel.DescribeDBInstanceVersionReq{
		InstanceId: req.InstanceId,
	}
	resp := &vdbModel.DescribeDBInstanceVersionResp{}
	err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDBInstanceVersion.String(), describeInstanceVersionReq, resp)
	if err != nil {
		log.Warn(ctx, "failed to call veDB InnerDescribeInstanceVersion, err=%+v", err)
		return nil, err
	}
	return &datasource.DescribeInstanceVersionResp{
		Version:           resp.DBMinorVersion,
		DBRevisionVersion: resp.DBRevisionVersion,
	}, nil
}

func (m *vedbImpl) DescribeInstanceVariables(ctx context.Context, req *datasource.DescribeInstanceVariablesReq) (
	*datasource.DescribeInstanceVariablesResp, error) {
	conn, err := m.getConn(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "SQLAdvisor get conn error:%s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_ConnectionFailed)
	}
	defer conn.Close()

	// 循环变量取值
	variables := make(map[string]string)
	for _, param := range req.Variables {
		var res = &VariableValue{}
		if err = conn.Raw(fmt.Sprintf(" %s SHOW VARIABLES LIKE \"%s\"", DBW_CONSOLE_DEFAULT_HINT, param)).Scan(res); err != nil {
			log.Warn(ctx, "SQLAdvisor get variable %s error:%s", param, err.Error())
		}
		log.Info(ctx, "SQLAdvisor get variable %v", res)
		variables[param] = res.Value
	}
	return &datasource.DescribeInstanceVariablesResp{
		Variables: variables,
	}, nil
}

func (m *vedbImpl) DescribeSQLAdvisorTableMeta(ctx context.Context, req *datasource.DescribeSQLAdvisorTableMetaReq) (
	ret *datasource.DescribeSQLAdvisorTableMetaResp, err error) {
	ret = &datasource.DescribeSQLAdvisorTableMetaResp{
		Success: true,
	}
	err = m.ConnPool.Call(
		ctx, &datasource.GetConnReq{Ds: req.Source, Admin: true}, func(conn datasource.Conn) error {
			for _, tbl := range req.TableList {
				var res = &datasource.SQLAdvisorTableMetaData{}
				tblName := fmt.Sprintf("%s.%s", req.DBName, tbl)
				if err = conn.Raw(
					fmt.Sprintf(
						"%s select * from information_schema.tables where table_schema='%s' and table_name='%s'",
						DBW_CONSOLE_DEFAULT_HINT, req.DBName, tbl,
					),
				).Scan(&res.TableInfo); err != nil {
					log.Warn(ctx, "SQLAdvisor get information_schema.tables %s error:%s", tbl, err.Error())
					continue
				}
				if err = conn.Raw(
					fmt.Sprintf(
						"%s select * from information_schema.columns  where table_schema='%s' and table_name='%s'",
						DBW_CONSOLE_DEFAULT_HINT, req.DBName, tbl,
					),
				).Scan(&res.ColumnInfo); err != nil {
					log.Warn(ctx, "SQLAdvisor get mysql.innodb_table_stats %s error:%s", tbl, err.Error())
					continue
				}
				if err = conn.Raw(
					fmt.Sprintf(
						"%s select * from information_schema.statistics where table_schema='%s' and table_name='%s'",
						DBW_CONSOLE_DEFAULT_HINT, req.DBName, tbl,
					),
				).Scan(&res.StatisticsInfo); err != nil {
					log.Warn(ctx, "SQLAdvisor get information_schema.statistics %s error:%s", tbl, err.Error())
					continue
				}
				if err = conn.Raw(
					fmt.Sprintf(
						"%s select * from mysql.innodb_table_stats where database_name='%s' and table_name='%s'",
						DBW_CONSOLE_DEFAULT_HINT, req.DBName, tbl,
					),
				).Scan(&res.InnodbTableStatsInfo); err != nil {
					log.Warn(ctx, "SQLAdvisor get mysql.innodb_table_stats %s error:%s", tbl, err.Error())
					continue
				}

				if err = conn.Raw(
					fmt.Sprintf(
						"%s SHOW CREATE TABLE %s", DBW_CONSOLE_DEFAULT_HINT, tblName,
					),
				).Scan(&res.CreateTableInfo); err != nil {
					log.Warn(ctx, "SQLAdvisor get create table info %s error:%s", tblName, err.Error())
					continue
				}
				log.Info(ctx, "DescribeSQLAdvisorTableMeta res is %v", res)
				res.Name = tbl
				ret.Data = append(ret.Data, res)
			}
			return nil
		},
	)
	if err != nil {
		log.Warn(ctx, "SQLAdvisor get conn error:%s", err.Error())
		return
	}
	return
}

func (m *vedbImpl) DescribePrimaryKeyRange(ctx context.Context, req *datasource.DescribePrimaryKeyRangeReq) (
	ret *datasource.DescribePrimaryKeyRangeResp, err error) {
	ret = &datasource.DescribePrimaryKeyRangeResp{}
	err = m.ConnPool.Call(
		ctx, &datasource.GetConnReq{Ds: req.Source, Admin: true}, func(conn datasource.Conn) error {
			minSQL := getMinPrimaryKeySQL(req.Columns, req.DBName, req.TableName)
			maxSQL := getMaxPrimaryKeySQL(req.Columns, req.DBName, req.TableName)
			log.Info(ctx, "SQLAdvisor minSQL is %s,maxSQL is %s", minSQL, maxSQL)
			// TODO 这块可能需要自己实现一个方法来进行查询，Scan的方法无法处理动态的字符串，可以参考GetIndexValue 获取主键或者非空唯一索引的最大值和最小值
			//var minPk []string
			//if err = conn.Raw(minSQL).Scan(&minPk); err != nil {
			//	log.Warn(ctx, "SQLAdvisor get minSQL %s error:%s", minSQL, err.Error())
			//	return nil, err
			//}
			//
			//var maxPk []string
			//if err = conn.Raw(maxSQL).Scan(&maxPk); err != nil {
			//	log.Warn(ctx, "SQLAdvisor get maxSQL %s error:%s", minSQL, err.Error())
			//	return nil, err
			//}
			var colLength = int32(len(req.Columns))
			minSQLResult, err := getSQLResult(conn, minSQL, colLength, req.Columns)
			if err != nil {
				return err
			}
			maxSQLResult, err := getSQLResult(conn, maxSQL, colLength, req.Columns)
			if err != nil {
				return err
			}
			ret.PrimaryKeyInfo = &datasource.PrimaryKeyInfo{}
			for _, val := range req.Columns {
				ret.PrimaryKeyInfo.MinNum = append(ret.PrimaryKeyInfo.MinNum, &datasource.PrimaryKeyValue{
					ColumnName: val,
					Value:      minSQLResult[val],
				})
				ret.PrimaryKeyInfo.MaxNum = append(ret.PrimaryKeyInfo.MaxNum, &datasource.PrimaryKeyValue{
					ColumnName: val,
					Value:      maxSQLResult[val],
				})
			}
			return nil
		},
	)
	if err != nil {
		log.Warn(ctx, "SQLAdvisor get conn error:%s", err.Error())
		return
	}
	return
}

func (m *vedbImpl) DescribeSampleData(ctx context.Context, req *datasource.DescribeSampleDataReq) (
	ret *datasource.DescribeSampleDataResp, err error) {
	ret = &datasource.DescribeSampleDataResp{}
	// 这里连接数据库执行命令
	err = m.ConnPool.Call(
		ctx, &datasource.GetConnReq{Ds: req.Source, Admin: true}, func(conn datasource.Conn) error {
			var (
				minCondition = getMinColumnCondition(req.MinNum)
				maxCondition = getMaxColumnCondition(req.MaxNum)
				tblName      = fmt.Sprintf("%s.%s", req.DbName, req.TableName)
				columns      = strings.Join(req.Columns, ",")
				colLength    = len(req.Columns)
				orderBy      = getOrderByCondition(req.PrimaryKey, req.OrderBy.String())
			)
			var command = fmt.Sprintf("%s select %s from %s where (%s) and (%s) order by %s limit %d ",
				DBW_CONSOLE_DEFAULT_HINT, columns, tblName, minCondition, maxCondition, orderBy, req.Limit)
			cursor, err := conn.Rows(command)
			if err != nil {
				log.Warn(ctx, "get definition error %s", err)
				return err
			}
			values := make([]sql.RawBytes, colLength)
			scanArgs := make([]interface{}, colLength)
			for i := range values {
				scanArgs[i] = &values[i]
			}

			cols, err := cursor.Columns()
			if err != nil {
				log.Warn(ctx, "get columns error:%s", err.Error())
				return err
			}
			log.Info(ctx, "command is %s,columns is %s , length is %d", command, cols, len(cols))

			for cursor.Next() {
				var item = map[string]string{}
				if err = cursor.Scan(scanArgs...); err != nil {
					log.Warn(ctx, "sql brain sample data scan error:%s", err.Error())
					return err
				}
				for idx, col := range values {
					if col == nil {
						item[req.Columns[idx]] = "NULL"
					} else {
						item[req.Columns[idx]] = string(col) // "2024-04-26 x:xx:xx"
					}
				}
				ret.Records = append(ret.Records, item)
				ret.Total++
			}
			return nil
		},
	)
	if err != nil {
		log.Error(ctx, "SQLAdvisor get conn error:%s", err.Error())
		return
	}
	return
}

func (self *vedbImpl) DescribeInstanceFeatures(ctx context.Context, req *datasource.DescribeInstanceFeaturesReq) (*datasource.DescribeInstanceFeaturesResp, error) {
	featureList := make([]model.InstanceFeatureType, 0)
	rreq := &vdbModel.GetDBWFeatureGatesReq{
		InstanceId: utils.StringRef(req.InstanceId),
	}
	resp := &vdbModel.GetDBWFeatureGatesResp{}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_GetDBWFeatureGates.String(), rreq, resp); err != nil {
		log.Warn(ctx, "get vedb instance details fail %v", err)
		return nil, err
	}
	if resp.GetFeatureGates() != nil {
		for _, feat := range resp.GetFeatureGates() {
			if feat.String() == "SessionManagement" {
				featureList = append(featureList, model.InstanceFeatureType_DialogMgmt)
			} else {
				feature, _ := model.InstanceFeatureTypeFromString(feat.String())
				featureList = append(featureList, feature)
			}
		}
		//FIXME：ContinuousKill特性vedb 3.10.2版本支持返回，该版本上线前，dbw自动补齐该字段(vedb agent 3.2.2版本线上已全部支持持续kill)
		var isContinuousKillExist bool
		for _, feat := range featureList {
			if feat == model.InstanceFeatureType_ContinuousKill {
				isContinuousKillExist = true
				break
			}
		}
		if !isContinuousKillExist {
			featureList = append(featureList, model.InstanceFeatureType_ContinuousKill)
		}
		return &datasource.DescribeInstanceFeaturesResp{
			Features: featureList,
		}, nil
	} else {
		return &datasource.DescribeInstanceFeaturesResp{Features: featureList}, nil
	}

}

func (self *vedbImpl) DescribeDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq,
) (*datasource.DescribeDialogInfosResp, error) {
	ret := &datasource.DescribeDialogInfosResp{}
	dialogInfos, err := self.getAllDialogInfos(ctx, req)
	if err != nil {
		return nil, err
	}
	// get details info
	ret.DialogDetails = self.filterDialogDetails(ctx, dialogInfos, req.QueryFilter, req.Offset, req.Limit)
	log.Info(ctx, "vedbImpl DescribeDialogInfos finished, ret is:%v", utils.Show(ret))
	return ret, nil
}

func (self *vedbImpl) getAllDialogInfos(ctx context.Context, req *datasource.DescribeDialogInfosReq) ([]*datasource.DialogInfo, error) {
	var (
		dialogInfos []*datasource.DialogInfo
	)
	instanceId := req.Source.InstanceId
	// DB侧会话
	if req.Component == model.Component_DBEngine.String() {
		nodeId := req.QueryFilter.GetNodeId()
		// 直连pod
		maSource := *req.Source
		podList, err := self.ListInstancePods(ctx, &datasource.ListInstancePodsReq{InstanceId: instanceId})
		if err != nil {
			return nil, err
		}
		for _, pod := range podList.Data {
			if pod.Name == nodeId {
				//maSource.Address = fmt.Sprintf("%s:%s", pod.PodIP, pod.Containers[0].GetPort())
				// 优先使用adminPort
				if pod.Containers[0].GetAdminPort() != "" {
					maSource.Address = fmt.Sprintf("%s:%s", pod.PodIP, pod.Containers[0].GetAdminPort())
				} else {
					maSource.Address = fmt.Sprintf("%s:%s", pod.PodIP, pod.Containers[0].GetPort())
				}
				break
			}
		}
		maSource.NodeId = nodeId
		log.Info(ctx, "Current node %s address is %s", nodeId, maSource.Address)
		conn, err := self.getConnV2(ctx, &maSource)
		if err != nil {
			log.Warn(ctx, "connect to datasource %s fail %v", maSource.Address, err)
			return nil, consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
		}
		defer self.ConnPool.Put(ctx, conn)
		sql := consts.DBW_DIAGNOSIS_DEFAULT_HINT + VeDBDialogAnalysis
		if len(req.InternalUsers) > 0 {
			sql += fmt.Sprintf("WHERE USER not in ('%s')", strings.Join(req.InternalUsers, "','"))
		}
		if err = conn.Raw(sql).Scan(&dialogInfos); err != nil {
			log.Warn(ctx, "exec sql %s failed:%s", sql, err.Error())
			return nil, consts.ErrorWithParam(model.ErrorCode_ConnectionFailedByReason, err.Error())
		}
		//.Info(ctx, "DescribeDialogInfos nodeId %s dialogInfos: %s", nodeId, dialogInfos)
		// fill NULL column
		fp.StreamOf(dialogInfos).Foreach(func(d *datasource.DialogInfo) {
			if !d.Info.Valid {
				d.Info.String = "NULL"
				d.SqlTemplate, d.SqlTemplateID = datasource.GetSqlTemplate(ctx, "-")
				d.SqlType = "-"
			} else {
				d.SqlTemplate, d.SqlTemplateID = datasource.GetSqlTemplate(ctx, d.Info.String)
				sqlType, err := datasource.GetRdsSqlType(ctx, d.Info.String)
				if err != nil {
					d.SqlType = "-"
				} else {
					d.SqlType = sqlType
				}
			}
			// fill nodeId and nodeType
			d.NodeId = req.QueryFilter.NodeId
			d.NodeType = req.QueryFilter.NodeType
		}).ToSlice(&dialogInfos)
	} else {
		// 直连proxy
		maSource := *req.Source
		podList, err := self.ListInstancePods(ctx, &datasource.ListInstancePodsReq{InstanceId: instanceId})
		if err != nil {
			return nil, err
		}
		var pods []*shared.KubePod
		for _, pod := range podList.Data {
			if pod.Component == "proxy" {
				pods = append(pods, pod)
			}
		}
		if len(pods) < 1 {
			log.Warn(ctx, "No Proxy pod found")
			return nil, err
		}
		//获取终端信息
		endPointMap := self.getEndpointDetail(ctx, req.Source.InstanceId)
		log.Info(ctx, "endPointMap is %s", utils.Show(endPointMap))
		var proxyDialList []*datasource.VeDBProxyDialog
		for _, pod := range pods {
			for _, port := range VedbProxyPortsList {
				var tmpDialInfo []*datasource.VeDBProxyDialog
				address := fmt.Sprintf("%s:%s", pod.PodIP, port)
				maSource.NodeId = pod.NodeId
				_, err := net.Dial("tcp", address) // 连通性测试
				if err == nil {
					maSource.Address = address
					conn, err := self.getConnV2(ctx, &maSource)
					if err != nil {
						log.Warn(ctx, "connect to datasource %s fail %v", maSource.Address, err)
						self.ConnPool.Put(ctx, conn)
						continue
					}
					sql := consts.DBW_DIAGNOSIS_DEFAULT_HINT + consts.VeDBProxyShowProcessList
					if err = conn.Raw(sql).Scan(&tmpDialInfo); err != nil {
						self.ConnPool.Put(ctx, conn)
						continue
					}
					self.ConnPool.Put(ctx, conn)
					for _, dialInfo := range tmpDialInfo {
						dialInfo.Port = port
						dialInfo.ProxyId = pod.Name
						proxyDialList = append(proxyDialList, dialInfo)
					}
				}
			}
		}
		log.Info(ctx, "proxyDialList is %s", utils.Show(proxyDialList))
		if err := fp.StreamOf(proxyDialList).Map(func(db *datasource.VeDBProxyDialog) *datasource.DialogInfo {
			item := &datasource.DialogInfo{
				ProcessID: db.ProcessID,
				DB:        db.DB,
				Host:      db.Host,
				Command:   db.Command,
				User:      db.User,
				Time:      db.Time,
				State:     "", // proxy不支持
				NodeId:    db.ProxyId,
				NodeType:  "Proxy",
				Info: sql.NullString{
					String: db.Command,
					Valid:  true,
				},
			}
			if endPointMap != nil {
				endpointItem, ok := endPointMap[db.Port]
				if ok {
					item.EndpointId = endpointItem.EndpointId
					item.EndpointName = endpointItem.EndpointName
				}
			}
			item.SqlTemplate, item.SqlTemplateID = datasource.GetSqlTemplate(ctx, db.Command)
			sqlType, err := datasource.GetRdsSqlType(ctx, db.Command)
			if err != nil {
				item.SqlType = "-"
			} else {
				item.SqlType = sqlType
			}
			return item
		}).ToSlice(&dialogInfos); err != nil {
			return nil, err
		}
		log.Info(ctx, "raw dialogInfos is %s", utils.Show(dialogInfos))
		// filter internal user
		dialogInfos = self.filterUser(ctx, dialogInfos, req.InternalUsers)
	}
	log.Info(ctx, "dialogInfos is %s", utils.Show(dialogInfos))
	return dialogInfos, nil
}
func (self *vedbImpl) DescribeDialogStatistics(ctx context.Context, req *datasource.DescribeDialogStatisticsReq,
) (ret *datasource.DescribeDialogStatisticsResp, err error) {

	dialogInfos, err := self.getAllDialogInfos(ctx, &datasource.DescribeDialogInfosReq{
		Source:        req.Source,
		Component:     req.Component,
		QueryFilter:   req.QueryFilter,
		InternalUsers: req.InternalUsers,
	})
	if err != nil {
		return nil, err
	}
	// get aggregated info
	statistics := self.getDialogStatistics(ctx, dialogInfos, req.TopN)

	ret = &datasource.DescribeDialogStatisticsResp{
		DialogStatistics: statistics,
	}

	return ret, nil
}

func (self *vedbImpl) KillProcess(ctx context.Context, req *datasource.KillProcessReq) (*datasource.KillProcessResp, error) {
	ret := &datasource.KillProcessResp{}
	pods, err := self.ListInstancePods(ctx, &datasource.ListInstancePodsReq{
		InstanceId: req.Source.InstanceId,
		Type:       self.Type(),
	})
	if err != nil {
		return nil, err
	}
	for _, pod := range pods.Data {
		if pod.NodeId == req.Source.NodeId {
			for _, container := range pod.Containers {
				if container.Name == "ndb-dbengine" {
					// 优先使用adminPort
					if container.AdminPort != "" {
						req.Source.Address = fmt.Sprintf("%s:%s", pod.PodIP, container.AdminPort)
					} else {
						req.Source.Address = fmt.Sprintf("%s:%s", pod.PodIP, container.Port)
					}
					break
				}
			}
		}
	}
	log.Info(ctx, "vedb source is %s", utils.Show(req.Source))
	conn, err := self.getConnV2(ctx, req.Source)
	if err != nil {
		log.Warn(ctx, "connect to datasource %s fail %v", req.Source.Address, err)
		return nil, err
	}
	for _, processID := range req.ProcessIDs {
		if err = conn.Exec("KILL ?", processID); err != nil {
			// 不存在的threadId报错不统计
			if !strings.Contains(strings.ToLower(err.Error()), strings.ToLower("unknown thread id")) {
				ret.FailInfoList = append(ret.FailInfoList, &shared.KillFailInfo{
					ProcessId:    processID,
					ErrorMessage: err.Error(),
				})
			}
		}
	}
	defer self.ConnPool.Put(ctx, conn)
	return ret, nil
}

func (self *vedbImpl) DescribeDBInstanceDetailForPilot(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (string, error) {
	vreq := &vdbModel.DescribeDBInstanceDetailReq{
		InstanceId: utils.StringRef(req.InstanceId),
	}
	vresp := &vdbModel.DescribeDBInstanceDetailResp{}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDBInstanceDetail.String(), vreq, vresp); err != nil {
		log.Warn(ctx, "vedbImpl.DescribeDBInstanceDetailForPilot: %v", err)
		return "", err
	}
	if IsVeDBInstanceDelete(vresp.InstanceDetail.GetInstanceStatus()) {
		log.Info(ctx, "vedb is delete, instance_status:%s", utils.Show(vresp))
		return "Instance Not Found", nil
	}

	return utils.Show(vresp), nil
}

func (self *vedbImpl) DescribeDBInstanceParametersForPilot(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (string, error) {
	vreq := &vdbModel.DescribeDBInstanceParametersReq{
		InstanceId: req.InstanceId,
	}
	vresp := &vdbModel.DescribeDBInstanceParametersResp{}
	if err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDBInstanceParameters.String(), vreq, vresp); err != nil {
		log.Warn(ctx, "vedbImpl.DescribeDBInstanceParametersForPilot: %v", err)
		return "", err
	}

	return utils.Show(vresp), nil
}

func (self *vedbImpl) getDialogStatistics(ctx context.Context, data []*datasource.DialogInfo, topN int32) *shared.DialogStatistics {
	userInfo := make(map[string]*datasource.UserAggregatedInfo)
	ipInfo := make(map[string]*datasource.IPAggregatedInfo)
	dbInfo := make(map[string]*datasource.DBAggregatedInfo)
	psmInfo := make(map[string]*datasource.PSMAggregatedInfo)
	var activeConn, totalConn int32 = 0, 0
	// fill NULL column, count active/total conns, aggregate user/ip/db info
	fp.StreamOf(data).Foreach(func(d *datasource.DialogInfo) {
		totalConn += 1
		// 提取psm
		psmItem := datasource.ExtractRdsPsm(d.Info.String)
		if _, ok := psmInfo[psmItem.Psm]; !ok {
			psmInfo[psmItem.Psm] = &datasource.PSMAggregatedInfo{PSM: psmItem.Psm}
		}
		psmInfo[psmItem.Psm].TotalConn += 1

		if _, ok := userInfo[d.User]; !ok {
			userInfo[d.User] = &datasource.UserAggregatedInfo{User: d.User}
		}
		userInfo[d.User].TotalConn += 1
		ip := datasource.ExtractIP(d.Host)
		log.Info(ctx, "ExtractIP former: %s, after: %s", d.Host, ip)
		if _, ok := ipInfo[ip]; !ok {
			ipInfo[ip] = &datasource.IPAggregatedInfo{IP: ip}
		}
		ipInfo[ip].TotalConn += 1
		if _, ok := dbInfo[d.DB]; !ok {
			dbInfo[d.DB] = &datasource.DBAggregatedInfo{DB: d.DB}
		}
		dbInfo[d.DB].TotalConn += 1
		if strings.ToLower(d.Command) != "sleep" {
			activeConn += 1
			userInfo[d.User].ActiveConn += 1
			ipInfo[ip].ActiveConn += 1
			dbInfo[d.DB].ActiveConn += 1
			psmInfo[psmItem.Psm].ActiveConn += 1
		}
	}).Run()

	var userList []*shared.UserAggregatedInfo
	var ipList []*shared.IPAggregatedInfo
	var dbList []*shared.DBAggregatedInfo
	var psmList []*shared.PSMAggregatedInfo
	// sort aggregate info, take top 5
	fp.KVStreamOf(userInfo).ZipMap(func(k string, v *datasource.UserAggregatedInfo) *shared.UserAggregatedInfo {
		return &shared.UserAggregatedInfo{
			User:              v.User,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&userList)
	fp.KVStreamOf(ipInfo).ZipMap(func(k string, v *datasource.IPAggregatedInfo) *shared.IPAggregatedInfo {
		return &shared.IPAggregatedInfo{
			IP:                v.IP,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&ipList)
	fp.KVStreamOf(dbInfo).ZipMap(func(k string, v *datasource.DBAggregatedInfo) *shared.DBAggregatedInfo {
		return &shared.DBAggregatedInfo{
			DB:                v.DB,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&dbList)
	fp.KVStreamOf(psmInfo).ZipMap(func(k string, v *datasource.PSMAggregatedInfo) *shared.PSMAggregatedInfo {
		return &shared.PSMAggregatedInfo{
			PSM:               v.PSM,
			ActiveConnections: v.ActiveConn,
			TotalConnections:  v.TotalConn,
		}
	}).ToSlice(&psmList)

	sort.Slice(userList, func(i, j int) bool {
		if userList[i].TotalConnections > userList[j].TotalConnections {
			return true
		}
		if userList[i].TotalConnections == userList[j].TotalConnections &&
			userList[i].ActiveConnections == userList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(ipList, func(i, j int) bool {
		if ipList[i].TotalConnections > ipList[j].TotalConnections {
			return true
		}
		if ipList[i].TotalConnections == ipList[j].TotalConnections &&
			ipList[i].ActiveConnections == ipList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(dbList, func(i, j int) bool {
		if dbList[i].TotalConnections > dbList[j].TotalConnections {
			return true
		}
		if dbList[i].TotalConnections == dbList[j].TotalConnections &&
			dbList[i].ActiveConnections == dbList[j].ActiveConnections {
			return true
		}
		return false
	})
	sort.Slice(psmList, func(i, j int) bool {
		if psmList[i].TotalConnections > psmList[j].TotalConnections {
			return true
		}
		if psmList[i].TotalConnections == psmList[j].TotalConnections &&
			psmList[i].ActiveConnections == psmList[j].ActiveConnections {
			return true
		}
		return false
	})

	return &shared.DialogStatistics{
		DialogOver: &shared.DialogOverview{
			ActiveConnections: activeConn,
			TotalConnections:  totalConn,
		},
		UserAggregatedInfo: userList[:fp.MinInt(int(topN), len(userList))],
		IPAggregatedInfo:   ipList[:fp.MinInt(int(topN), len(ipList))],
		DBAggregatedInfo:   dbList[:fp.MinInt(int(topN), len(dbList))],
		PSMAggregatedInfo:  psmList[:fp.MinInt(int(topN), len(psmList))],
	}
}

func (self *vedbImpl) GetDiskSize(ctx context.Context, req *datasource.GetDiskSizeReq) (*datasource.GetDiskSizeResp, error) {
	// 1.数据大小
	dataStorage, err := self.getDataStorage(ctx, req)
	if err != nil {
		return nil, err
	}
	// 2.redoLog大小
	redoLogStorage, err := self.getRedoLogStorage(ctx, req)
	if err != nil {
		return nil, err
	}
	// 3.undoLog大小
	undoLogStorage, err := self.getUndoLogStorage(ctx, req)
	if err != nil {
		return nil, err
	}
	// 4.binLog大小
	binLogStorage, err := self.getBinLogStorage(ctx, req)
	if err != nil {
		return nil, err
	}
	// 5.系统空间使用量
	sysUsedStorage, err := self.getSysUsedStorage(ctx, req)
	if err != nil {
		return nil, err
	}
	// 6.临时空间大小
	tmpUsedStorageType, err := self.getTmpUsedStorage(ctx, req)
	if err != nil {
		return nil, err
	}
	// 已使用大小
	usedStorage, err := self.getUsedStorage(ctx, req)
	if err != nil {
		return nil, err
	}
	// 7.日志大小（把2-5加起来）
	return &datasource.GetDiskSizeResp{
		DiskDataSize:   dataStorage,
		UsedStorage:    usedStorage,
		DiskBinLogSize: binLogStorage,
		RedoLogSize:    redoLogStorage,
		UndoLogSize:    undoLogStorage,
		SysUsedStorage: sysUsedStorage,
		TmpUsedStorage: tmpUsedStorageType,
		DiskLogSize:    binLogStorage + redoLogStorage + undoLogStorage + sysUsedStorage + tmpUsedStorageType,
	}, nil
}

func (self *vedbImpl) ConvertTableSpaceToModel(ctx context.Context, sourceType shared.DataSourceType, resp *shared.DescribeTableSpaceResp) *model.DescribeTableSpaceResp {
	var tableStats []*model.TableStat
	for _, value := range resp.TableStats {
		tableStat := &model.TableStat{
			Name:                   value.Name,
			DB:                     value.DB,
			TableSpace:             &value.TableSpace,
			TableSpaceRatio:        &value.TableSpaceRatio,
			Engine:                 &value.Engine,
			IndexLength:            &value.IndexLength,
			DataLength:             &value.DataLength,
			TableRows:              &value.TableRows,
			SpaceFragmentationRate: &value.SpaceFragmentationRate,
			AutoIdUsedRate:         &value.AutoIdUsedRate,
			AvgRowLength:           &value.AvgRowLength,
		}
		tableStats = append(tableStats, tableStat)
	}
	return &model.DescribeTableSpaceResp{
		Total:      resp.Total,
		TableStats: tableStats,
	}
}

func (self *vedbImpl) FormatDescribeStorageCapacityResp(sourceType shared.DataSourceType, diskSize *datasource.GetDiskSizeResp, storageSpace float64) *model.DescribeStorageCapacityResp {
	resp := &model.DescribeStorageCapacityResp{
		UsedSize:       diagnosis.DecimalFloat(diskSize.UsedStorage),
		DiskDataSize:   diagnosis.DecimalFloat(diskSize.DiskDataSize),
		DiskLogSize:    diagnosis.DecimalFloat(diskSize.DiskLogSize),
		DiskBinLogSize: diagnosis.DecimalPointFloat(diskSize.DiskBinLogSize),
		RedoLogSize:    diagnosis.DecimalPointFloat(diskSize.RedoLogSize),
		UndoLogSize:    diagnosis.DecimalPointFloat(diskSize.UndoLogSize),
		SysUsedStorage: diagnosis.DecimalPointFloat(diskSize.SysUsedStorage),
		TmpUsedStorage: diagnosis.DecimalPointFloat(diskSize.TmpUsedStorage),
	}
	return resp
}

func (self *vedbImpl) EnsureAccount(ctx context.Context, req *datasource.EnsureAccountReq) error {
	// 默认连接主节点
	pods, err := self.ListInstancePods(ctx, &datasource.ListInstancePodsReq{
		InstanceId: req.Source.InstanceId,
		Type:       self.Type(),
	})
	if err != nil {
		return err
	}
	var (
		address   string
		adminPort string
		userPort  string
	)
	// 如果没有传, 默认取主节点地址
	if req.Source.NodeId == "" {
		for _, pod := range pods.Data {
			if pod.Role == model.NodeType_Primary.String() {
				for _, container := range pod.Containers {
					if container.Name == "ndb-dbengine" {
						// 优先使用adminPort
						if container.AdminPort != "" {
							address = fmt.Sprintf("%s:%s", pod.PodIP, container.AdminPort)
						} else {
							address = fmt.Sprintf("%s:%s", pod.PodIP, container.Port)
						}
						req.Source.NodeId = pod.NodeId
						req.Source.AdminPort = adminPort
						req.Source.NormalPort = userPort
						break
					}
				}
			}
		}
	} else {
		for _, pod := range pods.Data {
			if pod.NodeId == req.Source.NodeId {
				for _, container := range pod.Containers {
					if container.Name == "ndb-dbengine" {
						// 优先使用adminPort
						if container.AdminPort != "" {
							address = fmt.Sprintf("%s:%s", pod.PodIP, container.AdminPort)
						} else {
							address = fmt.Sprintf("%s:%s", pod.PodIP, container.Port)
						}
						req.Source.AdminPort = container.AdminPort
						req.Source.NormalPort = container.Port
						break
					}
				}
			}
		}
	}
	req.Source.Address = address
	conn, err := self.getConnV2(ctx, req.Source)
	if err != nil && (strings.Contains(strings.ToLower(err.Error()), consts.MySQLAccountError)) {
		log.Warn(ctx, "vedb get conn error,maybe there is no account, instanceId: %s", req.Source.InstanceId)
		// 创建一个新的账号,然后去连接
		err = self.resetAccount(ctx, req.Source.InstanceId, shared.VeDBMySQL)
		if err != nil {
			log.Warn(ctx, "vedb resetAccount err: %v", err)
			return err
		}
	}
	if conn != nil {
		self.ConnPool.Put(ctx, conn)
	}
	return nil
}

func (self *vedbImpl) ResetAccount(ctx context.Context, instanceId string, dsType shared.DataSourceType) error {
	return self.resetAccount(ctx, instanceId, dsType)
}

func (self *vedbImpl) GrantReplicationPrivilege(ctx context.Context, ds *shared.DataSource, accountName string) error {
	connAdmin, err := self.getConn(ctx, ds)
	if err != nil {
		log.Warn(ctx, "instanceId:%s, address:%s, get conn error:%s", ds.InstanceId, ds.Address, err.Error())
		return err
	}
	defer func(connAdmin db.Conn) { _ = connAdmin.Close() }(connAdmin)

	// GRANT REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'user'@'%'
	grantSql := fmt.Sprintf("GRANT REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO  '%s'@'%s'", accountName, "%")
	return connAdmin.Exec(grantSql)
}

func (self *vedbImpl) resetAccount(ctx context.Context, instanceId string, dsType shared.DataSourceType) error {
	if !self.checkInstanceIsRunning(ctx, instanceId, dsType) {
		return consts.ErrorOf(model.ErrorCode_NotSupportAction)
	}

	//删除账号
	// 我们直接调用删除接口，确保删除调这个账号后重建
	c3cfg := self.C3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)

	if err := self.DeleteAccount(ctx, &datasource.DeleteAccountReq{
		DSType:      dsType,
		InstanceId:  instanceId,
		AccountName: c3cfg.DBWAccountName,
	}); err != nil {
		log.Warn(ctx, "vedb EnsureAccount failed to delete account, err=%v", err)
		return err
	}

	//创建账号

	if err := self.CreateAccount(ctx, &datasource.CreateAccountReq{
		DSType:          dsType,
		InstanceId:      instanceId,
		AccountName:     c3cfg.DBWAccountName,
		AccountPassword: self.getAccountPassword(c3cfg.DbwAccountPasswordGenKey, instanceId),
		AccountType:     rdsModel.AccountType_Normal.String(),
	}); err != nil {
		log.Warn(ctx, "vedb failed to create account, err=%v", err)
		return err
	}
	// vedb 3.8.2版本后，不需要为dbw_admin单独走授权接口
	//if err := handler.AddPrivilegeToDB(ctx, self.ActorClient, instanceId, model.DSType(dsType)); err != nil {
	//	log.Warn(ctx, "vedb AddPrivilegeToDB fail: %v", err.Error())
	//	return consts.ErrorOf(model.ErrorCode_AddPrivilegeToDBFailed)
	//}

	return nil
}

func (self *vedbImpl) checkInstanceIsRunning(ctx context.Context, instanceId string, dsType shared.DataSourceType) bool {
	var retryCount int8
LOOP:
	retryCount += 1
	if retryCount > 3 {
		log.Info(ctx, "Check instance status exceed over 3 times,quit")
		return false
	}
	describeDBInstanceDetailReq := &datasource.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
		Type:       dsType,
	}
	resp, err := self.DescribeDBInstanceDetail(ctx, describeDBInstanceDetailReq)
	if err != nil {
		return false
	}
	if resp.InstanceStatus != "Running" {
		time.Sleep(5 * time.Second)
		goto LOOP
	}
	return true
}

func (self *vedbImpl) getAccountPassword(key string, instanceId string) string {
	mac := hmac.New(sha256.New, []byte(key))
	mac.Write([]byte(instanceId))
	expectedMac := hex.EncodeToString(mac.Sum(nil))
	return "Dbw_" + expectedMac[:26]
}

func (self *vedbImpl) getEndpointDetail(ctx context.Context, instanceId string) map[string]*SimplifiedEndpointInfo {
	endPointMap := map[string]*SimplifiedEndpointInfo{}
	describeDBEndpointForDBWReq := &vdbModel.DescribeDBEndpointForDBWReq{
		InstanceId: &instanceId,
	}
	describeDBEndpointForDBWResp := &vdbModel.DescribeDBEndpointForDBWRsp{}
	err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeDBEndpointForDBW.String(), describeDBEndpointForDBWReq, describeDBEndpointForDBWResp)
	if err != nil {
		if strings.Contains(err.Error(), "InstanceNotFound") {
			log.Warn(ctx, "describeDBEndpointForDBW InstanceNotFound, err=%v", err)
		} else {
			log.Warn(ctx, "failed to call describeDBEndpointForDBW, err=%v", err)
		}
		return nil
	}
	for _, dbPort := range VedbProxyPortsList {
		portInt64, err := strconv.ParseInt(dbPort, 10, 64)
		if err != nil {
			log.Warn(ctx, "get invalid port: %s", dbPort)
			continue
		}
		for _, ep := range describeDBEndpointForDBWResp.Endpoints {
			if ep.Addresses[0].GetStorageInnerPort() == int16(portInt64) {
				endPointMap[dbPort] = &SimplifiedEndpointInfo{
					EndpointId:    ep.EndpointId,
					EndpointName:  ep.EndpointName,
					ReadWriteMode: ep.GetReadWriteMode(),
				}
			}
		}
	}
	return endPointMap
}

func (self *vedbImpl) DescribeSqlFingerPrintOrKeywords(ctx context.Context, req *datasource.DescribeSqlFingerPrintOrKeywordsReq) (*datasource.DescribeSqlFingerPrintOrKeywordsResp, error) {
	rreq := &vdbModel.InnerGetFingerprintAndKeywordReq{
		Sql:             req.SqlText,
		SQLThrottleType: &req.ObjectType,
	}
	rresp := &vdbModel.InnerGetFingerprintAndKeywordResp{}
	err := self.mysql.Get().Call(ctx, vdbModel.Action_InnerGetFingerprintAndKeyword.String(), rreq, rresp)
	if err != nil {
		log.Warn(ctx, "failed to call vdb InnerGetFingerprintAndKeyword, err=%+v", err)
		return nil, err
	}
	ret := &datasource.DescribeSqlFingerPrintOrKeywordsResp{
		FingerPrint: rresp.GetFingerprint(),
		Keywords:    rresp.GetKeyword(),
	}
	return ret, nil
}
func (self *vedbImpl) DescribeSqlType(ctx context.Context, req *datasource.DescribeSqlTypeReq) (*datasource.DescribeSqlTypeResp, error) {
	ret := &datasource.DescribeSqlTypeResp{}
	sqlType, err := datasource.GetRdsSqlType(ctx, req.SqlText)
	if err != nil {
		return nil, consts.ErrorOf(model.ErrorCode_SQLReviewParserSqlError)
	}
	ret.SqlType = sqlType
	return ret, nil
}

func (self *vedbImpl) ModifyProxyThrottleRule(ctx context.Context, req *datasource.ModifyProxyThrottleRuleReq) (*datasource.ModifyProxyThrottleRuleResp, error) {
	ruleInfo := &vdbModel.ProxyThrottleRule{
		Value: int64(req.ProxyThrottleRule.ThrottleThreshold),
	}
	switch req.ProxyThrottleRule.ThrottleTarget {
	case model.ThrottleTarget_FingerQPS.String():
		ruleInfo.Finger = &req.ProxyThrottleRule.ThrottleFingerPrint
	case model.ThrottleTarget_PsmDbQPS.String():
		ruleInfo.PSM = &req.ProxyThrottleRule.ThrottleHost
		ruleInfo.DB = &req.ProxyThrottleRule.ThrottleDB
	case model.ThrottleTarget_SqlQPS.String():
		ruleInfo.Sql = &req.ProxyThrottleRule.ThrottleSqlText
	case model.ThrottleTarget_KeywordQPS.String():
		ruleInfo.Keyword = &req.ProxyThrottleRule.Keywords
	}
	rreq := &vdbModel.InnerModifyProxyThrottleRuleReq{
		InstanceId:                 req.InstanceId,
		EndpointId:                 req.ProxyThrottleRule.EndpointID,
		ProxyThrottleRuleOperation: req.Action,
		ProxyThrottleType:          req.ProxyThrottleRule.ThrottleTarget,
		ProxyThrottleRule:          ruleInfo,
	}
	rresp := &vdbModel.InnerModifyProxyThrottleRuleResp{}
	err := self.mysql.Get().Call(ctx, vdbModel.Action_InnerModifyProxyThrottleRule.String(), rreq, rresp)
	if err != nil {
		log.Warn(ctx, "failed to call vedb InnerModifyProxyThrottleRule, err=%+v", err)
		return nil, err
	}
	ret := &datasource.ModifyProxyThrottleRuleResp{}
	return ret, nil
}

func (self *vedbImpl) DescribeFullSQLLogConfig(ctx context.Context, req *datasource.DescribeFullSQLLogConfigReq) (*datasource.DescribeFullSQLLogConfigResp, error) {
	rreq := &vdbModel.InnerDescribeFullSQLLogConfigReq{InstanceId: req.InstanceID}
	rresp := &vdbModel.InnerDescribeFullSQLLogConfigResp{}
	err := self.mysql.Get().Call(ctx, vdbModel.Action_InnerDescribeFullSQLLogConfig.String(), rreq, rresp, client.WithVersion(RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "InnerDescribeFullSQLLogConfig fail, error:%s", err)
		return nil, err
	}
	return &datasource.DescribeFullSQLLogConfigResp{
		SQLCollectorStatus: datasource.SQLCollectorStatus(rresp.SQLCollectorStatus),
		TLSDomain:          rresp.GetTLSDomain(),
		TLSProjectId:       rresp.GetTLSProjectId(),
		TLSTopic:           rresp.GetTLSTopic(),
	}, nil
}

func (self *vedbImpl) ModifyFullSQLLogConfig(ctx context.Context, req *datasource.ModifyFullSQLLogConfigReq) (*datasource.ModifyFullSQLLogConfigResp, error) {
	rreq := &vdbModel.InnerModifyFullSQLLogConfigReq{
		InstanceId:         req.InstanceID,
		SQLCollectorStatus: vdbModel.SQLCollectorStatusEnum(req.SQLCollectorStatus),
		DryRun:             utils.BoolRef(req.DryRun),
	}
	err := self.mysql.Get().Call(ctx, vdbModel.Action_InnerModifyFullSQLLogConfig.String(), rreq, nil)
	if err != nil {
		log.Warn(ctx, "InnerModifyFullSQLLogConfig fail, error:%s", err)
		return nil, err
	}
	return &datasource.ModifyFullSQLLogConfigResp{}, nil
}

func (self *vedbImpl) ModifySQLKillRule(ctx context.Context, req *datasource.ModifySQLKillRuleReq) (*datasource.ModifySQLKillRuleResp, error) {
	// 查询实例当前的sql kill规则
	listSQLKillRuleReq := &vdbModel.DescribeSQLKillConfigReq{
		InstanceId: &req.InstanceId,
	}
	sqlKillRules := &vdbModel.DescribeSQLKillConfigResp{}
	err := self.mysql.Get().Call(ctx, vdbModel.Action_DescribeSQLKillConfig.String(), listSQLKillRuleReq, sqlKillRules, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call vdb DescribeSQLKillConfig, err=%+v", err)
		return nil, err
	}
	modifySQLKillRuleReq := &vdbModel.ModifySQLKillConfigReq{}
	switch req.Action {
	case model.KillSqlEventType_Add.String():
		var (
			toAddRule []*vdbModel.SQLKillInfoObject
		)
		killRule := req.KillRule[0]
		sqlType := strings.Split(killRule.SqlType, ",")
		nodeType := strings.Split(killRule.NodeType, ",")
		protectedUsers := strings.Split(req.ProtectedUsers, ",")
		// VeDB NodeType不支持传入多值...
		for _, item := range nodeType {
			sqlKillRule := &vdbModel.SQLKillInfoObject{
				MaxExecTime: int32(killRule.MaxExecTime),
				SQLType:     sqlType,
				NodeType:    []string{item},
			}
			toAddRule = append(toAddRule, sqlKillRule)
		}

		submittedRule := sqlKillRules.SQLKillInfo
		for _, rule := range toAddRule {
			var isExisted bool
			hashValue := hashRule(rule)
			for _, r := range sqlKillRules.SQLKillInfo {
				hv := hashRule(r)
				if hashValue == hv {
					isExisted = true
					break
				}
			}
			// 已存在的则跳过插入
			if !isExisted {
				submittedRule = append(submittedRule, rule)
			}
		}
		modifySQLKillRuleReq = &vdbModel.ModifySQLKillConfigReq{
			InstanceId:     &req.InstanceId,
			SQLKillInfo:    submittedRule,
			EnableSQLKill:  utils.BoolRef(true),
			ProtectedUsers: protectedUsers,
		}
	case model.KillSqlEventType_Delete.String():
		submittedRuleSet, err := self.filterDeletedSqlRule(req, sqlKillRules)
		if err != nil {
			return nil, err
		}
		protectedUsers := strings.Split(req.ProtectedUsers, ",")
		modifySQLKillRuleReq = &vdbModel.ModifySQLKillConfigReq{
			InstanceId:     &req.InstanceId,
			SQLKillInfo:    submittedRuleSet,
			EnableSQLKill:  utils.BoolRef(true),
			ProtectedUsers: protectedUsers,
		}

	case model.KillSqlEventType_Stop.String():
		submittedRuleSet, err := self.filterDeletedSqlRule(req, sqlKillRules)
		if err != nil {
			return nil, err
		}
		protectedUsers := strings.Split(req.ProtectedUsers, ",")
		modifySQLKillRuleReq = &vdbModel.ModifySQLKillConfigReq{
			InstanceId:     &req.InstanceId,
			SQLKillInfo:    submittedRuleSet,
			EnableSQLKill:  utils.BoolRef(true),
			ProtectedUsers: protectedUsers,
		}
	default:
		log.Warn(ctx, "Not supported kill sql event type %s", req.Action)
	}
	log.Info(ctx, "modifySQLKillRuleReq is %s", utils.Show(modifySQLKillRuleReq))
	//resp := &vdbModel.ModifySQLKillConfigResp{}
	err = self.mysql.Get().Call(ctx, vdbModel.Action_ModifySQLKillConfig.String(), modifySQLKillRuleReq, nil, client.WithVersion(consts.RDS_MySQL_Version_V2))
	if err != nil {
		log.Warn(ctx, "failed to call Vdb ModifySQLKillConfig, err=%+v", err)
		return nil, err
	}
	return &datasource.ModifySQLKillRuleResp{}, nil
}

func (self *vedbImpl) filterDeletedSqlRule(req *datasource.ModifySQLKillRuleReq, sqlKillRules *vdbModel.DescribeSQLKillConfigResp) ([]*vdbModel.SQLKillInfoObject, error) {
	var (
		deletedRuleSet []*vdbModel.SQLKillInfoObject
	)
	submittedRuleSet := make([]*vdbModel.SQLKillInfoObject, 0)
	for _, item := range req.KillRule {
		nodeType := strings.Split(item.NodeType, ",")
		for _, nodeTypeItem := range nodeType {
			rule := &vdbModel.SQLKillInfoObject{
				SQLType:     strings.Split(item.SqlType, ","),
				NodeType:    []string{nodeTypeItem},
				MaxExecTime: int32(item.MaxExecTime),
			}
			deletedRuleSet = append(deletedRuleSet, rule)
		}
	}
	for _, rule := range sqlKillRules.SQLKillInfo {
		var isExisted bool
		hashValue := hashRule(rule)
		for _, r := range deletedRuleSet {
			hv := hashRule(r)
			if hashValue == hv {
				isExisted = true
				break
			}
		}
		// 没有命中删除的规则，则继续保留
		if !isExisted {
			submittedRuleSet = append(submittedRuleSet, rule)
		}
	}
	return submittedRuleSet, nil
}
func (self *vedbImpl) AddSQLCCLRule(ctx context.Context, req *datasource.AddSQLCCLRuleReq) (*datasource.AddSQLCCLRuleResp, error) {
	// 获取pod
	pods, err := self.ListInstancePods(ctx, &datasource.ListInstancePodsReq{Type: req.Type, LinkType: shared.Volc, InstanceId: req.InstanceId})
	if err != nil {
		log.Warn(ctx, " Add ccl rules failed due to acquiring pods failed:%s", err)
		return nil, err
	}
	c3cfg := self.C3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	//建连
	ds := &shared.DataSource{
		Type:     shared.DataSourceType(model.DSType_VeDBMySQL),
		LinkType: shared.Volc,
		User:     c3cfg.DBWAccountName,
		Password: self.getAccountPassword(c3cfg.DbwAccountPasswordGenKey, req.InstanceId),
		Db:       "information_schema",
	}
	// add CCL rule
	rule := req.CCLRule
	sql := fmt.Sprintf(`ccl add('%s', '', '','', '', '', %d, '%s', 'Y', 'Y', %d, 100);`, rule.SqlType, rule.ConcurrencyCount, rule.Keywords, rule.ConcurrencyCount)
	versionDetail, err := self.DescribeInstanceVersion(ctx, &datasource.DescribeInstanceVersionReq{
		InstanceId: req.InstanceId,
		Type:       self.Type(),
	})
	if versionDetail != nil {
		isSupport := self.isSupportSQLTemplateCCL(ctx, versionDetail.DBRevisionVersion, CCLSupportSQLTemplateBaseLineVersion)
		if isSupport {
			// 兼容新版ccl语句(支持模版限流)
			sql = fmt.Sprintf(`ccl add('%s', '', '','', '', '', %d, '%s','', 'Y', 'Y', %d, 100);`, rule.SqlType, rule.ConcurrencyCount, rule.Keywords, rule.ConcurrencyCount)
		}
	}
	//ccl add('<Type>','<User>','<Host>','<Schema_name>','<Table_name>','<Partition_name>',<Concurrency_count>,'<Keywords>'，'<State>','<Ordered>',<Max_queue_size>,<Wait_timeout>);
	for _, pod := range pods.Data {
		// ccl add命令仅在leader节点上执行
		if pod.Component == "dbengine" && pod.Role == model.NodeType_Primary.String() {
			tempDs := ds
			addr := fmt.Sprintf("%s:%s", pod.PodIP, pod.Containers[0].Port) // IP:dbEngine-port
			tempDs.Address = addr
			conn, err := self.getConn(ctx, ds)
			if err != nil {
				log.Warn(ctx, "connect to datasource %s fail %v", utils.Show(tempDs), err)
				conn.Close()
				// check dbw_admin账号是否有service_connection_admin权限
				isSuperAdmin, err01 := self.CheckAccountPrivilege(ctx, &datasource.CheckDBWAccountReq{AccountName: c3cfg.DBWAccountName, InstanceId: req.InstanceId, DSType: req.Type})
				if err01 != nil || !isSuperAdmin {
					log.Warn(ctx, "dbw_admin account is not exist or privilege deny on InstanceId %s,reset account later on", req.InstanceId)
					if err02 := self.resetAccount(ctx, req.InstanceId, shared.VeDBMySQL); err02 != nil {
						log.Warn(ctx, "vedb resetAccount err: %v", err)
						return nil, err02
					}
				}
				adminAddr := fmt.Sprintf("%s:%s", pod.PodIP, pod.Containers[0].AdminPort)
				tempDs.Address = adminAddr
				connAdmin, err01 := self.getConn(ctx, tempDs)
				if err01 != nil {
					log.Warn(ctx, "connect to datasource with AdminPort %s fail %v", utils.Show(tempDs), err)
					connAdmin.Close()
					return nil, err01
				} else {
					if err = connAdmin.Exec(sql); err != nil {
						log.Warn(ctx, "node %s add ccl rule failed %v", adminAddr, err)
						connAdmin.Close()
						return nil, err
					}
					connAdmin.Close()
				}
			}
			if err = conn.Exec(sql); err != nil {
				log.Warn(ctx, "node %s add ccl rule failed %v", tempDs.Address, err)
				conn.Close()
				return nil, err
			}
			conn.Close()
		}
	}
	return &datasource.AddSQLCCLRuleResp{}, nil

}

func (self *vedbImpl) isSupportSQLTemplateCCL(ctx context.Context, actualVersion string, baseLineVersion string) bool {
	log.Info(ctx, "actualVersion:%s,baseLineVersion:%s", actualVersion, baseLineVersion)
	// 按照"."分割版本号字符串，如*******
	versionParts := strings.Split(actualVersion, ".")
	// 将各个版本号部分转换为整数
	majorVersion := stringToInt64(ctx, versionParts[0])
	minorVersion := stringToInt64(ctx, versionParts[1])
	patchVersion := stringToInt64(ctx, versionParts[2])
	lowerVersion := stringToInt64(ctx, versionParts[3])
	// 将各个版本号部分转换为整数
	baseVersionParts := strings.Split(baseLineVersion, ".")
	baseLineMajorVersion := stringToInt64(ctx, baseVersionParts[0])
	baseLineMinorVersion := stringToInt64(ctx, baseVersionParts[1])
	baseLinePatchVersion := stringToInt64(ctx, baseVersionParts[2])
	baseLowerVersion := stringToInt64(ctx, baseVersionParts[3])
	// 分别比较majorVersion,minorVersion,patchVersion
	if majorVersion > baseLineMajorVersion ||
		(majorVersion == baseLineMajorVersion && (minorVersion > baseLineMinorVersion ||
			(minorVersion == baseLineMinorVersion && patchVersion >= baseLinePatchVersion))) ||
		(majorVersion == baseLineMajorVersion && minorVersion == baseLineMinorVersion && patchVersion == baseLinePatchVersion &&
			lowerVersion >= baseLowerVersion) {
		return true
	} else {
		return false
	}
}

func (self *vedbImpl) DeleteSQLCCLRule(ctx context.Context, req *datasource.DeleteSQLCCLRuleReq) (*datasource.DeleteSQLCCLRuleResp, error) {
	// 获取pod
	pods, err := self.ListInstancePods(ctx, &datasource.ListInstancePodsReq{Type: req.Type, LinkType: shared.Volc, InstanceId: req.InstanceId})
	if err != nil {
		log.Warn(ctx, " delete ccl rules failed due to acquiring pods failed:%s", err)
		return nil, err
	}
	//建连
	c3cfg := self.C3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)

	ds := &shared.DataSource{
		Type:     shared.DataSourceType(model.DSType_VeDBMySQL),
		LinkType: shared.Volc,
		User:     c3cfg.DBWAccountName,
		Password: handler.GetAccountPassword(c3cfg.DbwAccountPasswordGenKey, req.InstanceId),
		Db:       "information_schema",
	}
	// delete CCL rule
	var cclDeleteSql string
	for _, pod := range pods.Data {
		// ccl add命令仅在leader节点上执行
		if pod.Component == "dbengine" && pod.Role == model.NodeType_Primary.String() {
			tempDs := ds
			addr := fmt.Sprintf("%s:%s", pod.PodIP, pod.Containers[0].Port) // IP:dbEngine-port
			tempDs.Address = addr
			log.Info(ctx, "ds address is %s", tempDs)
			conn, err := self.getConn(ctx, ds)
			if err != nil {
				log.Warn(ctx, "connect to datasource %s fail %v", utils.Show(tempDs), err)
				conn.Close()
				// check dbw_admin账号是否有service_connection_admin权限
				isSuperAdmin, err01 := self.CheckAccountPrivilege(ctx, &datasource.CheckDBWAccountReq{AccountName: c3cfg.DBWAccountName, InstanceId: req.InstanceId, DSType: req.Type})
				if err01 != nil || !isSuperAdmin {
					log.Warn(ctx, "dbw_admin account is not exist or privilege deny on InstanceId %s,reset account later on", req.InstanceId)
					if err02 := self.resetAccount(ctx, req.InstanceId, shared.VeDBMySQL); err02 != nil {
						log.Warn(ctx, "vedb resetAccount err: %v", err)
						return nil, err02
					}
				}
				adminAddr := fmt.Sprintf("%s:%s", pod.PodIP, pod.Containers[0].AdminPort)
				tempDs.Address = adminAddr
				log.Info(ctx, "ds admin address is %s", tempDs)
				connAdmin, err01 := self.getConn(ctx, tempDs)
				if err01 != nil {
					log.Warn(ctx, "connect to datasource with AdminPort %s fail %v", utils.Show(tempDs), err)
					connAdmin.Close()
					return nil, err01
				} else {
					// 先获取对应的ruleId
					ruleId, err0 := self.GetCCLRuleIdByKeywords(ctx, connAdmin, req.CCLRule)
					if err0 != nil {
						log.Warn(ctx, "get ccl ruleId failed %v", err0)
						connAdmin.Close()
						return nil, err0
					}
					log.Info(ctx, "node %s ruleId is %d", tempDs.Address, ruleId)
					cclDeleteSql = fmt.Sprintf(`ccl delete(%d);`, ruleId)
					if err = connAdmin.Exec(cclDeleteSql); err != nil {
						log.Warn(ctx, "node %s delete ccl rule failed %v", adminAddr, err)
						connAdmin.Close()
						return nil, err
					}
					connAdmin.Close()
				}
			}
			// 先获取对应的ruleId
			ruleId, err0 := self.GetCCLRuleIdByKeywords(ctx, conn, req.CCLRule)
			if err0 != nil {
				log.Warn(ctx, "get ccl ruleId failed %v", err0)
				conn.Close()
				return nil, err0
			}
			log.Info(ctx, "node %s ruleId is %d", tempDs.Address, ruleId)
			cclDeleteSql = fmt.Sprintf(`ccl delete(%d);`, ruleId)
			if err = conn.Exec(cclDeleteSql); err != nil {
				log.Warn(ctx, "node %s delete ccl rule failed %v", tempDs.Address, err)
				conn.Close()
				return nil, err
			}
			conn.Close()
		}
	}
	return &datasource.DeleteSQLCCLRuleResp{}, nil
}

// FlushSQLCCLRule veDB add/delete不需要单独flush
func (self *vedbImpl) FlushSQLCCLRule(ctx context.Context, req *datasource.FlushSQLCCLRuleReq) (*datasource.FlushSQLCCLRuleResp, error) {
	return &datasource.FlushSQLCCLRuleResp{}, nil
}
func (self *vedbImpl) ListSQLCCLRules(ctx context.Context, req *datasource.ListSQLCCLRulesReq) (*datasource.ListSQLCCLRulesResp, error) {
	// 获取pod
	pods, err := self.ListInstancePods(ctx, &datasource.ListInstancePodsReq{Type: req.Type, LinkType: shared.Volc, InstanceId: req.InstanceId})
	if err != nil {
		log.Warn(ctx, " List ccl rules failed due to acquiring pods failed:%s", err)
		return nil, err
	}
	c3cfg := self.C3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	var allRules []*datasource.VeDBCCLRuleItem
	//建连
	ds := &shared.DataSource{
		Type:     shared.DataSourceType(model.DSType_VeDBMySQL),
		LinkType: shared.Volc,
		User:     c3cfg.DBWAccountName,
		Password: handler.GetAccountPassword(c3cfg.DbwAccountPasswordGenKey, req.InstanceId),
		Db:       "information_schema",
	}
	// List CCL rules
	ret := &datasource.ListSQLCCLRulesResp{}
	sqlText := "ccl show;"
	// 获取所有节点的ccl规则
	for _, pod := range pods.Data {
		if pod.Component == "dbengine" {
			var tempCclRules []*datasource.VeDBCCLRuleItem
			tempDs := ds
			addr := fmt.Sprintf("%s:%s", pod.PodIP, pod.Containers[0].Port) // IP:dbEngine-port
			tempDs.Address = addr
			conn, err := self.getConn(ctx, ds)
			if err != nil {
				log.Warn(ctx, "connect to datasource %s fail %v", utils.Show(tempDs), err)
				adminAddr := fmt.Sprintf("%s:%s", pod.PodIP, pod.Containers[0].AdminPort)
				tempDs.Address = adminAddr
				connAdmin, err01 := self.getConn(ctx, tempDs)
				if err01 != nil {
					log.Warn(ctx, "connect to datasource with AdminPort %s fail %v", utils.Show(tempDs), err)
					connAdmin.Close()
					continue
				} else {
					if err = connAdmin.Raw(sqlText).Scan(&tempCclRules); err != nil {
						log.Warn(ctx, "node %s list ccl rule failed %v", adminAddr, err)
						connAdmin.Close()
						continue
					}
					connAdmin.Close()
				}
				allRules = append(allRules, tempCclRules...)
			} else {
				if err = conn.Raw(sqlText).Scan(&tempCclRules); err != nil {
					log.Warn(ctx, "node %s list ccl rule failed %v", tempDs.Address, err)
					conn.Close()
					continue
				}
				conn.Close()
				allRules = append(allRules, tempCclRules...)
			}
		}
	}
	log.Info(ctx, "all rules is %v", utils.Show(allRules))
	resultMap := make(map[string]*datasource.VeDBCCLRuleItem)
	for _, r := range allRules {
		temp := &datasource.VeDBCCLRuleItem{
			Keywords: r.Keywords,
			SqlType:  r.SqlType,
		}
		hashValue := hashRule(temp)
		if existing, exists := resultMap[hashValue]; exists {
			// 合并相同规则的记录
			// 保留更小的ID
			if r.RuleID < existing.RuleID {
				existing.RuleID = r.RuleID
			}
			existing.Rejected += r.Rejected
			resultMap[hashValue] = existing
		} else {
			// 新Rule记录
			resultMap[hashValue] = r
		}
	}
	log.Info(ctx, "resultMap is %v", utils.Show(resultMap))
	// 转换为切片并排序
	results := make([]*datasource.VeDBCCLRuleItem, 0, len(resultMap))
	for _, r := range resultMap {
		results = append(results, r)
	}
	if err = fp.StreamOf(results).Map(func(e *datasource.VeDBCCLRuleItem) *datasource.CCLRuleInfo {
		return &datasource.CCLRuleInfo{
			RuleID:           e.RuleID,
			UserID:           e.UserName,
			HostName:         e.HostName,
			SchemaName:       e.SchemaName,
			TableName:        e.TableName,
			PartitionName:    e.PartitionName,
			Keywords:         e.Keywords,
			State:            e.State,
			Rejected:         e.Rejected,
			WaitTimeout:      e.WaitTimeout,
			ConcurrencyCount: e.ConcurrencyCount,
			Ordered:          e.Ordered,
			SqlType:          e.SqlType,
		}
	}).ToSlice(&ret.CCLRules); err != nil {
		return nil, err
	}
	log.Info(ctx, "ret is %v", utils.Show(ret))
	return ret, nil
}

func (self *vedbImpl) DescribeSQLCCLConfig(ctx context.Context, req *datasource.DescribeSQLCCLConfigReq) (*datasource.DescribeSQLCCLConfigResp, error) {
	return &datasource.DescribeSQLCCLConfigResp{
		SQLConcurrencyControlStatus: true,
	}, nil
}

func (self *vedbImpl) GetCCLRuleIdByKeywords(ctx context.Context, conn db.Conn, ruleInfo *datasource.CCLRuleInfo) (int64, error) {
	var ret []*datasource.CCLRuleInfo
	cclShowSql := fmt.Sprintf("ccl show;")
	var cclRules []*datasource.VeDBCCLRuleItem
	if err := conn.Raw(cclShowSql).Scan(&cclRules); err != nil {
		log.Warn(ctx, "list ccl rule failed %v", err)
		return -1, err
	}
	if err := fp.StreamOf(cclRules).Map(func(e *datasource.VeDBCCLRuleItem) *datasource.CCLRuleInfo {
		return &datasource.CCLRuleInfo{
			RuleID:           e.RuleID,
			UserID:           e.UserName,
			HostName:         e.HostName,
			SchemaName:       e.SchemaName,
			TableName:        e.TableName,
			PartitionName:    e.PartitionName,
			Keywords:         e.Keywords,
			State:            e.State,
			Rejected:         e.Rejected,
			WaitTimeout:      e.WaitTimeout,
			ConcurrencyCount: e.ConcurrencyCount,
			Ordered:          e.Ordered,
			SqlType:          e.SqlType,
		}
	}).ToSlice(&ret); err != nil {
		return -1, err
	}
	log.Info(ctx, "list ccl rules ret is %s", ret)
	for _, item := range ret {
		if strings.EqualFold(item.Keywords, trimKeyword(ruleInfo.Keywords)) && item.SqlType == ruleInfo.SqlType {
			return item.RuleID, nil
		}
	}
	return -1, errors.New("keywords not found")
}
func trimKeyword(keywords string) string {
	var keySlice []string
	temp := strings.Split(keywords, ",")
	for _, item := range temp {
		str := strings.TrimSpace(item)
		if str != "" {
			keySlice = append(keySlice, str)
		}
	}
	return strings.Join(keySlice, ",")
}
func (self *vedbImpl) ExecuteCCL(ctx context.Context, req *datasource.ExecuteCCLReq) (*datasource.ExecuteCCLResp, error) {
	return &datasource.ExecuteCCLResp{}, nil
}
func (self *vedbImpl) GetManagedAccountAndPwd(ctx context.Context, req *shared.DataSource) (*datasource.GetManagedAccountAndPwdResp, error) {
	convertedDs := &model.DataSource{
		Type:       model.DSType_VeDBMySQL,
		InstanceId: utils.StringRef(req.InstanceId),
		LinkType:   model.LinkType_Volc,
	}
	if err := self.userMgmtSvc.GetDBAccount(ctx, convertedDs); err != nil {
		return nil, err
	}
	log.Info(ctx, "userName:%s,pwd:%s", convertedDs.GetUsername(), convertedDs.GetPassword())
	return &datasource.GetManagedAccountAndPwdResp{
			AccountName:     convertedDs.GetUsername(),
			AccountPassword: convertedDs.GetPassword()},
		nil

}

func (m *vedbImpl) DescribeTableNoPrimaryKey(ctx context.Context, req *datasource.DescribeTableNoPrimaryKeyReq) (ret *datasource.ListTablesResp, err error) {
	ret = &datasource.ListTablesResp{}
	err = m.ConnPool.Call(
		ctx, &datasource.GetConnReq{
			Ds:    req.Source,
			Admin: true,
		}, func(conn datasource.Conn) error {
			// 查询表是否有主键
			queryPrimaryKey := fmt.Sprintf("/*DBW SQL CONSOLE DEFAULT*/ "+
				"SELECT TABLE_SCHEMA, TABLE_NAME FROM information_schema.TABLES "+
				"WHERE TABLE_TYPE = 'BASE TABLE' "+
				"AND TABLE_SCHEMA NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys', 'byte_rds_meta', 'ndb_meta_info') "+
				"AND TABLE_NAME NOT IN (SELECT TABLE_NAME FROM information_schema.TABLE_CONSTRAINTS WHERE CONSTRAINT_TYPE = 'PRIMARY KEY') "+
				"LIMIT %d OFFSET %d;", req.Limit, req.Offset)
			type tableItem struct {
				TableSchema string `gorm:"column:TABLE_SCHEMA"`
				TableName   string `gorm:"column:TABLE_NAME"`
			}
			var items []*tableItem
			err = conn.Raw(queryPrimaryKey).Scan(&items)
			if err != nil {
				return err
			}
			for _, item := range items {
				ret.Items = append(ret.Items, item.TableName)
				ret.Tables = append(ret.Tables, &datasource.Table{
					Name:   item.TableName,
					Schema: item.TableSchema,
				})
			}
			totalQuery := fmt.Sprintf("/* DBW SQL CONSOLE DEFAULT*/ "+
				"SELECT COUNT(1) FROM information_schema.TABLES "+
				"WHERE TABLE_TYPE = 'BASE TABLE' "+
				"AND TABLE_SCHEMA NOT IN ('mysql', 'information_schema', 'performance_schema', 'sys', 'byte_rds_meta', 'ndb_meta_info') "+
				"AND TABLE_NAME NOT IN (SELECT TABLE_NAME FROM information_schema.TABLE_CONSTRAINTS WHERE CONSTRAINT_TYPE = 'PRIMARY KEY') "+
				"LIMIT %d OFFSET %d;", req.Limit, req.Offset)
			var total int64
			err = conn.Raw(totalQuery).Scan(&total)
			if err != nil {
				return err
			}
			ret.Total = total
			return nil
		},
	)
	if err != nil {
		log.Error(ctx, "get conn failed %v", err)
		return
	}
	return
}

// 暂不支持分页
func (self *vedbImpl) DescribeTableIndex(ctx context.Context, req *datasource.DescribeTableInfoReq) (ret *shared.DescribeTableIndexResp, err error) {
	ret = &shared.DescribeTableIndexResp{}
	err = self.ConnPool.Call(
		ctx, &datasource.GetConnReq{
			Ds:    req.Source,
			Admin: true,
		}, func(conn datasource.Conn) error {
			type (
				indexInfo struct {
					IndexName  string `json:"index_name"`
					ColumnName string `json:"column_name"`
				}
			)
			var indexInfos []indexInfo

			// detail
			detailSQl := fmt.Sprintf(
				"/*+ DBW SQL CONSOLE DEFAULT*/ SELECT index_name, column_name FROM information_schema.statistics WHERE table_name = '%s' AND table_schema = '%s'",
				req.Table, req.Database,
			) // ignore_security_alert
			if err = conn.Raw(detailSQl).
				Scan(&indexInfos); err != nil {
				return err
			}

			// 按索引分组并合并列名
			indexes := make(map[string][]string)
			for _, info := range indexInfos {
				indexes[info.IndexName] = append(indexes[info.IndexName], info.ColumnName)
			}

			// 将每个索引的列名转换为逗号分隔的字符串
			for indexName, columns := range indexes {
				ret.IndexStats = append(
					ret.IndexStats, &shared.IndexStat{
						IndexName: indexName,
						Columns:   strings.Join(columns, ","),
					},
				)
				ret.Total++
			}
			return nil
		},
	)
	if err != nil {
		log.Warn(ctx, "DescribeTableIndex-err %v", err)
	}
	return
}
