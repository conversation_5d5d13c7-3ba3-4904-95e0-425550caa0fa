package handler

import (
	"code.byted.org/infcs/dbw-mgr/biz/service/usermgmt"
	bizUtils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/persistence"
	"context"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"

	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
)

// DropDatabase drop the database specified in req
func (h *DropDatabaseHandler) DropDatabase(ctx context.Context, req *model.DropDatabaseReq) (*model.DropDatabaseResp, error) {
	if req.GetDB() == "" {
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}
	source, err := validateConnection(ctx, req, h.cli, h.peeker)
	if err != nil {
		return nil, err
	}
	pri := h.priSvc.CheckDatabasePrivilegeBySession(ctx, req.GetDB(), source, true, model.DbwPrivilegeType_CORRECT)
	if pri != "" {
		return nil, consts.ErrorWithParam(model.ErrorCode_SqlPrivilegeCheckFailed, pri)
	}
	cmd := "DROP DATABASE `" + strings.TrimSpace(req.GetDB()) + "`"
	resp, err := h.executeCommandSetHandler.ExecuteCommandSet(ctx, &model.ExecuteCommandSetReq{
		SessionId:         req.GetSessionId(),
		ConnectionId:      req.GetConnectionId(),
		CommandSetContent: utils.StringRef(cmd),
	})
	if err != nil {
		log.Warn(ctx, "fail to run executeCommandSet, err=%v\"", err)
		return nil, err
	}
	// Wait command set result, cancel if timeout.
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()
	timeout := time.NewTimer(10 * time.Second)
	for {
		select {
		case <-ticker.C:
			if ok, err := h.checkCommandSetFinished(ctx, resp.GetCommandSetId()); ok {
				if err != nil {
					return nil, err
				}
				// 不落库查询需要手动DescribeCommand一次
				cnf := h.describeCommandHandler.cnf.Get(ctx)
				if bizUtils.IsTenantEnabledFromCtx(ctx, cnf.DBLessTenantIdList) {
					command := resp.GetCommands()[0]
					descRsp, err := h.describeCommandHandler.DescribeCommand(ctx, &model.DescribeCommandReq{
						CommandId: command.CommandId,
					})
					if err != nil {
						return nil, err
					}
					if descRsp != nil {
						log.Info(ctx, "db less tenant, manual describe command, rows=%+v", descRsp.Rows)
					}
				}
				return &model.DropDatabaseResp{}, nil
			}
		case <-timeout.C:
			log.Warn(ctx, "drop database timeout, command set %s", resp.GetCommandSetId())
			h.cancelCommandSetHandler.CancelCommandSet(ctx, &model.CancelCommandSetReq{
				CommandSetId: utils.StringRef(resp.GetCommandSetId()),
			})
			// TODO: 是否需要定义"ErrorCode_CommandSetExecuteTimeout ErrorCode = -300014" 超时错误？
			err = consts.ErrorOf(model.ErrorCode_CommandSetExecuteTimeout)
			return nil, err
		}
	}

}

func (h *DropDatabaseHandler) checkCommandSetFinished(ctx context.Context, csID string) (bool, error) {
	resp, err := h.describeCommandSetHandler.DescribeCommandSet(ctx, &model.DescribeCommandSetReq{
		CommandSetId: utils.StringRef(csID),
	})
	if err != nil {
		log.Warn(ctx, "DescribeCommandSet set %s error", csID)
		return false, err
	}
	// Check the execution progress of commandSet.
	if resp.GetEndTime() == 0 {
		return false, nil
	}
	log.Info(ctx, "command set %s ended", csID)

	// All command in CommandSet is terminated.
	if len(resp.GetCommands()) <= 0 {
		log.Warn(ctx, "command set %s has no command", csID)
		// TODO:这里使用InternalError是否正确？
		return true, consts.ErrorOf(model.ErrorCode_CommandSetEmpty)
	}
	if reason := resp.GetCommands()[0].GetReason(); reason != model.CommandTerminatedReason_Success {
		log.Warn(ctx, "command set %s failed by %s", csID, reason)
		// TODO:这里使用ErrorCode_DataSourceOpFail是否正确？
		return true, consts.ErrorOf(model.ErrorCode_DataSourceOpFail)
	}
	return true, nil
}

// DropDatabaseHandler represents a handler to drop database.
type DropDatabaseHandler struct {
	executeCommandSetHandler  *ExecuteCommandSetHandler
	describeCommandSetHandler *DescribeCommandSetHandler
	cancelCommandSetHandler   *CancelCommandSetHandler
	describeCommandHandler    *DescribeCommandHandler
	priSvc                    usermgmt.PrivilegeServiceInterface
	cli                       cli.ActorClient
	peeker                    persistence.ActorStoragePeeker
}

// NewDropDatabaseHandler returns a new envelope of dropDatabaseHandler.
func NewDropDatabaseHandler(
	executeCommandSetHandler *ExecuteCommandSetHandler,
	describeCommandSetHandler *DescribeCommandSetHandler,
	cancelCommandSetHandler *CancelCommandSetHandler,
	describeCommandHandler *DescribeCommandHandler,
	priSvc usermgmt.PrivilegeServiceInterface,
	cli cli.ActorClient,
	peeker persistence.ActorStoragePeeker,

) HandlerImplementationEnvolope {
	hder := &DropDatabaseHandler{
		executeCommandSetHandler:  executeCommandSetHandler,
		describeCommandSetHandler: describeCommandSetHandler,
		cancelCommandSetHandler:   cancelCommandSetHandler,
		describeCommandHandler:    describeCommandHandler,
		priSvc:                    priSvc,
		cli:                       cli,
		peeker:                    peeker,
	}
	return NewHandler(hder.DropDatabase)
}
