package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"testing"
)

func mockSubmitTicketHandler() *SubmitTicketHandler {
	return &SubmitTicketHandler{
		service: &mocks.MockTicketService{},
	}
}

func TestNewSubmitTicketHandler(t *testing.T) {
	NewSubmitTicketHandler(nil, nil)
}

func TestSubmitTicketHandler(t *testing.T) {
	handler := mockSubmitTicketHandler()
	req := &model.SubmitTicketReq{
		TicketId: "",
	}
	mock1 := mockey.Mock((*mocks.MockTicketService).SubmitTicket).Return(nil, fmt.Errorf("err")).Build()
	defer mock1.UnPatch()
	handler.SubmitTicket(context.Background(), req)

	req1 := &model.SubmitTicketReq{
		TicketId: "1",
	}
	handler.SubmitTicket(context.Background(), req1)

}
