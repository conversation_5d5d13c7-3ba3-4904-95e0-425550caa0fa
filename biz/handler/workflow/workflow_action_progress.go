package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/http"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strings"
	"time"
)

const DBWWorkFlowAction = "WorkflowAction"

// NewWorkflowActionProgressHandler 这个接口,是给小基架工单回调火山DBW的接口
func NewWorkflowActionProgressHandler(service workflow.TicketService, cli http.Client) handler.HandlerImplementationEnvolope {
	h := &WorkflowActionProgressHandler{
		service: service,
		cli:     cli,
	}
	return handler.NewHandler(h.WorkflowActionProgress)
}

type WorkflowActionProgressHandler struct {
	service workflow.TicketService
	cli     http.Client
}

func (h *WorkflowActionProgressHandler) WorkflowActionProgress(ctx context.Context, req *model.WorkflowActionReq) (*model.WorkflowActionResp, error) {
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}
	log.Info(ctx, "bizContex is %v", fwctx.GetBizContext(ctx))
	extra := fwctx.GetExtra(ctx)
	log.Info(ctx, "extra is %v", utils.Show(extra))
	var (
		tenantId, jwtStr, volcRegion, multiEndPoint, byteCloudEnv, instanceId, dbType string
	)
	if _, ok := extra["X-Bytecloud-Tenantid"]; ok {
		tenantId = extra["X-Bytecloud-Tenantid"]
	}
	if _, ok := extra["X-Jwt-Token"]; ok {
		jwtStr = extra["X-Jwt-Token"]
	}
	// 多云平台的火山实例,取这个
	if jwtStr == "" {
		if _, ok := extra["X-Jwt-Dbw"]; ok {
			jwtStr = extra["X-Jwt-Dbw"]
		}
	}
	if _, ok := extra["X-Instance-Id"]; ok {
		instanceId = extra["X-Instance-Id"]
		dbType = GetDBType(strings.Split(instanceId, "-")[0])
	}
	if _, ok := extra["X-Volc-Region"]; ok {
		volcRegion = extra["X-Volc-Region"]
	}
	if _, ok := extra["X-Bytecloud-Endpoint"]; ok {
		multiEndPoint = extra["X-Bytecloud-Endpoint"]
	}
	if tenantId == "" || jwtStr == "" || volcRegion == "" || multiEndPoint == "" {
		msg := fmt.Sprintf("get empty param from extra error,[%v],[%v],[%v],[%v]", tenantId, jwtStr, volcRegion, multiEndPoint)
		log.Warn(ctx, msg)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, msg)
	}
	log.Info(ctx, "tenantid is %v,jwtStr is %v, volcRegion is %v,multiEndpoint is %v", tenantId, jwtStr, volcRegion, multiEndPoint)
	if _, ok := extra["X-Bytecloud-Env"]; ok {
		byteCloudEnv = extra["X-Bytecloud-Env"]
	}
	log.Info(ctx, "bytecloud env is %v", byteCloudEnv)
	accountMeta := fmt.Sprintf("main_account_id=%v", tenantId)
	//var conf *volcengine.Config
	var result struct {
		Code   string `json:"Code"`
		ErrMsg string `json:"ErrMsg"`
	}
	log.Info(ctx, "use boe env")
	meta, err := h.doRequest(ctx, &reqData{
		version:     DBWVersion,
		action:      DBWWorkFlowAction,
		environment: byteCloudEnv,

		endpoint:    multiEndPoint,
		accountMeta: accountMeta,
		instanceId:  instanceId,
		dsType:      dbType,
		jwtStr:      jwtStr,
		service:     "dbw",

		body: map[string]interface{}{
			"TicketId":   req.TicketId,
			"ActionType": req.ActionType.String(),
			"InstanceId": instanceId,
			"DSType":     dbType,
		},
	}, &result)
	if err != nil {
		log.Warn(ctx, "do request error %v", err)
		return &model.WorkflowActionResp{
			Code: model.ErrCode_Error, ErrMsg: err.Error(),
		}, err
	}
	if meta.NoError() {
		log.Info(ctx, "result is %v,meta is %v", result, meta)
		return &model.WorkflowActionResp{
			Code:   model.ErrCode_Success,
			ErrMsg: "workflow pass success",
		}, nil
	}
	log.Warn(ctx, "meta is %v", meta)
	log.Warn(ctx, "workflow action error %v", meta.Error.Message)
	return &model.WorkflowActionResp{
		Code: model.ErrCode_Error, ErrMsg: meta.Error.Message,
	}, errors.New(meta.Error.Code)
}

func (h *WorkflowActionProgressHandler) checkReq(ctx context.Context, req *model.WorkflowActionReq) error {
	if strings.Trim(req.GetTicketId(), " ") == "" {
		log.Warn(ctx, "workflow action progress : get empty ticket id")
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "get empty ticket id,please check")
	}
	return nil
}

func (h *WorkflowActionProgressHandler) doRequest(ctx context.Context, reqData *reqData, resp interface{}) (*ResponseMetadata, error) {
	urls := url.Values{}
	urls.Set(`Version`, reqData.version)
	urls.Set(`Action`, reqData.action)
	urls.Set(`InstanceId`, reqData.instanceId)
	urls.Set(`DSType`, reqData.dsType)

	endpoint := fmt.Sprintf("http://%s?%s", reqData.endpoint, urls.Encode())
	if reqData.environment == "boe" {
		h.cli.SetHeaders(map[string]string{
			"x-bcgw-accountmeta": reqData.accountMeta,
			"InstanceId":         reqData.instanceId,
			"DSType":             reqData.dsType,
			"x-jwt-token":        reqData.jwtStr,
			"x-bcgw-producttype": reqData.service,
			"volc-env":           "cn_stable",
		})
	} else {
		h.cli.SetHeaders(map[string]string{
			"x-bcgw-accountmeta": reqData.accountMeta,
			"InstanceId":         reqData.instanceId,
			"DSType":             reqData.dsType,
			"x-jwt-token":        reqData.jwtStr,
			"x-bcgw-producttype": reqData.service,
		})
	}

	reqBody, err := json.Marshal(reqData.body)
	if err != nil {
		log.Warn(ctx, "marshal request body fail %v", err)
		return nil, err
	}
	rsp := h.cli.SetTimeout(30*time.Second).Post(ctx, endpoint, reqBody)
	body, err := rsp.GetBody()
	if err != nil {
		log.Warn(ctx, "request volc fail %s %v", string(body), err)
		return nil, err
	}
	var st struct {
		ResponseMetadata ResponseMetadata `json:"ResponseMetadata"`
		Result           json.RawMessage  `json:"Result"`
	}
	//TODO 这里解析后st里的ResponseMetadata有err
	if err = json.Unmarshal(body, &st); err != nil {
		log.Warn(ctx, "unmarshal volc response fail %s %v", string(body), err)
		return nil, err
	}
	if len(st.Result) > 0 {
		if err = json.Unmarshal(st.Result, resp); err != nil {
			log.Warn(ctx, "unmarshal volc result fail %s,err is %v", string(body), err)
			return nil, err
		}
	}
	return &st.ResponseMetadata, nil
}
