package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/luoshiqi/mockito"
	"context"
	"errors"
	"fmt"
	"github.com/golang/mock/gomock"
	"github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"testing"
)

type DescribeTicketDetailTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

// 测试开始之前需要做的事情
func (suite *DescribeTicketDetailTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

// 测试结束之后需要做的事情
func (suite *DescribeTicketDetailTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestDescribeTicketDetailTestSuite(t *testing.T) {
	suite.Run(t, new(DescribeTicketDetailTestSuite))
}

func TestNewDescribeTicketDetailHandler_OK(t *testing.T) {
	NewDescribeTicketDetailHandler(nil, nil)
}

func (suite *DescribeTicketDetailTestSuite) Test_DescribeTicketDetail() {
	ctx := context.Background()

	ticketService := mocks.NewMockTicketService(suite.ctrl)
	d := DescribeTicketDetailHandler{
		service: ticketService,
	}
	mockito.PatchConvey("DescribeTicketDetail test", suite.T(), func() {
		mockito.PatchConvey("checkReq error", func() {
			req := &model.DescribeTicketDetailReq{
				TicketId: "",
			}
			ret, err := d.DescribeTicketDetail(ctx, req)
			fmt.Println(ret, err)
			convey.So(err, convey.ShouldNotBeNil)
		})

		mockito.PatchConvey("tenantId error", func() {
			req := &model.DescribeTicketDetailReq{
				TicketId: "1234",
			}
			ticketService.EXPECT().DescribeTicketDetail(gomock.Any(), gomock.Any()).Return(nil, errors.New("DescribeTicketDetail error")).Times(1)
			ret, err := d.DescribeTicketDetail(ctx, req)
			fmt.Println(ret, err)
			convey.So(err, convey.ShouldNotBeNil)
		})
	})
}
