package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"strconv"
)

type ExecuteTicketHandler struct {
	service workflow.TicketService
	cnf     config.ConfigProvider
}

func NewExecuteTicketHandler(service workflow.TicketService, cnf config.ConfigProvider) handler.HandlerImplementationEnvolope {
	h := &ExecuteTicketHandler{
		service: service,
		cnf:     cnf,
	}
	return handler.NewHandler(h.ExecuteTicket)
}

func (h *ExecuteTicketHandler) ExecuteTicket(ctx context.Context, req *model.ExecuteTicketReq) (ret *model.ExecuteTicketResp, err error) {
	//ticket, err := h.service.GetTicket(ctx, conv.StrToInt64(req.TicketId, 0))
	//if err != nil {
	//	log.Warn(ctx, "ticketId: %d getTicket error :%v", req.TicketId, err)
	//	return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	//}
	//if !IsVolcInstance(ticket.InstanceType.String()) {
	//	return ForwardExecuteTicketToByteRDS(ctx, byterds.NewByteRDSClient(h.cnf), ticket)
	//}
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}
	// 开始真正地执行逻辑
	return h.service.ExecuteTicket(ctx, req)
}

func (h *ExecuteTicketHandler) checkReq(ctx context.Context, req *model.ExecuteTicketReq) error {
	if req.GetTicketId() == "" {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "ticketId 为空,请检查")
	}
	if _, err := strconv.Atoi(req.TicketId); err != nil {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "ticketId 错误,请检查")
	}
	return nil
}
