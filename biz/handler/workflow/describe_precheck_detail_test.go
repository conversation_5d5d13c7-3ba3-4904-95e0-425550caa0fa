package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"testing"
)

func mockDescribePreCheckDetailHandler() *DescribePreCheckDetailHandler {
	return &DescribePreCheckDetailHandler{
		service: &mocks.MockTicketService{},
	}
}

func TestNewDescribePreCheckDetailHandler(t *testing.T) {
	NewDescribePreCheckDetailHandler(nil, nil)
}

func TestDescribePreCheckDetailHandler(t *testing.T) {
	handler := mockDescribePreCheckDetailHandler()
	req := &model.DescribePreCheckDetailReq{
		TicketId: "",
	}
	mock1 := mockey.Mock((*mocks.MockTicketService).DescribePreCheckDetail).Return(nil, fmt.Errorf("err")).Build()
	defer mock1.UnPatch()
	handler.DescribePreCheckDetail(context.Background(), req)

	req1 := &model.DescribePreCheckDetailReq{
		TicketId: "1",
	}
	handler.DescribePreCheckDetail(context.Background(), req1)

}
