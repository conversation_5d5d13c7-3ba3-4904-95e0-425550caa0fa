package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
)

func NewSubmitTicketHandler(service workflow.TicketService, cnf config.ConfigProvider) handler.HandlerImplementationEnvolope {
	h := &SubmitTicketHandler{
		service: service,
		cnf:     cnf,
	}
	return handler.NewHandler(h.SubmitTicket)
}

type SubmitTicketHandler struct {
	service workflow.TicketService
	cnf     config.ConfigProvider
}

func (h *SubmitTicketHandler) SubmitTicket(ctx context.Context, req *model.SubmitTicketReq) (*model.SubmitTicketResp, error) {
	//ticket, err := h.service.GetTicket(ctx, conv.StrToInt64(req.TicketId, 0))
	//if err != nil {
	//	log.Warn(ctx, "ticketId: %d getTicket error :%v", req.TicketId, err)
	//	return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	//}
	//if !IsVolcInstance(ticket.InstanceType.String()) {
	//	return ForwardSubmitTicketToByteRDS(ctx, byterds.NewByteRDSClient(h.cnf), ticket)
	//}
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}
	return h.service.SubmitTicket(ctx, req)
}

func (h *SubmitTicketHandler) checkReq(ctx context.Context, req *model.SubmitTicketReq) error {
	if req.GetTicketId() == "" {
		log.Warn(ctx, "stopTicket: 工单id错误,请检查")
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "工单id错误,请检查")
	}
	if req.OnlineDDlConfig != nil && req.OnlineDDlConfig.GetChangeTableLockTimeout() > 10 {
		log.Warn(ctx, "stopTicket: online ddl LockTimeout 错误,请检查")
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "online ddl 切表超时最大为10s")
	}
	return nil
}
