package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/luoshiqi/mockito"
	"context"
	"fmt"
	"github.com/golang/mock/gomock"
	"github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"testing"
)

type ExecuteTicketTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

// 测试开始之前需要做的事情
func (suite *ExecuteTicketTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

// 测试结束之后需要做的事情
func (suite *ExecuteTicketTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestExecuteTicketTestSuite(t *testing.T) {
	suite.Run(t, new(ExecuteTicketTestSuite))
}

func TestNewExecuteTicketHandler_OK(t *testing.T) {
	NewExecuteTicketHandler(nil, nil)
}

func (suite *ExecuteTicketTestSuite) Test_ExecuteTicket() {
	ctx := context.Background()

	ticketService := mocks.NewMockTicketService(suite.ctrl)
	d := ExecuteTicketHandler{
		service: ticketService,
	}
	mockito.PatchConvey("DescribeTickets test", suite.T(), func() {
		mockito.PatchConvey("checkReq ticketId error", func() {
			req := &model.ExecuteTicketReq{
				TicketId: "abc",
			}
			ret, err := d.ExecuteTicket(ctx, req)
			fmt.Println(ret, err)
			convey.So(err, convey.ShouldNotBeNil)
		})
		mockito.PatchConvey("checkReq ticketId nil error", func() {
			req := &model.ExecuteTicketReq{
				TicketId: "",
			}
			ret, err := d.ExecuteTicket(ctx, req)
			fmt.Println(ret, err)
			convey.So(err, convey.ShouldNotBeNil)
		})
		mockito.PatchConvey("checkReq ticketId ok", func() {
			req := &model.ExecuteTicketReq{
				TicketId: "111",
			}
			ticketService.EXPECT().ExecuteTicket(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)

			ret, err := d.ExecuteTicket(ctx, req)
			fmt.Println(ret, err)
			convey.So(err, convey.ShouldBeNil)
		})
	})
}
