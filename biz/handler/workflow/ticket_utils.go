package workflow

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/middleware"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/byterds"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/http"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	sysHttp "net/http"
	"net/url"
	"os"
	"strings"
	"time"
)

const (
	BPMAuthTypeBasicAuth = "BasicAuth"
	BPMAuthTypeJWT       = "Jwt"
	getBasicInfoPath     = "/openapi/multi_cloud/v1/database/get_volc_db_basic_info/"
)

type BasicInfoResp struct {
	Code int        `json:"code"`
	Msg  string     `json:"msg"`
	Data *BasicInfo `json:"data"`
}

type BasicInfo struct {
	DBName        string `json:"db_name"`
	VolcAccountId string `json:"volc_account_id"`
	VolcRegion    string `json:"volc_region"`
}

type TopResponse struct {
	ResponseMetadata struct {
		RequestId string `json:"RequestId"`
		Action    string `json:"Action"`
		Version   string `json:"Version"`
		Service   string `json:"Service"`
		Region    string `json:"Region"`
		Error     *struct {
			Code    string `json:"Code"`
			Message string `json:"Message"`
		} `json:"Error"`
	}
	Result json.RawMessage `json:"Result"`
}

type ActionInfo struct {
	instanceId string
	dsType     string
	tenant     string
	region     string
	action     string
}

func ForwardCreateTicketToByteRDS(ctx context.Context, cli byterds.ByteRDSClient, req *model.CreateTicketReq) (*model.CreateTicketResp, error) {
	basicInfo, err := GetVolcRDSBasicInfo(ctx, cli, req.InstanceId)
	if err != nil {
		return nil, err
	}
	var actionInfo = &ActionInfo{
		instanceId: req.InstanceId,
		dsType:     req.InstanceType.String(),
		tenant:     basicInfo.VolcAccountId,
		region:     basicInfo.VolcRegion,
		action:     "CreateTicket",
	}
	var body = map[string]interface{}{
		"InstanceType":      req.InstanceType,
		"InstanceID":        req.InstanceId,
		"SqlText":           req.SqlText,
		"TicketExecuteType": req.TicketExecuteType,
		"TicketType":        req.TicketType,
		"CreateUser":        req.CreateUser,
		"DatabaseName":      req.DatabaseName,
		"ExecStartTime":     req.ExecStartTime,
		"ExecEndTime":       req.ExecEndTime,
		"BatchConfig":       req.BatchConfig,
		"ArchiveConfig":     req.ArchiveConfig,
		"CreateFrom":        req.CreateFrom,
		"Memo":              req.Memo,
		"Title":             req.Title,
		"CreateUserName":    req.CreateUserName,
	}
	res, err := CallMultiCloudProxy(ctx, body, actionInfo)
	if err != nil {
		return nil, err
	}
	var resp = &model.CreateTicketResp{}
	if err = json.Unmarshal(res, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func GetVolcRDSBasicInfo(ctx context.Context, cli byterds.ByteRDSClient, instanceId string) (*BasicInfo, error) {
	var region string
	// 测试环境
	// TCE_ZONE=China-BOE2
	// TCE_INTERNAL_IDC = BOE2
	// 线上环境
	// TCE_ZONE=China-North
	// TCE_INTERNAL_IDC=LF
	tceZone := os.Getenv("TCE_ZONE")
	region = getRegionByTceZone(tceZone)
	params := map[string]string{
		"region":           region,
		"volc_instance_id": instanceId,
	}
	log.Info(ctx, "get volc rds basic info region is %v", region)
	resp := cli.Get(ctx, getBasicInfoPath, params, nil, region, true)
	if resp == nil || resp.StatusCode != 200 {
		log.Error(ctx, "Get volc rds basic info error")
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	info, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var basicInfoResp BasicInfoResp
	if err := json.Unmarshal(info, &basicInfoResp); err != nil {
		return nil, err
	}
	return basicInfoResp.Data, nil
}

func getRegionByTceZone(tceZone string) string {
	switch tceZone {
	case "China-North":
		return "cn"
	default:
		return "boe"
	}
}

func CallMultiCloudProxy(ctx context.Context, body map[string]interface{}, info *ActionInfo) ([]byte, error) {
	headers := buildHeaders(ctx, info.instanceId, info.dsType, info.tenant)
	params := map[string]string{
		"Version":    DBWVersion,
		"Region":     info.region,
		"Action":     info.action,
		"instanceId": info.instanceId,
		"dsType":     info.dsType,
	}
	resp, err := doRequest(ctx, headers, body, params)
	if err != nil {
		log.Error(ctx, "doRequest error: %+v", err)
		return []byte{}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if resp.ResponseMetadata.Error != nil {
		log.Error(ctx, "doRequest error: %+v, RequestId %s", resp.ResponseMetadata.Error, resp.ResponseMetadata.RequestId)
		errorCode, err := model.ErrorCodeFromString(resp.ResponseMetadata.Error.Code)
		if err != nil {
			log.Error(ctx, "ErrorCodeFromString error: %+v", err)
			var opts []middleware.StandardOption
			opts = append(opts, middleware.WithHTTPCode(sysHttp.StatusForbidden))
			opts = append(opts, middleware.WithMsg(resp.ResponseMetadata.Error.Message))
			return []byte{}, middleware.NewCustomStandardError(opts...)
		}
		return []byte{}, consts.ErrorOf(errorCode)
	}
	result := resp.Result
	out, err := json.Marshal(result)
	if err != nil {
		log.Error(ctx, "Marshal result error: %+v", err)
		return []byte{}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	log.Info(ctx, "forward success")
	return out, nil
}

func buildHeaders(ctx context.Context, instanceId string, instanceType string, accountId string) map[string]string {
	extra := fwctx.GetExtra(ctx)
	var (
		jwtStr string
	)
	if _, ok := extra["X-Jwt-Token"]; ok {
		jwtStr = extra["X-Jwt-Token"]
	}
	var headers map[string]string
	if env.IsBoe() {
		headers = map[string]string{
			"x-bcgw-accountmeta": fmt.Sprintf("main_account_id=%v", accountId),
			"instanceId":         instanceId,
			"dSType":             instanceType,
			"x-jwt-token":        jwtStr,
			"x-bcgw-producttype": "dbw",
			//"volc-env":           "cn_stable",
			"x-tt-trace-id": fwctx.GetExtra(ctx)["X-Tt-Trace-Id"],
			"x-tt-logid":    fwctx.GetExtra(ctx)["X-Tt-Logid"],
		}
	} else {
		headers = map[string]string{
			"x-bcgw-accountmeta": fmt.Sprintf("main_account_id=%v", accountId),
			"InstanceId":         instanceId,
			"DSType":             instanceType,
			"x-jwt-token":        jwtStr,
			"x-bcgw-producttype": "dbw",
			"x-tt-trace-id":      fwctx.GetExtra(ctx)["X-Tt-Trace-Id"],
			"x-tt-logid":         fwctx.GetExtra(ctx)["X-Tt-Logid"],
		}
	}
	return headers
}

func doRequest(ctx context.Context, headers map[string]string, body map[string]interface{}, param map[string]string) (*TopResponse, error) {
	urls := url.Values{}
	for k, v := range param {
		urls.Set(k, v)
	}
	multiCloudHost := os.Getenv("MULTI_CLOUD_HOST")
	endpoint := fmt.Sprintf("http://%s?%s", multiCloudHost, urls.Encode())
	cli := http.NewClient()
	cli.SetHeaders(headers)
	reqBody, err := json.Marshal(body)
	if err != nil {
		log.Warn(ctx, "marshal request body fail %v", err)
		return nil, err
	}
	log.Info(ctx, "request volc endpoint: %s, body: %s", endpoint, string(reqBody))
	rsp := cli.SetTimeout(30*time.Second).Post(ctx, endpoint, reqBody)
	log.Info(ctx, "request volc rsp: %s", utils.Show(rsp))
	respBody, err := rsp.GetBody()
	if err != nil {
		log.Warn(ctx, "request volc fail %v, logId %s traceId %s", err, headers["X-Tt-Logid"], headers["X-Tt-Trace-Id"])
		return nil, err
	}
	var res TopResponse
	if err = json.Unmarshal(respBody, &res); err != nil {
		log.Warn(ctx, "unmarshal volc response fail %s", err)
		return nil, err
	}
	return &res, nil
}

func IsVolcInstance(instanceType string) bool {
	return !strings.HasPrefix(instanceType, "Byte")
}

func ForwardDescribePreCheckDetailToByteRDS(ctx context.Context, cli byterds.ByteRDSClient, ticket *shared.Ticket) (*model.DescribePreCheckDetailResp, error) {
	basicInfo, err := GetVolcRDSBasicInfo(ctx, cli, ticket.InstanceId)
	if err != nil {
		return nil, err
	}
	var actionInfo = &ActionInfo{
		instanceId: ticket.InstanceId,
		dsType:     ticket.InstanceType.String(),
		tenant:     basicInfo.VolcAccountId,
		region:     basicInfo.VolcRegion,
		action:     "CreateTicket",
	}
	var body = map[string]interface{}{
		"TicketId": ticket.TicketId,
	}
	res, err := CallMultiCloudProxy(ctx, body, actionInfo)
	if err != nil {
		return nil, err
	}
	var resp = &model.DescribePreCheckDetailResp{}
	if err = json.Unmarshal(res, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func ForwardDescribeTicketDetailToByteRDS(ctx context.Context, cli byterds.ByteRDSClient, ticket *shared.Ticket) (*model.DescribeTicketDetailResp, error) {
	basicInfo, err := GetVolcRDSBasicInfo(ctx, cli, ticket.InstanceId)
	if err != nil {
		return nil, err
	}
	var actionInfo = &ActionInfo{
		instanceId: ticket.InstanceId,
		dsType:     ticket.InstanceType.String(),
		tenant:     basicInfo.VolcAccountId,
		region:     basicInfo.VolcRegion,
		action:     "CreateTicket",
	}
	var body = map[string]interface{}{
		"TicketId": ticket.TicketId,
	}
	res, err := CallMultiCloudProxy(ctx, body, actionInfo)
	if err != nil {
		return nil, err
	}
	var resp = &model.DescribeTicketDetailResp{}
	if err = json.Unmarshal(res, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func ForwardExecuteTicketToByteRDS(ctx context.Context, cli byterds.ByteRDSClient, ticket *shared.Ticket) (*model.ExecuteTicketResp, error) {
	basicInfo, err := GetVolcRDSBasicInfo(ctx, cli, ticket.InstanceId)
	if err != nil {
		return nil, err
	}
	var actionInfo = &ActionInfo{
		instanceId: ticket.InstanceId,
		dsType:     ticket.InstanceType.String(),
		tenant:     basicInfo.VolcAccountId,
		region:     basicInfo.VolcRegion,
		action:     "ExecuteTicket",
	}
	var body = map[string]interface{}{
		"TicketId": ticket.TicketId,
	}
	res, err := CallMultiCloudProxy(ctx, body, actionInfo)
	if err != nil {
		return nil, err
	}
	var resp = &model.ExecuteTicketResp{}
	if err = json.Unmarshal(res, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func ForwardSubmitTicketToByteRDS(ctx context.Context, cli byterds.ByteRDSClient, ticket *shared.Ticket) (*model.SubmitTicketResp, error) {
	basicInfo, err := GetVolcRDSBasicInfo(ctx, cli, ticket.InstanceId)
	if err != nil {
		return nil, err
	}
	var actionInfo = &ActionInfo{
		instanceId: ticket.InstanceId,
		dsType:     ticket.InstanceType.String(),
		tenant:     basicInfo.VolcAccountId,
		region:     basicInfo.VolcRegion,
		action:     "SubmitTicket",
	}
	var body = map[string]interface{}{
		"TicketId": ticket.TicketId,
	}
	res, err := CallMultiCloudProxy(ctx, body, actionInfo)
	if err != nil {
		return nil, err
	}
	var resp = &model.SubmitTicketResp{}
	if err = json.Unmarshal(res, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func ForwardStopTicketToByteRDS(ctx context.Context, cli byterds.ByteRDSClient, ticket *shared.Ticket) (*model.StopTicketResp, error) {
	basicInfo, err := GetVolcRDSBasicInfo(ctx, cli, ticket.InstanceId)
	if err != nil {
		return nil, err
	}
	var actionInfo = &ActionInfo{
		instanceId: ticket.InstanceId,
		dsType:     ticket.InstanceType.String(),
		tenant:     basicInfo.VolcAccountId,
		region:     basicInfo.VolcRegion,
		action:     "StopTicket",
	}
	var body = map[string]interface{}{
		"TicketId": ticket.TicketId,
	}
	res, err := CallMultiCloudProxy(ctx, body, actionInfo)
	if err != nil {
		return nil, err
	}
	var resp = &model.StopTicketResp{}
	if err = json.Unmarshal(res, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func ForwardWorkflowActionToByteRDS(ctx context.Context, cli byterds.ByteRDSClient, ticket *shared.Ticket) (*model.WorkflowActionResp, error) {
	basicInfo, err := GetVolcRDSBasicInfo(ctx, cli, ticket.InstanceId)
	if err != nil {
		return nil, err
	}
	var actionInfo = &ActionInfo{
		instanceId: ticket.InstanceId,
		dsType:     ticket.InstanceType.String(),
		tenant:     basicInfo.VolcAccountId,
		region:     basicInfo.VolcRegion,
		action:     "StopTicket",
	}
	var body = map[string]interface{}{
		"TicketId":   ticket.TicketId,
		"ActionType": "Pass",
	}
	res, err := CallMultiCloudProxy(ctx, body, actionInfo)
	if err != nil {
		return nil, err
	}
	var resp = &model.WorkflowActionResp{}
	if err = json.Unmarshal(res, resp); err != nil {
		return nil, err
	}
	return resp, nil
}
