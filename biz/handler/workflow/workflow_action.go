package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/data_migration"
	"code.byted.org/infcs/dbw-mgr/biz/service/message"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"go.uber.org/dig"
	"strconv"
)

func NewWorkflowActionHandler(in NewActionServiceIn) handler.HandlerImplementationEnvolope {
	h := &FlowActionHandler{
		workflowService:      in.WorkflowService,
		dataMigrationService: in.DataMigrationService,
		ticketService:        in.TicketService,
		approvalFlowService:  in.ApprovalFlowService,
		msgService:           in.MsgService,
		cnf:                  in.Cnf,
		ticketRepo:           in.TicketRepo,
	}
	return handler.NewHandler(h.WorkflowAction)
}

type FlowActionHandler struct {
	ticketService        workflow.TicketService
	dataMigrationService data_migration.DataMigrationService
	workflowService      workflow.TicketWorkflowService
	approvalFlowService  approval_flow.ApprovalFlowService
	msgService           message.MsgService
	cnf                  config.ConfigProvider
	ticketRepo           repository.TicketRepo
}

type NewActionServiceIn struct {
	dig.In
	TicketService        workflow.TicketService
	DataMigrationService data_migration.DataMigrationService
	WorkflowService      workflow.TicketWorkflowService
	ApprovalFlowService  approval_flow.ApprovalFlowService
	MsgService           message.MsgService
	Cnf                  config.ConfigProvider
	TicketRepo           repository.TicketRepo
}

func (selfHandler FlowActionHandler) WorkflowAction(ctx context.Context, req *model.WorkflowActionReq) (*model.WorkflowActionResp, error) {
	//ticketItem, err := selfHandler.ticketService.GetTicket(ctx, conv.StrToInt64(req.TicketId, 0))
	//if err != nil {
	//	log.Warn(ctx, "ticketId: %d getTicket error :%v", req.TicketId, err)
	//	return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	//}
	//if !IsVolcInstance(ticketItem.InstanceType.String()) {
	//	return ForwardWorkflowActionToByteRDS(ctx, byterds.NewByteRDSClient(selfHandler.cnf), ticketItem)
	//}
	// 1.检查当前操作人是不是当前节点应该触发的人
	userId := fwctx.GetUserID(ctx)
	tenantId := fwctx.GetTenantID(ctx)
	if tenantId == "" {
		return &model.WorkflowActionResp{Code: model.ErrCode_Error, ErrMsg: "未获取到租户ID"}, consts.ErrorWithParam(model.ErrorCode_InputParamError, "未获取到租户ID")
	}
	if userId == "" {
		// 同create Ticket
		userId = tenantId
		// return &model.WorkflowActionResp{Code: model.ErrCode_Error, ErrMsg: "未获取到用户ID"}, nil
	}
	log.Info(ctx, "tenantId is %v,userId is %v", tenantId, userId)
	// 通过ticketId获取工单
	ticketId, _ := strconv.ParseInt(req.TicketId, 10, 64)
	ticket, err := selfHandler.ticketService.GetTicket(ctx, ticketId)
	if err != nil || ticket == nil {
		log.Warn(ctx, "get ticket error, ticket Id:%v error:%v", req.TicketId, err)
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "get ticket error")
	}
	if !selfHandler.isSubmit(ctx, req.TicketId) {
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "ticket is not submit, can't do action")
	}
	// 有时候不知道流量重放还是什么问题会导致对执行完成的工单进行审批,将工单又更新成等待执行,这里block掉这种情况
	if ticket.TicketStatus == int32(model.TicketStatus_TicketFinished) ||
		ticket.TicketStatus == int32(model.TicketStatus_TicketError) ||
		ticket.TicketStatus == int32(model.TicketStatus_TicketExecute) {
		log.Warn(ctx, "ticket status is %v , not support workflow action", ticket.TicketStatus)
		return &model.WorkflowActionResp{Code: model.ErrCode_Success}, nil
	}
	// 检查实例是不是正常，如果不正常，或已经解除纳管，就不允许再操作工单了
	isInstanceAvailable, err := selfHandler.ticketService.IsInstanceAvailable(ctx, tenantId, ticket.InstanceId)
	if err != nil {
		return &model.WorkflowActionResp{Code: model.ErrCode_Error, ErrMsg: "get instance info error"}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if !isInstanceAvailable {
		return &model.WorkflowActionResp{Code: model.ErrCode_Error, ErrMsg: "instance not exist,or instance is not in manage mode"}, consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// NOTE 去掉老的BPM逻辑,不再兼容老的工单
	//if ticket.WorkflowId != 0 {
	//	// 如果 WorkflowId 不为0，则说明是走bpm的老的工单，必须走BPM审批流
	//	return selfHandler.OldAction(ctx, req, ticketId, tenantId, userId)
	//}
	// 这里分流,确认是走新的审批流还是走老的审批流
	//// TODO 这里应该通过新的审批流来获取真正的审批人
	//if tenant.IsRDSMultiCloudPlatform(ctx, selfHandler.cnf) {
	//	if err = selfHandler.ticketService.ModifyTicketFlow(ctx, &dao.FlowInfo{
	//		TicketId:      ticket.TicketId,
	//		UserIds:       "",
	//		UserRole:      "",
	//		Status:        int(model.TicketStatus_TicketWaitExecute), // 工单状态改为等待执行
	//		FlowStep:      1,
	//		PreOperatorId: "",
	//	}); err != nil {
	//		log.Warn(ctx, "ticket %d update status error:%s", ticket.TicketId, err.Error())
	//		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	//	}
	//	if err = selfHandler.autoExecute(ctx, ticketId); err != nil {
	//		log.Warn(ctx, "ticket %d auto execute error:%s", ticket.TicketId, err.Error())
	//		return nil, err
	//	}
	//	return &model.WorkflowActionResp{
	//		Code: model.ErrCode_Success,
	//	}, nil
	//}
	// 如果WorkflowId为0，则说明是新的审批流
	return selfHandler.NewAction(ctx, req, ticket, userId)
}

func (selfHandler FlowActionHandler) isSubmit(ctx context.Context, ticketId string) bool {
	ticket, err := selfHandler.ticketRepo.GetByID(ctx, ticketId)
	if err != nil || ticket == nil {
		log.Warn(ctx, "get ticket error, ticket Id:%v error:%v", ticketId, err)
		// TODO 理论上前面做过一回GetTicket，不会在这里报错
		// 但是防御性编程，因为这一期比较赶，保证不影响原来的流程
		return true
	}
	return ticket.Submitted == 1
}

func (selfHandler FlowActionHandler) NewAction(ctx context.Context, req *model.WorkflowActionReq, ticket *shared.Ticket, userId string) (*model.WorkflowActionResp, error) {
	log.Info(ctx, "ticket %v begin NewAction", utils.Show(ticket))
	/*  根据ticket里面保存的approval_flow_id来查询当前的审批节点信息，审批流只关联节点，不关联审批人
	MySQL [dbwmgr]> select * from dbw_approval_flow where flow_id='1905194327596879872';
	+---------------------+--------------------+------------+------------------+--------+------+------------------+-------------+---------+
	| flow_id             | instance_id        | tenant_id  | flow_template_id | status | step | approver_node_id | create_time | deleted |
	+---------------------+--------------------+------------+------------------+--------+------+------------------+-------------+---------+
	| 1905194327596879872 | mysql-7109a6dc390a | 2100067216 |                0 |      1 |    2 |                0 |           0 |       0 |
	+---------------------+--------------------+------------+------------------+--------+------+------------------+-------------+---------+
	1 row in set (0.00 sec)
	*/
	approvalFlow, err := selfHandler.approvalFlowService.GetApprovalFlowRecord(ctx, ticket.ApprovalFlowId)
	if err != nil || approvalFlow == nil {
		log.Warn(ctx, "get approval flow error :%s", err.Error())
		return &model.WorkflowActionResp{Code: model.ErrCode_Error, ErrMsg: err.Error()}, consts.ErrorWithParam(model.ErrorCode_SystemError, "get approval flow error")
	}
	// 检查当前ctx里面的这个UserId,在不在当前审批节点的审批人里面
	err = selfHandler.approvalFlowService.CheckNodePermission(ctx, approvalFlow.ApproverNodeId, ticket.InstanceId, ticket.TenantId, userId)
	if err != nil {
		return nil, err
	}
	switch req.ActionType {
	case model.FlowActionType_Pass:
		log.Info(ctx, "ticket %v pass workflow", utils.Show(ticket))
		return selfHandler.ticketService.NewPassWorkflow(ctx, approvalFlow, ticket, userId)
	case model.FlowActionType_Reject:
		return selfHandler.NewRejectWorkflow(ctx, approvalFlow, ticket, userId, req.Memo)
	}
	return &model.WorkflowActionResp{Code: model.ErrCode_Error, ErrMsg: "没有此执行类型的动作，仅支持pass(0)，reject(1)"}, nil
}

// Deprecated: 已不再使用
func (selfHandler FlowActionHandler) OldAction(ctx context.Context, req *model.WorkflowActionReq, ticketId int64, tenantId string, userId string) (*model.WorkflowActionResp, error) {
	bpmFlowInfo, err := selfHandler.ticketService.GetAndCheckTicketFlow(ctx, ticketId, tenantId, userId)
	if err != nil {
		return &model.WorkflowActionResp{Code: model.ErrCode_Error, ErrMsg: err.Error()}, err
	}
	switch req.ActionType {
	case model.FlowActionType_Pass:
		return selfHandler.PassWorkflow(ctx, bpmFlowInfo, userId)
	case model.FlowActionType_Reject:
		return selfHandler.RejectWorkflow(ctx, bpmFlowInfo, req, userId)
	}
	return &model.WorkflowActionResp{Code: model.ErrCode_Error, ErrMsg: "没有此执行类型的动作，仅支持pass(0)，reject(1)"}, nil
}

// Deprecated: 已不再使用
func (selfHandler FlowActionHandler) PassWorkflow(ctx context.Context, bpmFlowInfo *dao.BpmFlowInfo, userId string) (*model.WorkflowActionResp, error) {
	log.Info(ctx, "ticket %d pass workflow", bpmFlowInfo.TicketId)
	// 调用Bpm进行审批 TODO totalStep后续从approval_flow_config表获取
	totalStep := 3
	err := selfHandler.workflowService.PassWorkflowRecord(ctx, bpmFlowInfo, totalStep, userId)
	if err != nil {
		return &model.WorkflowActionResp{Code: model.ErrCode_Error, ErrMsg: err.Error()}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	// 获取'当前处理人'（也就是下一步的处理人）
	currentUserRole, err := selfHandler.ticketService.GetNextUserIdInfo(ctx, bpmFlowInfo)
	if err != nil {
		return &model.WorkflowActionResp{Code: model.ErrCode_Error, ErrMsg: "获取待审批用户失败"}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	// 修改工单状态
	err = selfHandler.modifyTicketFlow(ctx, userId, bpmFlowInfo, currentUserRole, totalStep)
	if err != nil {
		return &model.WorkflowActionResp{Code: model.ErrCode_Error, ErrMsg: err.Error()}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return &model.WorkflowActionResp{Code: model.ErrCode_Success}, nil
}

func (selfHandler FlowActionHandler) NewRejectWorkflow(ctx context.Context, approvalFlow *entity.ApprovalFlow, ticket *shared.Ticket, userId string, memo *string) (*model.WorkflowActionResp, error) {
	// 调用Bpm
	err := selfHandler.approvalFlowService.RejectApprovalFlowRecord(ctx, approvalFlow.FlowId, userId)
	if err != nil {
		return &model.WorkflowActionResp{Code: model.ErrCode_Error, ErrMsg: err.Error()}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	// 修改工单状态
	err = selfHandler.changeTicketFlow(ctx, ticket.TicketId, int(approvalFlow.Step), userId, memo)
	if err != nil {
		return &model.WorkflowActionResp{Code: model.ErrCode_Error, ErrMsg: err.Error()}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return &model.WorkflowActionResp{Code: model.ErrCode_Success}, nil
}

func (selfHandler FlowActionHandler) modifyTicketFlow(ctx context.Context, preOperatorId string, bpmFlowInfo *dao.BpmFlowInfo, currentUserRole *dao.UserRole, totalStep int) error {
	log.Info(ctx, "ticket %s: modifyTicketFlow ", bpmFlowInfo.TicketId)
	nextStatus := workflow.TicketExamine
	nextFlowStep := bpmFlowInfo.FlowStep + 1
	if bpmFlowInfo.FlowStep == int32(totalStep) {
		log.Info(ctx, "ticket %s: next status change ", bpmFlowInfo.TicketId)
		nextStatus = workflow.TicketWaitExecute
		nextFlowStep = bpmFlowInfo.FlowStep
	}
	log.Info(ctx, "ticket %s totalStep is %v,bpm Flow Step is %v,bpm next flow step is %v,nextStatus is %v",
		bpmFlowInfo.TicketId, totalStep, bpmFlowInfo.FlowStep, nextFlowStep, nextStatus)
	err := selfHandler.ticketService.ModifyTicketFlow(ctx, &dao.FlowInfo{
		TicketId:      bpmFlowInfo.TicketId,
		UserIds:       currentUserRole.Id,
		UserRole:      currentUserRole.Role,
		Status:        nextStatus,
		FlowStep:      int(nextFlowStep),
		PreOperatorId: preOperatorId,
	})
	if err != nil {
		log.Warn(ctx, "ticket %d update status error:%s", bpmFlowInfo.TicketId, err.Error())
		return err
	}
	log.Info(ctx, "ticket %s begin to auto execute ", bpmFlowInfo.TicketId)
	return selfHandler.autoExecute(ctx, bpmFlowInfo.TicketId)
}

func (selfHandler FlowActionHandler) autoExecute(ctx context.Context, ticketId int64) error {
	autoExecute, err := selfHandler.ticketService.IsAutoExecute(ctx, ticketId)
	if err != nil {
		log.Warn(ctx, "ticket %s get execute type error:%v", ticketId, err.Error())
		return err
	}
	log.Info(ctx, "ticket %v: autoExecute is %v,err is %v", ticketId, autoExecute, err)
	if !autoExecute {
		return nil
	}
	go func() {
		// 异步执行
		ticket, err := selfHandler.ticketService.GetTicket(ctx, ticketId)
		if err != nil {
			log.Warn(ctx, "get ticket error %v", err)
		}
		switch ticket.GetTicketType() {
		case int32(model.TicketType_DataMigrationImport),
			int32(model.TicketType_DataMigrationExportDB),
			int32(model.TicketType_DataMigrationExportSqlResult):
			selfHandler.ticketService.ExecuteMigrationTicket(ctx, &model.ExecuteMigrationTicketReq{TicketId: strconv.Itoa(int(ticketId))})
		default:
			selfHandler.ticketService.ExecuteTicket(ctx, &model.ExecuteTicketReq{TicketId: strconv.Itoa(int(ticketId))})
		}
	}()
	return nil
}

func (selfHandler FlowActionHandler) RejectWorkflow(ctx context.Context, bpmFlowInfo *dao.BpmFlowInfo, req *model.WorkflowActionReq, userId string) (*model.WorkflowActionResp, error) {
	// 调用Bpm
	err := selfHandler.workflowService.RejectWorkflowRecord(ctx, bpmFlowInfo, userId)
	if err != nil {
		return &model.WorkflowActionResp{Code: model.ErrCode_Error, ErrMsg: err.Error()}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	// 修改工单状态
	err = selfHandler.changeTicketFlow(ctx, bpmFlowInfo.TicketId, int(bpmFlowInfo.FlowStep), userId, req.Memo)
	if err != nil {
		return &model.WorkflowActionResp{Code: model.ErrCode_Error, ErrMsg: err.Error()}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	return &model.WorkflowActionResp{Code: model.ErrCode_Success}, nil
}

func (selfHandler FlowActionHandler) changeTicketFlow(ctx context.Context, ticketId int64, flowStep int, preOperatorId string, memo *string) error {
	comment := ""
	if memo != nil {
		comment = *memo
	}
	err := selfHandler.ticketService.ModifyTicketFlow(ctx, &dao.FlowInfo{
		TicketId:      ticketId,
		UserIds:       "",
		UserRole:      "",
		Status:        workflow.TicketReject,
		FlowStep:      flowStep,
		Comment:       comment,
		PreOperatorId: preOperatorId,
	})
	return err
}
