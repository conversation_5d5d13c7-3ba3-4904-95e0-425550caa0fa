package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	service_config "code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/tenant"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/lang/gg/gptr"
	"context"
	"errors"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"testing"
	"time"
)

//import (
//	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
//	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
//	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
//	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
//	"code.byted.org/infcs/ds-lib/common/utils"
//	fwctx "code.byted.org/infcs/ds-lib/framework/context"
//	"context"
//	"errors"
//	"github.com/bytedance/mockey"
//	"testing"
//)
//
//type MockTicketWorkflowService struct {
//}
//
//func (*MockTicketWorkflowService) CreateBpmWorkflowRecord(ctx context.Context) (int64, error) {
//	return 0, nil
//}
//func (*MockTicketWorkflowService) PassWorkflowRecord(ctx context.Context, bpmFlowInfo *dao.BpmFlowInfo, totalStep int, operatorId string) error {
//	return nil
//}
//func (*MockTicketWorkflowService) RejectWorkflowRecord(ctx context.Context, bpmFlowInfo *dao.BpmFlowInfo, operatorId string) error {
//	return nil
//}
//func (*MockTicketWorkflowService) CancelWorkflowRecord(ctx context.Context, workflowId int64, operatorId string) error {
//	return nil
//}
//func (*MockTicketWorkflowService) DescribeWorkflowLogs(ctx context.Context, workflowId int64) (bpm workflow.BpmLogResponse, err error) {
//	return workflow.BpmLogResponse{}, nil
//}
//
//func TestCreateTicket(t *testing.T) {
//	ticketService := &mocks.MockTicketService{}
//	ticketWorkflowService := &MockTicketWorkflowService{}
//	handler := CreateTicketHandler{
//		ticketService:       ticketService,
//		workflowService:     ticketWorkflowService,
//		approvalFlowService: &mocks.MockApprovalFlowService{},
//	}
//	ctx := context.Background()
//
//	mock1 := mockey.Mock(fwctx.GetTenantID).Return("123").Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock(fwctx.GetUserID).Return("").Build()
//	defer mock2.UnPatch()
//
//	_, err := handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//	mock3 := mockey.Mock((*mocks.MockTicketService).IsUserExists).Return(true, nil).Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*MockTicketWorkflowService).CreateBpmWorkflowRecord).Return(1, nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*mocks.MockTicketService).CreateDDLTicket).Return("1", nil).Build()
//	defer mock5.UnPatch()
//	mock7 := mockey.Mock((*CreateTicketHandler).preCheck).Return().Build()
//	defer mock7.UnPatch()
//	mock6 := mockey.Mock((*mocks.MockTicketService).IsInstanceAvailable).Return(true, nil).Build()
//	defer mock6.UnPatch()
//	appMock := mockey.Mock((*CreateTicketHandler).createApprovalFlow).Return(1, 1, nil).Build()
//	defer appMock.UnPatch()
//
//	sql := "update t_test set a = 1"
//	_, err = handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx",
//		InstanceType: model.InstanceType_MySQL, SqlText: &sql, CreateUser: "dbw", DatabaseName: "test"})
//	if err != nil {
//		t.Fatal("failed", err)
//	}
//}
//
//func TestCreateTicket_Err(t *testing.T) {
//	ticketService := &mocks.MockTicketService{}
//	ticketWorkflowService := &MockTicketWorkflowService{}
//	handler := CreateTicketHandler{
//		ticketService:   ticketService,
//		workflowService: ticketWorkflowService,
//	}
//	ctx := context.Background()
//
//	mock1 := mockey.Mock(fwctx.GetTenantID).Return("").Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock(fwctx.GetUserID).Return("").Build()
//	defer mock2.UnPatch()
//	appMock := mockey.Mock((*CreateTicketHandler).createApprovalFlow).Return(1, 1, nil).Build()
//	defer appMock.UnPatch()
//
//	_, err := handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//	mock3 := mockey.Mock((*mocks.MockTicketService).IsUserExists).Return(true, nil).Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*MockTicketWorkflowService).CreateBpmWorkflowRecord).Return(1, nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*mocks.MockTicketService).CreateDDLTicket).Return("1", nil).Build()
//	defer mock5.UnPatch()
//	mock7 := mockey.Mock((*CreateTicketHandler).preCheck).Return().Build()
//	defer mock7.UnPatch()
//	mock6 := mockey.Mock((*mocks.MockTicketService).IsInstanceAvailable).Return(true, nil).Build()
//	defer mock6.UnPatch()
//
//	sql := "update t_test set a = 1"
//	_, err = handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx", SqlText: &sql, CreateUser: "dbw", DatabaseName: "test"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//}
//
//func TestCreateTicket_Err1(t *testing.T) {
//	ticketService := &mocks.MockTicketService{}
//	ticketWorkflowService := &MockTicketWorkflowService{}
//	handler := CreateTicketHandler{
//		ticketService:   ticketService,
//		workflowService: ticketWorkflowService,
//	}
//	ctx := context.Background()
//
//	mock1 := mockey.Mock(fwctx.GetTenantID).Return("123").Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock(fwctx.GetUserID).Return("").Build()
//	defer mock2.UnPatch()
//	approvalMock := mockey.Mock((*CreateTicketHandler).createApprovalFlow).Return(1, 1, nil).Build()
//	defer approvalMock.UnPatch()
//
//	_, err := handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//	mock3 := mockey.Mock((*mocks.MockTicketService).IsUserExists).Return(true, errors.New("error")).Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*MockTicketWorkflowService).CreateBpmWorkflowRecord).Return(1, nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*mocks.MockTicketService).CreateDDLTicket).Return("1", nil).Build()
//	defer mock5.UnPatch()
//	mock7 := mockey.Mock((*CreateTicketHandler).preCheck).Return().Build()
//	defer mock7.UnPatch()
//	mock6 := mockey.Mock((*mocks.MockTicketService).IsInstanceAvailable).Return(true, nil).Build()
//	defer mock6.UnPatch()
//
//	sql := "update t_test set a = 1"
//	_, err = handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx", SqlText: &sql, CreateUser: "dbw", DatabaseName: "test"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//}
//
//func TestCreateTicket_Err2(t *testing.T) {
//	ticketService := &mocks.MockTicketService{}
//	ticketWorkflowService := &MockTicketWorkflowService{}
//	handler := CreateTicketHandler{
//		ticketService:   ticketService,
//		workflowService: ticketWorkflowService,
//	}
//	ctx := context.Background()
//
//	mock1 := mockey.Mock(fwctx.GetTenantID).Return("123").Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock(fwctx.GetUserID).Return("").Build()
//	defer mock2.UnPatch()
//	approvalMock := mockey.Mock((*CreateTicketHandler).createApprovalFlow).Return(1, 1, nil).Build()
//	defer approvalMock.UnPatch()
//
//	_, err := handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//	mock3 := mockey.Mock((*mocks.MockTicketService).IsUserExists).Return(false, nil).Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*MockTicketWorkflowService).CreateBpmWorkflowRecord).Return(1, nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*mocks.MockTicketService).CreateDDLTicket).Return("1", nil).Build()
//	defer mock5.UnPatch()
//	mock7 := mockey.Mock((*CreateTicketHandler).preCheck).Return().Build()
//	defer mock7.UnPatch()
//	mock6 := mockey.Mock((*mocks.MockTicketService).IsInstanceAvailable).Return(true, nil).Build()
//	defer mock6.UnPatch()
//
//	sql := "update t_test set a = 1"
//	_, err = handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx", SqlText: &sql, CreateUser: "dbw", DatabaseName: "test"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//}
//
//func TestCreateTicket_Err3(t *testing.T) {
//	ticketService := &mocks.MockTicketService{}
//	ticketWorkflowService := &MockTicketWorkflowService{}
//	handler := CreateTicketHandler{
//		ticketService:   ticketService,
//		workflowService: ticketWorkflowService,
//	}
//	ctx := context.Background()
//
//	mock1 := mockey.Mock(fwctx.GetTenantID).Return("123").Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock(fwctx.GetUserID).Return("").Build()
//	defer mock2.UnPatch()
//	approvalMock := mockey.Mock((*CreateTicketHandler).createApprovalFlow).Return(1, 1, nil).Build()
//	defer approvalMock.UnPatch()
//
//	_, err := handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//	mock3 := mockey.Mock((*mocks.MockTicketService).IsUserExists).Return(true, nil).Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*MockTicketWorkflowService).CreateBpmWorkflowRecord).Return(1, nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*mocks.MockTicketService).CreateDDLTicket).Return("1", nil).Build()
//	defer mock5.UnPatch()
//	mock7 := mockey.Mock((*CreateTicketHandler).preCheck).Return().Build()
//	defer mock7.UnPatch()
//	mock6 := mockey.Mock((*mocks.MockTicketService).IsInstanceAvailable).Return(true, errors.New("error")).Build()
//	defer mock6.UnPatch()
//
//	sql := "update t_test set a = 1"
//	_, err = handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx", SqlText: &sql, CreateUser: "dbw", DatabaseName: "test"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//}
//
//func TestCreateTicket_Err4(t *testing.T) {
//	ticketService := &mocks.MockTicketService{}
//	ticketWorkflowService := &MockTicketWorkflowService{}
//	handler := CreateTicketHandler{
//		ticketService:   ticketService,
//		workflowService: ticketWorkflowService,
//	}
//	ctx := context.Background()
//
//	mock1 := mockey.Mock(fwctx.GetTenantID).Return("123").Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock(fwctx.GetUserID).Return("").Build()
//	defer mock2.UnPatch()
//	approvalMock := mockey.Mock((*CreateTicketHandler).createApprovalFlow).Return(1, 1, nil).Build()
//	defer approvalMock.UnPatch()
//
//	_, err := handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//	mock3 := mockey.Mock((*mocks.MockTicketService).IsUserExists).Return(true, nil).Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*MockTicketWorkflowService).CreateBpmWorkflowRecord).Return(1, nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*mocks.MockTicketService).CreateDDLTicket).Return("1", nil).Build()
//	defer mock5.UnPatch()
//	mock7 := mockey.Mock((*CreateTicketHandler).preCheck).Return().Build()
//	defer mock7.UnPatch()
//	mock6 := mockey.Mock((*mocks.MockTicketService).IsInstanceAvailable).Return(false, nil).Build()
//	defer mock6.UnPatch()
//
//	sql := "update t_test set a = 1"
//	_, err = handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx", SqlText: &sql, CreateUser: "dbw", DatabaseName: "test"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//}
//
//func TestCreateTicket_Err5(t *testing.T) {
//	ticketService := &mocks.MockTicketService{}
//	ticketWorkflowService := &MockTicketWorkflowService{}
//	handler := CreateTicketHandler{
//		ticketService:   ticketService,
//		workflowService: ticketWorkflowService,
//	}
//	ctx := context.Background()
//
//	mock1 := mockey.Mock(fwctx.GetTenantID).Return("").Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock(fwctx.GetUserID).Return("").Build()
//	defer mock2.UnPatch()
//	approvalMock := mockey.Mock((*CreateTicketHandler).createApprovalFlow).Return(1, 1, nil).Build()
//	defer approvalMock.UnPatch()
//
//	_, err := handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//	mock3 := mockey.Mock((*mocks.MockTicketService).IsUserExists).Return(true, nil).Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*MockTicketWorkflowService).CreateBpmWorkflowRecord).Return(1, nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*mocks.MockTicketService).CreateDDLTicket).Return("1", errors.New("error")).Build()
//	defer mock5.UnPatch()
//	mock7 := mockey.Mock((*CreateTicketHandler).preCheck).Return().Build()
//	defer mock7.UnPatch()
//	mock6 := mockey.Mock((*mocks.MockTicketService).IsInstanceAvailable).Return(true, nil).Build()
//	defer mock6.UnPatch()
//
//	sql := "update t_test set a = 1"
//	_, err = handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx", SqlText: &sql, CreateUser: "dbw", DatabaseName: "test"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//}
//
//func TestCreateTicket_Err6(t *testing.T) {
//	ticketService := &mocks.MockTicketService{}
//	ticketWorkflowService := &MockTicketWorkflowService{}
//	handler := CreateTicketHandler{
//		ticketService:   ticketService,
//		workflowService: ticketWorkflowService,
//	}
//	ctx := context.Background()
//
//	mock1 := mockey.Mock(fwctx.GetTenantID).Return("13").Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock(fwctx.GetUserID).Return("").Build()
//	defer mock2.UnPatch()
//	approvalMock := mockey.Mock((*CreateTicketHandler).createApprovalFlow).Return(1, 1, nil).Build()
//	defer approvalMock.UnPatch()
//
//	_, err := handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//	mock3 := mockey.Mock((*mocks.MockTicketService).IsUserExists).Return(true, nil).Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*MockTicketWorkflowService).CreateBpmWorkflowRecord).Return(1, errors.New("error")).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*mocks.MockTicketService).CreateDDLTicket).Return("1", nil).Build()
//	defer mock5.UnPatch()
//	mock7 := mockey.Mock((*CreateTicketHandler).preCheck).Return().Build()
//	defer mock7.UnPatch()
//	mock6 := mockey.Mock((*mocks.MockTicketService).IsInstanceAvailable).Return(true, nil).Build()
//	defer mock6.UnPatch()
//
//	sql := "update t_test set a = 1"
//	_, err = handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx", SqlText: &sql, CreateUser: "dbw", DatabaseName: "test"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//}
//
//func TestCreateTicket_Err7(t *testing.T) {
//	ticketService := &mocks.MockTicketService{}
//	ticketWorkflowService := &MockTicketWorkflowService{}
//	handler := CreateTicketHandler{
//		ticketService:   ticketService,
//		workflowService: ticketWorkflowService,
//	}
//	ctx := context.Background()
//
//	mock1 := mockey.Mock(fwctx.GetTenantID).Return("13").Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock(fwctx.GetUserID).Return("").Build()
//	defer mock2.UnPatch()
//	approvalMock := mockey.Mock((*CreateTicketHandler).createApprovalFlow).Return(1, 1, nil).Build()
//	defer approvalMock.UnPatch()
//
//	_, err := handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//	mock3 := mockey.Mock((*mocks.MockTicketService).IsUserExists).Return(true, nil).Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*MockTicketWorkflowService).CreateBpmWorkflowRecord).Return(1, nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*mocks.MockTicketService).CreateDDLTicket).Return("1", errors.New("error")).Build()
//	defer mock5.UnPatch()
//	mock7 := mockey.Mock((*CreateTicketHandler).preCheck).Return().Build()
//	defer mock7.UnPatch()
//	mock6 := mockey.Mock((*mocks.MockTicketService).IsInstanceAvailable).Return(true, nil).Build()
//	defer mock6.UnPatch()
//
//	sql := "update t_test set a = 1"
//	_, err = handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx", SqlText: &sql, CreateUser: "dbw", DatabaseName: "test"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//}
//
//func TestCreateTicket_Err8(t *testing.T) {
//	ticketService := &mocks.MockTicketService{}
//	ticketWorkflowService := &MockTicketWorkflowService{}
//	handler := CreateTicketHandler{
//		ticketService:   ticketService,
//		workflowService: ticketWorkflowService,
//	}
//	ctx := context.Background()
//
//	mock1 := mockey.Mock(fwctx.GetTenantID).Return("13").Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock(fwctx.GetUserID).Return("").Build()
//	defer mock2.UnPatch()
//	approvalMock := mockey.Mock((*CreateTicketHandler).createApprovalFlow).Return(1, 1, nil).Build()
//	defer approvalMock.UnPatch()
//
//	_, err := handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//	mock3 := mockey.Mock((*mocks.MockTicketService).IsUserExists).Return(true, nil).Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*MockTicketWorkflowService).CreateBpmWorkflowRecord).Return(1, nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*mocks.MockTicketService).CreateDDLTicket).Return("1", errors.New("error")).Build()
//	defer mock5.UnPatch()
//	mock7 := mockey.Mock((*CreateTicketHandler).preCheck).Return().Build()
//	defer mock7.UnPatch()
//	mock6 := mockey.Mock((*mocks.MockTicketService).IsInstanceAvailable).Return(true, nil).Build()
//	defer mock6.UnPatch()
//
//	sql := "update t_test set a = 1"
//	_, err = handler.CreateDDLTicket(ctx, &model.CreateTicketReq{ExecEndTime: utils.Int32Ref(-1), InstanceId: "instanceId", SqlText: &sql, CreateUser: "dbw", DatabaseName: "test"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//}
//
//func TestCreateTicket_Err9(t *testing.T) {
//	ticketService := &mocks.MockTicketService{}
//	ticketWorkflowService := &MockTicketWorkflowService{}
//	handler := CreateTicketHandler{
//		ticketService:   ticketService,
//		workflowService: ticketWorkflowService,
//	}
//	ctx := context.Background()
//
//	mock1 := mockey.Mock(fwctx.GetTenantID).Return("13").Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock(fwctx.GetUserID).Return("").Build()
//	defer mock2.UnPatch()
//	approvalMock := mockey.Mock((*CreateTicketHandler).createApprovalFlow).Return(1, 1, nil).Build()
//	defer approvalMock.UnPatch()
//
//	_, err := handler.CreateDDLTicket(ctx, &model.CreateTicketReq{InstanceId: "mysql-xxx"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//	mock3 := mockey.Mock((*mocks.MockTicketService).IsUserExists).Return(true, nil).Build()
//	defer mock3.UnPatch()
//	mock4 := mockey.Mock((*MockTicketWorkflowService).CreateBpmWorkflowRecord).Return(1, nil).Build()
//	defer mock4.UnPatch()
//	mock5 := mockey.Mock((*mocks.MockTicketService).CreateDDLTicket).Return("1", errors.New("error")).Build()
//	defer mock5.UnPatch()
//	mock7 := mockey.Mock((*CreateTicketHandler).preCheck).Return().Build()
//	defer mock7.UnPatch()
//	mock6 := mockey.Mock((*mocks.MockTicketService).IsInstanceAvailable).Return(true, nil).Build()
//	defer mock6.UnPatch()
//
//	sql := "update t_test set a = 1"
//	_, err = handler.CreateDDLTicket(ctx, &model.CreateTicketReq{ExecEndTime: utils.Int32Ref(10), ExecStartTime: utils.Int32Ref(20), InstanceId: "instanceId", SqlText: &sql, CreateUser: "dbw", DatabaseName: "test"})
//	if err == nil {
//		t.Fatal("failed", err)
//	}
//}
//
//func TestNewCreateTicketHandler_OK(t *testing.T) {
//	NewCreateTicketHandler(NewCreateTicketServiceIn{})
//}

// MockTicketService_CreateTicket is a mock for the workflow.TicketService interface.
type MockTicketService_CreateTicket struct {
	workflow.TicketService
}

func (m *MockTicketService_CreateTicket) IsUserExists(ctx context.Context, userId string, tenantId string) (bool, error) {
	return false, nil
}
func (m *MockTicketService_CreateTicket) IsInstanceAvailable(ctx context.Context, tenantId string, instanceId string) (bool, error) {
	return false, nil
}
func (m *MockTicketService_CreateTicket) CreateTicket(ctx context.Context, req *model.CreateTicketReq, approvalFlowId int64, approvalTemplateId int64, workFlowId int64, ticketId int64, tenantId string, userId string) (string, error) {
	return "", nil
}

// MockTicketWorkflowService_CreateTicket is a mock for the workflow.TicketWorkflowService interface.
type MockTicketWorkflowService_CreateTicket struct {
	workflow.TicketWorkflowService
}

func (m *MockTicketWorkflowService_CreateTicket) CreateBpmWorkflowRecord(ctx context.Context, BpmType string, configData workflow.BpmConfigData) (int64, error) {
	return 0, nil
}

// MockApprovalFlowService_CreateTicket is a mock for the approval_flow.ApprovalFlowService interface.
type MockApprovalFlowService_CreateTicket struct {
	approval_flow.ApprovalFlowService
}

// MockConfigProvider_CreateTicket is a mock for the service_config.ConfigProvider interface.
type MockConfigProvider_CreateTicket struct {
	service_config.ConfigProvider
}

// MockIdgenService_CreateTicket is a mock for the idgen.Service interface.
type MockIdgenService_CreateTicket struct {
	idgen.Service
}

func (m *MockIdgenService_CreateTicket) NextID(ctx context.Context) (int64, error) {
	return 0, nil
}

// StandardErrorForTest is a helper for mocking consts.StandardError
type StandardErrorForTest struct {
	consts.StandardError
	CodeVal int32
	Msg     string
}

func (e *StandardErrorForTest) Error() string {
	return e.Msg
}
func (e *StandardErrorForTest) Code() int32 {
	return e.CodeVal
}
func Test_CreateTicketHandler_CreateTicket_BitsUTGen(t *testing.T) {
	// Common setup for all tests
	ctx := context.Background()
	req := &model.CreateTicketReq{
		InstanceType:   model.InstanceType_MySQL,
		InstanceId:     "mysql-instance-1",
		SqlText:        gptr.Of("SELECT 1;"),
		TicketType:     model.TicketType_NormalSqlChange,
		DatabaseName:   "test_db",
		CreateUser:     "test_user",
		CreateUserName: gptr.Of("Test User"),
	}
	mockTicketID := int64(12345)
	mockWorkflowID := int64(67890)
	mockApprovalFlowID := int64(111)
	mockApprovalTemplateID := int64(222)

	// Handler initialization with mock dependencies
	mockTicketService := &MockTicketService_CreateTicket{}
	mockWorkflowService := &MockTicketWorkflowService_CreateTicket{}
	mockApprovalFlowService := &MockApprovalFlowService_CreateTicket{}
	mockCnf := &MockConfigProvider_CreateTicket{}
	mockIdg := &MockIdgenService_CreateTicket{}

	h := &CreateTicketHandler{
		ticketService:       mockTicketService,
		workflowService:     mockWorkflowService,
		approvalFlowService: mockApprovalFlowService,
		cnf:                 mockCnf,
		idg:                 mockIdg,
	}

	mockey.PatchConvey("Test_CreateTicketHandler_CreateTicket", t, func() {
		mockey.PatchConvey("成功场景: Volc引擎实例, 非多云平台, 成功创建工单", func() {
			// 场景描述：
			// 当请求为火山引擎实例，且非多云平台时，所有校验和依赖调用均成功，最终成功创建工单。
			// 数据构造：
			// - req.InstanceType: model.InstanceType_MySQL (Volc)
			// - req.CreateFrom: 默认为空, 非归档
			// 逻辑链路：
			// 1. IsVolcInstance 判断为 true，不走 Forward 逻辑。
			// 2. checkReq, checkSqlText 校验通过。
			// 3. 获取 TenantID 和 UserID 成功。
			// 4. IsUserExists, IsInstanceAvailable 校验通过。
			// 5. idg.NextID 生成工单ID成功。
			// 6. IsRDSMultiCloudPlatform 判断为 false，不走BPM流程。
			// 7. createApprovalFlow 创建审批流成功。
			// 8. ticketService.CreateTicket 创建工单记录成功。
			// 9. preCheck 异步检查被调用。
			// 10. 函数返回成功的响应。
			mockey.Mock((*CreateTicketHandler).checkReq).Return(nil).Build()
			mockey.Mock((*CreateTicketHandler).checkSqlText).Return(nil).Build()
			mockey.Mock(fwctx.GetTenantID).Return("test_tenant").Build()
			mockey.Mock(fwctx.GetUserID).Return("test_user").Build()
			mockey.Mock((*MockTicketService_CreateTicket).IsUserExists).Return(true, nil).Build()
			mockey.Mock((*MockTicketService_CreateTicket).IsInstanceAvailable).Return(true, nil).Build()
			mockey.Mock((*MockIdgenService_CreateTicket).NextID).Return(mockTicketID, nil).Build()
			mockey.Mock(tenant.IsRDSMultiCloudPlatform).Return(false).Build()
			mockey.Mock((*CreateTicketHandler).createApprovalFlow).Return(mockApprovalFlowID, mockApprovalTemplateID, nil).Build()
			mockey.Mock((*MockTicketService_CreateTicket).CreateTicket).Return("some_id", nil).Build()
			mockey.Mock((*CreateTicketHandler).preCheck).To(func(ctx context.Context, ticketId string) {}).Build()
			mockey.Mock(time.Sleep).To(func(d time.Duration) {}).Build() // 避免测试等待

			resp, err := h.CreateTicket(ctx, req)

			convey.So(err, convey.ShouldBeNil)
			convey.So(resp.Code, convey.ShouldEqual, model.ErrCode_Success)
			convey.So(resp.TicketId, convey.ShouldEqual, utils.Int64ToStr(mockTicketID))
		})

		mockey.PatchConvey("成功场景: Volc引擎实例, 多云平台, 成功创建BPM工单", func() {
			// 场景描述：
			// 当请求为火山引擎实例，且为多云平台时，所有校验和依赖调用均成功，包括BPM流程，最终成功创建工单。
			// 数据构造：
			// - req.InstanceType: model.InstanceType_MySQL (Volc)
			// 逻辑链路：
			// 1. IsVolcInstance 判断为 true。
			// 2. 各项校验通过。
			// 3. IsRDSMultiCloudPlatform 判断为 true，进入BPM流程。
			// 4. getBPMAssigneeUsers 获取审批人成功。
			// 5. workflowService.CreateBpmWorkflowRecord 创建BPM记录成功。
			// 6. 后续流程同非BPM场景，均成功。
			// 7. 函数返回成功的响应。
			mockey.Mock((*CreateTicketHandler).checkReq).Return(nil).Build()
			mockey.Mock((*CreateTicketHandler).checkSqlText).Return(nil).Build()
			mockey.Mock(fwctx.GetTenantID).Return("test_tenant").Build()
			mockey.Mock(fwctx.GetUserID).Return("test_user").Build()
			mockey.Mock((*MockTicketService_CreateTicket).IsUserExists).Return(true, nil).Build()
			mockey.Mock((*MockTicketService_CreateTicket).IsInstanceAvailable).Return(true, nil).Build()
			mockey.Mock((*MockIdgenService_CreateTicket).NextID).Return(mockTicketID, nil).Build()
			mockey.Mock(tenant.IsRDSMultiCloudPlatform).Return(true).Build()
			mockey.Mock((*CreateTicketHandler).getBPMAssigneeUsers).Return([]string{"bpm_user"}, nil).Build()
			mockey.Mock((*MockTicketWorkflowService_CreateTicket).CreateBpmWorkflowRecord).Return(mockWorkflowID, nil).Build()
			mockey.Mock((*CreateTicketHandler).createApprovalFlow).Return(mockApprovalFlowID, mockApprovalTemplateID, nil).Build()
			mockey.Mock((*MockTicketService_CreateTicket).CreateTicket).Return("some_id", nil).Build()
			mockey.Mock((*CreateTicketHandler).preCheck).To(func(ctx context.Context, ticketId string) {}).Build()
			mockey.Mock(time.Sleep).To(func(d time.Duration) {}).Build()

			resp, err := h.CreateTicket(ctx, req)

			convey.So(err, convey.ShouldBeNil)
			convey.So(resp.Code, convey.ShouldEqual, model.ErrCode_Success)
			convey.So(resp.TicketId, convey.ShouldEqual, utils.Int64ToStr(mockTicketID))
		})

		mockey.PatchConvey("成功场景: 字节云实例, 转发创建工单", func() {
			// 场景描述：
			// 当请求为字节云实例时，函数应直接转发请求到字节云处理。
			// 数据构造：
			// - req.InstanceType: model.InstanceType_ByteRDS
			// 逻辑链路：
			// 1. IsVolcInstance 判断为 false。
			// 2. 调用 ForwardCreateTicketToByteRDS 转发请求。
			// 3. 函数直接返回 ForwardCreateTicketToByteRDS 的结果。
			byteRDSReq := &model.CreateTicketReq{
				InstanceType: model.InstanceType_ByteRDS,
				InstanceId:   "byterds-instance-1",
			}
			expectedResp := &model.CreateTicketResp{Code: model.ErrCode_Success, TicketId: "forwarded-ticket-id"}
			mockey.Mock(ForwardCreateTicketToByteRDS).Return(expectedResp, nil).Build()

			resp, err := h.CreateTicket(ctx, byteRDSReq)

			convey.So(err, convey.ShouldBeNil)
			convey.So(resp, convey.ShouldEqual, expectedResp)
		})

		mockey.PatchConvey("失败场景: 请求校验失败 (checkReq)", func() {
			// 场景描述：
			// 当请求参数不合法，checkReq 返回错误时，函数应立即返回错误。
			// 逻辑链路：
			// 1. IsVolcInstance 判断为 true。
			// 2. checkReq 返回错误。
			// 3. 函数直接返回错误响应。
			mockey.Mock((*CreateTicketHandler).checkReq).Return(errors.New("invalid request")).Build()

			resp, err := h.CreateTicket(ctx, req)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "invalid request")
			convey.So(resp.Code, convey.ShouldEqual, model.ErrCode_Error)
			convey.So(resp.ErrMsg, convey.ShouldContainSubstring, "invalid request")
		})

		mockey.PatchConvey("失败场景: SQL文本校验失败 (checkSqlText)", func() {
			// 场景描述：
			// 当工单非数据归档类型，且SQL文本校验失败时，函数应返回错误。
			// 逻辑链路：
			// 1. IsVolcInstance, checkReq 通过。
			// 2. GetCreateFrom 判断为非归档。
			// 3. checkSqlText 返回错误。
			// 4. 函数返回 CreateTicketError 错误。
			mockey.Mock((*CreateTicketHandler).checkReq).Return(nil).Build()
			mockey.Mock((*CreateTicketHandler).checkSqlText).Return(errors.New("invalid sql")).Build()
			mockey.Mock(consts.ErrorWithParam).Return(&StandardErrorForTest{
				Msg: "CreateTicketError: invalid sql",
			}).Build()

			resp, err := h.CreateTicket(ctx, req)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "CreateTicketError: invalid sql")
			convey.So(resp.Code, convey.ShouldEqual, model.ErrCode_Error)
			convey.So(resp.ErrMsg, convey.ShouldContainSubstring, "invalid sql")
		})

		mockey.PatchConvey("失败场景: 未获取到租户ID", func() {
			// 场景描述：
			// 当上下文中无法获取到 TenantID 时，函数应返回参数错误。
			// 逻辑链路：
			// 1. 前置校验通过。
			// 2. fwctx.GetTenantID 返回空字符串。
			// 3. 函数返回 InputParamError。
			mockey.Mock((*CreateTicketHandler).checkReq).Return(nil).Build()
			mockey.Mock((*CreateTicketHandler).checkSqlText).Return(nil).Build()
			mockey.Mock(fwctx.GetTenantID).Return("").Build()
			mockey.Mock(consts.ErrorWithParam).Return(&StandardErrorForTest{
				Msg: "未获取到租户ID",
			}).Build()

			resp, err := h.CreateTicket(ctx, req)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "未获取到租户ID")
			convey.So(resp.Code, convey.ShouldEqual, model.ErrCode_Error)
			convey.So(resp.ErrMsg, convey.ShouldEqual, "未获取到租户ID")
		})

		mockey.PatchConvey("失败场景: 用户不存在或被禁用", func() {
			// 场景描述：
			// 当 ticketService.IsUserExists 校验用户不存在时，函数应返回错误。
			// 逻辑链路：
			// 1. 前置校验通过。
			// 2. ticketService.IsUserExists 返回 false。
			// 3. 函数返回 UserNotJoinUserMgmt 错误。
			mockey.Mock((*CreateTicketHandler).checkReq).Return(nil).Build()
			mockey.Mock((*CreateTicketHandler).checkSqlText).Return(nil).Build()
			mockey.Mock(fwctx.GetTenantID).Return("test_tenant").Build()
			mockey.Mock(fwctx.GetUserID).Return("test_user").Build()
			mockey.Mock((*MockTicketService_CreateTicket).IsUserExists).Return(false, nil).Build()
			mockey.Mock(consts.ErrorOf).Return(&StandardErrorForTest{
				Msg: "用户不存在，或已被删除/禁用",
			}).Build()

			resp, err := h.CreateTicket(ctx, req)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "用户不存在")
			convey.So(resp.Code, convey.ShouldEqual, model.ErrCode_Error)
			convey.So(resp.ErrMsg, convey.ShouldEqual, "用户不存在，或已被删除/禁用")
		})

		mockey.PatchConvey("失败场景: 实例不存在或未纳管", func() {
			// 场景描述：
			// 当 ticketService.IsInstanceAvailable 校验实例不可用时，函数应返回错误。
			// 逻辑链路：
			// 1. 用户校验通过。
			// 2. ticketService.IsInstanceAvailable 返回 false。
			// 3. 函数返回 InstanceNotFound 错误。
			mockey.Mock((*CreateTicketHandler).checkReq).Return(nil).Build()
			mockey.Mock((*CreateTicketHandler).checkSqlText).Return(nil).Build()
			mockey.Mock(fwctx.GetTenantID).Return("test_tenant").Build()
			mockey.Mock(fwctx.GetUserID).Return("test_user").Build()
			mockey.Mock((*MockTicketService_CreateTicket).IsUserExists).Return(true, nil).Build()
			mockey.Mock((*MockTicketService_CreateTicket).IsInstanceAvailable).Return(false, nil).Build()
			mockey.Mock(consts.ErrorOf).Return(&StandardErrorForTest{
				Msg: "实例不存在，或未加入管控",
			}).Build()

			resp, err := h.CreateTicket(ctx, req)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "实例不存在")
			convey.So(resp.Code, convey.ShouldEqual, model.ErrCode_Error)
			convey.So(resp.ErrMsg, convey.ShouldEqual, "实例不存在，或未加入管控")
		})

		mockey.PatchConvey("失败场景: 生成工单ID失败", func() {
			// 场景描述：
			// 当 idg.NextID 调用失败时，函数应返回内部错误。
			// 逻辑链路：
			// 1. 实例校验通过。
			// 2. idg.NextID 返回错误。
			// 3. 函数返回 InternalError。
			mockey.Mock((*CreateTicketHandler).checkReq).Return(nil).Build()
			mockey.Mock((*CreateTicketHandler).checkSqlText).Return(nil).Build()
			mockey.Mock(fwctx.GetTenantID).Return("test_tenant").Build()
			mockey.Mock(fwctx.GetUserID).Return("test_user").Build()
			mockey.Mock((*MockTicketService_CreateTicket).IsUserExists).Return(true, nil).Build()
			mockey.Mock((*MockTicketService_CreateTicket).IsInstanceAvailable).Return(true, nil).Build()
			mockey.Mock((*MockIdgenService_CreateTicket).NextID).Return(int64(0), errors.New("idg failed")).Build()
			mockey.Mock(consts.ErrorOf).Return(&StandardErrorForTest{
				Msg: "InternalError",
			}).Build()

			resp, err := h.CreateTicket(ctx, req)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "InternalError")
			convey.So(resp, convey.ShouldBeNil)
		})

		mockey.PatchConvey("失败场景: 创建BPM审批流失败", func() {
			// 场景描述：
			// 在多云平台场景下，如果创建BPM记录失败，函数应返回系统错误。
			// 逻辑链路：
			// 1. 进入BPM流程。
			// 2. getBPMAssigneeUsers 成功。
			// 3. workflowService.CreateBpmWorkflowRecord 返回错误。
			// 4. 函数返回 SystemError。
			mockey.Mock((*CreateTicketHandler).checkReq).Return(nil).Build()
			mockey.Mock((*CreateTicketHandler).checkSqlText).Return(nil).Build()
			mockey.Mock(fwctx.GetTenantID).Return("test_tenant").Build()
			mockey.Mock(fwctx.GetUserID).Return("test_user").Build()
			mockey.Mock((*MockTicketService_CreateTicket).IsUserExists).Return(true, nil).Build()
			mockey.Mock((*MockTicketService_CreateTicket).IsInstanceAvailable).Return(true, nil).Build()
			mockey.Mock((*MockIdgenService_CreateTicket).NextID).Return(mockTicketID, nil).Build()
			mockey.Mock(tenant.IsRDSMultiCloudPlatform).Return(true).Build()
			mockey.Mock((*CreateTicketHandler).getBPMAssigneeUsers).Return([]string{"bpm_user"}, nil).Build()
			mockey.Mock((*MockTicketWorkflowService_CreateTicket).CreateBpmWorkflowRecord).Return(int64(0), errors.New("bpm create failed")).Build()
			mockey.Mock(consts.ErrorWithParam).Return(&StandardErrorForTest{
				Msg: "get workflow id from bpm error",
			}).Build()

			resp, err := h.CreateTicket(ctx, req)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "get workflow id from bpm error")
			convey.So(resp.Code, convey.ShouldEqual, model.ErrCode_Error)
			convey.So(resp.ErrMsg, convey.ShouldEqual, "创建审批流失败")
		})

		mockey.PatchConvey("失败场景: 创建内部审批流失败 (createApprovalFlow)", func() {
			// 场景描述：
			// 当 createApprovalFlow 方法调用失败时，函数应返回系统错误。
			// 逻辑链路：
			// 1. BPM流程（如果适用）或前置校验通过。
			// 2. createApprovalFlow 返回错误。
			// 3. 函数返回 SystemError。
			mockey.Mock((*CreateTicketHandler).checkReq).Return(nil).Build()
			mockey.Mock((*CreateTicketHandler).checkSqlText).Return(nil).Build()
			mockey.Mock(fwctx.GetTenantID).Return("test_tenant").Build()
			mockey.Mock(fwctx.GetUserID).Return("test_user").Build()
			mockey.Mock((*MockTicketService_CreateTicket).IsUserExists).Return(true, nil).Build()
			mockey.Mock((*MockTicketService_CreateTicket).IsInstanceAvailable).Return(true, nil).Build()
			mockey.Mock((*MockIdgenService_CreateTicket).NextID).Return(mockTicketID, nil).Build()
			mockey.Mock(tenant.IsRDSMultiCloudPlatform).Return(false).Build()
			mockey.Mock((*CreateTicketHandler).createApprovalFlow).Return(int64(0), int64(0), errors.New("create approval flow failed")).Build()
			mockey.Mock(consts.ErrorWithParam).Return(&StandardErrorForTest{
				Msg: "create approval flow error",
			}).Build()

			resp, err := h.CreateTicket(ctx, req)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "create approval flow error")
			convey.So(resp, convey.ShouldBeNil)
		})

		mockey.PatchConvey("失败场景: 数据库创建工单记录失败 (ticketService.CreateTicket)", func() {
			// 场景描述：
			// 当所有前置流程都成功，但在最后一步向数据库写入工单记录时失败，函数应返回系统错误。
			// 逻辑链路：
			// 1. 所有校验和审批流创建均成功。
			// 2. ticketService.CreateTicket 返回错误。
			// 3. 函数返回 SystemError。
			mockey.Mock((*CreateTicketHandler).checkReq).Return(nil).Build()
			mockey.Mock((*CreateTicketHandler).checkSqlText).Return(nil).Build()
			mockey.Mock(fwctx.GetTenantID).Return("test_tenant").Build()
			mockey.Mock(fwctx.GetUserID).Return("test_user").Build()
			mockey.Mock((*MockTicketService_CreateTicket).IsUserExists).Return(true, nil).Build()
			mockey.Mock((*MockTicketService_CreateTicket).IsInstanceAvailable).Return(true, nil).Build()
			mockey.Mock((*MockIdgenService_CreateTicket).NextID).Return(mockTicketID, nil).Build()
			mockey.Mock(tenant.IsRDSMultiCloudPlatform).Return(false).Build()
			mockey.Mock((*CreateTicketHandler).createApprovalFlow).Return(mockApprovalFlowID, mockApprovalTemplateID, nil).Build()
			mockey.Mock((*MockTicketService_CreateTicket).CreateTicket).Return("", errors.New("db create ticket failed")).Build()
			mockey.Mock(consts.ErrorOf).Return(&StandardErrorForTest{
				Msg: "SystemError",
			}).Build()

			resp, err := h.CreateTicket(ctx, req)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "SystemError")
			convey.So(resp.Code, convey.ShouldEqual, model.ErrCode_Error)
			convey.So(resp.ErrMsg, convey.ShouldContainSubstring, "db create ticket failed")
		})
	})
}
