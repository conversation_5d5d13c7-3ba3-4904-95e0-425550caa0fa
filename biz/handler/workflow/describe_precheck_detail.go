package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
)

func NewDescribePreCheckDetailHandler(service workflow.TicketService, cnf config.ConfigProvider) handler.HandlerImplementationEnvolope {
	h := &DescribePreCheckDetailHandler{
		service: service,
		cnf:     cnf,
	}
	return handler.NewHandler(h.DescribePreCheckDetail)
}

type DescribePreCheckDetailHandler struct {
	service workflow.TicketService
	cnf     config.ConfigProvider
}

func (h *DescribePreCheckDetailHandler) DescribePreCheckDetail(ctx context.Context, req *model.DescribePreCheckDetailReq) (*model.DescribePreCheckDetailResp, error) {
	//ticket, err := h.service.GetTicket(ctx, conv.StrToInt64(req.TicketId, 0))
	//if err != nil {
	//	log.Warn(ctx, "ticketId: %d getTicket error :%v", req.TicketId, err)
	//	return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	//}
	//// 这里判断一下是字节云的工单，还是火山的工单,如果是火山实例的工单,forward到火山引擎
	//if !IsVolcInstance(ticket.InstanceType.String()) {
	//	log.Info(ctx, "this is a volc ticket, forward to volc")
	//	return ForwardDescribePreCheckDetailToByteRDS(ctx, byterds.NewByteRDSClient(h.cnf), ticket)
	//}
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}
	return h.service.DescribePreCheckDetail(ctx, req)
}

func (h *DescribePreCheckDetailHandler) checkReq(ctx context.Context, req *model.DescribePreCheckDetailReq) error {
	if req.GetTicketId() == "" {
		log.Warn(ctx, "stopTicket: 工单id错误,请检查")
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "工单id错误,请检查")
	}
	return nil
}
