package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
)

func NewStopTicketHandler(service workflow.TicketService, cnf config.ConfigProvider) handler.HandlerImplementationEnvolope {
	h := &StopTicketHandler{
		service: service,
		cnf:     cnf,
	}
	return handler.NewHandler(h.StopTicket)
}

type StopTicketHandler struct {
	service workflow.TicketService
	cnf     config.ConfigProvider
}

func (h *StopTicketHandler) StopTicket(ctx context.Context, req *model.StopTicketReq) (*model.StopTicketResp, error) {
	//ticket, err := h.service.GetTicket(ctx, conv.StrToInt64(req.TicketId, 0))
	//if err != nil {
	//	log.Warn(ctx, "ticketId: %d getTicket error :%v", req.TicketId, err)
	//	return nil, consts.ErrorOf(model.ErrorCode_SystemError)
	//}
	//if !IsVolcInstance(ticket.InstanceType.String()) {
	//	return ForwardStopTicketToByteRDS(ctx, byterds.NewByteRDSClient(h.cnf), ticket)
	//}
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}
	return h.service.StopTicket(ctx, req)
}

func (h *StopTicketHandler) checkReq(ctx context.Context, req *model.StopTicketReq) error {
	if req.GetTicketId() == "" {
		log.Warn(ctx, "stopTicket: 工单id错误,请检查")
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "工单id错误,请检查")
	}
	return nil
}
