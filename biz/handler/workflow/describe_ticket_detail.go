package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"time"
)

func NewDescribeTicketDetailHandler(service workflow.TicketService, cnf config.ConfigProvider) handler.HandlerImplementationEnvolope {
	h := &DescribeTicketDetailHandler{
		service: service,
		cnf:     cnf,
	}
	return handler.NewHandler(h.DescribeTicketDetail)
}

type DescribeTicketDetailHandler struct {
	service workflow.TicketService
	cnf     config.ConfigProvider
}

func (h *DescribeTicketDetailHandler) DescribeTicketDetail(ctx context.Context, req *model.DescribeTicketDetailReq) (*model.DescribeTicketDetailResp, error) {
	//// 这里判断一下是字节云的工单，还是火山的工单,如果是字节云的工单,forward到字节云去
	//ticket, err := h.service.GetTicket(ctx, conv.StrToInt64(req.TicketId, 0))
	//if err != nil {
	//	log.Warn(ctx, "ticketId: %d getTicket error :%v", req.TicketId, err)
	//	return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	//}
	//if !IsVolcInstance(ticket.InstanceType.String()) {
	//	log.Info(ctx, "this is a volc ticket, forward to volc")
	//	return ForwardDescribeTicketDetailToByteRDS(ctx, byterds.NewByteRDSClient(h.cnf), ticket)
	//}
	if err := h.checkReq(ctx, req); err != nil {
		return nil, err
	}
	resp, err := h.service.DescribeTicketDetail(ctx, req)
	if err != nil {
		return nil, err
	}
	nextTime, err := getNextTime(resp.GetArchiveConfig())
	if err != nil {
		return nil, err
	}
	if resp.ArchiveConfig != nil && resp.GetArchiveConfig().ArchiveType == model.ArchiveType_Cycle && resp.TicketStatus == model.TicketStatus_TicketExecute {
		t := time.Unix(nextTime, 0)
		// 格式化为字符串
		formattedTime := t.Format("2006-01-02 15:04:05")
		resp.NextArchiveTime = &formattedTime
	}
	return resp, nil
}

func (h *DescribeTicketDetailHandler) checkReq(ctx context.Context, req *model.DescribeTicketDetailReq) error {
	if req.GetTicketId() == "" {
		log.Info(ctx, "工单id错误,请检查")
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "工单id错误,请检查")
	}
	return nil
}
