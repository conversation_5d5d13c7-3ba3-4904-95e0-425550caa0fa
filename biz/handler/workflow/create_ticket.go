package workflow

import (
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/byterds"
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	pg_parser "github.com/pganalyze/pg_query_go/v6"

	parser "code.byted.org/infcs/ds-sql-parser"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/handler"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/tenant"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"github.com/qjpcpu/fp"
	"github.com/robfig/cron/v3"
	"go.uber.org/dig"
)

func NewCreateTicketHandler(in NewCreateTicketServiceIn) handler.HandlerImplementationEnvolope {
	h := &CreateTicketHandler{
		ticketService:       in.TicketService,
		workflowService:     in.WorkflowService,
		approvalFlowService: in.ApprovalFlowService,
		cnf:                 in.Cnf,
		idg:                 in.IDgen,
	}
	return handler.NewHandler(h.CreateTicket)
}

type CreateTicketHandler struct {
	ticketService       workflow.TicketService
	workflowService     workflow.TicketWorkflowService
	approvalFlowService approval_flow.ApprovalFlowService
	cnf                 config.ConfigProvider
	idg                 idgen.Service
}

type NewCreateTicketServiceIn struct {
	dig.In
	TicketService       workflow.TicketService
	WorkflowService     workflow.TicketWorkflowService
	ApprovalFlowService approval_flow.ApprovalFlowService
	Cnf                 config.ConfigProvider
	IDgen               idgen.Service
}

func (h *CreateTicketHandler) CreateTicket(ctx context.Context, req *model.CreateTicketReq) (*model.CreateTicketResp, error) {
	// 这里判断一下是字节云的工单，还是火山的工单,如果是字节云的工单,forward到字节云去
	if !IsVolcInstance(req.InstanceType.String()) {
		return ForwardCreateTicketToByteRDS(ctx, byterds.NewByteRDSClient(h.cnf), req)
	}
	if err := h.checkReq(ctx, req); err != nil {
		return &model.CreateTicketResp{Code: model.ErrCode_Error, ErrMsg: err.Error()}, err
	}

	// 加一步SQL校验, 校验SQL是否合法,对归档之外的其他工单做校验
	if req.GetCreateFrom() != workflow.TicketFromDataArchive {
		if err := h.checkSqlText(ctx, req); err != nil {
			return &model.CreateTicketResp{Code: model.ErrCode_Error, ErrMsg: err.Error()}, consts.ErrorWithParam(model.ErrorCode_CreateTicketError, err.Error())
		}
	}
	// 拿租户ID和用户ID
	tenantId := fwctx.GetTenantID(ctx)
	userId := fwctx.GetUserID(ctx)
	if tenantId == "" {
		return &model.CreateTicketResp{Code: model.ErrCode_Error, ErrMsg: "未获取到租户ID"}, consts.ErrorWithParam(model.ErrorCode_InputParamError, "未获取到租户ID")
	}
	if userId == "" {
		// tenantId是一定存在的， 如果userId不存在，那认为这种情况是主账号，因为主账号没有userID
		// TODO 那么是存在一种可能问题，这个用户就是有问题，拿不到他的userId，导致程序识别了他是主账户，而进行了放行
		userId = tenantId
	}
	// 检查用户是不是存在，不存在/已删除/禁用 不能发起工单
	isExist, err := h.ticketService.IsUserExists(ctx, userId, tenantId)
	if err != nil {
		return &model.CreateTicketResp{Code: model.ErrCode_Error, ErrMsg: "获取用户信息失败"}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if !isExist {
		return &model.CreateTicketResp{Code: model.ErrCode_Error, ErrMsg: "用户不存在，或已被删除/禁用"}, consts.ErrorOf(model.ErrorCode_UserNotJoinUserMgmt)
	}
	// 检查实例，需要从实例数据库中获取，需要判断是不是接入安全管控
	isInstanceAvailable, err := h.ticketService.IsInstanceAvailable(ctx, tenantId, req.InstanceId)
	if err != nil {
		return &model.CreateTicketResp{Code: model.ErrCode_Error, ErrMsg: "获取实例信息失败"}, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	if !isInstanceAvailable {
		return &model.CreateTicketResp{Code: model.ErrCode_Error, ErrMsg: "实例不存在，或未加入管控"}, consts.ErrorOf(model.ErrorCode_InstanceNotFound)
	}
	// 这里需要区分一下是小基架账号，还是火山账号
	// 小基架账号需要创建BPM
	// 火山账号不需要创建BPM
	ticketId, err := h.idg.NextID(ctx)
	if err != nil {
		errMsg := fmt.Sprintf("创建工单失败, 生成工单ID失败，err:%s", err.Error())
		log.Warn(ctx, errMsg)
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}

	var (
		workflowId         int64
		approvalFlowId     int64
		approvalTemplateId int64
	)
	// NOTE 小基架账号 && 多云账号需要创建BPM,到这里,其实我们区分不出来是小基架还是多云的工单
	if tenant.IsRDSMultiCloudPlatform(ctx, h.cnf) {
		log.Info(ctx, "create bpm workflow for multi cloud platform or sinf")
		// 根据工单状态，获取不同的审批模板id
		configData := workflow.BpmConfigData{
			InstanceId: req.InstanceId,
			//Assignee:   "yeyazhou.54", // FixMe 这里审批人先写死可以用来测试,后面改为从用户信息中获取
			DBName:     req.DatabaseName,
			ExecSQL:    req.GetSqlText(),
			Background: req.GetMemo(),
			TicketID:   utils.Int64ToStr(ticketId),
			TicketType: req.TicketType.String(),
		}
		users, err := h.getBPMAssigneeUsers(ctx, req)
		if err != nil {
			log.Warn(ctx, "get assignee users error %s", err)
			return &model.CreateTicketResp{Code: model.ErrCode_Error, ErrMsg: "创建审批流失败"}, consts.ErrorWithParam(model.ErrorCode_SystemError, fmt.Sprintf("get assignee users error %s", err))
		}
		configData.Assignee = users
		workflowId, err = h.workflowService.CreateBpmWorkflowRecord(ctx, BPMAuthTypeJWT, configData)
		if err != nil {
			log.Warn(ctx, "get workflow id from bpm error %v", err)
			return &model.CreateTicketResp{Code: model.ErrCode_Error, ErrMsg: "创建审批流失败"}, consts.ErrorWithParam(model.ErrorCode_SystemError, fmt.Sprintf("get workflow id from bpm error %v", err))
		}
	}

	approvalFlowId, approvalTemplateId, err = h.createApprovalFlow(ctx, req, tenantId)
	if err != nil {
		log.Warn(ctx, "createApprovalFlow error: %s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "create approval flow error")
	}

	_, err = h.ticketService.CreateTicket(ctx, req, approvalFlowId, approvalTemplateId, workflowId, ticketId, tenantId, userId)
	if err != nil {
		return &model.CreateTicketResp{Code: model.ErrCode_Error, ErrMsg: err.Error()}, consts.ErrorOf(model.ErrorCode_SystemError)
	}
	h.preCheck(ctx, utils.Int64ToStr(ticketId))
	time.Sleep(1 * time.Second)
	return &model.CreateTicketResp{Code: model.ErrCode_Success, TicketId: utils.Int64ToStr(ticketId)}, nil
}

func (h *CreateTicketHandler) createApprovalFlow(ctx context.Context, req *model.CreateTicketReq, tenantId string) (int64, int64, error) {
	instanceInfo, err := h.approvalFlowService.GetInstanceInfo(ctx, req.InstanceId, req.InstanceType.String(), tenantId)
	if err != nil {
		log.Warn(ctx, "GetInstanceInfo error: %s", err.Error())
		return 0, 0, err
	}
	// 获取工单的审批模板id ticketApprovalTemplateId
	flowConfig, err := h.approvalFlowService.GetApprovalFlowConfig(ctx, instanceInfo.ApprovalFlowConfigId)
	if err != nil {
		log.Warn(ctx, "ListApprovalFlowConfig error: %s", err.Error())
		return 0, 0, err
	}
	if flowConfig == nil {
		log.Warn(ctx, "there is no flowConfig configId:%d", instanceInfo.ApprovalFlowConfigId)
		return 0, 0, fmt.Errorf(fmt.Sprintf("there is no flowConfig configId:%d", instanceInfo.ApprovalFlowConfigId))
	}
	ticketApprovalTemplateId := h.getTicketApprovalTemplateID(flowConfig)
	approvalFlowId, err := h.approvalFlowService.CreatApprovalFlowRecord(ctx, fmt.Sprintf("%d", ticketApprovalTemplateId), req.InstanceId)
	if err != nil {
		log.Warn(ctx, "CreatApprovalFlowRecord error: %s", err.Error())
		return 0, 0, err
	}
	return approvalFlowId, ticketApprovalTemplateId, nil
}

func (h *CreateTicketHandler) getTicketApprovalTemplateID(flowConfig *entity.ApprovalFlowConfig) int64 {
	for _, flowScenes := range flowConfig.FlowScenes {
		if flowScenes.ScenesType == entity.TicketFlowType {
			return flowScenes.TemplateId
		}
	}
	return 0
}

func (h *CreateTicketHandler) GetMultiCloudDMLConfigId(ctx context.Context) int64 {
	cnf := h.cnf.Get(ctx)
	return cnf.BpmMultiCloudDMLConfigId
}

func (h *CreateTicketHandler) GetMultiCloudDDLConfigId(ctx context.Context) int64 {
	cnf := h.cnf.Get(ctx)
	return cnf.BpmMultiCloudDDLConfigId
}

func (h *CreateTicketHandler) getBPMAssigneeUsers(ctx context.Context, req *model.CreateTicketReq) (usersName []string, err error) {
	instanceInfo, err := h.approvalFlowService.GetInstanceInfo(ctx, req.InstanceId, req.InstanceType.String(), fwctx.GetTenantID(ctx))
	if err != nil {
		log.Warn(ctx, "GetInstanceInfo error: %s", err.Error())
		return
	}
	configIdStr := strconv.FormatInt(instanceInfo.ApprovalFlowConfigId, 10)
	flowConfigs, err := h.approvalFlowService.ListApprovalFlowConfig(ctx, fwctx.GetTenantID(ctx), &model.ListApprovalFlowConfigReq{ConfigId: &configIdStr, PageNumber: 1, PageSize: 1})
	if err != nil {
		log.Warn(ctx, "ListApprovalFlowConfig error: %s", err.Error())
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	var templateId int64
	if len(flowConfigs) > 0 && len(flowConfigs[0].FlowScenes) > 0 {
		templateId = flowConfigs[0].FlowScenes[0].TemplateId
	}
	nodes, err := h.formatTemplateApprovalNodeInfo(ctx, templateId)
	for _, node := range nodes {
		for _, user := range node.ApprovalUsers {
			usersName = append(usersName, user.UserName)
		}
	}
	return
}

func (h *CreateTicketHandler) formatTemplateApprovalNodeInfo(ctx context.Context, templateId int64) ([]*model.ApprovalNode, error) {
	template, err := h.approvalFlowService.DescribeApprovalTemplate(ctx, templateId)
	if err != nil {
		log.Warn(ctx, "DescribeApprovalTemplate error: %s", err.Error())
		return nil, err
	}
	users := fp.StreamOf(template.FlowNodes).Map(func(node *entity.ApprovalNode) string {
		if node != nil {
			return node.ApproverIds
		}
		return ""
	}).JoinStrings(",")

	userNameMap, err := h.ticketService.GetUserNameByIds(ctx, users, fwctx.GetTenantID(ctx))
	if err != nil {
		log.Warn(ctx, "获取用户信息报错：%s", err.Error())
		return nil, consts.ErrorWithParam(model.ErrorCode_SystemError, "get user info error")
	}
	return h.covertNode(template, *userNameMap), nil
}

func (h *CreateTicketHandler) covertNode(template *entity.ApprovalFlowTemplate, userNameMap map[string]string) []*model.ApprovalNode {
	var result []*model.ApprovalNode
	for _, node := range template.FlowNodes {
		approvalNode := &model.ApprovalNode{
			NodeId:        strconv.FormatInt(node.NodeId, 10),
			NodeName:      node.NodeName,
			NodeType:      model.ConfigType(node.NodeType),
			CreateUser:    &model.UserInfo{UserId: node.CreateUser, UserName: userNameMap[node.CreateUser]},
			Memo:          &node.Memo,
			ApprovalUsers: covertApprovalUsers(node.ApproverIds, userNameMap),
		}
		result = append(result, approvalNode)
	}

	return result
}

func (h *CreateTicketHandler) checkReq(ctx context.Context, req *model.CreateTicketReq) error {
	log.Info(ctx, "create ticket req is %v", utils.Show(req))
	// 目前支持mysql、vedb、pg工单
	if req.GetInstanceType() != model.InstanceType_MySQL && req.GetInstanceType() != model.InstanceType_VeDBMySQL &&
		req.GetInstanceType() != model.InstanceType_Postgres && req.GetInstanceType() != model.InstanceType_MySQLSharding &&
		req.GetInstanceType() != model.InstanceType_MetaMySQL {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "instance type error,please check")
	}
	if req.GetInstanceId() == "" ||
		req.GetCreateUser() == "" || req.GetDatabaseName() == "" {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "input param error,please check if instance_id、create_user or database_name is empty")
	}
	if !handler.ValidateInstanceId(req.InstanceId) {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "input param instance_id error")
	}
	// 定时工单,必须有起始时间
	if req.GetTicketExecuteType() == model.ExecuteType_Cron && req.GetExecStartTime() <= 0 {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "this is a cron DDL task,but start time is invalid")
	}
	// 自动执行和手动执行的工单,如果没有设置起止时间,则设置为0
	if req.ExecStartTime == nil {
		req.SetExecStartTime(utils.Int32Ref(0))
	}
	if req.ExecEndTime == nil {
		req.SetExecEndTime(utils.Int32Ref(0))
	}
	if req.GetExecStartTime() < 0 || req.GetExecEndTime() < 0 {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "DDL exec time error,please check")
	}
	if req.GetExecEndTime() != 0 && req.GetExecStartTime() > req.GetExecEndTime() {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "exec time range error")
	}

	// 无锁DML变更 参数限制
	// 如果没有设置参数,默认为每个批次1000条记录,每10批次记录sleep 0.5秒,
	if req.GetTicketType() == model.TicketType_FreeLockSqlChange {
		if req.BatchConfig != nil && req.BatchConfig.GetBatchSize() < consts.TicketBatchLowest {
			return consts.ErrorWithParam(model.ErrorCode_InputParamError, fmt.Sprintf("batch size lower than %d,cannot execute!!!", consts.TicketBatchLowest))
		}
		if req.BatchConfig == nil {
			req.BatchConfig = new(model.BatchConfig)
			req.BatchConfig.BatchSize = utils.Int32Ref(consts.TicketBatchLimit)
			req.BatchConfig.SleepTimeMs = utils.Int32Ref(consts.TicketBatchSleepMs)
			req.BatchConfig.IsEnableDelayCheck = utils.BoolRef(false)
			req.BatchConfig.ReplicaDelaySeconds = utils.Int32Ref(0)
			req.BatchConfig.DBBatchNum = utils.Int32Ref(consts.TicketDBBatchNum)
		}
	}

	if req.GetCreateFrom() == workflow.TicketFromDataArchive {
		if req.ArchiveConfig == nil {
			return consts.ErrorWithParam(model.ErrorCode_InputParamError, "ArchiveConfig is illegal")
		}
		if err := setCronStr(ctx, req); err != nil {
			return consts.ErrorWithParam(model.ErrorCode_InputParamError, "cron str is illegal")
		}
		if req.SqlText == nil || strings.TrimSpace(req.GetSqlText()) == "" {
			tmpSqlText := "data archive"
			// 数据归档用不到sqlText字段，修改这个内容，将这个改为 归档字样，飞书发送用户观察
			req.SqlText = &tmpSqlText
		}
		// TODO 检查归档备份的参数
	}
	// 如果工单说明超过1000个字符,只取前1000个
	if req.IsSetMemo() && len(req.GetMemo()) > 1000 {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, fmt.Sprintf("memo is too long,[%v] more than 1000", len(req.GetMemo())))
	}
	if !req.IsSetMemo() {
		req.SetMemo(utils.StringRef(""))
	}
	if req.IsSetTitle() && len(req.GetTitle()) > 1000 {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, fmt.Sprintf("title is too long,[%v] more than 1000", len(req.GetTitle())))
	}
	if !req.IsSetTitle() {
		req.SetTitle(utils.StringRef(""))
	}
	// 校验SQL语法

	return nil
}

func setCronStr(ctx context.Context, req *model.CreateTicketReq) error {
	if req.GetArchiveConfig().ArchiveType == model.ArchiveType_Once {
		return nil
	}
	if req.GetArchiveConfig().ArchiveCycleInfo == nil {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, "ArchiveCycleInfo is illegal")
	}
	cronStr, err := formatCronStr(req.ArchiveConfig.GetArchiveCycleInfo())
	if err != nil {
		return consts.ErrorWithParam(model.ErrorCode_InputParamError, err.Error())
	}
	req.ArchiveConfig.ArchiveCycleInfo.SetCronStr(&cronStr)
	// 检查cron生成后的表达式，是否合法
	cronParser := cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor)
	_, err = cronParser.Parse(cronStr)
	if err != nil {
		log.Warn(ctx, "parser cron str error:%s", err.Error())
		return err
	}
	return nil
}

func formatCronStr(cycleInfo *model.ArchiveCycleInfo) (string, error) {
	switch cycleInfo.CycleType {
	case model.CycleType_Hour:
		return formatHourCron(cycleInfo)
	case model.CycleType_Day:
		return formatDayCronNew(cycleInfo)
	case model.CycleType_Week:
		return formatWeekCronNew(cycleInfo)
	case model.CycleType_Month:
		return formatMonthCronNew(cycleInfo)
	case model.CycleType_Cron:
		return cycleInfo.GetCronStr(), nil
	}
	return "", nil
}

func formatHourCron(cycleInfo *model.ArchiveCycleInfo) (string, error) {
	if cycleInfo.GetIsFixed() {
		return formatFixedHourCron(cycleInfo)
	}
	return formatUnFixedHourCron(cycleInfo)
}

func formatFixedHourCron(cycleInfo *model.ArchiveCycleInfo) (string, error) {
	cronBase := "00 00 %s * * ?"
	hours := intArraysToString(cycleInfo.Hours)
	return fmt.Sprintf(cronBase, hours), nil
}

func formatUnFixedHourCron(cycleInfo *model.ArchiveCycleInfo) (string, error) {
	cronBase := "00 %s %s-%s/%d * * ?"
	startTime := strings.Split(cycleInfo.GetRangeStart(), ":")
	if len(startTime) != 2 {
		return "", fmt.Errorf(" time is illegal，just support hh:MM format")
	}
	entTime := strings.Split(cycleInfo.GetRangeEnd(), ":")
	if len(entTime) != 2 {
		return "", fmt.Errorf(" time is illegal，just support hh:MM format")
	}

	return fmt.Sprintf(cronBase, startTime[1], startTime[0], entTime[0], cycleInfo.GetBetween()), nil
}

func formatDayCron(cycleInfo *model.ArchiveCycleInfo) (string, error) {
	cronBase := "00 %s %s * * ?"
	time := strings.Split(cycleInfo.GetFixedHour(), ":")
	if len(time) != 2 {
		return "", fmt.Errorf(" time is illegal，just support hh:MM format")
	}
	return fmt.Sprintf(cronBase, time[1], time[0]), nil
}

func formatWeekCron(cycleInfo *model.ArchiveCycleInfo) (string, error) {
	cronBase := "00 %s %s ? * %s"
	time := strings.Split(cycleInfo.GetFixedHour(), ":")
	if len(time) != 2 {
		return "", fmt.Errorf(" time is illegal，just support hh:MM format")
	}
	weeks := intArraysToString(cycleInfo.Weeks)
	return fmt.Sprintf(cronBase, time[1], time[0], weeks), nil
}

func intArraysToString(nums []int32) string {
	return fp.StreamOf(nums).Map(func(num int32) string {
		return fmt.Sprintf("%d", num)
	}).JoinStrings(",")
}

func formatMonthCron(cycleInfo *model.ArchiveCycleInfo) (string, error) {
	cronBase := "00 %s %s %s * ?"
	time := strings.Split(cycleInfo.GetFixedHour(), ":")
	if len(time) != 2 {
		return "", fmt.Errorf(" time is illegal，just support hh:MM format")
	}
	months := fp.StreamOf(cycleInfo.DateMonth).JoinStrings(",")
	return fmt.Sprintf(cronBase, time[1], time[0], months), nil
}

func covertApprovalUsers(approvalIds string, userNameMap map[string]string) []*model.UserInfo {
	spaceRe, _ := regexp.Compile(`\s*,\s*`)
	idList := spaceRe.Split(approvalIds, -1)
	var res []*model.UserInfo
	for _, id := range idList {
		res = append(res, &model.UserInfo{UserId: id, UserName: userNameMap[id]})
	}
	return res
}

func (h *CreateTicketHandler) preCheck(ctx context.Context, ticketId string) {
	go func() {
		_, _ = h.ticketService.PreCheck(ctx, &model.PreCheckTicketReq{TicketId: ticketId})
	}()
}

func (h *CreateTicketHandler) checkSqlText(ctx context.Context, req *model.CreateTicketReq) error {
	switch req.InstanceType {
	case model.InstanceType_MySQL, model.InstanceType_VeDBMySQL, model.InstanceType_MetaMySQL:
		return checkMySQLFormat(ctx, req)
	case model.InstanceType_Postgres: // Pg语法解析单独写一份
		return checkPgFormat(ctx, req)
	case model.InstanceType_MySQLSharding:
		return nil
	default:
		return fmt.Errorf("instance type error")
	}
}

func checkMySQLFormat(ctx context.Context, req *model.CreateTicketReq) error {
	p := parser.New()
	stmts, _, err := p.Parse(req.GetSqlText(), "utf8", "")
	if err != nil {
		log.Warn(ctx, fmt.Sprintf("SQL statement %v error or unsupported, reason: %v", req.GetSqlText(), err))
		return err
	}
	if len(stmts) == 0 {
		log.Warn(ctx, fmt.Sprintf("SQL statement %v error or unsupported, reason: empty stmts", req.GetSqlText()))
		return err
	}
	return nil
}

func checkPgFormat(ctx context.Context, req *model.CreateTicketReq) error {
	// 0、解析SQL
	res, err := pg_parser.Parse(req.GetSqlText())
	if err != nil {
		log.Warn(ctx, "SQL statement %v error or unsupported, reason: %v", req.GetSqlText(), err)
		return err
	}
	if len(res.Stmts) == 0 {
		log.Warn(ctx, fmt.Sprintf("SQL statement %v error or unsupported, reason: empty stmts", req.GetSqlText()))
		return err
	}
	return nil
}

// 清表工单优化的地方的修改
// cronParser := cron.NewParser(cron.Second | cron.Minute | cron.Hour | cron.Dom | cron.Month | cron.Dow | cron.Descriptor)
func formatDayCronNew(cycleInfo *model.ArchiveCycleInfo) (string, error) {
	cronBase := "00 00 %s * * ?"
	hours := intArraysToString(cycleInfo.GetHours())
	return fmt.Sprintf(cronBase, hours), nil
}

func formatWeekCronNew(cycleInfo *model.ArchiveCycleInfo) (string, error) {
	cronBase := "00 00 %s ? * %s"
	hours := intArraysToString(cycleInfo.GetHours())
	weeks := intArraysToString(cycleInfo.GetWeeks())
	return fmt.Sprintf(cronBase, hours, weeks), nil
}

func formatMonthCronNew(cycleInfo *model.ArchiveCycleInfo) (string, error) {
	cronBase := "00 00 %s %s * ?"
	hours := intArraysToString(cycleInfo.GetHours())
	months := fp.StreamOf(cycleInfo.DateMonth).JoinStrings(",")
	return fmt.Sprintf(cronBase, hours, months), nil
}
