package handler

import (
	"context"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	bizUtils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type CreateDatabaseTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *CreateDatabaseTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *CreateDatabaseTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestCreateDatabaseTestSuite(t *testing.T) {
	suite.Run(t, new(CreateDatabaseTestSuite))
}

// 测试当租户在DBLessTenantIdList白名单中时，IsTenantEnabledFromCtx返回true
func (suite *CreateDatabaseTestSuite) TestCreateDatabase_DBLessTenant_IsTenantEnabled() {
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "test-tenant-123"

	// Test config with tenant in DBLessTenantIdList
	testConfig := &config.Config{
		DBLessTenantIdList: []string{"test-tenant-123"},
	}

	// Test the utility function that our code uses
	result := bizUtils.IsTenantEnabledFromCtx(ctx, testConfig.DBLessTenantIdList)
	suite.True(result, "IsTenantEnabledFromCtx should return true for whitelisted tenant")
}

// 测试当租户不在DBLessTenantIdList白名单中时，IsTenantEnabledFromCtx返回false
func (suite *CreateDatabaseTestSuite) TestCreateDatabase_NonDBLessTenant_IsTenantNotEnabled() {
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "other-tenant-456"

	// Test config without tenant in DBLessTenantIdList
	testConfig := &config.Config{
		DBLessTenantIdList: []string{"test-tenant-123"}, // different tenant
	}

	// Test the utility function that our code uses
	result := bizUtils.IsTenantEnabledFromCtx(ctx, testConfig.DBLessTenantIdList)
	suite.False(result, "IsTenantEnabledFromCtx should return false for non-whitelisted tenant")
}

// 测试通配符 "*" 的情况
func (suite *CreateDatabaseTestSuite) TestCreateDatabase_WildcardTenant_IsTenantEnabled() {
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "any-tenant"

	// Test config with wildcard in DBLessTenantIdList
	testConfig := &config.Config{
		DBLessTenantIdList: []string{"*"},
	}

	// Test the utility function that our code uses
	result := bizUtils.IsTenantEnabledFromCtx(ctx, testConfig.DBLessTenantIdList)
	suite.True(result, "IsTenantEnabledFromCtx should return true for wildcard config")
}

// 测试空白名单的情况
func (suite *CreateDatabaseTestSuite) TestCreateDatabase_EmptyTenantList_IsTenantNotEnabled() {
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "any-tenant"

	// Test config with empty DBLessTenantIdList
	testConfig := &config.Config{
		DBLessTenantIdList: []string{},
	}

	// Test the utility function that our code uses
	result := bizUtils.IsTenantEnabledFromCtx(ctx, testConfig.DBLessTenantIdList)
	suite.False(result, "IsTenantEnabledFromCtx should return false for empty tenant list")
}

// 测试DescribeCommand调用的逻辑
func (suite *CreateDatabaseTestSuite) TestCreateDatabase_DescribeCommandLogic() {
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "test-tenant-123"

	// Mock config provider
	mockConfigProvider := mocks.NewMockConfigProvider(suite.ctrl)
	testConfig := &config.Config{
		DBLessTenantIdList: []string{"test-tenant-123"},
	}
	mockConfigProvider.EXPECT().Get(ctx).Return(testConfig).AnyTimes()

	// Test the logic that would be executed in the CreateDatabase method
	cnf := mockConfigProvider.Get(ctx)

	// Simulate the condition check
	if bizUtils.IsTenantEnabledFromCtx(ctx, cnf.DBLessTenantIdList) {
		// This is where DescribeCommand would be called
		// We're testing that the condition is met
		suite.True(true, "Should enter the DBLess tenant branch")

		// Simulate the command structure that would be used
		commandId := "test-command-456"

		// Verify the command ID is properly set for DescribeCommand request
		describeReq := &model.DescribeCommandReq{
			CommandId: utils.StringRef(commandId),
		}

		suite.NotNil(describeReq.CommandId)
		suite.Equal(commandId, describeReq.GetCommandId())
	} else {
		suite.Fail("Should have entered the DBLess tenant branch")
	}
}

// 测试非DBLess租户不会进入DescribeCommand逻辑
func (suite *CreateDatabaseTestSuite) TestCreateDatabase_NonDBLessTenant_SkipsDescribeCommand() {
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "other-tenant-456"

	// Mock config provider
	mockConfigProvider := mocks.NewMockConfigProvider(suite.ctrl)
	testConfig := &config.Config{
		DBLessTenantIdList: []string{"test-tenant-123"}, // different tenant
	}
	mockConfigProvider.EXPECT().Get(ctx).Return(testConfig).AnyTimes()

	// Test the logic that would be executed in the CreateDatabase method
	cnf := mockConfigProvider.Get(ctx)

	// Simulate the condition check
	if bizUtils.IsTenantEnabledFromCtx(ctx, cnf.DBLessTenantIdList) {
		suite.Fail("Should not have entered the DBLess tenant branch")
	} else {
		// This is the expected path for non-whitelisted tenants
		suite.True(true, "Should skip the DBLess tenant branch")
	}
}
