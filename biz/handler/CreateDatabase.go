package handler

import (
	bizUtils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"context"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"

	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
)

func (h *CreateDatabaseHandler) CreateDatabase(ctx context.Context, req *model.CreateDatabaseReq) (*model.CreateDatabaseResp, error) {
	if err := checkDBName(req.GetDB()); err != nil {
		return nil, err
	}
	if req.GetCharacterSet() == "" {
		return nil, consts.ErrorOf(model.ErrorCode_ParamError)
	}

	s := []string{
		"CREATE DATABASE",
		"`" + strings.TrimSpace(req.GetDB()) + "`",
		"CHARACTER SET",
		req.GetCharacterSet(),
	}
	if req.GetCollate() != "" {
		s = append(s, "COLLATE "+req.GetCollate())
	}

	cmd := strings.Join(s, " ")

	resp, err := h.executeCommandSetHandler.ExecuteCommandSet(ctx, &model.ExecuteCommandSetReq{
		SessionId:         req.GetSessionId(),
		ConnectionId:      req.GetConnectionId(),
		CommandSetContent: utils.StringRef(cmd),
	})
	if err != nil {
		log.Warn(ctx, "fail to run executeCommandSet, err=%v", err)
		return nil, err
	}
	// Wait command set result, cancel if timeout.
	ticker := time.NewTicker(500 * time.Millisecond)
	defer ticker.Stop()
	timeout := time.NewTimer(10 * time.Second)
	for {
		select {
		case <-ticker.C:
			if ok, err := h.checkCommandSetFinished(ctx, resp.GetCommandSetId()); ok {
				if err != nil {
					return nil, err
				}
				// 不落库查询需要手动DescribeCommand一次
				cnf := h.describeCommandHandler.cnf.Get(ctx)
				if bizUtils.IsTenantEnabledFromCtx(ctx, cnf.DBLessTenantIdList) {
					command := resp.GetCommands()[0]
					descRsp, err := h.describeCommandHandler.DescribeCommand(ctx, &model.DescribeCommandReq{
						CommandId: command.CommandId,
					})
					if err != nil {
						return nil, err
					}
					if descRsp != nil {
						log.Info(ctx, "db less tenant, manual describe command, rows=%+v", descRsp.Rows)
					}
				}
				log.Info(ctx, "create database %s succeed, command set %s", req.GetDB(), resp.GetCommandSetId())
				return &model.CreateDatabaseResp{}, nil
			}
		case <-timeout.C:
			log.Warn(ctx, "create database timeout, command set %s", resp.GetCommandSetId())
			h.cancelCommandSetHandler.CancelCommandSet(ctx, &model.CancelCommandSetReq{
				CommandSetId: utils.StringRef(resp.GetCommandSetId()),
			})
			err = consts.BuildDBErrorWithParam(model.ErrorCode_CommandSetExecuteTimeout, req.GetDB())
			return nil, err
		}
	}

}

func (h *CreateDatabaseHandler) checkCommandSetFinished(ctx context.Context, csID string) (bool, error) {
	log.Info(ctx, "check whether command set %s is ready", csID)
	resp, err := h.describeCommandSetHandler.DescribeCommandSet(ctx, &model.DescribeCommandSetReq{
		CommandSetId: utils.StringRef(csID),
	})
	if err != nil {
		log.Warn(ctx, "DescribeCommandSet set %s error", csID)
		return false, err
	}

	// Check the execution progress of commandSet.
	if resp.GetEndTime() == 0 {
		return false, nil
	}
	log.Info(ctx, "command set %s ended", csID)

	// All command in CommandSet is terminated.
	if len(resp.GetCommands()) <= 0 {
		log.Warn(ctx, "command set %s has no command", csID)
		return true, consts.ErrorOf(model.ErrorCode_CommandSetEmpty)
	}
	if reason := resp.GetCommands()[0].GetReason(); reason != model.CommandTerminatedReason_Success {
		log.Warn(ctx, "command set %s failed by %s", csID, resp.GetCommands()[0].GetReasonDetail())
		return true, consts.ErrorWithParam(model.ErrorCode_CommandExecuteFailed, resp.GetCommands()[0].GetReasonDetail())
	}
	return true, nil
}

// CreateDatabaseHandler represents a handler to create database.
type CreateDatabaseHandler struct {
	executeCommandSetHandler  *ExecuteCommandSetHandler
	describeCommandSetHandler *DescribeCommandSetHandler
	describeCommandHandler    *DescribeCommandHandler
	cancelCommandSetHandler   *CancelCommandSetHandler
}

// NewCreateDatabaseHandler returns a new envelope of CreateDatabaseHandler.
func NewCreateDatabaseHandler(
	executeCommandSetHandler *ExecuteCommandSetHandler,
	describeCommandSetHandler *DescribeCommandSetHandler,
	describeCommandHandler *DescribeCommandHandler,
	cancelCommandSetHandler *CancelCommandSetHandler,
) HandlerImplementationEnvolope {
	hder := &CreateDatabaseHandler{
		executeCommandSetHandler:  executeCommandSetHandler,
		describeCommandSetHandler: describeCommandSetHandler,
		cancelCommandSetHandler:   cancelCommandSetHandler,
		describeCommandHandler:    describeCommandHandler,
	}
	return NewHandler(hder.CreateDatabase)
}
