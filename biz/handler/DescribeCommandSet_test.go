package handler

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"context"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"testing"
)

type DescribeCommandSetTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *DescribeCommandSetTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *DescribeCommandSetTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestDescribeCommandSetTestSuite(t *testing.T) {
	suite.Run(t, new(DescribeCommandSetTestSuite))
}

func (suite *DescribeCommandSetTestSuite) TestReturnCorrect() {
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "tenant11"
	cmdRepo := mocks.NewMockCommandRepo(suite.ctrl)
	req := &model.DescribeCommandSetReq{
		CommandSetId: utils.StringRef("xxx"),
	}
	cs := &entity.CommandSet{
		ID:           "11",
		SessionID:    "xxx",
		CreateTimeMS: 1112343,
		EndTimeMS:    2223434,
		Progress:     1,
		Content:      "xxxx",
		TenantID:     "tenant11",
	}
	cmdRepo.EXPECT().GetCommandSet(ctx, gomock.Any()).Return(cs, nil)
	h := &DescribeCommandSetHandler{cmdRepo: cmdRepo}
	suite.Suite.Run("xxx", func() {
		_, err := h.DescribeCommandSet(ctx, req)
		suite.Empty(err)
	})
}

func (suite *DescribeCommandSetTestSuite) TestReturnError() {
	ctx := fwctx.SetBizContext(context.Background())
	bizCtx := fwctx.GetBizContext(ctx)
	bizCtx.TenantID = "11"
	cmdRepo := mocks.NewMockCommandRepo(suite.ctrl)
	req := &model.DescribeCommandSetReq{
		CommandSetId: utils.StringRef("xxx"),
	}
	cs := &entity.CommandSet{
		ID:           "11",
		SessionID:    "xxx",
		CreateTimeMS: 1112343,
		EndTimeMS:    2223434,
		Progress:     1,
		Content:      "xxxx",
		TenantID:     "tenant11",
	}
	cmdRepo.EXPECT().GetCommandSet(ctx, gomock.Any()).Return(cs, nil)
	h := &DescribeCommandSetHandler{cmdRepo: cmdRepo}
	suite.Suite.Run("xxx", func() {
		_, err := h.DescribeCommandSet(ctx, req)
		suite.NotEmpty(err)
	})
}
