// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: audit.proto

package shared

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strconv "strconv"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type CloseType int32

const (
	CloseType_OnlyCloseAuditCollect   CloseType = 0
	CloseType_CloseAuditCollectAndTls CloseType = 1
)

var CloseType_name = map[int32]string{
	0: "CloseType_OnlyCloseAuditCollect",
	1: "CloseType_CloseAuditCollectAndTls",
}

var CloseType_value = map[string]int32{
	"CloseType_OnlyCloseAuditCollect":   0,
	"CloseType_CloseAuditCollectAndTls": 1,
}

func (CloseType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{0}
}

type UpgradeType int32

const (
	NoneUpgrade              UpgradeType = 0
	UpgradeFullSqlTableAggr  UpgradeType = 1
	UpgradeFullSqlTopicIndex UpgradeType = 3
)

var UpgradeType_name = map[int32]string{
	0: "NoneUpgrade",
	1: "UpgradeFullSqlTableAggr",
	3: "UpgradeFullSqlTopicIndex",
}

var UpgradeType_value = map[string]int32{
	"NoneUpgrade":              0,
	"UpgradeFullSqlTableAggr":  1,
	"UpgradeFullSqlTopicIndex": 3,
}

func (UpgradeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{1}
}

type InstanceFullSqlOpenType int32

const (
	OpenNone               InstanceFullSqlOpenType = 0
	OpenPartOfInstanceNode InstanceFullSqlOpenType = 1
	OpenAllInstanceNode    InstanceFullSqlOpenType = 2
)

var InstanceFullSqlOpenType_name = map[int32]string{
	0: "OpenNone",
	1: "OpenPartOfInstanceNode",
	2: "OpenAllInstanceNode",
}

var InstanceFullSqlOpenType_value = map[string]int32{
	"OpenNone":               0,
	"OpenPartOfInstanceNode": 1,
	"OpenAllInstanceNode":    2,
}

func (InstanceFullSqlOpenType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{2}
}

// PG实例开启审计
type PgCreateAuditReq struct {
	RegionId         string         `protobuf:"bytes,1,opt,name=RegionId,proto3" json:"RegionId,omitempty"`
	FollowInstanceID string         `protobuf:"bytes,2,opt,name=FollowInstanceID,proto3" json:"FollowInstanceID,omitempty"`
	DSType           DataSourceType `protobuf:"varint,3,opt,name=DSType,proto3,enum=shared.DataSourceType" json:"DSType,omitempty"`
	Ttl              int32          `protobuf:"varint,4,opt,name=Ttl,proto3" json:"Ttl,omitempty"`
}

func (m *PgCreateAuditReq) Reset()      { *m = PgCreateAuditReq{} }
func (*PgCreateAuditReq) ProtoMessage() {}
func (*PgCreateAuditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{0}
}
func (m *PgCreateAuditReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PgCreateAuditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PgCreateAuditReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PgCreateAuditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgCreateAuditReq.Merge(m, src)
}
func (m *PgCreateAuditReq) XXX_Size() int {
	return m.Size()
}
func (m *PgCreateAuditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PgCreateAuditReq.DiscardUnknown(m)
}

var xxx_messageInfo_PgCreateAuditReq proto.InternalMessageInfo

func (m *PgCreateAuditReq) GetRegionId() string {
	if m != nil {
		return m.RegionId
	}
	return ""
}

func (m *PgCreateAuditReq) GetFollowInstanceID() string {
	if m != nil {
		return m.FollowInstanceID
	}
	return ""
}

func (m *PgCreateAuditReq) GetDSType() DataSourceType {
	if m != nil {
		return m.DSType
	}
	return NoneType
}

func (m *PgCreateAuditReq) GetTtl() int32 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

// PG实例关闭审计
type PgDeleteAuditReq struct {
	RegionId         string         `protobuf:"bytes,1,opt,name=RegionId,proto3" json:"RegionId,omitempty"`
	FollowInstanceID string         `protobuf:"bytes,2,opt,name=FollowInstanceID,proto3" json:"FollowInstanceID,omitempty"`
	DSType           DataSourceType `protobuf:"varint,3,opt,name=DSType,proto3,enum=shared.DataSourceType" json:"DSType,omitempty"`
	CloseType        CloseType      `protobuf:"varint,4,opt,name=CloseType,proto3,enum=shared.CloseType" json:"CloseType,omitempty"`
}

func (m *PgDeleteAuditReq) Reset()      { *m = PgDeleteAuditReq{} }
func (*PgDeleteAuditReq) ProtoMessage() {}
func (*PgDeleteAuditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{1}
}
func (m *PgDeleteAuditReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PgDeleteAuditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PgDeleteAuditReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PgDeleteAuditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgDeleteAuditReq.Merge(m, src)
}
func (m *PgDeleteAuditReq) XXX_Size() int {
	return m.Size()
}
func (m *PgDeleteAuditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PgDeleteAuditReq.DiscardUnknown(m)
}

var xxx_messageInfo_PgDeleteAuditReq proto.InternalMessageInfo

func (m *PgDeleteAuditReq) GetRegionId() string {
	if m != nil {
		return m.RegionId
	}
	return ""
}

func (m *PgDeleteAuditReq) GetFollowInstanceID() string {
	if m != nil {
		return m.FollowInstanceID
	}
	return ""
}

func (m *PgDeleteAuditReq) GetDSType() DataSourceType {
	if m != nil {
		return m.DSType
	}
	return NoneType
}

func (m *PgDeleteAuditReq) GetCloseType() CloseType {
	if m != nil {
		return m.CloseType
	}
	return CloseType_OnlyCloseAuditCollect
}

type PgCreateAuditSuccess struct {
}

func (m *PgCreateAuditSuccess) Reset()      { *m = PgCreateAuditSuccess{} }
func (*PgCreateAuditSuccess) ProtoMessage() {}
func (*PgCreateAuditSuccess) Descriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{2}
}
func (m *PgCreateAuditSuccess) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PgCreateAuditSuccess) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PgCreateAuditSuccess.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PgCreateAuditSuccess) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgCreateAuditSuccess.Merge(m, src)
}
func (m *PgCreateAuditSuccess) XXX_Size() int {
	return m.Size()
}
func (m *PgCreateAuditSuccess) XXX_DiscardUnknown() {
	xxx_messageInfo_PgCreateAuditSuccess.DiscardUnknown(m)
}

var xxx_messageInfo_PgCreateAuditSuccess proto.InternalMessageInfo

type PgCreateAuditFailResp struct {
	Error string `protobuf:"bytes,1,opt,name=Error,proto3" json:"Error,omitempty"`
}

func (m *PgCreateAuditFailResp) Reset()      { *m = PgCreateAuditFailResp{} }
func (*PgCreateAuditFailResp) ProtoMessage() {}
func (*PgCreateAuditFailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{3}
}
func (m *PgCreateAuditFailResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PgCreateAuditFailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PgCreateAuditFailResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PgCreateAuditFailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgCreateAuditFailResp.Merge(m, src)
}
func (m *PgCreateAuditFailResp) XXX_Size() int {
	return m.Size()
}
func (m *PgCreateAuditFailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PgCreateAuditFailResp.DiscardUnknown(m)
}

var xxx_messageInfo_PgCreateAuditFailResp proto.InternalMessageInfo

func (m *PgCreateAuditFailResp) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type PgDeleteAuditSuccess struct {
}

func (m *PgDeleteAuditSuccess) Reset()      { *m = PgDeleteAuditSuccess{} }
func (*PgDeleteAuditSuccess) ProtoMessage() {}
func (*PgDeleteAuditSuccess) Descriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{4}
}
func (m *PgDeleteAuditSuccess) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PgDeleteAuditSuccess) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PgDeleteAuditSuccess.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PgDeleteAuditSuccess) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgDeleteAuditSuccess.Merge(m, src)
}
func (m *PgDeleteAuditSuccess) XXX_Size() int {
	return m.Size()
}
func (m *PgDeleteAuditSuccess) XXX_DiscardUnknown() {
	xxx_messageInfo_PgDeleteAuditSuccess.DiscardUnknown(m)
}

var xxx_messageInfo_PgDeleteAuditSuccess proto.InternalMessageInfo

type PgDeleteAuditFailResp struct {
	Error string `protobuf:"bytes,1,opt,name=Error,proto3" json:"Error,omitempty"`
}

func (m *PgDeleteAuditFailResp) Reset()      { *m = PgDeleteAuditFailResp{} }
func (*PgDeleteAuditFailResp) ProtoMessage() {}
func (*PgDeleteAuditFailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{5}
}
func (m *PgDeleteAuditFailResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *PgDeleteAuditFailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_PgDeleteAuditFailResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *PgDeleteAuditFailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgDeleteAuditFailResp.Merge(m, src)
}
func (m *PgDeleteAuditFailResp) XXX_Size() int {
	return m.Size()
}
func (m *PgDeleteAuditFailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PgDeleteAuditFailResp.DiscardUnknown(m)
}

var xxx_messageInfo_PgDeleteAuditFailResp proto.InternalMessageInfo

func (m *PgDeleteAuditFailResp) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

type CreateAuditFlowRequest struct {
	InstanceID  string         `protobuf:"bytes,1,opt,name=InstanceID,proto3" json:"InstanceID,omitempty"`
	Msg         string         `protobuf:"bytes,2,opt,name=Msg,proto3" json:"Msg,omitempty"`
	DSType      DataSourceType `protobuf:"varint,3,opt,name=DSType,proto3,enum=shared.DataSourceType" json:"DSType,omitempty"`
	ProductType string         `protobuf:"bytes,4,opt,name=ProductType,proto3" json:"ProductType,omitempty"`
}

func (m *CreateAuditFlowRequest) Reset()      { *m = CreateAuditFlowRequest{} }
func (*CreateAuditFlowRequest) ProtoMessage() {}
func (*CreateAuditFlowRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{6}
}
func (m *CreateAuditFlowRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CreateAuditFlowRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CreateAuditFlowRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CreateAuditFlowRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAuditFlowRequest.Merge(m, src)
}
func (m *CreateAuditFlowRequest) XXX_Size() int {
	return m.Size()
}
func (m *CreateAuditFlowRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAuditFlowRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAuditFlowRequest proto.InternalMessageInfo

func (m *CreateAuditFlowRequest) GetInstanceID() string {
	if m != nil {
		return m.InstanceID
	}
	return ""
}

func (m *CreateAuditFlowRequest) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *CreateAuditFlowRequest) GetDSType() DataSourceType {
	if m != nil {
		return m.DSType
	}
	return NoneType
}

func (m *CreateAuditFlowRequest) GetProductType() string {
	if m != nil {
		return m.ProductType
	}
	return ""
}

type DeleteAuditFlowRequest struct {
	InstanceID  string         `protobuf:"bytes,1,opt,name=InstanceID,proto3" json:"InstanceID,omitempty"`
	Msg         string         `protobuf:"bytes,2,opt,name=Msg,proto3" json:"Msg,omitempty"`
	CloseType   string         `protobuf:"bytes,3,opt,name=CloseType,proto3" json:"CloseType,omitempty"`
	DSType      DataSourceType `protobuf:"varint,4,opt,name=DSType,proto3,enum=shared.DataSourceType" json:"DSType,omitempty"`
	ProductType string         `protobuf:"bytes,5,opt,name=ProductType,proto3" json:"ProductType,omitempty"`
}

func (m *DeleteAuditFlowRequest) Reset()      { *m = DeleteAuditFlowRequest{} }
func (*DeleteAuditFlowRequest) ProtoMessage() {}
func (*DeleteAuditFlowRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{7}
}
func (m *DeleteAuditFlowRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteAuditFlowRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteAuditFlowRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteAuditFlowRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteAuditFlowRequest.Merge(m, src)
}
func (m *DeleteAuditFlowRequest) XXX_Size() int {
	return m.Size()
}
func (m *DeleteAuditFlowRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteAuditFlowRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteAuditFlowRequest proto.InternalMessageInfo

func (m *DeleteAuditFlowRequest) GetInstanceID() string {
	if m != nil {
		return m.InstanceID
	}
	return ""
}

func (m *DeleteAuditFlowRequest) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *DeleteAuditFlowRequest) GetCloseType() string {
	if m != nil {
		return m.CloseType
	}
	return ""
}

func (m *DeleteAuditFlowRequest) GetDSType() DataSourceType {
	if m != nil {
		return m.DSType
	}
	return NoneType
}

func (m *DeleteAuditFlowRequest) GetProductType() string {
	if m != nil {
		return m.ProductType
	}
	return ""
}

type UpgradeInstanceFlowRequest struct {
	InstanceID          string         `protobuf:"bytes,1,opt,name=InstanceID,proto3" json:"InstanceID,omitempty"`
	DSType              DataSourceType `protobuf:"varint,2,opt,name=DSType,proto3,enum=shared.DataSourceType" json:"DSType,omitempty"`
	ProductType         string         `protobuf:"bytes,3,opt,name=ProductType,proto3" json:"ProductType,omitempty"`
	InstanceUpgradeType UpgradeType    `protobuf:"varint,4,opt,name=InstanceUpgradeType,proto3,enum=shared.UpgradeType" json:"InstanceUpgradeType,omitempty"`
}

func (m *UpgradeInstanceFlowRequest) Reset()      { *m = UpgradeInstanceFlowRequest{} }
func (*UpgradeInstanceFlowRequest) ProtoMessage() {}
func (*UpgradeInstanceFlowRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{8}
}
func (m *UpgradeInstanceFlowRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpgradeInstanceFlowRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpgradeInstanceFlowRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UpgradeInstanceFlowRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpgradeInstanceFlowRequest.Merge(m, src)
}
func (m *UpgradeInstanceFlowRequest) XXX_Size() int {
	return m.Size()
}
func (m *UpgradeInstanceFlowRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpgradeInstanceFlowRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpgradeInstanceFlowRequest proto.InternalMessageInfo

func (m *UpgradeInstanceFlowRequest) GetInstanceID() string {
	if m != nil {
		return m.InstanceID
	}
	return ""
}

func (m *UpgradeInstanceFlowRequest) GetDSType() DataSourceType {
	if m != nil {
		return m.DSType
	}
	return NoneType
}

func (m *UpgradeInstanceFlowRequest) GetProductType() string {
	if m != nil {
		return m.ProductType
	}
	return ""
}

func (m *UpgradeInstanceFlowRequest) GetInstanceUpgradeType() UpgradeType {
	if m != nil {
		return m.InstanceUpgradeType
	}
	return NoneUpgrade
}

type DeleteResourceFlowRequest struct {
	InstanceID  string         `protobuf:"bytes,1,opt,name=InstanceID,proto3" json:"InstanceID,omitempty"`
	DSType      DataSourceType `protobuf:"varint,2,opt,name=DSType,proto3,enum=shared.DataSourceType" json:"DSType,omitempty"`
	ProductType string         `protobuf:"bytes,3,opt,name=ProductType,proto3" json:"ProductType,omitempty"`
	CloseType   string         `protobuf:"bytes,4,opt,name=CloseType,proto3" json:"CloseType,omitempty"`
}

func (m *DeleteResourceFlowRequest) Reset()      { *m = DeleteResourceFlowRequest{} }
func (*DeleteResourceFlowRequest) ProtoMessage() {}
func (*DeleteResourceFlowRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{9}
}
func (m *DeleteResourceFlowRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteResourceFlowRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteResourceFlowRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteResourceFlowRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteResourceFlowRequest.Merge(m, src)
}
func (m *DeleteResourceFlowRequest) XXX_Size() int {
	return m.Size()
}
func (m *DeleteResourceFlowRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteResourceFlowRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteResourceFlowRequest proto.InternalMessageInfo

func (m *DeleteResourceFlowRequest) GetInstanceID() string {
	if m != nil {
		return m.InstanceID
	}
	return ""
}

func (m *DeleteResourceFlowRequest) GetDSType() DataSourceType {
	if m != nil {
		return m.DSType
	}
	return NoneType
}

func (m *DeleteResourceFlowRequest) GetProductType() string {
	if m != nil {
		return m.ProductType
	}
	return ""
}

func (m *DeleteResourceFlowRequest) GetCloseType() string {
	if m != nil {
		return m.CloseType
	}
	return ""
}

type GCAuditFlowRequest struct {
	InstanceID string `protobuf:"bytes,1,opt,name=InstanceID,proto3" json:"InstanceID,omitempty"`
	CloseType  string `protobuf:"bytes,2,opt,name=CloseType,proto3" json:"CloseType,omitempty"`
}

func (m *GCAuditFlowRequest) Reset()      { *m = GCAuditFlowRequest{} }
func (*GCAuditFlowRequest) ProtoMessage() {}
func (*GCAuditFlowRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{10}
}
func (m *GCAuditFlowRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GCAuditFlowRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GCAuditFlowRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GCAuditFlowRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GCAuditFlowRequest.Merge(m, src)
}
func (m *GCAuditFlowRequest) XXX_Size() int {
	return m.Size()
}
func (m *GCAuditFlowRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GCAuditFlowRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GCAuditFlowRequest proto.InternalMessageInfo

func (m *GCAuditFlowRequest) GetInstanceID() string {
	if m != nil {
		return m.InstanceID
	}
	return ""
}

func (m *GCAuditFlowRequest) GetCloseType() string {
	if m != nil {
		return m.CloseType
	}
	return ""
}

type RunFlowRequest struct {
}

func (m *RunFlowRequest) Reset()      { *m = RunFlowRequest{} }
func (*RunFlowRequest) ProtoMessage() {}
func (*RunFlowRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{11}
}
func (m *RunFlowRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RunFlowRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RunFlowRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RunFlowRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RunFlowRequest.Merge(m, src)
}
func (m *RunFlowRequest) XXX_Size() int {
	return m.Size()
}
func (m *RunFlowRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RunFlowRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RunFlowRequest proto.InternalMessageInfo

type CheckParentInstance struct {
	FollowInstanceID string `protobuf:"bytes,1,opt,name=FollowInstanceID,proto3" json:"FollowInstanceID,omitempty"`
}

func (m *CheckParentInstance) Reset()      { *m = CheckParentInstance{} }
func (*CheckParentInstance) ProtoMessage() {}
func (*CheckParentInstance) Descriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{12}
}
func (m *CheckParentInstance) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CheckParentInstance) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CheckParentInstance.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CheckParentInstance) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckParentInstance.Merge(m, src)
}
func (m *CheckParentInstance) XXX_Size() int {
	return m.Size()
}
func (m *CheckParentInstance) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckParentInstance.DiscardUnknown(m)
}

var xxx_messageInfo_CheckParentInstance proto.InternalMessageInfo

func (m *CheckParentInstance) GetFollowInstanceID() string {
	if m != nil {
		return m.FollowInstanceID
	}
	return ""
}

type CreateInnerAuditFlowRequest struct {
	InstanceID   string                  `protobuf:"bytes,1,opt,name=InstanceID,proto3" json:"InstanceID,omitempty"`
	DSType       DataSourceType          `protobuf:"varint,2,opt,name=DSType,proto3,enum=shared.DataSourceType" json:"DSType,omitempty"`
	ProductType  string                  `protobuf:"bytes,3,opt,name=ProductType,proto3" json:"ProductType,omitempty"`
	Nodes        []string                `protobuf:"bytes,4,rep,name=Nodes,proto3" json:"Nodes,omitempty"`
	TTL          int32                   `protobuf:"varint,5,opt,name=TTL,proto3" json:"TTL,omitempty"`
	SimplingRate int32                   `protobuf:"varint,6,opt,name=SimplingRate,proto3" json:"SimplingRate,omitempty"`
	OpenType     InstanceFullSqlOpenType `protobuf:"varint,7,opt,name=OpenType,proto3,enum=shared.InstanceFullSqlOpenType" json:"OpenType,omitempty"`
}

func (m *CreateInnerAuditFlowRequest) Reset()      { *m = CreateInnerAuditFlowRequest{} }
func (*CreateInnerAuditFlowRequest) ProtoMessage() {}
func (*CreateInnerAuditFlowRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{13}
}
func (m *CreateInnerAuditFlowRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CreateInnerAuditFlowRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CreateInnerAuditFlowRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CreateInnerAuditFlowRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateInnerAuditFlowRequest.Merge(m, src)
}
func (m *CreateInnerAuditFlowRequest) XXX_Size() int {
	return m.Size()
}
func (m *CreateInnerAuditFlowRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateInnerAuditFlowRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateInnerAuditFlowRequest proto.InternalMessageInfo

func (m *CreateInnerAuditFlowRequest) GetInstanceID() string {
	if m != nil {
		return m.InstanceID
	}
	return ""
}

func (m *CreateInnerAuditFlowRequest) GetDSType() DataSourceType {
	if m != nil {
		return m.DSType
	}
	return NoneType
}

func (m *CreateInnerAuditFlowRequest) GetProductType() string {
	if m != nil {
		return m.ProductType
	}
	return ""
}

func (m *CreateInnerAuditFlowRequest) GetNodes() []string {
	if m != nil {
		return m.Nodes
	}
	return nil
}

func (m *CreateInnerAuditFlowRequest) GetTTL() int32 {
	if m != nil {
		return m.TTL
	}
	return 0
}

func (m *CreateInnerAuditFlowRequest) GetSimplingRate() int32 {
	if m != nil {
		return m.SimplingRate
	}
	return 0
}

func (m *CreateInnerAuditFlowRequest) GetOpenType() InstanceFullSqlOpenType {
	if m != nil {
		return m.OpenType
	}
	return OpenNone
}

type DeleteInnerAuditFlowRequest struct {
	InstanceID  string         `protobuf:"bytes,1,opt,name=InstanceID,proto3" json:"InstanceID,omitempty"`
	CloseType   string         `protobuf:"bytes,2,opt,name=CloseType,proto3" json:"CloseType,omitempty"`
	DSType      DataSourceType `protobuf:"varint,3,opt,name=DSType,proto3,enum=shared.DataSourceType" json:"DSType,omitempty"`
	ProductType string         `protobuf:"bytes,4,opt,name=ProductType,proto3" json:"ProductType,omitempty"`
}

func (m *DeleteInnerAuditFlowRequest) Reset()      { *m = DeleteInnerAuditFlowRequest{} }
func (*DeleteInnerAuditFlowRequest) ProtoMessage() {}
func (*DeleteInnerAuditFlowRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5594839dd8e38a1b, []int{14}
}
func (m *DeleteInnerAuditFlowRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteInnerAuditFlowRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteInnerAuditFlowRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteInnerAuditFlowRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteInnerAuditFlowRequest.Merge(m, src)
}
func (m *DeleteInnerAuditFlowRequest) XXX_Size() int {
	return m.Size()
}
func (m *DeleteInnerAuditFlowRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteInnerAuditFlowRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteInnerAuditFlowRequest proto.InternalMessageInfo

func (m *DeleteInnerAuditFlowRequest) GetInstanceID() string {
	if m != nil {
		return m.InstanceID
	}
	return ""
}

func (m *DeleteInnerAuditFlowRequest) GetCloseType() string {
	if m != nil {
		return m.CloseType
	}
	return ""
}

func (m *DeleteInnerAuditFlowRequest) GetDSType() DataSourceType {
	if m != nil {
		return m.DSType
	}
	return NoneType
}

func (m *DeleteInnerAuditFlowRequest) GetProductType() string {
	if m != nil {
		return m.ProductType
	}
	return ""
}

func init() {
	proto.RegisterEnum("shared.CloseType", CloseType_name, CloseType_value)
	proto.RegisterEnum("shared.UpgradeType", UpgradeType_name, UpgradeType_value)
	proto.RegisterEnum("shared.InstanceFullSqlOpenType", InstanceFullSqlOpenType_name, InstanceFullSqlOpenType_value)
	proto.RegisterType((*PgCreateAuditReq)(nil), "shared.PgCreateAuditReq")
	proto.RegisterType((*PgDeleteAuditReq)(nil), "shared.PgDeleteAuditReq")
	proto.RegisterType((*PgCreateAuditSuccess)(nil), "shared.PgCreateAuditSuccess")
	proto.RegisterType((*PgCreateAuditFailResp)(nil), "shared.PgCreateAuditFailResp")
	proto.RegisterType((*PgDeleteAuditSuccess)(nil), "shared.PgDeleteAuditSuccess")
	proto.RegisterType((*PgDeleteAuditFailResp)(nil), "shared.PgDeleteAuditFailResp")
	proto.RegisterType((*CreateAuditFlowRequest)(nil), "shared.CreateAuditFlowRequest")
	proto.RegisterType((*DeleteAuditFlowRequest)(nil), "shared.DeleteAuditFlowRequest")
	proto.RegisterType((*UpgradeInstanceFlowRequest)(nil), "shared.UpgradeInstanceFlowRequest")
	proto.RegisterType((*DeleteResourceFlowRequest)(nil), "shared.DeleteResourceFlowRequest")
	proto.RegisterType((*GCAuditFlowRequest)(nil), "shared.GCAuditFlowRequest")
	proto.RegisterType((*RunFlowRequest)(nil), "shared.RunFlowRequest")
	proto.RegisterType((*CheckParentInstance)(nil), "shared.CheckParentInstance")
	proto.RegisterType((*CreateInnerAuditFlowRequest)(nil), "shared.CreateInnerAuditFlowRequest")
	proto.RegisterType((*DeleteInnerAuditFlowRequest)(nil), "shared.DeleteInnerAuditFlowRequest")
}

func init() { proto.RegisterFile("audit.proto", fileDescriptor_5594839dd8e38a1b) }

var fileDescriptor_5594839dd8e38a1b = []byte{
	// 735 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x56, 0x4f, 0x4f, 0x13, 0x4f,
	0x18, 0xde, 0x69, 0x69, 0x7f, 0xf0, 0x96, 0xf0, 0x5b, 0xa6, 0x58, 0x6a, 0x21, 0x4b, 0x5d, 0x63,
	0x42, 0x9a, 0x58, 0x13, 0xf4, 0xe6, 0xa9, 0xb6, 0x60, 0x9a, 0x20, 0x34, 0xd3, 0x1a, 0x63, 0x62,
	0x62, 0x96, 0xdd, 0xb1, 0x34, 0x8e, 0x33, 0x65, 0x77, 0x1b, 0xe4, 0xe6, 0x47, 0xf0, 0xe8, 0x81,
	0x0f, 0x60, 0x62, 0xfc, 0x02, 0xfa, 0x05, 0x3c, 0x72, 0x31, 0xe1, 0x28, 0xcb, 0xc5, 0x23, 0x1f,
	0xc1, 0xec, 0x6c, 0xb7, 0x9d, 0x52, 0xd4, 0x14, 0x49, 0xf4, 0xb6, 0xef, 0x9f, 0x79, 0xe6, 0x79,
	0x9f, 0x77, 0xde, 0x37, 0x0b, 0x19, 0xab, 0xe7, 0x74, 0xfc, 0x72, 0xd7, 0x15, 0xbe, 0xc0, 0x69,
	0x6f, 0xd7, 0x72, 0xa9, 0x53, 0x98, 0xb7, 0x05, 0xe7, 0xd4, 0xf6, 0x3b, 0x82, 0x7b, 0x51, 0xc8,
	0x3c, 0x44, 0xa0, 0x37, 0xda, 0x55, 0x97, 0x5a, 0x3e, 0xad, 0x84, 0x47, 0x08, 0xdd, 0xc3, 0x05,
	0x98, 0x26, 0xb4, 0xdd, 0x11, 0xbc, 0xee, 0xe4, 0x51, 0x11, 0xad, 0xce, 0x90, 0x81, 0x8d, 0x4b,
	0xa0, 0x6f, 0x08, 0xc6, 0xc4, 0x7e, 0x9d, 0x7b, 0xbe, 0xc5, 0x6d, 0x5a, 0xaf, 0xe5, 0x13, 0x32,
	0x67, 0xcc, 0x8f, 0xcb, 0x90, 0xae, 0x35, 0x5b, 0x07, 0x5d, 0x9a, 0x4f, 0x16, 0xd1, 0xea, 0xdc,
	0x5a, 0xae, 0x1c, 0x11, 0x29, 0xd7, 0x2c, 0xdf, 0x6a, 0x8a, 0x9e, 0x6b, 0xd3, 0x30, 0x4a, 0xfa,
	0x59, 0x58, 0x87, 0x64, 0xcb, 0x67, 0xf9, 0xa9, 0x22, 0x5a, 0x4d, 0x91, 0xf0, 0xd3, 0xfc, 0x2c,
	0xe9, 0xd5, 0x28, 0xa3, 0xff, 0x00, 0xbd, 0x3b, 0x30, 0x53, 0x65, 0xc2, 0x93, 0x4e, 0x49, 0x72,
	0x6e, 0x6d, 0x3e, 0x3e, 0x32, 0x08, 0x90, 0x61, 0x8e, 0x99, 0x83, 0x85, 0x11, 0x6d, 0x9b, 0x3d,
	0xdb, 0xa6, 0x9e, 0x67, 0xde, 0x86, 0x6b, 0x23, 0xfe, 0x0d, 0xab, 0xc3, 0x08, 0xf5, 0xba, 0x78,
	0x01, 0x52, 0xeb, 0xae, 0x2b, 0xdc, 0x7e, 0x59, 0x91, 0x11, 0xc1, 0x28, 0x1a, 0x8c, 0xc0, 0x28,
	0xfe, 0xdf, 0xc0, 0x1c, 0x22, 0xc8, 0xa9, 0x97, 0x32, 0xb1, 0x4f, 0xe8, 0x5e, 0x8f, 0x7a, 0x3e,
	0x36, 0x00, 0x14, 0xbd, 0xa2, 0x53, 0x8a, 0x27, 0x6c, 0xcc, 0x23, 0xaf, 0xdd, 0x17, 0x32, 0xfc,
	0x9c, 0x58, 0xbb, 0x22, 0x64, 0x1a, 0xae, 0x70, 0x7a, 0xb6, 0x3f, 0x50, 0x6f, 0x86, 0xa8, 0x2e,
	0xf3, 0x13, 0x82, 0x9c, 0x5a, 0xcc, 0x1f, 0xd1, 0x5b, 0x56, 0x5b, 0x95, 0x94, 0xfe, 0xa1, 0x43,
	0x21, 0x3f, 0x75, 0x19, 0xf2, 0xa9, 0x71, 0xf2, 0x5f, 0x11, 0x14, 0x1e, 0x77, 0xdb, 0xae, 0xe5,
	0xd0, 0x98, 0xd7, 0x24, 0x05, 0x0c, 0x09, 0x25, 0x2e, 0x43, 0x28, 0x39, 0x46, 0x08, 0xaf, 0x43,
	0x36, 0xc6, 0xef, 0xf3, 0x52, 0xea, 0xcd, 0xc6, 0xf0, 0x4a, 0x88, 0x5c, 0x94, 0x6f, 0x7e, 0x40,
	0x70, 0x3d, 0x6a, 0x0a, 0xa1, 0x9e, 0xe4, 0xf1, 0x77, 0xcb, 0x5a, 0x3e, 0x3f, 0x82, 0x6a, 0x5f,
	0x4d, 0x02, 0xf8, 0x61, 0x75, 0xe2, 0xd7, 0x33, 0x82, 0x99, 0x38, 0x8f, 0xa9, 0xc3, 0x1c, 0xe9,
	0x71, 0x05, 0xcf, 0xac, 0x40, 0xb6, 0xba, 0x4b, 0xed, 0x97, 0x0d, 0xcb, 0xa5, 0xdc, 0x8f, 0x81,
	0x2e, 0xdc, 0x3c, 0xe8, 0xe2, 0xcd, 0x63, 0xbe, 0x4b, 0xc0, 0x52, 0x34, 0x8a, 0x75, 0xce, 0xa9,
	0x3b, 0x31, 0xe5, 0xab, 0x17, 0x76, 0x01, 0x52, 0x5b, 0xc2, 0xa1, 0x5e, 0x7e, 0xaa, 0x98, 0x0c,
	0x57, 0x86, 0x34, 0xe4, 0x42, 0x6e, 0x6d, 0xca, 0x07, 0x1f, 0x2e, 0xe4, 0xd6, 0x26, 0x36, 0x61,
	0xb6, 0xd9, 0x79, 0xd5, 0x65, 0x1d, 0xde, 0x26, 0x96, 0x4f, 0xf3, 0x69, 0x19, 0x1a, 0xf1, 0xe1,
	0xfb, 0x30, 0xbd, 0xdd, 0xa5, 0x5c, 0x5e, 0xf5, 0x9f, 0xe4, 0xb7, 0x12, 0xf3, 0x1b, 0x0c, 0x47,
	0x8f, 0xb1, 0xe6, 0x1e, 0x8b, 0xd3, 0xc8, 0xe0, 0x80, 0xf9, 0x11, 0xc1, 0x52, 0xf4, 0xe2, 0x2e,
	0x27, 0xcd, 0x2f, 0xbb, 0x79, 0xf5, 0x6b, 0xab, 0xf4, 0x44, 0xb9, 0x0f, 0xdf, 0x84, 0x95, 0x81,
	0xf1, 0x7c, 0x9b, 0xb3, 0x03, 0x69, 0xc9, 0x1a, 0xaa, 0x82, 0x31, 0x6a, 0xfb, 0xba, 0x86, 0x6f,
	0xc1, 0x8d, 0x61, 0xd2, 0x58, 0x42, 0x85, 0x3b, 0x2d, 0xe6, 0xe9, 0xa8, 0xf4, 0x14, 0x32, 0xca,
	0x24, 0xe2, 0xff, 0x21, 0xb3, 0x25, 0x78, 0x3c, 0x9c, 0xba, 0x86, 0x97, 0x60, 0xb1, 0x6f, 0xf4,
	0xc5, 0x6c, 0x59, 0x3b, 0x8c, 0x56, 0xda, 0x6d, 0x57, 0x47, 0x78, 0x19, 0xf2, 0xe7, 0x82, 0xa2,
	0xdb, 0xb1, 0xeb, 0xdc, 0xa1, 0xaf, 0xf5, 0x64, 0xe9, 0x19, 0x2c, 0xfe, 0xa4, 0x11, 0x78, 0x36,
	0xea, 0x5d, 0x78, 0x95, 0xae, 0xe1, 0x02, 0xe4, 0x42, 0xab, 0x61, 0xb9, 0xfe, 0xf6, 0x8b, 0xf8,
	0x48, 0xf8, 0x34, 0x74, 0x84, 0x17, 0x21, 0x1b, 0xc6, 0x2a, 0x8c, 0x8d, 0x04, 0x12, 0x0f, 0xee,
	0x1d, 0x9d, 0x18, 0xda, 0xf1, 0x89, 0xa1, 0x9d, 0x9d, 0x18, 0xe8, 0x4d, 0x60, 0xa0, 0xf7, 0x81,
	0x81, 0xbe, 0x04, 0x06, 0x3a, 0x0a, 0x0c, 0xf4, 0x2d, 0x30, 0xd0, 0xf7, 0xc0, 0xd0, 0xce, 0x02,
	0x03, 0xbd, 0x3d, 0x35, 0xb4, 0xa3, 0x53, 0x43, 0x3b, 0x3e, 0x35, 0xb4, 0x9d, 0xb4, 0xfc, 0x1f,
	0xb9, 0xfb, 0x23, 0x00, 0x00, 0xff, 0xff, 0x71, 0xdd, 0x51, 0x1c, 0xb9, 0x08, 0x00, 0x00,
}

func (x CloseType) String() string {
	s, ok := CloseType_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (x UpgradeType) String() string {
	s, ok := UpgradeType_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (x InstanceFullSqlOpenType) String() string {
	s, ok := InstanceFullSqlOpenType_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (this *PgCreateAuditReq) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*PgCreateAuditReq)
	if !ok {
		that2, ok := that.(PgCreateAuditReq)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.RegionId != that1.RegionId {
		return false
	}
	if this.FollowInstanceID != that1.FollowInstanceID {
		return false
	}
	if this.DSType != that1.DSType {
		return false
	}
	if this.Ttl != that1.Ttl {
		return false
	}
	return true
}
func (this *PgDeleteAuditReq) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*PgDeleteAuditReq)
	if !ok {
		that2, ok := that.(PgDeleteAuditReq)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.RegionId != that1.RegionId {
		return false
	}
	if this.FollowInstanceID != that1.FollowInstanceID {
		return false
	}
	if this.DSType != that1.DSType {
		return false
	}
	if this.CloseType != that1.CloseType {
		return false
	}
	return true
}
func (this *PgCreateAuditSuccess) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*PgCreateAuditSuccess)
	if !ok {
		that2, ok := that.(PgCreateAuditSuccess)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *PgCreateAuditFailResp) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*PgCreateAuditFailResp)
	if !ok {
		that2, ok := that.(PgCreateAuditFailResp)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Error != that1.Error {
		return false
	}
	return true
}
func (this *PgDeleteAuditSuccess) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*PgDeleteAuditSuccess)
	if !ok {
		that2, ok := that.(PgDeleteAuditSuccess)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *PgDeleteAuditFailResp) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*PgDeleteAuditFailResp)
	if !ok {
		that2, ok := that.(PgDeleteAuditFailResp)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Error != that1.Error {
		return false
	}
	return true
}
func (this *CreateAuditFlowRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CreateAuditFlowRequest)
	if !ok {
		that2, ok := that.(CreateAuditFlowRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.InstanceID != that1.InstanceID {
		return false
	}
	if this.Msg != that1.Msg {
		return false
	}
	if this.DSType != that1.DSType {
		return false
	}
	if this.ProductType != that1.ProductType {
		return false
	}
	return true
}
func (this *DeleteAuditFlowRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DeleteAuditFlowRequest)
	if !ok {
		that2, ok := that.(DeleteAuditFlowRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.InstanceID != that1.InstanceID {
		return false
	}
	if this.Msg != that1.Msg {
		return false
	}
	if this.CloseType != that1.CloseType {
		return false
	}
	if this.DSType != that1.DSType {
		return false
	}
	if this.ProductType != that1.ProductType {
		return false
	}
	return true
}
func (this *UpgradeInstanceFlowRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*UpgradeInstanceFlowRequest)
	if !ok {
		that2, ok := that.(UpgradeInstanceFlowRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.InstanceID != that1.InstanceID {
		return false
	}
	if this.DSType != that1.DSType {
		return false
	}
	if this.ProductType != that1.ProductType {
		return false
	}
	if this.InstanceUpgradeType != that1.InstanceUpgradeType {
		return false
	}
	return true
}
func (this *DeleteResourceFlowRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DeleteResourceFlowRequest)
	if !ok {
		that2, ok := that.(DeleteResourceFlowRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.InstanceID != that1.InstanceID {
		return false
	}
	if this.DSType != that1.DSType {
		return false
	}
	if this.ProductType != that1.ProductType {
		return false
	}
	if this.CloseType != that1.CloseType {
		return false
	}
	return true
}
func (this *GCAuditFlowRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*GCAuditFlowRequest)
	if !ok {
		that2, ok := that.(GCAuditFlowRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.InstanceID != that1.InstanceID {
		return false
	}
	if this.CloseType != that1.CloseType {
		return false
	}
	return true
}
func (this *RunFlowRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*RunFlowRequest)
	if !ok {
		that2, ok := that.(RunFlowRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *CheckParentInstance) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CheckParentInstance)
	if !ok {
		that2, ok := that.(CheckParentInstance)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.FollowInstanceID != that1.FollowInstanceID {
		return false
	}
	return true
}
func (this *CreateInnerAuditFlowRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CreateInnerAuditFlowRequest)
	if !ok {
		that2, ok := that.(CreateInnerAuditFlowRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.InstanceID != that1.InstanceID {
		return false
	}
	if this.DSType != that1.DSType {
		return false
	}
	if this.ProductType != that1.ProductType {
		return false
	}
	if len(this.Nodes) != len(that1.Nodes) {
		return false
	}
	for i := range this.Nodes {
		if this.Nodes[i] != that1.Nodes[i] {
			return false
		}
	}
	if this.TTL != that1.TTL {
		return false
	}
	if this.SimplingRate != that1.SimplingRate {
		return false
	}
	if this.OpenType != that1.OpenType {
		return false
	}
	return true
}
func (this *DeleteInnerAuditFlowRequest) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DeleteInnerAuditFlowRequest)
	if !ok {
		that2, ok := that.(DeleteInnerAuditFlowRequest)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.InstanceID != that1.InstanceID {
		return false
	}
	if this.CloseType != that1.CloseType {
		return false
	}
	if this.DSType != that1.DSType {
		return false
	}
	if this.ProductType != that1.ProductType {
		return false
	}
	return true
}
func (this *PgCreateAuditReq) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.PgCreateAuditReq{")
	s = append(s, "RegionId: "+fmt.Sprintf("%#v", this.RegionId)+",\n")
	s = append(s, "FollowInstanceID: "+fmt.Sprintf("%#v", this.FollowInstanceID)+",\n")
	s = append(s, "DSType: "+fmt.Sprintf("%#v", this.DSType)+",\n")
	s = append(s, "Ttl: "+fmt.Sprintf("%#v", this.Ttl)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *PgDeleteAuditReq) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.PgDeleteAuditReq{")
	s = append(s, "RegionId: "+fmt.Sprintf("%#v", this.RegionId)+",\n")
	s = append(s, "FollowInstanceID: "+fmt.Sprintf("%#v", this.FollowInstanceID)+",\n")
	s = append(s, "DSType: "+fmt.Sprintf("%#v", this.DSType)+",\n")
	s = append(s, "CloseType: "+fmt.Sprintf("%#v", this.CloseType)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *PgCreateAuditSuccess) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&shared.PgCreateAuditSuccess{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *PgCreateAuditFailResp) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.PgCreateAuditFailResp{")
	s = append(s, "Error: "+fmt.Sprintf("%#v", this.Error)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *PgDeleteAuditSuccess) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&shared.PgDeleteAuditSuccess{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *PgDeleteAuditFailResp) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.PgDeleteAuditFailResp{")
	s = append(s, "Error: "+fmt.Sprintf("%#v", this.Error)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CreateAuditFlowRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.CreateAuditFlowRequest{")
	s = append(s, "InstanceID: "+fmt.Sprintf("%#v", this.InstanceID)+",\n")
	s = append(s, "Msg: "+fmt.Sprintf("%#v", this.Msg)+",\n")
	s = append(s, "DSType: "+fmt.Sprintf("%#v", this.DSType)+",\n")
	s = append(s, "ProductType: "+fmt.Sprintf("%#v", this.ProductType)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DeleteAuditFlowRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 9)
	s = append(s, "&shared.DeleteAuditFlowRequest{")
	s = append(s, "InstanceID: "+fmt.Sprintf("%#v", this.InstanceID)+",\n")
	s = append(s, "Msg: "+fmt.Sprintf("%#v", this.Msg)+",\n")
	s = append(s, "CloseType: "+fmt.Sprintf("%#v", this.CloseType)+",\n")
	s = append(s, "DSType: "+fmt.Sprintf("%#v", this.DSType)+",\n")
	s = append(s, "ProductType: "+fmt.Sprintf("%#v", this.ProductType)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *UpgradeInstanceFlowRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.UpgradeInstanceFlowRequest{")
	s = append(s, "InstanceID: "+fmt.Sprintf("%#v", this.InstanceID)+",\n")
	s = append(s, "DSType: "+fmt.Sprintf("%#v", this.DSType)+",\n")
	s = append(s, "ProductType: "+fmt.Sprintf("%#v", this.ProductType)+",\n")
	s = append(s, "InstanceUpgradeType: "+fmt.Sprintf("%#v", this.InstanceUpgradeType)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DeleteResourceFlowRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.DeleteResourceFlowRequest{")
	s = append(s, "InstanceID: "+fmt.Sprintf("%#v", this.InstanceID)+",\n")
	s = append(s, "DSType: "+fmt.Sprintf("%#v", this.DSType)+",\n")
	s = append(s, "ProductType: "+fmt.Sprintf("%#v", this.ProductType)+",\n")
	s = append(s, "CloseType: "+fmt.Sprintf("%#v", this.CloseType)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *GCAuditFlowRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.GCAuditFlowRequest{")
	s = append(s, "InstanceID: "+fmt.Sprintf("%#v", this.InstanceID)+",\n")
	s = append(s, "CloseType: "+fmt.Sprintf("%#v", this.CloseType)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *RunFlowRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&shared.RunFlowRequest{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CheckParentInstance) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.CheckParentInstance{")
	s = append(s, "FollowInstanceID: "+fmt.Sprintf("%#v", this.FollowInstanceID)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CreateInnerAuditFlowRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 11)
	s = append(s, "&shared.CreateInnerAuditFlowRequest{")
	s = append(s, "InstanceID: "+fmt.Sprintf("%#v", this.InstanceID)+",\n")
	s = append(s, "DSType: "+fmt.Sprintf("%#v", this.DSType)+",\n")
	s = append(s, "ProductType: "+fmt.Sprintf("%#v", this.ProductType)+",\n")
	s = append(s, "Nodes: "+fmt.Sprintf("%#v", this.Nodes)+",\n")
	s = append(s, "TTL: "+fmt.Sprintf("%#v", this.TTL)+",\n")
	s = append(s, "SimplingRate: "+fmt.Sprintf("%#v", this.SimplingRate)+",\n")
	s = append(s, "OpenType: "+fmt.Sprintf("%#v", this.OpenType)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DeleteInnerAuditFlowRequest) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.DeleteInnerAuditFlowRequest{")
	s = append(s, "InstanceID: "+fmt.Sprintf("%#v", this.InstanceID)+",\n")
	s = append(s, "CloseType: "+fmt.Sprintf("%#v", this.CloseType)+",\n")
	s = append(s, "DSType: "+fmt.Sprintf("%#v", this.DSType)+",\n")
	s = append(s, "ProductType: "+fmt.Sprintf("%#v", this.ProductType)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringAudit(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}
func (m *PgCreateAuditReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PgCreateAuditReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PgCreateAuditReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Ttl != 0 {
		i = encodeVarintAudit(dAtA, i, uint64(m.Ttl))
		i--
		dAtA[i] = 0x20
	}
	if m.DSType != 0 {
		i = encodeVarintAudit(dAtA, i, uint64(m.DSType))
		i--
		dAtA[i] = 0x18
	}
	if len(m.FollowInstanceID) > 0 {
		i -= len(m.FollowInstanceID)
		copy(dAtA[i:], m.FollowInstanceID)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.FollowInstanceID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RegionId) > 0 {
		i -= len(m.RegionId)
		copy(dAtA[i:], m.RegionId)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.RegionId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PgDeleteAuditReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PgDeleteAuditReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PgDeleteAuditReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.CloseType != 0 {
		i = encodeVarintAudit(dAtA, i, uint64(m.CloseType))
		i--
		dAtA[i] = 0x20
	}
	if m.DSType != 0 {
		i = encodeVarintAudit(dAtA, i, uint64(m.DSType))
		i--
		dAtA[i] = 0x18
	}
	if len(m.FollowInstanceID) > 0 {
		i -= len(m.FollowInstanceID)
		copy(dAtA[i:], m.FollowInstanceID)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.FollowInstanceID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.RegionId) > 0 {
		i -= len(m.RegionId)
		copy(dAtA[i:], m.RegionId)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.RegionId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PgCreateAuditSuccess) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PgCreateAuditSuccess) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PgCreateAuditSuccess) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *PgCreateAuditFailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PgCreateAuditFailResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PgCreateAuditFailResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Error) > 0 {
		i -= len(m.Error)
		copy(dAtA[i:], m.Error)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.Error)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *PgDeleteAuditSuccess) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PgDeleteAuditSuccess) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PgDeleteAuditSuccess) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *PgDeleteAuditFailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PgDeleteAuditFailResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *PgDeleteAuditFailResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Error) > 0 {
		i -= len(m.Error)
		copy(dAtA[i:], m.Error)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.Error)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CreateAuditFlowRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateAuditFlowRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CreateAuditFlowRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ProductType) > 0 {
		i -= len(m.ProductType)
		copy(dAtA[i:], m.ProductType)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.ProductType)))
		i--
		dAtA[i] = 0x22
	}
	if m.DSType != 0 {
		i = encodeVarintAudit(dAtA, i, uint64(m.DSType))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Msg) > 0 {
		i -= len(m.Msg)
		copy(dAtA[i:], m.Msg)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.Msg)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.InstanceID) > 0 {
		i -= len(m.InstanceID)
		copy(dAtA[i:], m.InstanceID)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.InstanceID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DeleteAuditFlowRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteAuditFlowRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteAuditFlowRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ProductType) > 0 {
		i -= len(m.ProductType)
		copy(dAtA[i:], m.ProductType)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.ProductType)))
		i--
		dAtA[i] = 0x2a
	}
	if m.DSType != 0 {
		i = encodeVarintAudit(dAtA, i, uint64(m.DSType))
		i--
		dAtA[i] = 0x20
	}
	if len(m.CloseType) > 0 {
		i -= len(m.CloseType)
		copy(dAtA[i:], m.CloseType)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.CloseType)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Msg) > 0 {
		i -= len(m.Msg)
		copy(dAtA[i:], m.Msg)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.Msg)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.InstanceID) > 0 {
		i -= len(m.InstanceID)
		copy(dAtA[i:], m.InstanceID)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.InstanceID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *UpgradeInstanceFlowRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpgradeInstanceFlowRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UpgradeInstanceFlowRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.InstanceUpgradeType != 0 {
		i = encodeVarintAudit(dAtA, i, uint64(m.InstanceUpgradeType))
		i--
		dAtA[i] = 0x20
	}
	if len(m.ProductType) > 0 {
		i -= len(m.ProductType)
		copy(dAtA[i:], m.ProductType)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.ProductType)))
		i--
		dAtA[i] = 0x1a
	}
	if m.DSType != 0 {
		i = encodeVarintAudit(dAtA, i, uint64(m.DSType))
		i--
		dAtA[i] = 0x10
	}
	if len(m.InstanceID) > 0 {
		i -= len(m.InstanceID)
		copy(dAtA[i:], m.InstanceID)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.InstanceID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DeleteResourceFlowRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteResourceFlowRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteResourceFlowRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.CloseType) > 0 {
		i -= len(m.CloseType)
		copy(dAtA[i:], m.CloseType)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.CloseType)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.ProductType) > 0 {
		i -= len(m.ProductType)
		copy(dAtA[i:], m.ProductType)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.ProductType)))
		i--
		dAtA[i] = 0x1a
	}
	if m.DSType != 0 {
		i = encodeVarintAudit(dAtA, i, uint64(m.DSType))
		i--
		dAtA[i] = 0x10
	}
	if len(m.InstanceID) > 0 {
		i -= len(m.InstanceID)
		copy(dAtA[i:], m.InstanceID)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.InstanceID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GCAuditFlowRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GCAuditFlowRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GCAuditFlowRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.CloseType) > 0 {
		i -= len(m.CloseType)
		copy(dAtA[i:], m.CloseType)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.CloseType)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.InstanceID) > 0 {
		i -= len(m.InstanceID)
		copy(dAtA[i:], m.InstanceID)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.InstanceID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RunFlowRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RunFlowRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RunFlowRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *CheckParentInstance) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckParentInstance) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CheckParentInstance) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.FollowInstanceID) > 0 {
		i -= len(m.FollowInstanceID)
		copy(dAtA[i:], m.FollowInstanceID)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.FollowInstanceID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CreateInnerAuditFlowRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateInnerAuditFlowRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CreateInnerAuditFlowRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.OpenType != 0 {
		i = encodeVarintAudit(dAtA, i, uint64(m.OpenType))
		i--
		dAtA[i] = 0x38
	}
	if m.SimplingRate != 0 {
		i = encodeVarintAudit(dAtA, i, uint64(m.SimplingRate))
		i--
		dAtA[i] = 0x30
	}
	if m.TTL != 0 {
		i = encodeVarintAudit(dAtA, i, uint64(m.TTL))
		i--
		dAtA[i] = 0x28
	}
	if len(m.Nodes) > 0 {
		for iNdEx := len(m.Nodes) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Nodes[iNdEx])
			copy(dAtA[i:], m.Nodes[iNdEx])
			i = encodeVarintAudit(dAtA, i, uint64(len(m.Nodes[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.ProductType) > 0 {
		i -= len(m.ProductType)
		copy(dAtA[i:], m.ProductType)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.ProductType)))
		i--
		dAtA[i] = 0x1a
	}
	if m.DSType != 0 {
		i = encodeVarintAudit(dAtA, i, uint64(m.DSType))
		i--
		dAtA[i] = 0x10
	}
	if len(m.InstanceID) > 0 {
		i -= len(m.InstanceID)
		copy(dAtA[i:], m.InstanceID)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.InstanceID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DeleteInnerAuditFlowRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteInnerAuditFlowRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteInnerAuditFlowRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.ProductType) > 0 {
		i -= len(m.ProductType)
		copy(dAtA[i:], m.ProductType)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.ProductType)))
		i--
		dAtA[i] = 0x22
	}
	if m.DSType != 0 {
		i = encodeVarintAudit(dAtA, i, uint64(m.DSType))
		i--
		dAtA[i] = 0x18
	}
	if len(m.CloseType) > 0 {
		i -= len(m.CloseType)
		copy(dAtA[i:], m.CloseType)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.CloseType)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.InstanceID) > 0 {
		i -= len(m.InstanceID)
		copy(dAtA[i:], m.InstanceID)
		i = encodeVarintAudit(dAtA, i, uint64(len(m.InstanceID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintAudit(dAtA []byte, offset int, v uint64) int {
	offset -= sovAudit(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *PgCreateAuditReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RegionId)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	l = len(m.FollowInstanceID)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	if m.DSType != 0 {
		n += 1 + sovAudit(uint64(m.DSType))
	}
	if m.Ttl != 0 {
		n += 1 + sovAudit(uint64(m.Ttl))
	}
	return n
}

func (m *PgDeleteAuditReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.RegionId)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	l = len(m.FollowInstanceID)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	if m.DSType != 0 {
		n += 1 + sovAudit(uint64(m.DSType))
	}
	if m.CloseType != 0 {
		n += 1 + sovAudit(uint64(m.CloseType))
	}
	return n
}

func (m *PgCreateAuditSuccess) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *PgCreateAuditFailResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Error)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	return n
}

func (m *PgDeleteAuditSuccess) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *PgDeleteAuditFailResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Error)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	return n
}

func (m *CreateAuditFlowRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.InstanceID)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	if m.DSType != 0 {
		n += 1 + sovAudit(uint64(m.DSType))
	}
	l = len(m.ProductType)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	return n
}

func (m *DeleteAuditFlowRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.InstanceID)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	l = len(m.Msg)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	l = len(m.CloseType)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	if m.DSType != 0 {
		n += 1 + sovAudit(uint64(m.DSType))
	}
	l = len(m.ProductType)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	return n
}

func (m *UpgradeInstanceFlowRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.InstanceID)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	if m.DSType != 0 {
		n += 1 + sovAudit(uint64(m.DSType))
	}
	l = len(m.ProductType)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	if m.InstanceUpgradeType != 0 {
		n += 1 + sovAudit(uint64(m.InstanceUpgradeType))
	}
	return n
}

func (m *DeleteResourceFlowRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.InstanceID)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	if m.DSType != 0 {
		n += 1 + sovAudit(uint64(m.DSType))
	}
	l = len(m.ProductType)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	l = len(m.CloseType)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	return n
}

func (m *GCAuditFlowRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.InstanceID)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	l = len(m.CloseType)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	return n
}

func (m *RunFlowRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *CheckParentInstance) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.FollowInstanceID)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	return n
}

func (m *CreateInnerAuditFlowRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.InstanceID)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	if m.DSType != 0 {
		n += 1 + sovAudit(uint64(m.DSType))
	}
	l = len(m.ProductType)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	if len(m.Nodes) > 0 {
		for _, s := range m.Nodes {
			l = len(s)
			n += 1 + l + sovAudit(uint64(l))
		}
	}
	if m.TTL != 0 {
		n += 1 + sovAudit(uint64(m.TTL))
	}
	if m.SimplingRate != 0 {
		n += 1 + sovAudit(uint64(m.SimplingRate))
	}
	if m.OpenType != 0 {
		n += 1 + sovAudit(uint64(m.OpenType))
	}
	return n
}

func (m *DeleteInnerAuditFlowRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.InstanceID)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	l = len(m.CloseType)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	if m.DSType != 0 {
		n += 1 + sovAudit(uint64(m.DSType))
	}
	l = len(m.ProductType)
	if l > 0 {
		n += 1 + l + sovAudit(uint64(l))
	}
	return n
}

func sovAudit(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozAudit(x uint64) (n int) {
	return sovAudit(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *PgCreateAuditReq) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&PgCreateAuditReq{`,
		`RegionId:` + fmt.Sprintf("%v", this.RegionId) + `,`,
		`FollowInstanceID:` + fmt.Sprintf("%v", this.FollowInstanceID) + `,`,
		`DSType:` + fmt.Sprintf("%v", this.DSType) + `,`,
		`Ttl:` + fmt.Sprintf("%v", this.Ttl) + `,`,
		`}`,
	}, "")
	return s
}
func (this *PgDeleteAuditReq) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&PgDeleteAuditReq{`,
		`RegionId:` + fmt.Sprintf("%v", this.RegionId) + `,`,
		`FollowInstanceID:` + fmt.Sprintf("%v", this.FollowInstanceID) + `,`,
		`DSType:` + fmt.Sprintf("%v", this.DSType) + `,`,
		`CloseType:` + fmt.Sprintf("%v", this.CloseType) + `,`,
		`}`,
	}, "")
	return s
}
func (this *PgCreateAuditSuccess) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&PgCreateAuditSuccess{`,
		`}`,
	}, "")
	return s
}
func (this *PgCreateAuditFailResp) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&PgCreateAuditFailResp{`,
		`Error:` + fmt.Sprintf("%v", this.Error) + `,`,
		`}`,
	}, "")
	return s
}
func (this *PgDeleteAuditSuccess) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&PgDeleteAuditSuccess{`,
		`}`,
	}, "")
	return s
}
func (this *PgDeleteAuditFailResp) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&PgDeleteAuditFailResp{`,
		`Error:` + fmt.Sprintf("%v", this.Error) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CreateAuditFlowRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CreateAuditFlowRequest{`,
		`InstanceID:` + fmt.Sprintf("%v", this.InstanceID) + `,`,
		`Msg:` + fmt.Sprintf("%v", this.Msg) + `,`,
		`DSType:` + fmt.Sprintf("%v", this.DSType) + `,`,
		`ProductType:` + fmt.Sprintf("%v", this.ProductType) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DeleteAuditFlowRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DeleteAuditFlowRequest{`,
		`InstanceID:` + fmt.Sprintf("%v", this.InstanceID) + `,`,
		`Msg:` + fmt.Sprintf("%v", this.Msg) + `,`,
		`CloseType:` + fmt.Sprintf("%v", this.CloseType) + `,`,
		`DSType:` + fmt.Sprintf("%v", this.DSType) + `,`,
		`ProductType:` + fmt.Sprintf("%v", this.ProductType) + `,`,
		`}`,
	}, "")
	return s
}
func (this *UpgradeInstanceFlowRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&UpgradeInstanceFlowRequest{`,
		`InstanceID:` + fmt.Sprintf("%v", this.InstanceID) + `,`,
		`DSType:` + fmt.Sprintf("%v", this.DSType) + `,`,
		`ProductType:` + fmt.Sprintf("%v", this.ProductType) + `,`,
		`InstanceUpgradeType:` + fmt.Sprintf("%v", this.InstanceUpgradeType) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DeleteResourceFlowRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DeleteResourceFlowRequest{`,
		`InstanceID:` + fmt.Sprintf("%v", this.InstanceID) + `,`,
		`DSType:` + fmt.Sprintf("%v", this.DSType) + `,`,
		`ProductType:` + fmt.Sprintf("%v", this.ProductType) + `,`,
		`CloseType:` + fmt.Sprintf("%v", this.CloseType) + `,`,
		`}`,
	}, "")
	return s
}
func (this *GCAuditFlowRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&GCAuditFlowRequest{`,
		`InstanceID:` + fmt.Sprintf("%v", this.InstanceID) + `,`,
		`CloseType:` + fmt.Sprintf("%v", this.CloseType) + `,`,
		`}`,
	}, "")
	return s
}
func (this *RunFlowRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&RunFlowRequest{`,
		`}`,
	}, "")
	return s
}
func (this *CheckParentInstance) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CheckParentInstance{`,
		`FollowInstanceID:` + fmt.Sprintf("%v", this.FollowInstanceID) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CreateInnerAuditFlowRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CreateInnerAuditFlowRequest{`,
		`InstanceID:` + fmt.Sprintf("%v", this.InstanceID) + `,`,
		`DSType:` + fmt.Sprintf("%v", this.DSType) + `,`,
		`ProductType:` + fmt.Sprintf("%v", this.ProductType) + `,`,
		`Nodes:` + fmt.Sprintf("%v", this.Nodes) + `,`,
		`TTL:` + fmt.Sprintf("%v", this.TTL) + `,`,
		`SimplingRate:` + fmt.Sprintf("%v", this.SimplingRate) + `,`,
		`OpenType:` + fmt.Sprintf("%v", this.OpenType) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DeleteInnerAuditFlowRequest) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DeleteInnerAuditFlowRequest{`,
		`InstanceID:` + fmt.Sprintf("%v", this.InstanceID) + `,`,
		`CloseType:` + fmt.Sprintf("%v", this.CloseType) + `,`,
		`DSType:` + fmt.Sprintf("%v", this.DSType) + `,`,
		`ProductType:` + fmt.Sprintf("%v", this.ProductType) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringAudit(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *PgCreateAuditReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PgCreateAuditReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PgCreateAuditReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RegionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FollowInstanceID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FollowInstanceID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DSType", wireType)
			}
			m.DSType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DSType |= DataSourceType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ttl", wireType)
			}
			m.Ttl = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ttl |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAudit(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAudit
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PgDeleteAuditReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PgDeleteAuditReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PgDeleteAuditReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RegionId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RegionId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FollowInstanceID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FollowInstanceID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DSType", wireType)
			}
			m.DSType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DSType |= DataSourceType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CloseType", wireType)
			}
			m.CloseType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CloseType |= CloseType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAudit(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAudit
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PgCreateAuditSuccess) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PgCreateAuditSuccess: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PgCreateAuditSuccess: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAudit(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAudit
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PgCreateAuditFailResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PgCreateAuditFailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PgCreateAuditFailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Error = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAudit(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAudit
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PgDeleteAuditSuccess) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PgDeleteAuditSuccess: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PgDeleteAuditSuccess: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAudit(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAudit
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *PgDeleteAuditFailResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: PgDeleteAuditFailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: PgDeleteAuditFailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Error = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAudit(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAudit
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateAuditFlowRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateAuditFlowRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateAuditFlowRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstanceID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstanceID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DSType", wireType)
			}
			m.DSType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DSType |= DataSourceType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAudit(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAudit
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteAuditFlowRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteAuditFlowRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteAuditFlowRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstanceID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstanceID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CloseType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CloseType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DSType", wireType)
			}
			m.DSType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DSType |= DataSourceType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAudit(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAudit
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpgradeInstanceFlowRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpgradeInstanceFlowRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpgradeInstanceFlowRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstanceID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstanceID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DSType", wireType)
			}
			m.DSType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DSType |= DataSourceType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstanceUpgradeType", wireType)
			}
			m.InstanceUpgradeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InstanceUpgradeType |= UpgradeType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAudit(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAudit
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteResourceFlowRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteResourceFlowRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteResourceFlowRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstanceID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstanceID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DSType", wireType)
			}
			m.DSType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DSType |= DataSourceType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CloseType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CloseType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAudit(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAudit
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GCAuditFlowRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GCAuditFlowRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GCAuditFlowRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstanceID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstanceID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CloseType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CloseType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAudit(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAudit
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RunFlowRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RunFlowRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RunFlowRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipAudit(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAudit
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckParentInstance) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CheckParentInstance: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CheckParentInstance: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FollowInstanceID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FollowInstanceID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAudit(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAudit
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateInnerAuditFlowRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateInnerAuditFlowRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateInnerAuditFlowRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstanceID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstanceID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DSType", wireType)
			}
			m.DSType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DSType |= DataSourceType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Nodes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Nodes = append(m.Nodes, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TTL", wireType)
			}
			m.TTL = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TTL |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SimplingRate", wireType)
			}
			m.SimplingRate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SimplingRate |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OpenType", wireType)
			}
			m.OpenType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpenType |= InstanceFullSqlOpenType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipAudit(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAudit
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteInnerAuditFlowRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteInnerAuditFlowRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteInnerAuditFlowRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstanceID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstanceID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CloseType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CloseType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DSType", wireType)
			}
			m.DSType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DSType |= DataSourceType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ProductType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthAudit
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthAudit
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ProductType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipAudit(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthAudit
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipAudit(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowAudit
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowAudit
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthAudit
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupAudit
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthAudit
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthAudit        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowAudit          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupAudit = fmt.Errorf("proto: unexpected end of group")
)
