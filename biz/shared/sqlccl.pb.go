// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: sqlccl.proto

package shared

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strconv "strconv"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type SqlType int32

const (
	SELECT  SqlType = 0
	UPDATE  SqlType = 1
	DELETE  SqlType = 2
	INSERT  SqlType = 3
	REPLACE SqlType = 4
)

var SqlType_name = map[int32]string{
	0: "SELECT",
	1: "UPDATE",
	2: "DELETE",
	3: "INSERT",
	4: "REPLACE",
}

var SqlType_value = map[string]int32{
	"SELECT":  0,
	"UPDATE":  1,
	"DELETE":  2,
	"INSERT":  3,
	"REPLACE": 4,
}

func (SqlType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_29cccc3d5aac0b55, []int{0}
}

type CCLActionType int32

const (
	CclShow   CCLActionType = 0
	CclAdd    CCLActionType = 1
	CclDelete CCLActionType = 2
	CclStop   CCLActionType = 3
)

var CCLActionType_name = map[int32]string{
	0: "CclShow",
	1: "CclAdd",
	2: "CclDelete",
	3: "CclStop",
}

var CCLActionType_value = map[string]int32{
	"CclShow":   0,
	"CclAdd":    1,
	"CclDelete": 2,
	"CclStop":   3,
}

func (CCLActionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_29cccc3d5aac0b55, []int{1}
}

// 新建SQL限流任务
type CreateSqlConcurrencyControlRuleReq struct {
	TaskId     int64  `protobuf:"varint,1,opt,name=TaskId,proto3" json:"TaskId,omitempty"`
	TenantId   string `protobuf:"bytes,2,opt,name=TenantId,proto3" json:"TenantId,omitempty"`
	BizContext string `protobuf:"bytes,3,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
}

func (m *CreateSqlConcurrencyControlRuleReq) Reset()      { *m = CreateSqlConcurrencyControlRuleReq{} }
func (*CreateSqlConcurrencyControlRuleReq) ProtoMessage() {}
func (*CreateSqlConcurrencyControlRuleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_29cccc3d5aac0b55, []int{0}
}
func (m *CreateSqlConcurrencyControlRuleReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CreateSqlConcurrencyControlRuleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CreateSqlConcurrencyControlRuleReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CreateSqlConcurrencyControlRuleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateSqlConcurrencyControlRuleReq.Merge(m, src)
}
func (m *CreateSqlConcurrencyControlRuleReq) XXX_Size() int {
	return m.Size()
}
func (m *CreateSqlConcurrencyControlRuleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateSqlConcurrencyControlRuleReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateSqlConcurrencyControlRuleReq proto.InternalMessageInfo

func (m *CreateSqlConcurrencyControlRuleReq) GetTaskId() int64 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *CreateSqlConcurrencyControlRuleReq) GetTenantId() string {
	if m != nil {
		return m.TenantId
	}
	return ""
}

func (m *CreateSqlConcurrencyControlRuleReq) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

type ActionSuccess struct {
}

func (m *ActionSuccess) Reset()      { *m = ActionSuccess{} }
func (*ActionSuccess) ProtoMessage() {}
func (*ActionSuccess) Descriptor() ([]byte, []int) {
	return fileDescriptor_29cccc3d5aac0b55, []int{1}
}
func (m *ActionSuccess) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ActionSuccess) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ActionSuccess.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ActionSuccess) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActionSuccess.Merge(m, src)
}
func (m *ActionSuccess) XXX_Size() int {
	return m.Size()
}
func (m *ActionSuccess) XXX_DiscardUnknown() {
	xxx_messageInfo_ActionSuccess.DiscardUnknown(m)
}

var xxx_messageInfo_ActionSuccess proto.InternalMessageInfo

// 删除SQL限流任务
type DeleteSqlConcurrencyControlRuleReq struct {
	TaskIds    []int64 `protobuf:"varint,1,rep,packed,name=TaskIds,proto3" json:"TaskIds,omitempty"`
	TenantId   string  `protobuf:"bytes,2,opt,name=TenantId,proto3" json:"TenantId,omitempty"`
	BizContext string  `protobuf:"bytes,3,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
}

func (m *DeleteSqlConcurrencyControlRuleReq) Reset()      { *m = DeleteSqlConcurrencyControlRuleReq{} }
func (*DeleteSqlConcurrencyControlRuleReq) ProtoMessage() {}
func (*DeleteSqlConcurrencyControlRuleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_29cccc3d5aac0b55, []int{2}
}
func (m *DeleteSqlConcurrencyControlRuleReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteSqlConcurrencyControlRuleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteSqlConcurrencyControlRuleReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteSqlConcurrencyControlRuleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteSqlConcurrencyControlRuleReq.Merge(m, src)
}
func (m *DeleteSqlConcurrencyControlRuleReq) XXX_Size() int {
	return m.Size()
}
func (m *DeleteSqlConcurrencyControlRuleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteSqlConcurrencyControlRuleReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteSqlConcurrencyControlRuleReq proto.InternalMessageInfo

func (m *DeleteSqlConcurrencyControlRuleReq) GetTaskIds() []int64 {
	if m != nil {
		return m.TaskIds
	}
	return nil
}

func (m *DeleteSqlConcurrencyControlRuleReq) GetTenantId() string {
	if m != nil {
		return m.TenantId
	}
	return ""
}

func (m *DeleteSqlConcurrencyControlRuleReq) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

// 关闭SQL限流任务
type StopSqlConcurrencyControlRuleReq struct {
	TaskIds    []int64 `protobuf:"varint,1,rep,packed,name=TaskIds,proto3" json:"TaskIds,omitempty"`
	TenantId   string  `protobuf:"bytes,2,opt,name=TenantId,proto3" json:"TenantId,omitempty"`
	State      int64   `protobuf:"varint,3,opt,name=State,proto3" json:"State,omitempty"`
	BizContext string  `protobuf:"bytes,4,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
}

func (m *StopSqlConcurrencyControlRuleReq) Reset()      { *m = StopSqlConcurrencyControlRuleReq{} }
func (*StopSqlConcurrencyControlRuleReq) ProtoMessage() {}
func (*StopSqlConcurrencyControlRuleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_29cccc3d5aac0b55, []int{3}
}
func (m *StopSqlConcurrencyControlRuleReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StopSqlConcurrencyControlRuleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StopSqlConcurrencyControlRuleReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StopSqlConcurrencyControlRuleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopSqlConcurrencyControlRuleReq.Merge(m, src)
}
func (m *StopSqlConcurrencyControlRuleReq) XXX_Size() int {
	return m.Size()
}
func (m *StopSqlConcurrencyControlRuleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StopSqlConcurrencyControlRuleReq.DiscardUnknown(m)
}

var xxx_messageInfo_StopSqlConcurrencyControlRuleReq proto.InternalMessageInfo

func (m *StopSqlConcurrencyControlRuleReq) GetTaskIds() []int64 {
	if m != nil {
		return m.TaskIds
	}
	return nil
}

func (m *StopSqlConcurrencyControlRuleReq) GetTenantId() string {
	if m != nil {
		return m.TenantId
	}
	return ""
}

func (m *StopSqlConcurrencyControlRuleReq) GetState() int64 {
	if m != nil {
		return m.State
	}
	return 0
}

func (m *StopSqlConcurrencyControlRuleReq) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

// 执行CCL返回结果
type ExecuteCCLResp struct {
	Result string `protobuf:"bytes,1,opt,name=Result,proto3" json:"Result,omitempty"`
}

func (m *ExecuteCCLResp) Reset()      { *m = ExecuteCCLResp{} }
func (*ExecuteCCLResp) ProtoMessage() {}
func (*ExecuteCCLResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_29cccc3d5aac0b55, []int{4}
}
func (m *ExecuteCCLResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExecuteCCLResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExecuteCCLResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExecuteCCLResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExecuteCCLResp.Merge(m, src)
}
func (m *ExecuteCCLResp) XXX_Size() int {
	return m.Size()
}
func (m *ExecuteCCLResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExecuteCCLResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExecuteCCLResp proto.InternalMessageInfo

func (m *ExecuteCCLResp) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

type CCLRuleInfo struct {
	Id                 string `protobuf:"bytes,1,opt,name=Id,proto3" json:"Id,omitempty"`
	Type               string `protobuf:"bytes,2,opt,name=Type,proto3" json:"Type,omitempty"`
	User               string `protobuf:"bytes,3,opt,name=User,proto3" json:"User,omitempty"`
	Host               string `protobuf:"bytes,4,opt,name=Host,proto3" json:"Host,omitempty"`
	Schema             string `protobuf:"bytes,5,opt,name=Schema,proto3" json:"Schema,omitempty"`
	Table              string `protobuf:"bytes,6,opt,name=Table,proto3" json:"Table,omitempty"`
	Keywords           string `protobuf:"bytes,7,opt,name=Keywords,proto3" json:"Keywords,omitempty"`
	State              string `protobuf:"bytes,8,opt,name=State,proto3" json:"State,omitempty"`
	Ordered            string `protobuf:"bytes,9,opt,name=Ordered,proto3" json:"Ordered,omitempty"`
	ConcurrencyCount   string `protobuf:"bytes,10,opt,name=ConcurrencyCount,proto3" json:"ConcurrencyCount,omitempty"`
	WaitTimeout        string `protobuf:"bytes,11,opt,name=WaitTimeout,proto3" json:"WaitTimeout,omitempty"`
	MaxWaitThreadCount string `protobuf:"bytes,12,opt,name=MaxWaitThreadCount,proto3" json:"MaxWaitThreadCount,omitempty"`
	Matched            string `protobuf:"bytes,13,opt,name=Matched,proto3" json:"Matched,omitempty"`
	Running            string `protobuf:"bytes,14,opt,name=Running,proto3" json:"Running,omitempty"`
	Waiting            string `protobuf:"bytes,15,opt,name=Waiting,proto3" json:"Waiting,omitempty"`
}

func (m *CCLRuleInfo) Reset()      { *m = CCLRuleInfo{} }
func (*CCLRuleInfo) ProtoMessage() {}
func (*CCLRuleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_29cccc3d5aac0b55, []int{5}
}
func (m *CCLRuleInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CCLRuleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CCLRuleInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CCLRuleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CCLRuleInfo.Merge(m, src)
}
func (m *CCLRuleInfo) XXX_Size() int {
	return m.Size()
}
func (m *CCLRuleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CCLRuleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CCLRuleInfo proto.InternalMessageInfo

func (m *CCLRuleInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CCLRuleInfo) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *CCLRuleInfo) GetUser() string {
	if m != nil {
		return m.User
	}
	return ""
}

func (m *CCLRuleInfo) GetHost() string {
	if m != nil {
		return m.Host
	}
	return ""
}

func (m *CCLRuleInfo) GetSchema() string {
	if m != nil {
		return m.Schema
	}
	return ""
}

func (m *CCLRuleInfo) GetTable() string {
	if m != nil {
		return m.Table
	}
	return ""
}

func (m *CCLRuleInfo) GetKeywords() string {
	if m != nil {
		return m.Keywords
	}
	return ""
}

func (m *CCLRuleInfo) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

func (m *CCLRuleInfo) GetOrdered() string {
	if m != nil {
		return m.Ordered
	}
	return ""
}

func (m *CCLRuleInfo) GetConcurrencyCount() string {
	if m != nil {
		return m.ConcurrencyCount
	}
	return ""
}

func (m *CCLRuleInfo) GetWaitTimeout() string {
	if m != nil {
		return m.WaitTimeout
	}
	return ""
}

func (m *CCLRuleInfo) GetMaxWaitThreadCount() string {
	if m != nil {
		return m.MaxWaitThreadCount
	}
	return ""
}

func (m *CCLRuleInfo) GetMatched() string {
	if m != nil {
		return m.Matched
	}
	return ""
}

func (m *CCLRuleInfo) GetRunning() string {
	if m != nil {
		return m.Running
	}
	return ""
}

func (m *CCLRuleInfo) GetWaiting() string {
	if m != nil {
		return m.Waiting
	}
	return ""
}

type CCLRuleInfoResp struct {
	CCLRule []*CCLRuleInfo `protobuf:"bytes,1,rep,name=CCLRule,proto3" json:"CCLRule,omitempty"`
}

func (m *CCLRuleInfoResp) Reset()      { *m = CCLRuleInfoResp{} }
func (*CCLRuleInfoResp) ProtoMessage() {}
func (*CCLRuleInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_29cccc3d5aac0b55, []int{6}
}
func (m *CCLRuleInfoResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CCLRuleInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CCLRuleInfoResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CCLRuleInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CCLRuleInfoResp.Merge(m, src)
}
func (m *CCLRuleInfoResp) XXX_Size() int {
	return m.Size()
}
func (m *CCLRuleInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CCLRuleInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_CCLRuleInfoResp proto.InternalMessageInfo

func (m *CCLRuleInfoResp) GetCCLRule() []*CCLRuleInfo {
	if m != nil {
		return m.CCLRule
	}
	return nil
}

type CCLRuleActorFailResp struct {
	Error         string       `protobuf:"bytes,1,opt,name=Error,proto3" json:"Error,omitempty"`
	StandardError *StandardErr `protobuf:"bytes,3,opt,name=standard_error,json=standardError,proto3" json:"standard_error,omitempty"`
}

func (m *CCLRuleActorFailResp) Reset()      { *m = CCLRuleActorFailResp{} }
func (*CCLRuleActorFailResp) ProtoMessage() {}
func (*CCLRuleActorFailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_29cccc3d5aac0b55, []int{7}
}
func (m *CCLRuleActorFailResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CCLRuleActorFailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CCLRuleActorFailResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CCLRuleActorFailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CCLRuleActorFailResp.Merge(m, src)
}
func (m *CCLRuleActorFailResp) XXX_Size() int {
	return m.Size()
}
func (m *CCLRuleActorFailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CCLRuleActorFailResp.DiscardUnknown(m)
}

var xxx_messageInfo_CCLRuleActorFailResp proto.InternalMessageInfo

func (m *CCLRuleActorFailResp) GetError() string {
	if m != nil {
		return m.Error
	}
	return ""
}

func (m *CCLRuleActorFailResp) GetStandardError() *StandardErr {
	if m != nil {
		return m.StandardError
	}
	return nil
}

type DescribeRealTimeCCLRulesReq struct {
	TaskId     int64         `protobuf:"varint,1,opt,name=TaskId,proto3" json:"TaskId,omitempty"`
	BizContext string        `protobuf:"bytes,2,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
	TaskIds    []int64       `protobuf:"varint,3,rep,packed,name=TaskIds,proto3" json:"TaskIds,omitempty"`
	ActionType CCLActionType `protobuf:"varint,4,opt,name=ActionType,proto3,enum=shared.CCLActionType" json:"ActionType,omitempty"`
}

func (m *DescribeRealTimeCCLRulesReq) Reset()      { *m = DescribeRealTimeCCLRulesReq{} }
func (*DescribeRealTimeCCLRulesReq) ProtoMessage() {}
func (*DescribeRealTimeCCLRulesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_29cccc3d5aac0b55, []int{8}
}
func (m *DescribeRealTimeCCLRulesReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DescribeRealTimeCCLRulesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DescribeRealTimeCCLRulesReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DescribeRealTimeCCLRulesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescribeRealTimeCCLRulesReq.Merge(m, src)
}
func (m *DescribeRealTimeCCLRulesReq) XXX_Size() int {
	return m.Size()
}
func (m *DescribeRealTimeCCLRulesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DescribeRealTimeCCLRulesReq.DiscardUnknown(m)
}

var xxx_messageInfo_DescribeRealTimeCCLRulesReq proto.InternalMessageInfo

func (m *DescribeRealTimeCCLRulesReq) GetTaskId() int64 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *DescribeRealTimeCCLRulesReq) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

func (m *DescribeRealTimeCCLRulesReq) GetTaskIds() []int64 {
	if m != nil {
		return m.TaskIds
	}
	return nil
}

func (m *DescribeRealTimeCCLRulesReq) GetActionType() CCLActionType {
	if m != nil {
		return m.ActionType
	}
	return CclShow
}

type DescribeRealTimeCCLRulesResp struct {
	Consistent bool `protobuf:"varint,1,opt,name=Consistent,proto3" json:"Consistent,omitempty"`
	Success    bool `protobuf:"varint,2,opt,name=Success,proto3" json:"Success,omitempty"`
}

func (m *DescribeRealTimeCCLRulesResp) Reset()      { *m = DescribeRealTimeCCLRulesResp{} }
func (*DescribeRealTimeCCLRulesResp) ProtoMessage() {}
func (*DescribeRealTimeCCLRulesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_29cccc3d5aac0b55, []int{9}
}
func (m *DescribeRealTimeCCLRulesResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DescribeRealTimeCCLRulesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DescribeRealTimeCCLRulesResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DescribeRealTimeCCLRulesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DescribeRealTimeCCLRulesResp.Merge(m, src)
}
func (m *DescribeRealTimeCCLRulesResp) XXX_Size() int {
	return m.Size()
}
func (m *DescribeRealTimeCCLRulesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DescribeRealTimeCCLRulesResp.DiscardUnknown(m)
}

var xxx_messageInfo_DescribeRealTimeCCLRulesResp proto.InternalMessageInfo

func (m *DescribeRealTimeCCLRulesResp) GetConsistent() bool {
	if m != nil {
		return m.Consistent
	}
	return false
}

func (m *DescribeRealTimeCCLRulesResp) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

func init() {
	proto.RegisterEnum("shared.SqlType", SqlType_name, SqlType_value)
	proto.RegisterEnum("shared.CCLActionType", CCLActionType_name, CCLActionType_value)
	proto.RegisterType((*CreateSqlConcurrencyControlRuleReq)(nil), "shared.CreateSqlConcurrencyControlRuleReq")
	proto.RegisterType((*ActionSuccess)(nil), "shared.ActionSuccess")
	proto.RegisterType((*DeleteSqlConcurrencyControlRuleReq)(nil), "shared.DeleteSqlConcurrencyControlRuleReq")
	proto.RegisterType((*StopSqlConcurrencyControlRuleReq)(nil), "shared.StopSqlConcurrencyControlRuleReq")
	proto.RegisterType((*ExecuteCCLResp)(nil), "shared.ExecuteCCLResp")
	proto.RegisterType((*CCLRuleInfo)(nil), "shared.CCLRuleInfo")
	proto.RegisterType((*CCLRuleInfoResp)(nil), "shared.CCLRuleInfoResp")
	proto.RegisterType((*CCLRuleActorFailResp)(nil), "shared.CCLRuleActorFailResp")
	proto.RegisterType((*DescribeRealTimeCCLRulesReq)(nil), "shared.DescribeRealTimeCCLRulesReq")
	proto.RegisterType((*DescribeRealTimeCCLRulesResp)(nil), "shared.DescribeRealTimeCCLRulesResp")
}

func init() { proto.RegisterFile("sqlccl.proto", fileDescriptor_29cccc3d5aac0b55) }

var fileDescriptor_29cccc3d5aac0b55 = []byte{
	// 764 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x54, 0xcd, 0x8e, 0xe3, 0x44,
	0x10, 0x8e, 0xe3, 0x99, 0x64, 0x52, 0xd9, 0x64, 0x2c, 0x33, 0x20, 0x6b, 0x41, 0x26, 0xf2, 0x69,
	0x34, 0x12, 0x73, 0x58, 0xe0, 0xc2, 0x89, 0xe0, 0x78, 0x45, 0x44, 0x16, 0x56, 0x6d, 0xaf, 0xe0,
	0xb6, 0x72, 0xda, 0x05, 0xb1, 0xf0, 0x76, 0x27, 0xdd, 0x6d, 0xed, 0x64, 0xb9, 0xf0, 0x06, 0xf0,
	0x06, 0x1c, 0xe1, 0x51, 0x38, 0xce, 0x71, 0x8f, 0x4c, 0xe6, 0xc2, 0x71, 0x1f, 0x01, 0x75, 0xb7,
	0xc3, 0x78, 0x66, 0xf9, 0x39, 0x20, 0x6e, 0xf5, 0x7d, 0x5f, 0xb9, 0xeb, 0x73, 0x55, 0x77, 0xc1,
	0x3d, 0xb9, 0xa9, 0x28, 0xad, 0xce, 0xd7, 0x82, 0x2b, 0xee, 0xf7, 0xe4, 0x2a, 0x17, 0x58, 0xdc,
	0x1f, 0x49, 0x94, 0xb2, 0xe4, 0xcc, 0xd2, 0xd1, 0x16, 0xa2, 0x58, 0x60, 0xae, 0x30, 0xdd, 0x54,
	0x31, 0x67, 0xb4, 0x16, 0x02, 0x19, 0xdd, 0xc6, 0x9c, 0x29, 0xc1, 0x2b, 0x52, 0x57, 0x48, 0x70,
	0xe3, 0xbf, 0x05, 0xbd, 0x2c, 0x97, 0xdf, 0xce, 0x8b, 0xc0, 0x99, 0x38, 0xa7, 0x2e, 0x69, 0x90,
	0x7f, 0x1f, 0x8e, 0x32, 0x64, 0x39, 0x53, 0xf3, 0x22, 0xe8, 0x4e, 0x9c, 0xd3, 0x01, 0xf9, 0x13,
	0xfb, 0xef, 0xc2, 0x70, 0x59, 0xbe, 0x78, 0x4a, 0x39, 0x53, 0x78, 0xa1, 0x02, 0xd7, 0xc8, 0xb0,
	0x2c, 0x5f, 0xc4, 0x96, 0x89, 0x8e, 0x61, 0x34, 0xa5, 0xaa, 0xe4, 0x2c, 0xad, 0x29, 0x45, 0x29,
	0xa3, 0xef, 0x20, 0x9a, 0x61, 0x85, 0xff, 0xe2, 0x25, 0x80, 0xbe, 0xad, 0x2e, 0x03, 0x67, 0xe2,
	0x9e, 0xba, 0x64, 0x0f, 0xff, 0x9b, 0x9b, 0x1f, 0x1c, 0x98, 0xa4, 0x8a, 0xaf, 0xff, 0x87, 0xda,
	0x27, 0x70, 0x98, 0xaa, 0x5c, 0xa1, 0xa9, 0xea, 0x12, 0x0b, 0xee, 0x3a, 0x3a, 0x78, 0xcd, 0xd1,
	0x29, 0x8c, 0x93, 0x0b, 0xa4, 0xb5, 0xc2, 0x38, 0x5e, 0x10, 0x94, 0x6b, 0x3d, 0x06, 0x82, 0xb2,
	0xae, 0x94, 0x19, 0xc3, 0x80, 0x34, 0x28, 0xfa, 0xc9, 0x85, 0xa1, 0xce, 0xa9, 0x2b, 0x9c, 0xb3,
	0xaf, 0xb9, 0x3f, 0x86, 0x6e, 0x33, 0xaa, 0x01, 0xe9, 0xce, 0x0b, 0xdf, 0x87, 0x83, 0x6c, 0xbb,
	0xc6, 0xc6, 0x98, 0x89, 0x35, 0xf7, 0x44, 0xa2, 0x68, 0x3a, 0x61, 0x62, 0xcd, 0x7d, 0xca, 0xe5,
	0xde, 0x8b, 0x89, 0x75, 0xcd, 0x94, 0xae, 0xf0, 0x59, 0x1e, 0x1c, 0xda, 0x9a, 0x16, 0xe9, 0x9f,
	0xca, 0xf2, 0x65, 0x85, 0x41, 0xcf, 0xd0, 0x16, 0xe8, 0x36, 0x7c, 0x86, 0xdb, 0xe7, 0x5c, 0x14,
	0x32, 0xe8, 0xdb, 0x36, 0xec, 0xf1, 0x4d, 0x1b, 0x8e, 0xec, 0x17, 0xb6, 0x0d, 0x01, 0xf4, 0xbf,
	0x10, 0x05, 0x0a, 0x2c, 0x82, 0x81, 0xe1, 0xf7, 0xd0, 0x3f, 0x03, 0xef, 0xd6, 0x24, 0x6a, 0xa6,
	0x02, 0x30, 0x29, 0xaf, 0xf1, 0xfe, 0x04, 0x86, 0x5f, 0xe6, 0xa5, 0xca, 0xca, 0x67, 0xc8, 0x6b,
	0x15, 0x0c, 0x4d, 0x5a, 0x9b, 0xf2, 0xcf, 0xc1, 0x7f, 0x94, 0x5f, 0x18, 0x66, 0x25, 0x30, 0x2f,
	0xec, 0x79, 0xf7, 0x4c, 0xe2, 0x5f, 0x28, 0xda, 0xd7, 0xa3, 0x5c, 0xd1, 0x15, 0x16, 0xc1, 0xc8,
	0xfa, 0x6a, 0xa0, 0x56, 0x48, 0xcd, 0x58, 0xc9, 0xbe, 0x09, 0xc6, 0x56, 0x69, 0xa0, 0x56, 0xf4,
	0x31, 0x5a, 0x39, 0xb6, 0x4a, 0x03, 0xa3, 0x8f, 0xe1, 0xb8, 0x35, 0x20, 0x33, 0xcc, 0xf7, 0xa0,
	0xdf, 0x50, 0xe6, 0x2e, 0x0d, 0x1f, 0xbc, 0x71, 0x6e, 0x9f, 0xe8, 0x79, 0x3b, 0x73, 0x9f, 0x13,
	0xad, 0xe0, 0xa4, 0x09, 0xa7, 0x54, 0x71, 0xf1, 0x30, 0x2f, 0x2b, 0x73, 0xcc, 0x09, 0x1c, 0x26,
	0x42, 0x70, 0xd1, 0x8c, 0xdb, 0x02, 0xff, 0x23, 0x18, 0x4b, 0x95, 0xb3, 0x22, 0x17, 0xc5, 0x53,
	0x34, 0xb2, 0x9e, 0x73, 0xab, 0x46, 0xda, 0xa8, 0x89, 0x10, 0x64, 0x24, 0x6f, 0x00, 0x17, 0xd1,
	0xcf, 0x0e, 0xbc, 0x3d, 0x43, 0x49, 0x45, 0xb9, 0x44, 0x82, 0x79, 0xa5, 0x3b, 0xd8, 0x94, 0x96,
	0xff, 0xb4, 0x0c, 0xee, 0x5c, 0xe8, 0xee, 0xdd, 0x0b, 0xdd, 0x7e, 0x3d, 0xee, 0xed, 0xd7, 0xf3,
	0x21, 0x80, 0x5d, 0x05, 0xe6, 0x9a, 0xea, 0xeb, 0x37, 0x7e, 0xf0, 0x66, 0xab, 0x1d, 0x37, 0x22,
	0x69, 0x25, 0x46, 0x5f, 0xc1, 0x3b, 0x7f, 0x6f, 0x54, 0xae, 0xfd, 0x10, 0x20, 0xe6, 0x4c, 0x96,
	0x52, 0x21, 0xb3, 0x6f, 0xe6, 0x88, 0xb4, 0x18, 0x6d, 0xa8, 0xd9, 0x3d, 0xc6, 0xed, 0x11, 0xd9,
	0xc3, 0xb3, 0x87, 0xd0, 0x4f, 0x37, 0x95, 0x79, 0x28, 0x00, 0xbd, 0x34, 0x59, 0x24, 0x71, 0xe6,
	0x75, 0x74, 0xfc, 0xe4, 0xf1, 0x6c, 0x9a, 0x25, 0x9e, 0xa3, 0xe3, 0x59, 0xb2, 0x48, 0xb2, 0xc4,
	0xeb, 0xea, 0x78, 0xfe, 0x79, 0x9a, 0x90, 0xcc, 0x73, 0xfd, 0x21, 0xf4, 0x49, 0xf2, 0x78, 0x31,
	0x8d, 0x13, 0xef, 0xe0, 0x6c, 0x06, 0xa3, 0x5b, 0xf6, 0xb5, 0x1a, 0xd3, 0x2a, 0x5d, 0xf1, 0xe7,
	0xf6, 0xb8, 0x98, 0x56, 0xd3, 0xa2, 0xf0, 0x1c, 0x7f, 0x04, 0x83, 0x98, 0x56, 0x76, 0xff, 0x79,
	0xdd, 0x7d, 0x9e, 0xe2, 0x6b, 0xcf, 0xfd, 0xe4, 0x83, 0xcb, 0xab, 0xb0, 0xf3, 0xf2, 0x2a, 0xec,
	0xbc, 0xba, 0x0a, 0x9d, 0xef, 0x77, 0xa1, 0xf3, 0xcb, 0x2e, 0x74, 0x7e, 0xdd, 0x85, 0xce, 0xe5,
	0x2e, 0x74, 0x7e, 0xdb, 0x85, 0xce, 0xef, 0xbb, 0xb0, 0xf3, 0x6a, 0x17, 0x3a, 0x3f, 0x5e, 0x87,
	0x9d, 0xcb, 0xeb, 0xb0, 0xf3, 0xf2, 0x3a, 0xec, 0x2c, 0x7b, 0x66, 0xc3, 0xbf, 0xff, 0x47, 0x00,
	0x00, 0x00, 0xff, 0xff, 0x3d, 0x15, 0x18, 0x60, 0x08, 0x06, 0x00, 0x00,
}

func (x SqlType) String() string {
	s, ok := SqlType_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (x CCLActionType) String() string {
	s, ok := CCLActionType_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (this *CreateSqlConcurrencyControlRuleReq) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CreateSqlConcurrencyControlRuleReq)
	if !ok {
		that2, ok := that.(CreateSqlConcurrencyControlRuleReq)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TaskId != that1.TaskId {
		return false
	}
	if this.TenantId != that1.TenantId {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	return true
}
func (this *ActionSuccess) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ActionSuccess)
	if !ok {
		that2, ok := that.(ActionSuccess)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	return true
}
func (this *DeleteSqlConcurrencyControlRuleReq) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DeleteSqlConcurrencyControlRuleReq)
	if !ok {
		that2, ok := that.(DeleteSqlConcurrencyControlRuleReq)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.TaskIds) != len(that1.TaskIds) {
		return false
	}
	for i := range this.TaskIds {
		if this.TaskIds[i] != that1.TaskIds[i] {
			return false
		}
	}
	if this.TenantId != that1.TenantId {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	return true
}
func (this *StopSqlConcurrencyControlRuleReq) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*StopSqlConcurrencyControlRuleReq)
	if !ok {
		that2, ok := that.(StopSqlConcurrencyControlRuleReq)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.TaskIds) != len(that1.TaskIds) {
		return false
	}
	for i := range this.TaskIds {
		if this.TaskIds[i] != that1.TaskIds[i] {
			return false
		}
	}
	if this.TenantId != that1.TenantId {
		return false
	}
	if this.State != that1.State {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	return true
}
func (this *ExecuteCCLResp) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ExecuteCCLResp)
	if !ok {
		that2, ok := that.(ExecuteCCLResp)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Result != that1.Result {
		return false
	}
	return true
}
func (this *CCLRuleInfo) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CCLRuleInfo)
	if !ok {
		that2, ok := that.(CCLRuleInfo)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Id != that1.Id {
		return false
	}
	if this.Type != that1.Type {
		return false
	}
	if this.User != that1.User {
		return false
	}
	if this.Host != that1.Host {
		return false
	}
	if this.Schema != that1.Schema {
		return false
	}
	if this.Table != that1.Table {
		return false
	}
	if this.Keywords != that1.Keywords {
		return false
	}
	if this.State != that1.State {
		return false
	}
	if this.Ordered != that1.Ordered {
		return false
	}
	if this.ConcurrencyCount != that1.ConcurrencyCount {
		return false
	}
	if this.WaitTimeout != that1.WaitTimeout {
		return false
	}
	if this.MaxWaitThreadCount != that1.MaxWaitThreadCount {
		return false
	}
	if this.Matched != that1.Matched {
		return false
	}
	if this.Running != that1.Running {
		return false
	}
	if this.Waiting != that1.Waiting {
		return false
	}
	return true
}
func (this *CCLRuleInfoResp) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CCLRuleInfoResp)
	if !ok {
		that2, ok := that.(CCLRuleInfoResp)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.CCLRule) != len(that1.CCLRule) {
		return false
	}
	for i := range this.CCLRule {
		if !this.CCLRule[i].Equal(that1.CCLRule[i]) {
			return false
		}
	}
	return true
}
func (this *CCLRuleActorFailResp) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CCLRuleActorFailResp)
	if !ok {
		that2, ok := that.(CCLRuleActorFailResp)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Error != that1.Error {
		return false
	}
	if !this.StandardError.Equal(that1.StandardError) {
		return false
	}
	return true
}
func (this *DescribeRealTimeCCLRulesReq) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DescribeRealTimeCCLRulesReq)
	if !ok {
		that2, ok := that.(DescribeRealTimeCCLRulesReq)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TaskId != that1.TaskId {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	if len(this.TaskIds) != len(that1.TaskIds) {
		return false
	}
	for i := range this.TaskIds {
		if this.TaskIds[i] != that1.TaskIds[i] {
			return false
		}
	}
	if this.ActionType != that1.ActionType {
		return false
	}
	return true
}
func (this *DescribeRealTimeCCLRulesResp) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*DescribeRealTimeCCLRulesResp)
	if !ok {
		that2, ok := that.(DescribeRealTimeCCLRulesResp)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Consistent != that1.Consistent {
		return false
	}
	if this.Success != that1.Success {
		return false
	}
	return true
}
func (this *CreateSqlConcurrencyControlRuleReq) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&shared.CreateSqlConcurrencyControlRuleReq{")
	s = append(s, "TaskId: "+fmt.Sprintf("%#v", this.TaskId)+",\n")
	s = append(s, "TenantId: "+fmt.Sprintf("%#v", this.TenantId)+",\n")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ActionSuccess) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 4)
	s = append(s, "&shared.ActionSuccess{")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DeleteSqlConcurrencyControlRuleReq) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&shared.DeleteSqlConcurrencyControlRuleReq{")
	s = append(s, "TaskIds: "+fmt.Sprintf("%#v", this.TaskIds)+",\n")
	s = append(s, "TenantId: "+fmt.Sprintf("%#v", this.TenantId)+",\n")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *StopSqlConcurrencyControlRuleReq) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.StopSqlConcurrencyControlRuleReq{")
	s = append(s, "TaskIds: "+fmt.Sprintf("%#v", this.TaskIds)+",\n")
	s = append(s, "TenantId: "+fmt.Sprintf("%#v", this.TenantId)+",\n")
	s = append(s, "State: "+fmt.Sprintf("%#v", this.State)+",\n")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ExecuteCCLResp) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.ExecuteCCLResp{")
	s = append(s, "Result: "+fmt.Sprintf("%#v", this.Result)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CCLRuleInfo) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 19)
	s = append(s, "&shared.CCLRuleInfo{")
	s = append(s, "Id: "+fmt.Sprintf("%#v", this.Id)+",\n")
	s = append(s, "Type: "+fmt.Sprintf("%#v", this.Type)+",\n")
	s = append(s, "User: "+fmt.Sprintf("%#v", this.User)+",\n")
	s = append(s, "Host: "+fmt.Sprintf("%#v", this.Host)+",\n")
	s = append(s, "Schema: "+fmt.Sprintf("%#v", this.Schema)+",\n")
	s = append(s, "Table: "+fmt.Sprintf("%#v", this.Table)+",\n")
	s = append(s, "Keywords: "+fmt.Sprintf("%#v", this.Keywords)+",\n")
	s = append(s, "State: "+fmt.Sprintf("%#v", this.State)+",\n")
	s = append(s, "Ordered: "+fmt.Sprintf("%#v", this.Ordered)+",\n")
	s = append(s, "ConcurrencyCount: "+fmt.Sprintf("%#v", this.ConcurrencyCount)+",\n")
	s = append(s, "WaitTimeout: "+fmt.Sprintf("%#v", this.WaitTimeout)+",\n")
	s = append(s, "MaxWaitThreadCount: "+fmt.Sprintf("%#v", this.MaxWaitThreadCount)+",\n")
	s = append(s, "Matched: "+fmt.Sprintf("%#v", this.Matched)+",\n")
	s = append(s, "Running: "+fmt.Sprintf("%#v", this.Running)+",\n")
	s = append(s, "Waiting: "+fmt.Sprintf("%#v", this.Waiting)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CCLRuleInfoResp) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.CCLRuleInfoResp{")
	if this.CCLRule != nil {
		s = append(s, "CCLRule: "+fmt.Sprintf("%#v", this.CCLRule)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CCLRuleActorFailResp) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.CCLRuleActorFailResp{")
	s = append(s, "Error: "+fmt.Sprintf("%#v", this.Error)+",\n")
	if this.StandardError != nil {
		s = append(s, "StandardError: "+fmt.Sprintf("%#v", this.StandardError)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DescribeRealTimeCCLRulesReq) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.DescribeRealTimeCCLRulesReq{")
	s = append(s, "TaskId: "+fmt.Sprintf("%#v", this.TaskId)+",\n")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "TaskIds: "+fmt.Sprintf("%#v", this.TaskIds)+",\n")
	s = append(s, "ActionType: "+fmt.Sprintf("%#v", this.ActionType)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *DescribeRealTimeCCLRulesResp) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.DescribeRealTimeCCLRulesResp{")
	s = append(s, "Consistent: "+fmt.Sprintf("%#v", this.Consistent)+",\n")
	s = append(s, "Success: "+fmt.Sprintf("%#v", this.Success)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringSqlccl(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}
func (m *CreateSqlConcurrencyControlRuleReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateSqlConcurrencyControlRuleReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CreateSqlConcurrencyControlRuleReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.TenantId) > 0 {
		i -= len(m.TenantId)
		copy(dAtA[i:], m.TenantId)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.TenantId)))
		i--
		dAtA[i] = 0x12
	}
	if m.TaskId != 0 {
		i = encodeVarintSqlccl(dAtA, i, uint64(m.TaskId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ActionSuccess) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActionSuccess) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ActionSuccess) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *DeleteSqlConcurrencyControlRuleReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteSqlConcurrencyControlRuleReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteSqlConcurrencyControlRuleReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.TenantId) > 0 {
		i -= len(m.TenantId)
		copy(dAtA[i:], m.TenantId)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.TenantId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.TaskIds) > 0 {
		dAtA2 := make([]byte, len(m.TaskIds)*10)
		var j1 int
		for _, num1 := range m.TaskIds {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA2[j1] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j1++
			}
			dAtA2[j1] = uint8(num)
			j1++
		}
		i -= j1
		copy(dAtA[i:], dAtA2[:j1])
		i = encodeVarintSqlccl(dAtA, i, uint64(j1))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *StopSqlConcurrencyControlRuleReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StopSqlConcurrencyControlRuleReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StopSqlConcurrencyControlRuleReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0x22
	}
	if m.State != 0 {
		i = encodeVarintSqlccl(dAtA, i, uint64(m.State))
		i--
		dAtA[i] = 0x18
	}
	if len(m.TenantId) > 0 {
		i -= len(m.TenantId)
		copy(dAtA[i:], m.TenantId)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.TenantId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.TaskIds) > 0 {
		dAtA4 := make([]byte, len(m.TaskIds)*10)
		var j3 int
		for _, num1 := range m.TaskIds {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA4[j3] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j3++
			}
			dAtA4[j3] = uint8(num)
			j3++
		}
		i -= j3
		copy(dAtA[i:], dAtA4[:j3])
		i = encodeVarintSqlccl(dAtA, i, uint64(j3))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ExecuteCCLResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExecuteCCLResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExecuteCCLResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Result) > 0 {
		i -= len(m.Result)
		copy(dAtA[i:], m.Result)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.Result)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CCLRuleInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CCLRuleInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CCLRuleInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Waiting) > 0 {
		i -= len(m.Waiting)
		copy(dAtA[i:], m.Waiting)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.Waiting)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.Running) > 0 {
		i -= len(m.Running)
		copy(dAtA[i:], m.Running)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.Running)))
		i--
		dAtA[i] = 0x72
	}
	if len(m.Matched) > 0 {
		i -= len(m.Matched)
		copy(dAtA[i:], m.Matched)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.Matched)))
		i--
		dAtA[i] = 0x6a
	}
	if len(m.MaxWaitThreadCount) > 0 {
		i -= len(m.MaxWaitThreadCount)
		copy(dAtA[i:], m.MaxWaitThreadCount)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.MaxWaitThreadCount)))
		i--
		dAtA[i] = 0x62
	}
	if len(m.WaitTimeout) > 0 {
		i -= len(m.WaitTimeout)
		copy(dAtA[i:], m.WaitTimeout)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.WaitTimeout)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.ConcurrencyCount) > 0 {
		i -= len(m.ConcurrencyCount)
		copy(dAtA[i:], m.ConcurrencyCount)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.ConcurrencyCount)))
		i--
		dAtA[i] = 0x52
	}
	if len(m.Ordered) > 0 {
		i -= len(m.Ordered)
		copy(dAtA[i:], m.Ordered)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.Ordered)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.State) > 0 {
		i -= len(m.State)
		copy(dAtA[i:], m.State)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.State)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.Keywords) > 0 {
		i -= len(m.Keywords)
		copy(dAtA[i:], m.Keywords)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.Keywords)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.Table) > 0 {
		i -= len(m.Table)
		copy(dAtA[i:], m.Table)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.Table)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.Schema) > 0 {
		i -= len(m.Schema)
		copy(dAtA[i:], m.Schema)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.Schema)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.Host) > 0 {
		i -= len(m.Host)
		copy(dAtA[i:], m.Host)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.Host)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.User) > 0 {
		i -= len(m.User)
		copy(dAtA[i:], m.User)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.User)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Type) > 0 {
		i -= len(m.Type)
		copy(dAtA[i:], m.Type)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.Type)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Id) > 0 {
		i -= len(m.Id)
		copy(dAtA[i:], m.Id)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.Id)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CCLRuleInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CCLRuleInfoResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CCLRuleInfoResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.CCLRule) > 0 {
		for iNdEx := len(m.CCLRule) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.CCLRule[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintSqlccl(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *CCLRuleActorFailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CCLRuleActorFailResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CCLRuleActorFailResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.StandardError != nil {
		{
			size, err := m.StandardError.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintSqlccl(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Error) > 0 {
		i -= len(m.Error)
		copy(dAtA[i:], m.Error)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.Error)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *DescribeRealTimeCCLRulesReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DescribeRealTimeCCLRulesReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DescribeRealTimeCCLRulesReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ActionType != 0 {
		i = encodeVarintSqlccl(dAtA, i, uint64(m.ActionType))
		i--
		dAtA[i] = 0x20
	}
	if len(m.TaskIds) > 0 {
		dAtA7 := make([]byte, len(m.TaskIds)*10)
		var j6 int
		for _, num1 := range m.TaskIds {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA7[j6] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j6++
			}
			dAtA7[j6] = uint8(num)
			j6++
		}
		i -= j6
		copy(dAtA[i:], dAtA7[:j6])
		i = encodeVarintSqlccl(dAtA, i, uint64(j6))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintSqlccl(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0x12
	}
	if m.TaskId != 0 {
		i = encodeVarintSqlccl(dAtA, i, uint64(m.TaskId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DescribeRealTimeCCLRulesResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DescribeRealTimeCCLRulesResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DescribeRealTimeCCLRulesResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Success {
		i--
		if m.Success {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if m.Consistent {
		i--
		if m.Consistent {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintSqlccl(dAtA []byte, offset int, v uint64) int {
	offset -= sovSqlccl(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *CreateSqlConcurrencyControlRuleReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TaskId != 0 {
		n += 1 + sovSqlccl(uint64(m.TaskId))
	}
	l = len(m.TenantId)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	return n
}

func (m *ActionSuccess) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *DeleteSqlConcurrencyControlRuleReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.TaskIds) > 0 {
		l = 0
		for _, e := range m.TaskIds {
			l += sovSqlccl(uint64(e))
		}
		n += 1 + sovSqlccl(uint64(l)) + l
	}
	l = len(m.TenantId)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	return n
}

func (m *StopSqlConcurrencyControlRuleReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.TaskIds) > 0 {
		l = 0
		for _, e := range m.TaskIds {
			l += sovSqlccl(uint64(e))
		}
		n += 1 + sovSqlccl(uint64(l)) + l
	}
	l = len(m.TenantId)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	if m.State != 0 {
		n += 1 + sovSqlccl(uint64(m.State))
	}
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	return n
}

func (m *ExecuteCCLResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Result)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	return n
}

func (m *CCLRuleInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Id)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.Type)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.User)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.Host)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.Schema)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.Table)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.Keywords)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.State)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.Ordered)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.ConcurrencyCount)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.WaitTimeout)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.MaxWaitThreadCount)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.Matched)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.Running)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	l = len(m.Waiting)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	return n
}

func (m *CCLRuleInfoResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.CCLRule) > 0 {
		for _, e := range m.CCLRule {
			l = e.Size()
			n += 1 + l + sovSqlccl(uint64(l))
		}
	}
	return n
}

func (m *CCLRuleActorFailResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Error)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	if m.StandardError != nil {
		l = m.StandardError.Size()
		n += 1 + l + sovSqlccl(uint64(l))
	}
	return n
}

func (m *DescribeRealTimeCCLRulesReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TaskId != 0 {
		n += 1 + sovSqlccl(uint64(m.TaskId))
	}
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovSqlccl(uint64(l))
	}
	if len(m.TaskIds) > 0 {
		l = 0
		for _, e := range m.TaskIds {
			l += sovSqlccl(uint64(e))
		}
		n += 1 + sovSqlccl(uint64(l)) + l
	}
	if m.ActionType != 0 {
		n += 1 + sovSqlccl(uint64(m.ActionType))
	}
	return n
}

func (m *DescribeRealTimeCCLRulesResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Consistent {
		n += 2
	}
	if m.Success {
		n += 2
	}
	return n
}

func sovSqlccl(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozSqlccl(x uint64) (n int) {
	return sovSqlccl(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *CreateSqlConcurrencyControlRuleReq) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CreateSqlConcurrencyControlRuleReq{`,
		`TaskId:` + fmt.Sprintf("%v", this.TaskId) + `,`,
		`TenantId:` + fmt.Sprintf("%v", this.TenantId) + `,`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ActionSuccess) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ActionSuccess{`,
		`}`,
	}, "")
	return s
}
func (this *DeleteSqlConcurrencyControlRuleReq) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DeleteSqlConcurrencyControlRuleReq{`,
		`TaskIds:` + fmt.Sprintf("%v", this.TaskIds) + `,`,
		`TenantId:` + fmt.Sprintf("%v", this.TenantId) + `,`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`}`,
	}, "")
	return s
}
func (this *StopSqlConcurrencyControlRuleReq) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&StopSqlConcurrencyControlRuleReq{`,
		`TaskIds:` + fmt.Sprintf("%v", this.TaskIds) + `,`,
		`TenantId:` + fmt.Sprintf("%v", this.TenantId) + `,`,
		`State:` + fmt.Sprintf("%v", this.State) + `,`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ExecuteCCLResp) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ExecuteCCLResp{`,
		`Result:` + fmt.Sprintf("%v", this.Result) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CCLRuleInfo) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CCLRuleInfo{`,
		`Id:` + fmt.Sprintf("%v", this.Id) + `,`,
		`Type:` + fmt.Sprintf("%v", this.Type) + `,`,
		`User:` + fmt.Sprintf("%v", this.User) + `,`,
		`Host:` + fmt.Sprintf("%v", this.Host) + `,`,
		`Schema:` + fmt.Sprintf("%v", this.Schema) + `,`,
		`Table:` + fmt.Sprintf("%v", this.Table) + `,`,
		`Keywords:` + fmt.Sprintf("%v", this.Keywords) + `,`,
		`State:` + fmt.Sprintf("%v", this.State) + `,`,
		`Ordered:` + fmt.Sprintf("%v", this.Ordered) + `,`,
		`ConcurrencyCount:` + fmt.Sprintf("%v", this.ConcurrencyCount) + `,`,
		`WaitTimeout:` + fmt.Sprintf("%v", this.WaitTimeout) + `,`,
		`MaxWaitThreadCount:` + fmt.Sprintf("%v", this.MaxWaitThreadCount) + `,`,
		`Matched:` + fmt.Sprintf("%v", this.Matched) + `,`,
		`Running:` + fmt.Sprintf("%v", this.Running) + `,`,
		`Waiting:` + fmt.Sprintf("%v", this.Waiting) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CCLRuleInfoResp) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForCCLRule := "[]*CCLRuleInfo{"
	for _, f := range this.CCLRule {
		repeatedStringForCCLRule += strings.Replace(f.String(), "CCLRuleInfo", "CCLRuleInfo", 1) + ","
	}
	repeatedStringForCCLRule += "}"
	s := strings.Join([]string{`&CCLRuleInfoResp{`,
		`CCLRule:` + repeatedStringForCCLRule + `,`,
		`}`,
	}, "")
	return s
}
func (this *CCLRuleActorFailResp) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CCLRuleActorFailResp{`,
		`Error:` + fmt.Sprintf("%v", this.Error) + `,`,
		`StandardError:` + strings.Replace(fmt.Sprintf("%v", this.StandardError), "StandardErr", "StandardErr", 1) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DescribeRealTimeCCLRulesReq) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DescribeRealTimeCCLRulesReq{`,
		`TaskId:` + fmt.Sprintf("%v", this.TaskId) + `,`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`TaskIds:` + fmt.Sprintf("%v", this.TaskIds) + `,`,
		`ActionType:` + fmt.Sprintf("%v", this.ActionType) + `,`,
		`}`,
	}, "")
	return s
}
func (this *DescribeRealTimeCCLRulesResp) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&DescribeRealTimeCCLRulesResp{`,
		`Consistent:` + fmt.Sprintf("%v", this.Consistent) + `,`,
		`Success:` + fmt.Sprintf("%v", this.Success) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringSqlccl(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *CreateSqlConcurrencyControlRuleReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSqlccl
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateSqlConcurrencyControlRuleReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateSqlConcurrencyControlRuleReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskId", wireType)
			}
			m.TaskId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TaskId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSqlccl(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSqlccl
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActionSuccess) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSqlccl
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ActionSuccess: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ActionSuccess: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipSqlccl(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSqlccl
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteSqlConcurrencyControlRuleReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSqlccl
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteSqlConcurrencyControlRuleReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteSqlConcurrencyControlRuleReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSqlccl
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TaskIds = append(m.TaskIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSqlccl
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthSqlccl
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthSqlccl
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.TaskIds) == 0 {
					m.TaskIds = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowSqlccl
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TaskIds = append(m.TaskIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskIds", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSqlccl(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSqlccl
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StopSqlConcurrencyControlRuleReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSqlccl
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StopSqlConcurrencyControlRuleReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StopSqlConcurrencyControlRuleReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSqlccl
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TaskIds = append(m.TaskIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSqlccl
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthSqlccl
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthSqlccl
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.TaskIds) == 0 {
					m.TaskIds = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowSqlccl
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TaskIds = append(m.TaskIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskIds", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			m.State = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.State |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSqlccl(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSqlccl
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExecuteCCLResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSqlccl
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExecuteCCLResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExecuteCCLResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Result = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSqlccl(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSqlccl
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CCLRuleInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSqlccl
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CCLRuleInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CCLRuleInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Id = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field User", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.User = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Host", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Host = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Schema", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Schema = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Table", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Table = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Keywords", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Keywords = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.State = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ordered", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ordered = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ConcurrencyCount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ConcurrencyCount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WaitTimeout", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.WaitTimeout = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxWaitThreadCount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MaxWaitThreadCount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Matched", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Matched = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Running", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Running = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Waiting", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Waiting = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSqlccl(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSqlccl
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CCLRuleInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSqlccl
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CCLRuleInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CCLRuleInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CCLRule", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CCLRule = append(m.CCLRule, &CCLRuleInfo{})
			if err := m.CCLRule[len(m.CCLRule)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSqlccl(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSqlccl
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CCLRuleActorFailResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSqlccl
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CCLRuleActorFailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CCLRuleActorFailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Error = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StandardError", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.StandardError == nil {
				m.StandardError = &StandardErr{}
			}
			if err := m.StandardError.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSqlccl(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSqlccl
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DescribeRealTimeCCLRulesReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSqlccl
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DescribeRealTimeCCLRulesReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DescribeRealTimeCCLRulesReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskId", wireType)
			}
			m.TaskId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TaskId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSqlccl
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSqlccl
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSqlccl
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TaskIds = append(m.TaskIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowSqlccl
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthSqlccl
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthSqlccl
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.TaskIds) == 0 {
					m.TaskIds = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowSqlccl
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TaskIds = append(m.TaskIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskIds", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ActionType", wireType)
			}
			m.ActionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActionType |= CCLActionType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipSqlccl(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSqlccl
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DescribeRealTimeCCLRulesResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSqlccl
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DescribeRealTimeCCLRulesResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DescribeRealTimeCCLRulesResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Consistent", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Consistent = bool(v != 0)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Success", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Success = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipSqlccl(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSqlccl
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipSqlccl(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowSqlccl
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSqlccl
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthSqlccl
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupSqlccl
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthSqlccl
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthSqlccl        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowSqlccl          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupSqlccl = fmt.Errorf("proto: unexpected end of group")
)
