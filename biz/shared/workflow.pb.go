// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: workflow.proto

package shared

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
	reflect "reflect"
	strconv "strconv"
	strings "strings"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type ExecutedError int32

const (
	ExecutedStart ExecutedError = 0
	ExecutedOK    ExecutedError = 1
	ExecutedFail  ExecutedError = 2
)

var ExecutedError_name = map[int32]string{
	0: "ExecutedStart",
	1: "ExecutedOK",
	2: "ExecutedFail",
}

var ExecutedError_value = map[string]int32{
	"ExecutedStart": 0,
	"ExecutedOK":    1,
	"ExecutedFail":  2,
}

func (ExecutedError) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{0}
}

type RuntimeType int32

const (
	NoneRuntimeType RuntimeType = 0
	K8sRuntimeType  RuntimeType = 1
)

var RuntimeType_name = map[int32]string{
	0: "NoneRuntimeType",
	1: "K8sRuntimeType",
}

var RuntimeType_value = map[string]int32{
	"NoneRuntimeType": 0,
	"K8sRuntimeType":  1,
}

func (RuntimeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{1}
}

type Ticket struct {
	TicketId             int64          `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	FlowConfigId         int64          `protobuf:"varint,2,opt,name=flow_config_id,json=flowConfigId,proto3" json:"flow_config_id,omitempty"`
	WorkflowId           int64          `protobuf:"varint,3,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	TicketType           int32          `protobuf:"varint,4,opt,name=ticket_type,json=ticketType,proto3" json:"ticket_type,omitempty"`
	TicketStatus         int32          `protobuf:"varint,5,opt,name=ticket_status,json=ticketStatus,proto3" json:"ticket_status,omitempty"`
	FlowStep             int32          `protobuf:"varint,6,opt,name=flow_step,json=flowStep,proto3" json:"flow_step,omitempty"`
	ExecuteType          int32          `protobuf:"varint,7,opt,name=execute_type,json=executeType,proto3" json:"execute_type,omitempty"`
	CreateTime           int64          `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           int64          `protobuf:"varint,9,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateUserId         string         `protobuf:"bytes,10,opt,name=create_user_id,json=createUserId,proto3" json:"create_user_id,omitempty"`
	TenantId             string         `protobuf:"bytes,11,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	CurrentUserIds       string         `protobuf:"bytes,12,opt,name=current_user_ids,json=currentUserIds,proto3" json:"current_user_ids,omitempty"`
	InstanceType         DataSourceType `protobuf:"varint,13,opt,name=instance_type,json=instanceType,proto3,enum=shared.DataSourceType" json:"instance_type,omitempty"`
	InstanceId           string         `protobuf:"bytes,14,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
	SqlText              string         `protobuf:"bytes,15,opt,name=sql_text,json=sqlText,proto3" json:"sql_text,omitempty"`
	Description          string         `protobuf:"bytes,16,opt,name=description,proto3" json:"description,omitempty"`
	DbName               string         `protobuf:"bytes,17,opt,name=db_name,json=dbName,proto3" json:"db_name,omitempty"`
	ExecStartTime        int32          `protobuf:"varint,18,opt,name=exec_start_time,json=execStartTime,proto3" json:"exec_start_time,omitempty"`
	ExecEndTime          int32          `protobuf:"varint,19,opt,name=exec_end_time,json=execEndTime,proto3" json:"exec_end_time,omitempty"`
	TaskId               string         `protobuf:"bytes,20,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	BatchSize            int64          `protobuf:"varint,21,opt,name=batch_size,json=batchSize,proto3" json:"batch_size,omitempty"`
	IsReplicaDelayEnable bool           `protobuf:"varint,22,opt,name=is_replica_delay_enable,json=isReplicaDelayEnable,proto3" json:"is_replica_delay_enable,omitempty"`
	ReplicaDelaySeconds  int32          `protobuf:"varint,23,opt,name=replica_delay_seconds,json=replicaDelaySeconds,proto3" json:"replica_delay_seconds,omitempty"`
	SleepTimeMs          int32          `protobuf:"varint,24,opt,name=sleep_time_ms,json=sleepTimeMs,proto3" json:"sleep_time_ms,omitempty"`
	ApprovalFlowId       int64          `protobuf:"varint,25,opt,name=approval_flow_id,json=approvalFlowId,proto3" json:"approval_flow_id,omitempty"`
	ArchiveConfig        string         `protobuf:"bytes,26,opt,name=archiveConfig,proto3" json:"archiveConfig,omitempty"`
	CreateFrom           string         `protobuf:"bytes,27,opt,name=create_from,json=createFrom,proto3" json:"create_from,omitempty"`
	Memo                 string         `protobuf:"bytes,28,opt,name=memo,proto3" json:"memo,omitempty"`
	Title                string         `protobuf:"bytes,29,opt,name=title,proto3" json:"title,omitempty"`
	AffectedRows         string         `protobuf:"bytes,30,opt,name=affected_rows,json=affectedRows,proto3" json:"affected_rows,omitempty"`
	CreateUserName       string         `protobuf:"bytes,31,opt,name=create_user_name,json=createUserName,proto3" json:"create_user_name,omitempty"`
	Submitted            int32          `protobuf:"varint,32,opt,name=submitted,proto3" json:"submitted,omitempty"`
}

func (m *Ticket) Reset()      { *m = Ticket{} }
func (*Ticket) ProtoMessage() {}
func (*Ticket) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{0}
}
func (m *Ticket) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Ticket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Ticket.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Ticket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Ticket.Merge(m, src)
}
func (m *Ticket) XXX_Size() int {
	return m.Size()
}
func (m *Ticket) XXX_DiscardUnknown() {
	xxx_messageInfo_Ticket.DiscardUnknown(m)
}

var xxx_messageInfo_Ticket proto.InternalMessageInfo

func (m *Ticket) GetTicketId() int64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *Ticket) GetFlowConfigId() int64 {
	if m != nil {
		return m.FlowConfigId
	}
	return 0
}

func (m *Ticket) GetWorkflowId() int64 {
	if m != nil {
		return m.WorkflowId
	}
	return 0
}

func (m *Ticket) GetTicketType() int32 {
	if m != nil {
		return m.TicketType
	}
	return 0
}

func (m *Ticket) GetTicketStatus() int32 {
	if m != nil {
		return m.TicketStatus
	}
	return 0
}

func (m *Ticket) GetFlowStep() int32 {
	if m != nil {
		return m.FlowStep
	}
	return 0
}

func (m *Ticket) GetExecuteType() int32 {
	if m != nil {
		return m.ExecuteType
	}
	return 0
}

func (m *Ticket) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *Ticket) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *Ticket) GetCreateUserId() string {
	if m != nil {
		return m.CreateUserId
	}
	return ""
}

func (m *Ticket) GetTenantId() string {
	if m != nil {
		return m.TenantId
	}
	return ""
}

func (m *Ticket) GetCurrentUserIds() string {
	if m != nil {
		return m.CurrentUserIds
	}
	return ""
}

func (m *Ticket) GetInstanceType() DataSourceType {
	if m != nil {
		return m.InstanceType
	}
	return NoneType
}

func (m *Ticket) GetInstanceId() string {
	if m != nil {
		return m.InstanceId
	}
	return ""
}

func (m *Ticket) GetSqlText() string {
	if m != nil {
		return m.SqlText
	}
	return ""
}

func (m *Ticket) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *Ticket) GetDbName() string {
	if m != nil {
		return m.DbName
	}
	return ""
}

func (m *Ticket) GetExecStartTime() int32 {
	if m != nil {
		return m.ExecStartTime
	}
	return 0
}

func (m *Ticket) GetExecEndTime() int32 {
	if m != nil {
		return m.ExecEndTime
	}
	return 0
}

func (m *Ticket) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *Ticket) GetBatchSize() int64 {
	if m != nil {
		return m.BatchSize
	}
	return 0
}

func (m *Ticket) GetIsReplicaDelayEnable() bool {
	if m != nil {
		return m.IsReplicaDelayEnable
	}
	return false
}

func (m *Ticket) GetReplicaDelaySeconds() int32 {
	if m != nil {
		return m.ReplicaDelaySeconds
	}
	return 0
}

func (m *Ticket) GetSleepTimeMs() int32 {
	if m != nil {
		return m.SleepTimeMs
	}
	return 0
}

func (m *Ticket) GetApprovalFlowId() int64 {
	if m != nil {
		return m.ApprovalFlowId
	}
	return 0
}

func (m *Ticket) GetArchiveConfig() string {
	if m != nil {
		return m.ArchiveConfig
	}
	return ""
}

func (m *Ticket) GetCreateFrom() string {
	if m != nil {
		return m.CreateFrom
	}
	return ""
}

func (m *Ticket) GetMemo() string {
	if m != nil {
		return m.Memo
	}
	return ""
}

func (m *Ticket) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Ticket) GetAffectedRows() string {
	if m != nil {
		return m.AffectedRows
	}
	return ""
}

func (m *Ticket) GetCreateUserName() string {
	if m != nil {
		return m.CreateUserName
	}
	return ""
}

func (m *Ticket) GetSubmitted() int32 {
	if m != nil {
		return m.Submitted
	}
	return 0
}

type ExecTicket struct {
	TenantID            string      `protobuf:"bytes,1,opt,name=tenantID,proto3" json:"tenantID,omitempty"`
	UserID              string      `protobuf:"bytes,2,opt,name=userID,proto3" json:"userID,omitempty"`
	Source              *DataSource `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	TicketType          int32       `protobuf:"varint,4,opt,name=ticketType,proto3" json:"ticketType,omitempty"`
	ExecuteType         int32       `protobuf:"varint,5,opt,name=executeType,proto3" json:"executeType,omitempty"`
	ExecutableStartTime int32       `protobuf:"varint,6,opt,name=executableStartTime,proto3" json:"executableStartTime,omitempty"`
	ExecutableEndTime   int32       `protobuf:"varint,7,opt,name=executableEndTime,proto3" json:"executableEndTime,omitempty"`
	BizContext          string      `protobuf:"bytes,8,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
}

func (m *ExecTicket) Reset()      { *m = ExecTicket{} }
func (*ExecTicket) ProtoMessage() {}
func (*ExecTicket) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{1}
}
func (m *ExecTicket) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExecTicket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExecTicket.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExecTicket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExecTicket.Merge(m, src)
}
func (m *ExecTicket) XXX_Size() int {
	return m.Size()
}
func (m *ExecTicket) XXX_DiscardUnknown() {
	xxx_messageInfo_ExecTicket.DiscardUnknown(m)
}

var xxx_messageInfo_ExecTicket proto.InternalMessageInfo

func (m *ExecTicket) GetTenantID() string {
	if m != nil {
		return m.TenantID
	}
	return ""
}

func (m *ExecTicket) GetUserID() string {
	if m != nil {
		return m.UserID
	}
	return ""
}

func (m *ExecTicket) GetSource() *DataSource {
	if m != nil {
		return m.Source
	}
	return nil
}

func (m *ExecTicket) GetTicketType() int32 {
	if m != nil {
		return m.TicketType
	}
	return 0
}

func (m *ExecTicket) GetExecuteType() int32 {
	if m != nil {
		return m.ExecuteType
	}
	return 0
}

func (m *ExecTicket) GetExecutableStartTime() int32 {
	if m != nil {
		return m.ExecutableStartTime
	}
	return 0
}

func (m *ExecTicket) GetExecutableEndTime() int32 {
	if m != nil {
		return m.ExecutableEndTime
	}
	return 0
}

func (m *ExecTicket) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

type StopTicket struct {
	BizContext string `protobuf:"bytes,1,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
	TicketId   int64  `protobuf:"varint,2,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	TaskId     string `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	TenantId   string `protobuf:"bytes,4,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	InstanceId string `protobuf:"bytes,5,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`
}

func (m *StopTicket) Reset()      { *m = StopTicket{} }
func (*StopTicket) ProtoMessage() {}
func (*StopTicket) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{2}
}
func (m *StopTicket) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StopTicket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StopTicket.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StopTicket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopTicket.Merge(m, src)
}
func (m *StopTicket) XXX_Size() int {
	return m.Size()
}
func (m *StopTicket) XXX_DiscardUnknown() {
	xxx_messageInfo_StopTicket.DiscardUnknown(m)
}

var xxx_messageInfo_StopTicket proto.InternalMessageInfo

func (m *StopTicket) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

func (m *StopTicket) GetTicketId() int64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *StopTicket) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *StopTicket) GetTenantId() string {
	if m != nil {
		return m.TenantId
	}
	return ""
}

func (m *StopTicket) GetInstanceId() string {
	if m != nil {
		return m.InstanceId
	}
	return ""
}

type TicketExecuted struct {
	BizContext string        `protobuf:"bytes,1,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
	TicketId   string        `protobuf:"bytes,2,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
	UserId     string        `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Code       ExecutedError `protobuf:"varint,4,opt,name=code,proto3,enum=shared.ExecutedError" json:"code,omitempty"`
	Message    string        `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
}

func (m *TicketExecuted) Reset()      { *m = TicketExecuted{} }
func (*TicketExecuted) ProtoMessage() {}
func (*TicketExecuted) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{3}
}
func (m *TicketExecuted) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TicketExecuted) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TicketExecuted.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TicketExecuted) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketExecuted.Merge(m, src)
}
func (m *TicketExecuted) XXX_Size() int {
	return m.Size()
}
func (m *TicketExecuted) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketExecuted.DiscardUnknown(m)
}

var xxx_messageInfo_TicketExecuted proto.InternalMessageInfo

func (m *TicketExecuted) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

func (m *TicketExecuted) GetTicketId() string {
	if m != nil {
		return m.TicketId
	}
	return ""
}

func (m *TicketExecuted) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *TicketExecuted) GetCode() ExecutedError {
	if m != nil {
		return m.Code
	}
	return ExecutedStart
}

func (m *TicketExecuted) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type StopTicketResp struct {
	TicketId   string `protobuf:"bytes,1,opt,name=TicketId,proto3" json:"TicketId,omitempty"`
	ErrCode    string `protobuf:"bytes,2,opt,name=ErrCode,proto3" json:"ErrCode,omitempty"`
	ErrMessage string `protobuf:"bytes,3,opt,name=ErrMessage,proto3" json:"ErrMessage,omitempty"`
	Status     string `protobuf:"bytes,4,opt,name=Status,proto3" json:"Status,omitempty"`
}

func (m *StopTicketResp) Reset()      { *m = StopTicketResp{} }
func (*StopTicketResp) ProtoMessage() {}
func (*StopTicketResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{4}
}
func (m *StopTicketResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StopTicketResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StopTicketResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StopTicketResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopTicketResp.Merge(m, src)
}
func (m *StopTicketResp) XXX_Size() int {
	return m.Size()
}
func (m *StopTicketResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StopTicketResp.DiscardUnknown(m)
}

var xxx_messageInfo_StopTicketResp proto.InternalMessageInfo

func (m *StopTicketResp) GetTicketId() string {
	if m != nil {
		return m.TicketId
	}
	return ""
}

func (m *StopTicketResp) GetErrCode() string {
	if m != nil {
		return m.ErrCode
	}
	return ""
}

func (m *StopTicketResp) GetErrMessage() string {
	if m != nil {
		return m.ErrMessage
	}
	return ""
}

func (m *StopTicketResp) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

type ExecFreeLockDMLTicket struct {
	BizContext          string      `protobuf:"bytes,1,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
	TenantID            string      `protobuf:"bytes,2,opt,name=tenantID,proto3" json:"tenantID,omitempty"`
	UserID              string      `protobuf:"bytes,3,opt,name=userID,proto3" json:"userID,omitempty"`
	Source              *DataSource `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
	TicketType          int32       `protobuf:"varint,5,opt,name=ticketType,proto3" json:"ticketType,omitempty"`
	ExecuteType         int32       `protobuf:"varint,6,opt,name=executeType,proto3" json:"executeType,omitempty"`
	SqlText             string      `protobuf:"bytes,7,opt,name=sqlText,proto3" json:"sqlText,omitempty"`
	ExecutableStartTime int32       `protobuf:"varint,8,opt,name=executableStartTime,proto3" json:"executableStartTime,omitempty"`
	ExecutableEndTime   int32       `protobuf:"varint,9,opt,name=executableEndTime,proto3" json:"executableEndTime,omitempty"`
	CreateFrom          string      `protobuf:"bytes,10,opt,name=create_from,json=createFrom,proto3" json:"create_from,omitempty"`
	Hint                string      `protobuf:"bytes,11,opt,name=hint,proto3" json:"hint,omitempty"`
}

func (m *ExecFreeLockDMLTicket) Reset()      { *m = ExecFreeLockDMLTicket{} }
func (*ExecFreeLockDMLTicket) ProtoMessage() {}
func (*ExecFreeLockDMLTicket) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{5}
}
func (m *ExecFreeLockDMLTicket) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExecFreeLockDMLTicket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExecFreeLockDMLTicket.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExecFreeLockDMLTicket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExecFreeLockDMLTicket.Merge(m, src)
}
func (m *ExecFreeLockDMLTicket) XXX_Size() int {
	return m.Size()
}
func (m *ExecFreeLockDMLTicket) XXX_DiscardUnknown() {
	xxx_messageInfo_ExecFreeLockDMLTicket.DiscardUnknown(m)
}

var xxx_messageInfo_ExecFreeLockDMLTicket proto.InternalMessageInfo

func (m *ExecFreeLockDMLTicket) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

func (m *ExecFreeLockDMLTicket) GetTenantID() string {
	if m != nil {
		return m.TenantID
	}
	return ""
}

func (m *ExecFreeLockDMLTicket) GetUserID() string {
	if m != nil {
		return m.UserID
	}
	return ""
}

func (m *ExecFreeLockDMLTicket) GetSource() *DataSource {
	if m != nil {
		return m.Source
	}
	return nil
}

func (m *ExecFreeLockDMLTicket) GetTicketType() int32 {
	if m != nil {
		return m.TicketType
	}
	return 0
}

func (m *ExecFreeLockDMLTicket) GetExecuteType() int32 {
	if m != nil {
		return m.ExecuteType
	}
	return 0
}

func (m *ExecFreeLockDMLTicket) GetSqlText() string {
	if m != nil {
		return m.SqlText
	}
	return ""
}

func (m *ExecFreeLockDMLTicket) GetExecutableStartTime() int32 {
	if m != nil {
		return m.ExecutableStartTime
	}
	return 0
}

func (m *ExecFreeLockDMLTicket) GetExecutableEndTime() int32 {
	if m != nil {
		return m.ExecutableEndTime
	}
	return 0
}

func (m *ExecFreeLockDMLTicket) GetCreateFrom() string {
	if m != nil {
		return m.CreateFrom
	}
	return ""
}

func (m *ExecFreeLockDMLTicket) GetHint() string {
	if m != nil {
		return m.Hint
	}
	return ""
}

type ExecShardingFreeLockDMLTicket struct {
	BizContext string      `protobuf:"bytes,1,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
	Source     *DataSource `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	SqlText    string      `protobuf:"bytes,3,opt,name=sqlText,proto3" json:"sqlText,omitempty"`
	TableName  string      `protobuf:"bytes,4,opt,name=tableName,proto3" json:"tableName,omitempty"`
}

func (m *ExecShardingFreeLockDMLTicket) Reset()      { *m = ExecShardingFreeLockDMLTicket{} }
func (*ExecShardingFreeLockDMLTicket) ProtoMessage() {}
func (*ExecShardingFreeLockDMLTicket) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{6}
}
func (m *ExecShardingFreeLockDMLTicket) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExecShardingFreeLockDMLTicket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExecShardingFreeLockDMLTicket.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExecShardingFreeLockDMLTicket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExecShardingFreeLockDMLTicket.Merge(m, src)
}
func (m *ExecShardingFreeLockDMLTicket) XXX_Size() int {
	return m.Size()
}
func (m *ExecShardingFreeLockDMLTicket) XXX_DiscardUnknown() {
	xxx_messageInfo_ExecShardingFreeLockDMLTicket.DiscardUnknown(m)
}

var xxx_messageInfo_ExecShardingFreeLockDMLTicket proto.InternalMessageInfo

func (m *ExecShardingFreeLockDMLTicket) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

func (m *ExecShardingFreeLockDMLTicket) GetSource() *DataSource {
	if m != nil {
		return m.Source
	}
	return nil
}

func (m *ExecShardingFreeLockDMLTicket) GetSqlText() string {
	if m != nil {
		return m.SqlText
	}
	return ""
}

func (m *ExecShardingFreeLockDMLTicket) GetTableName() string {
	if m != nil {
		return m.TableName
	}
	return ""
}

type ExplainCommandReq struct {
	DB      string `protobuf:"bytes,1,opt,name=DB,proto3" json:"DB,omitempty"`
	SQLText string `protobuf:"bytes,2,opt,name=SQLText,proto3" json:"SQLText,omitempty"`
}

func (m *ExplainCommandReq) Reset()      { *m = ExplainCommandReq{} }
func (*ExplainCommandReq) ProtoMessage() {}
func (*ExplainCommandReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{7}
}
func (m *ExplainCommandReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExplainCommandReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExplainCommandReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExplainCommandReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExplainCommandReq.Merge(m, src)
}
func (m *ExplainCommandReq) XXX_Size() int {
	return m.Size()
}
func (m *ExplainCommandReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExplainCommandReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExplainCommandReq proto.InternalMessageInfo

func (m *ExplainCommandReq) GetDB() string {
	if m != nil {
		return m.DB
	}
	return ""
}

func (m *ExplainCommandReq) GetSQLText() string {
	if m != nil {
		return m.SQLText
	}
	return ""
}

type ExplainCommandResp struct {
	CommandRes []*ExplainCommandResult `protobuf:"bytes,1,rep,name=CommandRes,proto3" json:"CommandRes,omitempty"`
}

func (m *ExplainCommandResp) Reset()      { *m = ExplainCommandResp{} }
func (*ExplainCommandResp) ProtoMessage() {}
func (*ExplainCommandResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{8}
}
func (m *ExplainCommandResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExplainCommandResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExplainCommandResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExplainCommandResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExplainCommandResp.Merge(m, src)
}
func (m *ExplainCommandResp) XXX_Size() int {
	return m.Size()
}
func (m *ExplainCommandResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExplainCommandResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExplainCommandResp proto.InternalMessageInfo

func (m *ExplainCommandResp) GetCommandRes() []*ExplainCommandResult {
	if m != nil {
		return m.CommandRes
	}
	return nil
}

type ExplainCommandResult struct {
	Id           int32  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`
	SelectType   string `protobuf:"bytes,2,opt,name=SelectType,proto3" json:"SelectType,omitempty"`
	Table        string `protobuf:"bytes,3,opt,name=Table,proto3" json:"Table,omitempty"`
	Partitions   string `protobuf:"bytes,4,opt,name=Partitions,proto3" json:"Partitions,omitempty"`
	Type         string `protobuf:"bytes,5,opt,name=Type,proto3" json:"Type,omitempty"`
	PossibleKeys string `protobuf:"bytes,6,opt,name=PossibleKeys,proto3" json:"PossibleKeys,omitempty"`
	Key          string `protobuf:"bytes,7,opt,name=Key,proto3" json:"Key,omitempty"`
	KeyLen       string `protobuf:"bytes,8,opt,name=KeyLen,proto3" json:"KeyLen,omitempty"`
	Ref          string `protobuf:"bytes,9,opt,name=Ref,proto3" json:"Ref,omitempty"`
	Rows         string `protobuf:"bytes,10,opt,name=Rows,proto3" json:"Rows,omitempty"`
	Filtered     string `protobuf:"bytes,11,opt,name=Filtered,proto3" json:"Filtered,omitempty"`
	Extra        string `protobuf:"bytes,12,opt,name=Extra,proto3" json:"Extra,omitempty"`
}

func (m *ExplainCommandResult) Reset()      { *m = ExplainCommandResult{} }
func (*ExplainCommandResult) ProtoMessage() {}
func (*ExplainCommandResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{9}
}
func (m *ExplainCommandResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExplainCommandResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExplainCommandResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExplainCommandResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExplainCommandResult.Merge(m, src)
}
func (m *ExplainCommandResult) XXX_Size() int {
	return m.Size()
}
func (m *ExplainCommandResult) XXX_DiscardUnknown() {
	xxx_messageInfo_ExplainCommandResult.DiscardUnknown(m)
}

var xxx_messageInfo_ExplainCommandResult proto.InternalMessageInfo

func (m *ExplainCommandResult) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ExplainCommandResult) GetSelectType() string {
	if m != nil {
		return m.SelectType
	}
	return ""
}

func (m *ExplainCommandResult) GetTable() string {
	if m != nil {
		return m.Table
	}
	return ""
}

func (m *ExplainCommandResult) GetPartitions() string {
	if m != nil {
		return m.Partitions
	}
	return ""
}

func (m *ExplainCommandResult) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *ExplainCommandResult) GetPossibleKeys() string {
	if m != nil {
		return m.PossibleKeys
	}
	return ""
}

func (m *ExplainCommandResult) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *ExplainCommandResult) GetKeyLen() string {
	if m != nil {
		return m.KeyLen
	}
	return ""
}

func (m *ExplainCommandResult) GetRef() string {
	if m != nil {
		return m.Ref
	}
	return ""
}

func (m *ExplainCommandResult) GetRows() string {
	if m != nil {
		return m.Rows
	}
	return ""
}

func (m *ExplainCommandResult) GetFiltered() string {
	if m != nil {
		return m.Filtered
	}
	return ""
}

func (m *ExplainCommandResult) GetExtra() string {
	if m != nil {
		return m.Extra
	}
	return ""
}

type GetIndexInfoReq struct {
	TableName string `protobuf:"bytes,1,opt,name=TableName,proto3" json:"TableName,omitempty"`
	DB        string `protobuf:"bytes,2,opt,name=DB,proto3" json:"DB,omitempty"`
	Command   string `protobuf:"bytes,3,opt,name=Command,proto3" json:"Command,omitempty"`
}

func (m *GetIndexInfoReq) Reset()      { *m = GetIndexInfoReq{} }
func (*GetIndexInfoReq) ProtoMessage() {}
func (*GetIndexInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{10}
}
func (m *GetIndexInfoReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetIndexInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetIndexInfoReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetIndexInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIndexInfoReq.Merge(m, src)
}
func (m *GetIndexInfoReq) XXX_Size() int {
	return m.Size()
}
func (m *GetIndexInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIndexInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetIndexInfoReq proto.InternalMessageInfo

func (m *GetIndexInfoReq) GetTableName() string {
	if m != nil {
		return m.TableName
	}
	return ""
}

func (m *GetIndexInfoReq) GetDB() string {
	if m != nil {
		return m.DB
	}
	return ""
}

func (m *GetIndexInfoReq) GetCommand() string {
	if m != nil {
		return m.Command
	}
	return ""
}

type GetIndexInfoResp struct {
	TableIndexInfo []*TableIndexInfo `protobuf:"bytes,1,rep,name=TableIndexInfo,proto3" json:"TableIndexInfo,omitempty"`
}

func (m *GetIndexInfoResp) Reset()      { *m = GetIndexInfoResp{} }
func (*GetIndexInfoResp) ProtoMessage() {}
func (*GetIndexInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{11}
}
func (m *GetIndexInfoResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetIndexInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetIndexInfoResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetIndexInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIndexInfoResp.Merge(m, src)
}
func (m *GetIndexInfoResp) XXX_Size() int {
	return m.Size()
}
func (m *GetIndexInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIndexInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetIndexInfoResp proto.InternalMessageInfo

func (m *GetIndexInfoResp) GetTableIndexInfo() []*TableIndexInfo {
	if m != nil {
		return m.TableIndexInfo
	}
	return nil
}

type TableIndexInfo struct {
	TableName    string `protobuf:"bytes,1,opt,name=TableName,proto3" json:"TableName,omitempty"`
	IndexName    string `protobuf:"bytes,2,opt,name=IndexName,proto3" json:"IndexName,omitempty"`
	Nullable     string `protobuf:"bytes,3,opt,name=Nullable,proto3" json:"Nullable,omitempty"`
	SeqInIndex   int32  `protobuf:"varint,4,opt,name=SeqInIndex,proto3" json:"SeqInIndex,omitempty"`
	IndexType    string `protobuf:"bytes,5,opt,name=IndexType,proto3" json:"IndexType,omitempty"`
	ColumnName   string `protobuf:"bytes,6,opt,name=ColumnName,proto3" json:"ColumnName,omitempty"`
	SubPart      string `protobuf:"bytes,7,opt,name=SubPart,proto3" json:"SubPart,omitempty"`
	IndexComment string `protobuf:"bytes,8,opt,name=IndexComment,proto3" json:"IndexComment,omitempty"`
}

func (m *TableIndexInfo) Reset()      { *m = TableIndexInfo{} }
func (*TableIndexInfo) ProtoMessage() {}
func (*TableIndexInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{12}
}
func (m *TableIndexInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TableIndexInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TableIndexInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TableIndexInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TableIndexInfo.Merge(m, src)
}
func (m *TableIndexInfo) XXX_Size() int {
	return m.Size()
}
func (m *TableIndexInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TableIndexInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TableIndexInfo proto.InternalMessageInfo

func (m *TableIndexInfo) GetTableName() string {
	if m != nil {
		return m.TableName
	}
	return ""
}

func (m *TableIndexInfo) GetIndexName() string {
	if m != nil {
		return m.IndexName
	}
	return ""
}

func (m *TableIndexInfo) GetNullable() string {
	if m != nil {
		return m.Nullable
	}
	return ""
}

func (m *TableIndexInfo) GetSeqInIndex() int32 {
	if m != nil {
		return m.SeqInIndex
	}
	return 0
}

func (m *TableIndexInfo) GetIndexType() string {
	if m != nil {
		return m.IndexType
	}
	return ""
}

func (m *TableIndexInfo) GetColumnName() string {
	if m != nil {
		return m.ColumnName
	}
	return ""
}

func (m *TableIndexInfo) GetSubPart() string {
	if m != nil {
		return m.SubPart
	}
	return ""
}

func (m *TableIndexInfo) GetIndexComment() string {
	if m != nil {
		return m.IndexComment
	}
	return ""
}

type GetIndexValueReq struct {
	TableName string      `protobuf:"bytes,1,opt,name=TableName,proto3" json:"TableName,omitempty"`
	Source    *DataSource `protobuf:"bytes,2,opt,name=Source,proto3" json:"Source,omitempty"`
	Command   string      `protobuf:"bytes,3,opt,name=Command,proto3" json:"Command,omitempty"`
	Columns   []string    `protobuf:"bytes,4,rep,name=Columns,proto3" json:"Columns,omitempty"`
}

func (m *GetIndexValueReq) Reset()      { *m = GetIndexValueReq{} }
func (*GetIndexValueReq) ProtoMessage() {}
func (*GetIndexValueReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{13}
}
func (m *GetIndexValueReq) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetIndexValueReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetIndexValueReq.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetIndexValueReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIndexValueReq.Merge(m, src)
}
func (m *GetIndexValueReq) XXX_Size() int {
	return m.Size()
}
func (m *GetIndexValueReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIndexValueReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetIndexValueReq proto.InternalMessageInfo

func (m *GetIndexValueReq) GetTableName() string {
	if m != nil {
		return m.TableName
	}
	return ""
}

func (m *GetIndexValueReq) GetSource() *DataSource {
	if m != nil {
		return m.Source
	}
	return nil
}

func (m *GetIndexValueReq) GetCommand() string {
	if m != nil {
		return m.Command
	}
	return ""
}

func (m *GetIndexValueReq) GetColumns() []string {
	if m != nil {
		return m.Columns
	}
	return nil
}

type GetIndexValueResp struct {
	TableIndexValue []*TableIndexValue `protobuf:"bytes,1,rep,name=TableIndexValue,proto3" json:"TableIndexValue,omitempty"`
}

func (m *GetIndexValueResp) Reset()      { *m = GetIndexValueResp{} }
func (*GetIndexValueResp) ProtoMessage() {}
func (*GetIndexValueResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{14}
}
func (m *GetIndexValueResp) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetIndexValueResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetIndexValueResp.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetIndexValueResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetIndexValueResp.Merge(m, src)
}
func (m *GetIndexValueResp) XXX_Size() int {
	return m.Size()
}
func (m *GetIndexValueResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetIndexValueResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetIndexValueResp proto.InternalMessageInfo

func (m *GetIndexValueResp) GetTableIndexValue() []*TableIndexValue {
	if m != nil {
		return m.TableIndexValue
	}
	return nil
}

type TableIndexValue struct {
	IndexName  string `protobuf:"bytes,1,opt,name=IndexName,proto3" json:"IndexName,omitempty"`
	IndexValue string `protobuf:"bytes,2,opt,name=IndexValue,proto3" json:"IndexValue,omitempty"`
}

func (m *TableIndexValue) Reset()      { *m = TableIndexValue{} }
func (*TableIndexValue) ProtoMessage() {}
func (*TableIndexValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{15}
}
func (m *TableIndexValue) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TableIndexValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TableIndexValue.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TableIndexValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TableIndexValue.Merge(m, src)
}
func (m *TableIndexValue) XXX_Size() int {
	return m.Size()
}
func (m *TableIndexValue) XXX_DiscardUnknown() {
	xxx_messageInfo_TableIndexValue.DiscardUnknown(m)
}

var xxx_messageInfo_TableIndexValue proto.InternalMessageInfo

func (m *TableIndexValue) GetIndexName() string {
	if m != nil {
		return m.IndexName
	}
	return ""
}

func (m *TableIndexValue) GetIndexValue() string {
	if m != nil {
		return m.IndexValue
	}
	return ""
}

type ExecVeDBDDLTicket struct {
	BizContext          string      `protobuf:"bytes,1,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
	TenantID            string      `protobuf:"bytes,2,opt,name=tenantID,proto3" json:"tenantID,omitempty"`
	UserID              string      `protobuf:"bytes,3,opt,name=userID,proto3" json:"userID,omitempty"`
	Source              *DataSource `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
	TicketType          int32       `protobuf:"varint,5,opt,name=ticketType,proto3" json:"ticketType,omitempty"`
	ExecuteType         int32       `protobuf:"varint,6,opt,name=executeType,proto3" json:"executeType,omitempty"`
	SqlText             string      `protobuf:"bytes,7,opt,name=sqlText,proto3" json:"sqlText,omitempty"`
	ExecutableStartTime int32       `protobuf:"varint,8,opt,name=executableStartTime,proto3" json:"executableStartTime,omitempty"`
	ExecutableEndTime   int32       `protobuf:"varint,9,opt,name=executableEndTime,proto3" json:"executableEndTime,omitempty"`
}

func (m *ExecVeDBDDLTicket) Reset()      { *m = ExecVeDBDDLTicket{} }
func (*ExecVeDBDDLTicket) ProtoMessage() {}
func (*ExecVeDBDDLTicket) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{16}
}
func (m *ExecVeDBDDLTicket) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExecVeDBDDLTicket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExecVeDBDDLTicket.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExecVeDBDDLTicket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExecVeDBDDLTicket.Merge(m, src)
}
func (m *ExecVeDBDDLTicket) XXX_Size() int {
	return m.Size()
}
func (m *ExecVeDBDDLTicket) XXX_DiscardUnknown() {
	xxx_messageInfo_ExecVeDBDDLTicket.DiscardUnknown(m)
}

var xxx_messageInfo_ExecVeDBDDLTicket proto.InternalMessageInfo

func (m *ExecVeDBDDLTicket) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

func (m *ExecVeDBDDLTicket) GetTenantID() string {
	if m != nil {
		return m.TenantID
	}
	return ""
}

func (m *ExecVeDBDDLTicket) GetUserID() string {
	if m != nil {
		return m.UserID
	}
	return ""
}

func (m *ExecVeDBDDLTicket) GetSource() *DataSource {
	if m != nil {
		return m.Source
	}
	return nil
}

func (m *ExecVeDBDDLTicket) GetTicketType() int32 {
	if m != nil {
		return m.TicketType
	}
	return 0
}

func (m *ExecVeDBDDLTicket) GetExecuteType() int32 {
	if m != nil {
		return m.ExecuteType
	}
	return 0
}

func (m *ExecVeDBDDLTicket) GetSqlText() string {
	if m != nil {
		return m.SqlText
	}
	return ""
}

func (m *ExecVeDBDDLTicket) GetExecutableStartTime() int32 {
	if m != nil {
		return m.ExecutableStartTime
	}
	return 0
}

func (m *ExecVeDBDDLTicket) GetExecutableEndTime() int32 {
	if m != nil {
		return m.ExecutableEndTime
	}
	return 0
}

type CreateShardingSubTicket struct {
	BizContext string `protobuf:"bytes,1,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
}

func (m *CreateShardingSubTicket) Reset()      { *m = CreateShardingSubTicket{} }
func (*CreateShardingSubTicket) ProtoMessage() {}
func (*CreateShardingSubTicket) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{17}
}
func (m *CreateShardingSubTicket) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CreateShardingSubTicket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CreateShardingSubTicket.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CreateShardingSubTicket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateShardingSubTicket.Merge(m, src)
}
func (m *CreateShardingSubTicket) XXX_Size() int {
	return m.Size()
}
func (m *CreateShardingSubTicket) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateShardingSubTicket.DiscardUnknown(m)
}

var xxx_messageInfo_CreateShardingSubTicket proto.InternalMessageInfo

func (m *CreateShardingSubTicket) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

type ExecDataCleanTicket struct {
	BizContext          string      `protobuf:"bytes,1,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
	TenantID            string      `protobuf:"bytes,2,opt,name=tenantID,proto3" json:"tenantID,omitempty"`
	UserID              string      `protobuf:"bytes,3,opt,name=userID,proto3" json:"userID,omitempty"`
	Source              *DataSource `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
	TicketType          int32       `protobuf:"varint,5,opt,name=ticketType,proto3" json:"ticketType,omitempty"`
	ExecuteType         int32       `protobuf:"varint,6,opt,name=executeType,proto3" json:"executeType,omitempty"`
	SqlText             string      `protobuf:"bytes,7,opt,name=sqlText,proto3" json:"sqlText,omitempty"`
	ExecutableStartTime int32       `protobuf:"varint,8,opt,name=executableStartTime,proto3" json:"executableStartTime,omitempty"`
	ExecutableEndTime   int32       `protobuf:"varint,9,opt,name=executableEndTime,proto3" json:"executableEndTime,omitempty"`
}

func (m *ExecDataCleanTicket) Reset()      { *m = ExecDataCleanTicket{} }
func (*ExecDataCleanTicket) ProtoMessage() {}
func (*ExecDataCleanTicket) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{18}
}
func (m *ExecDataCleanTicket) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExecDataCleanTicket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExecDataCleanTicket.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExecDataCleanTicket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExecDataCleanTicket.Merge(m, src)
}
func (m *ExecDataCleanTicket) XXX_Size() int {
	return m.Size()
}
func (m *ExecDataCleanTicket) XXX_DiscardUnknown() {
	xxx_messageInfo_ExecDataCleanTicket.DiscardUnknown(m)
}

var xxx_messageInfo_ExecDataCleanTicket proto.InternalMessageInfo

func (m *ExecDataCleanTicket) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

func (m *ExecDataCleanTicket) GetTenantID() string {
	if m != nil {
		return m.TenantID
	}
	return ""
}

func (m *ExecDataCleanTicket) GetUserID() string {
	if m != nil {
		return m.UserID
	}
	return ""
}

func (m *ExecDataCleanTicket) GetSource() *DataSource {
	if m != nil {
		return m.Source
	}
	return nil
}

func (m *ExecDataCleanTicket) GetTicketType() int32 {
	if m != nil {
		return m.TicketType
	}
	return 0
}

func (m *ExecDataCleanTicket) GetExecuteType() int32 {
	if m != nil {
		return m.ExecuteType
	}
	return 0
}

func (m *ExecDataCleanTicket) GetSqlText() string {
	if m != nil {
		return m.SqlText
	}
	return ""
}

func (m *ExecDataCleanTicket) GetExecutableStartTime() int32 {
	if m != nil {
		return m.ExecutableStartTime
	}
	return 0
}

func (m *ExecDataCleanTicket) GetExecutableEndTime() int32 {
	if m != nil {
		return m.ExecutableEndTime
	}
	return 0
}

type ExecShardingFreeLockDDLTicket struct {
	BizContext          string      `protobuf:"bytes,1,opt,name=biz_context,json=bizContext,proto3" json:"biz_context,omitempty"`
	TenantID            string      `protobuf:"bytes,2,opt,name=tenantID,proto3" json:"tenantID,omitempty"`
	UserID              string      `protobuf:"bytes,3,opt,name=userID,proto3" json:"userID,omitempty"`
	Source              *DataSource `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
	TicketType          int32       `protobuf:"varint,5,opt,name=ticketType,proto3" json:"ticketType,omitempty"`
	ExecuteType         int32       `protobuf:"varint,6,opt,name=executeType,proto3" json:"executeType,omitempty"`
	SqlText             string      `protobuf:"bytes,7,opt,name=sqlText,proto3" json:"sqlText,omitempty"`
	ExecutableStartTime int32       `protobuf:"varint,8,opt,name=executableStartTime,proto3" json:"executableStartTime,omitempty"`
	ExecutableEndTime   int32       `protobuf:"varint,9,opt,name=executableEndTime,proto3" json:"executableEndTime,omitempty"`
}

func (m *ExecShardingFreeLockDDLTicket) Reset()      { *m = ExecShardingFreeLockDDLTicket{} }
func (*ExecShardingFreeLockDDLTicket) ProtoMessage() {}
func (*ExecShardingFreeLockDDLTicket) Descriptor() ([]byte, []int) {
	return fileDescriptor_892c7f566756b0be, []int{19}
}
func (m *ExecShardingFreeLockDDLTicket) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ExecShardingFreeLockDDLTicket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ExecShardingFreeLockDDLTicket.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ExecShardingFreeLockDDLTicket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExecShardingFreeLockDDLTicket.Merge(m, src)
}
func (m *ExecShardingFreeLockDDLTicket) XXX_Size() int {
	return m.Size()
}
func (m *ExecShardingFreeLockDDLTicket) XXX_DiscardUnknown() {
	xxx_messageInfo_ExecShardingFreeLockDDLTicket.DiscardUnknown(m)
}

var xxx_messageInfo_ExecShardingFreeLockDDLTicket proto.InternalMessageInfo

func (m *ExecShardingFreeLockDDLTicket) GetBizContext() string {
	if m != nil {
		return m.BizContext
	}
	return ""
}

func (m *ExecShardingFreeLockDDLTicket) GetTenantID() string {
	if m != nil {
		return m.TenantID
	}
	return ""
}

func (m *ExecShardingFreeLockDDLTicket) GetUserID() string {
	if m != nil {
		return m.UserID
	}
	return ""
}

func (m *ExecShardingFreeLockDDLTicket) GetSource() *DataSource {
	if m != nil {
		return m.Source
	}
	return nil
}

func (m *ExecShardingFreeLockDDLTicket) GetTicketType() int32 {
	if m != nil {
		return m.TicketType
	}
	return 0
}

func (m *ExecShardingFreeLockDDLTicket) GetExecuteType() int32 {
	if m != nil {
		return m.ExecuteType
	}
	return 0
}

func (m *ExecShardingFreeLockDDLTicket) GetSqlText() string {
	if m != nil {
		return m.SqlText
	}
	return ""
}

func (m *ExecShardingFreeLockDDLTicket) GetExecutableStartTime() int32 {
	if m != nil {
		return m.ExecutableStartTime
	}
	return 0
}

func (m *ExecShardingFreeLockDDLTicket) GetExecutableEndTime() int32 {
	if m != nil {
		return m.ExecutableEndTime
	}
	return 0
}

func init() {
	proto.RegisterEnum("shared.ExecutedError", ExecutedError_name, ExecutedError_value)
	proto.RegisterEnum("shared.RuntimeType", RuntimeType_name, RuntimeType_value)
	proto.RegisterType((*Ticket)(nil), "shared.Ticket")
	proto.RegisterType((*ExecTicket)(nil), "shared.ExecTicket")
	proto.RegisterType((*StopTicket)(nil), "shared.StopTicket")
	proto.RegisterType((*TicketExecuted)(nil), "shared.TicketExecuted")
	proto.RegisterType((*StopTicketResp)(nil), "shared.StopTicketResp")
	proto.RegisterType((*ExecFreeLockDMLTicket)(nil), "shared.ExecFreeLockDMLTicket")
	proto.RegisterType((*ExecShardingFreeLockDMLTicket)(nil), "shared.ExecShardingFreeLockDMLTicket")
	proto.RegisterType((*ExplainCommandReq)(nil), "shared.ExplainCommandReq")
	proto.RegisterType((*ExplainCommandResp)(nil), "shared.ExplainCommandResp")
	proto.RegisterType((*ExplainCommandResult)(nil), "shared.ExplainCommandResult")
	proto.RegisterType((*GetIndexInfoReq)(nil), "shared.GetIndexInfoReq")
	proto.RegisterType((*GetIndexInfoResp)(nil), "shared.GetIndexInfoResp")
	proto.RegisterType((*TableIndexInfo)(nil), "shared.TableIndexInfo")
	proto.RegisterType((*GetIndexValueReq)(nil), "shared.GetIndexValueReq")
	proto.RegisterType((*GetIndexValueResp)(nil), "shared.GetIndexValueResp")
	proto.RegisterType((*TableIndexValue)(nil), "shared.TableIndexValue")
	proto.RegisterType((*ExecVeDBDDLTicket)(nil), "shared.ExecVeDBDDLTicket")
	proto.RegisterType((*CreateShardingSubTicket)(nil), "shared.CreateShardingSubTicket")
	proto.RegisterType((*ExecDataCleanTicket)(nil), "shared.ExecDataCleanTicket")
	proto.RegisterType((*ExecShardingFreeLockDDLTicket)(nil), "shared.ExecShardingFreeLockDDLTicket")
}

func init() { proto.RegisterFile("workflow.proto", fileDescriptor_892c7f566756b0be) }

var fileDescriptor_892c7f566756b0be = []byte{
	// 1638 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x58, 0x4b, 0x6f, 0xdb, 0xc6,
	0x13, 0x17, 0x25, 0x5b, 0x96, 0x46, 0xb2, 0x2c, 0xaf, 0xed, 0x98, 0x49, 0x1c, 0x46, 0x7f, 0xfe,
	0x83, 0xc2, 0x35, 0x8a, 0xa0, 0x70, 0x1f, 0x28, 0xfa, 0x02, 0x6a, 0x4b, 0x2e, 0x04, 0x3b, 0x8f,
	0x52, 0x6e, 0x80, 0x9e, 0x04, 0x8a, 0x5c, 0xc7, 0x84, 0x29, 0x92, 0xe6, 0xae, 0xe2, 0xc7, 0xa1,
	0xe8, 0xa1, 0x97, 0xde, 0xf2, 0x15, 0x0a, 0xf4, 0xd0, 0x43, 0x81, 0xde, 0xfa, 0x19, 0x7a, 0x34,
	0x7a, 0xca, 0xa1, 0x87, 0xc6, 0xb9, 0xf4, 0x98, 0x8f, 0x50, 0xec, 0xec, 0x52, 0xa4, 0x64, 0xbb,
	0xce, 0xa3, 0xc7, 0xdc, 0x38, 0xbf, 0xf9, 0x71, 0x77, 0x76, 0x7e, 0x33, 0xb3, 0x94, 0xa0, 0x76,
	0x10, 0xc6, 0x7b, 0x3b, 0x7e, 0x78, 0x70, 0x3b, 0x8a, 0x43, 0x1e, 0x92, 0x22, 0xdb, 0xb5, 0x63,
	0xea, 0x5e, 0x9b, 0x75, 0xc2, 0x20, 0xa0, 0x0e, 0xf7, 0xc2, 0x80, 0x49, 0x97, 0xf9, 0xbc, 0x04,
	0xc5, 0x6d, 0xcf, 0xd9, 0xa3, 0x9c, 0x5c, 0x87, 0x32, 0xc7, 0xa7, 0xae, 0xe7, 0xea, 0x5a, 0x43,
	0x5b, 0x2e, 0x58, 0x25, 0x09, 0xb4, 0x5d, 0x72, 0x0b, 0x6a, 0x62, 0xc1, 0xae, 0x13, 0x06, 0x3b,
	0xde, 0x43, 0xc1, 0xc8, 0x23, 0xa3, 0x2a, 0xd0, 0x75, 0x04, 0xdb, 0x2e, 0xb9, 0x09, 0x95, 0x64,
	0x6b, 0x41, 0x29, 0x20, 0x05, 0x12, 0x48, 0x12, 0xd4, 0x1e, 0xfc, 0x28, 0xa2, 0xfa, 0x44, 0x43,
	0x5b, 0x9e, 0xb4, 0x40, 0x42, 0xdb, 0x47, 0x11, 0x25, 0xff, 0x87, 0x69, 0x45, 0x60, 0xdc, 0xe6,
	0x03, 0xa6, 0x4f, 0x22, 0xa5, 0x2a, 0xc1, 0x0e, 0x62, 0x22, 0x52, 0xdc, 0x82, 0x71, 0x1a, 0xe9,
	0x45, 0x24, 0x94, 0x04, 0xd0, 0xe1, 0x34, 0x22, 0xff, 0x83, 0x2a, 0x3d, 0xa4, 0xce, 0x80, 0x53,
	0xb9, 0xc7, 0x14, 0xfa, 0x2b, 0x0a, 0xc3, 0x4d, 0x6e, 0x42, 0xc5, 0x89, 0xa9, 0x2d, 0x18, 0x5e,
	0x9f, 0xea, 0x25, 0x19, 0xa6, 0x84, 0xb6, 0xbd, 0x3e, 0x12, 0x06, 0x91, 0x3b, 0x24, 0x94, 0x25,
	0x41, 0x42, 0x48, 0xb8, 0x05, 0x35, 0xb5, 0xc2, 0x80, 0xd1, 0x58, 0x9c, 0x15, 0x1a, 0xda, 0x72,
	0xd9, 0xaa, 0x4a, 0xf4, 0x6b, 0x46, 0xe3, 0xb6, 0x8b, 0x19, 0xa5, 0x81, 0x1d, 0x60, 0x46, 0x2b,
	0x48, 0x28, 0x49, 0xa0, 0xed, 0x92, 0x65, 0xa8, 0x3b, 0x83, 0x38, 0xa6, 0x01, 0x4f, 0xd6, 0x60,
	0x7a, 0x15, 0x39, 0x35, 0x85, 0xcb, 0x55, 0x18, 0xf9, 0x04, 0xa6, 0xbd, 0x80, 0x71, 0x3b, 0x70,
	0xd4, 0x91, 0xa6, 0x1b, 0xda, 0x72, 0x6d, 0xf5, 0xca, 0x6d, 0x29, 0xeb, 0xed, 0xa6, 0xcd, 0xed,
	0x4e, 0x38, 0x88, 0x1d, 0x3c, 0x9d, 0x55, 0x4d, 0xc8, 0xc9, 0x59, 0x87, 0x2f, 0x7b, 0xae, 0x5e,
	0xc3, 0x1d, 0x20, 0x81, 0xda, 0x2e, 0xb9, 0x0a, 0x25, 0xb6, 0xef, 0x77, 0x39, 0x3d, 0xe4, 0xfa,
	0x0c, 0x7a, 0xa7, 0xd8, 0xbe, 0xbf, 0x4d, 0x0f, 0x39, 0x69, 0x40, 0xc5, 0xa5, 0xcc, 0x89, 0xbd,
	0x48, 0x94, 0x8c, 0x5e, 0x47, 0x6f, 0x16, 0x22, 0x8b, 0x30, 0xe5, 0xf6, 0xba, 0x81, 0xdd, 0xa7,
	0xfa, 0x2c, 0x7a, 0x8b, 0x6e, 0xef, 0xae, 0xdd, 0xa7, 0xe4, 0x2d, 0x98, 0x11, 0x19, 0x17, 0x2a,
	0xc6, 0x5c, 0x66, 0x91, 0xa0, 0x10, 0xd3, 0x02, 0xee, 0x08, 0x14, 0x13, 0x69, 0x02, 0x02, 0x5d,
	0x1a, 0xb8, 0x92, 0x35, 0x97, 0xca, 0xd5, 0x0a, 0x5c, 0xe4, 0x2c, 0xc2, 0x14, 0xb7, 0xd9, 0x9e,
	0x08, 0x7f, 0x5e, 0x6e, 0x22, 0xcc, 0xb6, 0x4b, 0x6e, 0x00, 0xf4, 0x6c, 0xee, 0xec, 0x76, 0x99,
	0x77, 0x4c, 0xf5, 0x05, 0x54, 0xa9, 0x8c, 0x48, 0xc7, 0x3b, 0xa6, 0xe4, 0x03, 0x58, 0xf4, 0x58,
	0x37, 0xa6, 0x91, 0xef, 0x39, 0x76, 0xd7, 0xa5, 0xbe, 0x7d, 0xd4, 0xa5, 0x81, 0xdd, 0xf3, 0xa9,
	0x7e, 0xa5, 0xa1, 0x2d, 0x97, 0xac, 0x79, 0x8f, 0x59, 0xd2, 0xdb, 0x14, 0xce, 0x16, 0xfa, 0xc8,
	0x2a, 0x2c, 0x8c, 0xbe, 0xc3, 0xa8, 0x13, 0x06, 0x2e, 0xd3, 0x17, 0x31, 0xb4, 0xb9, 0x38, 0xf3,
	0x4a, 0x47, 0xba, 0xc4, 0x31, 0x98, 0x4f, 0x69, 0x84, 0x67, 0xe8, 0xf6, 0x99, 0xae, 0xcb, 0x63,
	0x20, 0x28, 0x0e, 0x71, 0x87, 0x09, 0xc1, 0xed, 0x28, 0x8a, 0xc3, 0x47, 0xb6, 0xdf, 0x4d, 0x3a,
	0xe4, 0x2a, 0xc6, 0x5c, 0x4b, 0xf0, 0x0d, 0xd9, 0x25, 0xb7, 0x60, 0xda, 0x8e, 0x9d, 0x5d, 0xef,
	0x11, 0x95, 0x9d, 0xa5, 0x5f, 0xc3, 0x63, 0x8f, 0x82, 0x99, 0x2a, 0xde, 0x89, 0xc3, 0xbe, 0x7e,
	0x5d, 0x2a, 0x2b, 0xa1, 0x8d, 0x38, 0xec, 0x13, 0x02, 0x13, 0x7d, 0xda, 0x0f, 0xf5, 0x25, 0xf4,
	0xe0, 0x33, 0x99, 0x87, 0x49, 0xee, 0x71, 0x9f, 0xea, 0x37, 0x10, 0x94, 0x86, 0xe8, 0x3a, 0x7b,
	0x67, 0x87, 0x3a, 0x9c, 0xba, 0xdd, 0x38, 0x3c, 0x60, 0xba, 0x21, 0xab, 0x39, 0x01, 0xad, 0xf0,
	0x00, 0xe3, 0xcf, 0xd6, 0x3c, 0x8a, 0x7e, 0x53, 0x15, 0xec, 0xb0, 0xea, 0x51, 0xfc, 0x25, 0x28,
	0xb3, 0x41, 0xaf, 0xef, 0x71, 0x4e, 0x5d, 0xbd, 0x81, 0x99, 0x48, 0x01, 0xf3, 0xd7, 0x3c, 0x40,
	0xeb, 0x90, 0x3a, 0x6a, 0xec, 0x5c, 0x83, 0xa4, 0x27, 0x9a, 0x38, 0x75, 0xd2, 0x1e, 0x69, 0x92,
	0x2b, 0x50, 0x14, 0x7b, 0xb5, 0x9b, 0x38, 0x6d, 0xca, 0x96, 0xb2, 0xc8, 0x0a, 0x14, 0x19, 0x16,
	0x3c, 0x8e, 0x98, 0xca, 0x2a, 0x39, 0xdb, 0x0a, 0x96, 0x62, 0x10, 0x03, 0x32, 0xf3, 0xe5, 0x9c,
	0x89, 0xd3, 0x80, 0xec, 0x6c, 0x50, 0xf3, 0x66, 0x64, 0x5c, 0xbc, 0x0b, 0x73, 0xd2, 0x14, 0xe5,
	0x31, 0x2c, 0x5d, 0x35, 0x78, 0xce, 0x73, 0x91, 0x77, 0x60, 0x36, 0x85, 0x55, 0x19, 0xab, 0x41,
	0x74, 0xd6, 0x21, 0x84, 0xec, 0x79, 0xc7, 0x62, 0xb4, 0x62, 0x13, 0x96, 0xa4, 0x90, 0x3d, 0xef,
	0x78, 0x5d, 0x22, 0xe6, 0x8f, 0x1a, 0x40, 0x87, 0x87, 0x91, 0xca, 0xd8, 0x18, 0x5f, 0x1b, 0xe7,
	0x8f, 0x4e, 0xf2, 0xfc, 0xd8, 0x24, 0xcf, 0x74, 0x53, 0x61, 0xa4, 0x9b, 0x46, 0xa6, 0xd5, 0xc4,
	0xd8, 0xb4, 0x1a, 0x1b, 0x23, 0x93, 0xe3, 0x63, 0xc4, 0xfc, 0x45, 0x83, 0x9a, 0x8c, 0xaf, 0x25,
	0x53, 0xe7, 0xbe, 0x42, 0x9c, 0xe5, 0xd1, 0x38, 0x93, 0xd9, 0x5a, 0xc8, 0x88, 0xef, 0x92, 0xb7,
	0x61, 0xc2, 0x09, 0x5d, 0x29, 0x65, 0x6d, 0x75, 0x21, 0x91, 0x3e, 0xd9, 0xb6, 0x15, 0xc7, 0x61,
	0x6c, 0x21, 0x85, 0xe8, 0x30, 0xd5, 0xa7, 0x8c, 0xd9, 0x0f, 0xa9, 0x8a, 0x38, 0x31, 0xcd, 0x6f,
	0xa1, 0x96, 0x66, 0xd4, 0xa2, 0x2c, 0x12, 0x75, 0xb8, 0xad, 0xf6, 0x4e, 0xea, 0x30, 0xb1, 0xc5,
	0x3a, 0xad, 0x38, 0x5e, 0x17, 0xbb, 0xca, 0x30, 0x13, 0x53, 0x54, 0x57, 0x2b, 0x8e, 0xef, 0xa8,
	0x4d, 0x64, 0xa0, 0x19, 0x44, 0x54, 0xb0, 0xbc, 0xb4, 0x54, 0x46, 0x95, 0x65, 0xfe, 0x50, 0x80,
	0x05, 0x11, 0xf1, 0x46, 0x4c, 0xe9, 0x56, 0xe8, 0xec, 0x35, 0xef, 0x6c, 0xbd, 0xa8, 0xba, 0xd9,
	0x86, 0xc9, 0x5f, 0xd8, 0x30, 0x85, 0x0b, 0x1a, 0x66, 0xe2, 0x25, 0x1b, 0x66, 0xf2, 0xb2, 0x86,
	0x29, 0x9e, 0x6d, 0x18, 0x1d, 0x92, 0x2b, 0x04, 0x8b, 0x3e, 0x73, 0xa3, 0x5c, 0xd0, 0x4a, 0xa5,
	0x97, 0x6c, 0xa5, 0xf2, 0xbf, 0xb4, 0x52, 0x76, 0x26, 0xc2, 0x79, 0x33, 0x71, 0xd7, 0x0b, 0xb8,
	0xba, 0x8d, 0xf1, 0xd9, 0xfc, 0x49, 0x83, 0x1b, 0x42, 0x8b, 0xce, 0xae, 0x1d, 0xbb, 0x5e, 0xf0,
	0xf0, 0x15, 0x34, 0x49, 0xf3, 0x9b, 0xbf, 0x34, 0xbf, 0x99, 0xec, 0x14, 0x46, 0xb3, 0xb3, 0x04,
	0x65, 0x3c, 0x8d, 0x18, 0xa2, 0xaa, 0x5e, 0x52, 0xc0, 0xfc, 0x0c, 0x66, 0x5b, 0x87, 0x91, 0x6f,
	0x7b, 0xc1, 0x7a, 0xd8, 0xef, 0xdb, 0x81, 0x6b, 0xd1, 0x7d, 0x52, 0x83, 0x7c, 0x73, 0x4d, 0x05,
	0x94, 0x6f, 0xae, 0x89, 0xc5, 0x3b, 0x5f, 0x6d, 0xe1, 0xe2, 0xaa, 0x52, 0x95, 0x69, 0x5a, 0x40,
	0xc6, 0x5f, 0x67, 0x11, 0xf9, 0x14, 0x20, 0x35, 0x75, 0xad, 0x51, 0x58, 0xae, 0xac, 0x2e, 0xa5,
	0x2d, 0x35, 0xc6, 0x1f, 0xf8, 0xdc, 0xca, 0xf0, 0xcd, 0xdf, 0xf2, 0x30, 0x7f, 0x1e, 0x49, 0x84,
	0xa5, 0xda, 0x68, 0xd2, 0xca, 0xb7, 0x5d, 0x51, 0x53, 0x1d, 0xea, 0x53, 0x47, 0xd6, 0x94, 0x8c,
	0x2c, 0x83, 0x88, 0x6b, 0x69, 0x1b, 0x2f, 0x66, 0x99, 0x11, 0x69, 0x88, 0xb7, 0xee, 0xdb, 0x31,
	0xf7, 0xf0, 0x83, 0x55, 0x25, 0x24, 0x83, 0x08, 0x31, 0x87, 0x35, 0x5a, 0xb6, 0xf0, 0x99, 0x98,
	0x50, 0xbd, 0x1f, 0x32, 0xe6, 0xf5, 0x7c, 0xba, 0x49, 0x8f, 0x18, 0x96, 0x67, 0xd9, 0x1a, 0xc1,
	0x48, 0x1d, 0x0a, 0x9b, 0xf4, 0x48, 0xd5, 0xa6, 0x78, 0x14, 0x7d, 0xb3, 0x49, 0x8f, 0xb6, 0x68,
	0xa0, 0xa6, 0xaf, 0xb2, 0x04, 0xd3, 0xa2, 0x3b, 0x58, 0x6f, 0x65, 0x4b, 0x3c, 0x8a, 0x3d, 0xc5,
	0x6d, 0xa8, 0x4a, 0x0b, 0x9f, 0x45, 0x47, 0x6e, 0x78, 0x3e, 0xa7, 0x31, 0x1d, 0x7e, 0xe6, 0x25,
	0xb6, 0x38, 0x59, 0xeb, 0x90, 0xc7, 0xb6, 0xfa, 0xb6, 0x93, 0x86, 0xf9, 0x0d, 0xcc, 0x7c, 0x49,
	0x79, 0x3b, 0x70, 0xe9, 0x61, 0x3b, 0xd8, 0x09, 0x85, 0x92, 0x4b, 0x50, 0xde, 0x1e, 0x8a, 0x2f,
	0x05, 0x4d, 0x01, 0xa5, 0x73, 0x3e, 0xab, 0xb3, 0xca, 0x78, 0x52, 0x44, 0xca, 0x34, 0x2d, 0xa8,
	0x8f, 0x2e, 0xcd, 0x22, 0xf2, 0x39, 0xd4, 0x70, 0xa9, 0x21, 0xaa, 0x94, 0x1e, 0x7e, 0x42, 0x8e,
	0x7a, 0xad, 0x31, 0xb6, 0xf9, 0x7d, 0x7e, 0x7c, 0x81, 0x4b, 0xc2, 0x5d, 0x82, 0x32, 0x52, 0xd1,
	0x2b, 0xa3, 0x4e, 0x01, 0x91, 0xaf, 0xbb, 0x03, 0xdf, 0xcf, 0x08, 0x3e, 0xb4, 0x65, 0xa5, 0xec,
	0xb7, 0x03, 0x64, 0x27, 0xd7, 0x75, 0x8a, 0x0c, 0x57, 0xce, 0x08, 0x9f, 0x02, 0xe2, 0xed, 0xf5,
	0xd0, 0x1f, 0xf4, 0x03, 0xdc, 0x58, 0x6a, 0x9f, 0x41, 0xb0, 0x3d, 0x06, 0x3d, 0x51, 0x42, 0xc9,
	0x64, 0x52, 0xa6, 0xa8, 0x1b, 0x5c, 0x46, 0xa4, 0x91, 0x06, 0xc9, 0x2d, 0x3c, 0x82, 0x99, 0x8f,
	0xb5, 0x34, 0xb7, 0x0f, 0x6c, 0x7f, 0x40, 0x2f, 0xd7, 0x6d, 0x05, 0x8a, 0x9d, 0x4b, 0x07, 0x43,
	0x67, 0x38, 0x18, 0xce, 0xd7, 0x54, 0x7a, 0xc4, 0x21, 0x44, 0x17, 0x14, 0xa4, 0x07, 0x4d, 0xf3,
	0x01, 0xcc, 0x8e, 0x45, 0xc4, 0x22, 0xf2, 0x05, 0xcc, 0xa4, 0x6a, 0x21, 0xac, 0xf4, 0x5e, 0x3c,
	0xab, 0xb7, 0x7c, 0x6b, 0x9c, 0x6f, 0xde, 0x3b, 0xb3, 0xc4, 0xa8, 0xa6, 0xda, 0xb8, 0xa6, 0x06,
	0x40, 0x66, 0x3b, 0xd5, 0xe1, 0x99, 0x05, 0x4f, 0xf2, 0x62, 0x7c, 0x51, 0xe7, 0x01, 0x6d, 0xae,
	0x35, 0x9b, 0x6f, 0x2e, 0xbb, 0xd7, 0xbf, 0xec, 0xcc, 0x8f, 0x61, 0x71, 0x1d, 0x6f, 0xb6, 0xe4,
	0xe2, 0xea, 0x0c, 0x7a, 0x2f, 0x98, 0x57, 0xf3, 0x8f, 0x3c, 0xcc, 0x09, 0x39, 0x44, 0x4a, 0xd6,
	0x7d, 0x6a, 0x07, 0x6f, 0x04, 0x79, 0x7d, 0x41, 0xfe, 0xcc, 0x5f, 0xf0, 0x21, 0xf1, 0xa6, 0xde,
	0xff, 0x83, 0xf4, 0xae, 0x34, 0x61, 0x7a, 0xe4, 0x23, 0x9f, 0xcc, 0xa6, 0x00, 0xae, 0x59, 0xcf,
	0x91, 0x9a, 0xfc, 0x6d, 0x29, 0xa0, 0x7b, 0x9b, 0x75, 0x8d, 0xd4, 0xa1, 0x9a, 0xd8, 0x1b, 0xb6,
	0xe7, 0xd7, 0xf3, 0x2b, 0x1f, 0x42, 0xc5, 0x1a, 0x04, 0xe2, 0x77, 0x3a, 0x1e, 0x67, 0x0e, 0x66,
	0xee, 0x86, 0x01, 0xcd, 0x40, 0xf5, 0x1c, 0x21, 0x50, 0xdb, 0xfc, 0x88, 0x65, 0x31, 0x6d, 0xed,
	0xfd, 0x93, 0xa7, 0x46, 0xee, 0xc9, 0x53, 0x23, 0xf7, 0xfc, 0xa9, 0xa1, 0x7d, 0x77, 0x6a, 0x68,
	0x3f, 0x9f, 0x1a, 0xda, 0xef, 0xa7, 0x86, 0x76, 0x72, 0x6a, 0x68, 0x7f, 0x9d, 0x1a, 0xda, 0xdf,
	0xa7, 0x46, 0xee, 0xf9, 0xa9, 0xa1, 0x3d, 0x7e, 0x66, 0xe4, 0x4e, 0x9e, 0x19, 0xb9, 0x27, 0xcf,
	0x8c, 0x5c, 0xaf, 0x88, 0x7f, 0xb3, 0xbd, 0xf7, 0x4f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x03, 0xfd,
	0xda, 0xe0, 0x93, 0x13, 0x00, 0x00,
}

func (x ExecutedError) String() string {
	s, ok := ExecutedError_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (x RuntimeType) String() string {
	s, ok := RuntimeType_name[int32(x)]
	if ok {
		return s
	}
	return strconv.Itoa(int(x))
}
func (this *Ticket) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*Ticket)
	if !ok {
		that2, ok := that.(Ticket)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TicketId != that1.TicketId {
		return false
	}
	if this.FlowConfigId != that1.FlowConfigId {
		return false
	}
	if this.WorkflowId != that1.WorkflowId {
		return false
	}
	if this.TicketType != that1.TicketType {
		return false
	}
	if this.TicketStatus != that1.TicketStatus {
		return false
	}
	if this.FlowStep != that1.FlowStep {
		return false
	}
	if this.ExecuteType != that1.ExecuteType {
		return false
	}
	if this.CreateTime != that1.CreateTime {
		return false
	}
	if this.UpdateTime != that1.UpdateTime {
		return false
	}
	if this.CreateUserId != that1.CreateUserId {
		return false
	}
	if this.TenantId != that1.TenantId {
		return false
	}
	if this.CurrentUserIds != that1.CurrentUserIds {
		return false
	}
	if this.InstanceType != that1.InstanceType {
		return false
	}
	if this.InstanceId != that1.InstanceId {
		return false
	}
	if this.SqlText != that1.SqlText {
		return false
	}
	if this.Description != that1.Description {
		return false
	}
	if this.DbName != that1.DbName {
		return false
	}
	if this.ExecStartTime != that1.ExecStartTime {
		return false
	}
	if this.ExecEndTime != that1.ExecEndTime {
		return false
	}
	if this.TaskId != that1.TaskId {
		return false
	}
	if this.BatchSize != that1.BatchSize {
		return false
	}
	if this.IsReplicaDelayEnable != that1.IsReplicaDelayEnable {
		return false
	}
	if this.ReplicaDelaySeconds != that1.ReplicaDelaySeconds {
		return false
	}
	if this.SleepTimeMs != that1.SleepTimeMs {
		return false
	}
	if this.ApprovalFlowId != that1.ApprovalFlowId {
		return false
	}
	if this.ArchiveConfig != that1.ArchiveConfig {
		return false
	}
	if this.CreateFrom != that1.CreateFrom {
		return false
	}
	if this.Memo != that1.Memo {
		return false
	}
	if this.Title != that1.Title {
		return false
	}
	if this.AffectedRows != that1.AffectedRows {
		return false
	}
	if this.CreateUserName != that1.CreateUserName {
		return false
	}
	if this.Submitted != that1.Submitted {
		return false
	}
	return true
}
func (this *ExecTicket) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ExecTicket)
	if !ok {
		that2, ok := that.(ExecTicket)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TenantID != that1.TenantID {
		return false
	}
	if this.UserID != that1.UserID {
		return false
	}
	if !this.Source.Equal(that1.Source) {
		return false
	}
	if this.TicketType != that1.TicketType {
		return false
	}
	if this.ExecuteType != that1.ExecuteType {
		return false
	}
	if this.ExecutableStartTime != that1.ExecutableStartTime {
		return false
	}
	if this.ExecutableEndTime != that1.ExecutableEndTime {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	return true
}
func (this *StopTicket) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*StopTicket)
	if !ok {
		that2, ok := that.(StopTicket)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	if this.TicketId != that1.TicketId {
		return false
	}
	if this.TaskId != that1.TaskId {
		return false
	}
	if this.TenantId != that1.TenantId {
		return false
	}
	if this.InstanceId != that1.InstanceId {
		return false
	}
	return true
}
func (this *TicketExecuted) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TicketExecuted)
	if !ok {
		that2, ok := that.(TicketExecuted)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	if this.TicketId != that1.TicketId {
		return false
	}
	if this.UserId != that1.UserId {
		return false
	}
	if this.Code != that1.Code {
		return false
	}
	if this.Message != that1.Message {
		return false
	}
	return true
}
func (this *StopTicketResp) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*StopTicketResp)
	if !ok {
		that2, ok := that.(StopTicketResp)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TicketId != that1.TicketId {
		return false
	}
	if this.ErrCode != that1.ErrCode {
		return false
	}
	if this.ErrMessage != that1.ErrMessage {
		return false
	}
	if this.Status != that1.Status {
		return false
	}
	return true
}
func (this *ExecFreeLockDMLTicket) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ExecFreeLockDMLTicket)
	if !ok {
		that2, ok := that.(ExecFreeLockDMLTicket)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	if this.TenantID != that1.TenantID {
		return false
	}
	if this.UserID != that1.UserID {
		return false
	}
	if !this.Source.Equal(that1.Source) {
		return false
	}
	if this.TicketType != that1.TicketType {
		return false
	}
	if this.ExecuteType != that1.ExecuteType {
		return false
	}
	if this.SqlText != that1.SqlText {
		return false
	}
	if this.ExecutableStartTime != that1.ExecutableStartTime {
		return false
	}
	if this.ExecutableEndTime != that1.ExecutableEndTime {
		return false
	}
	if this.CreateFrom != that1.CreateFrom {
		return false
	}
	if this.Hint != that1.Hint {
		return false
	}
	return true
}
func (this *ExecShardingFreeLockDMLTicket) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ExecShardingFreeLockDMLTicket)
	if !ok {
		that2, ok := that.(ExecShardingFreeLockDMLTicket)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	if !this.Source.Equal(that1.Source) {
		return false
	}
	if this.SqlText != that1.SqlText {
		return false
	}
	if this.TableName != that1.TableName {
		return false
	}
	return true
}
func (this *ExplainCommandReq) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ExplainCommandReq)
	if !ok {
		that2, ok := that.(ExplainCommandReq)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.DB != that1.DB {
		return false
	}
	if this.SQLText != that1.SQLText {
		return false
	}
	return true
}
func (this *ExplainCommandResp) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ExplainCommandResp)
	if !ok {
		that2, ok := that.(ExplainCommandResp)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.CommandRes) != len(that1.CommandRes) {
		return false
	}
	for i := range this.CommandRes {
		if !this.CommandRes[i].Equal(that1.CommandRes[i]) {
			return false
		}
	}
	return true
}
func (this *ExplainCommandResult) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ExplainCommandResult)
	if !ok {
		that2, ok := that.(ExplainCommandResult)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.Id != that1.Id {
		return false
	}
	if this.SelectType != that1.SelectType {
		return false
	}
	if this.Table != that1.Table {
		return false
	}
	if this.Partitions != that1.Partitions {
		return false
	}
	if this.Type != that1.Type {
		return false
	}
	if this.PossibleKeys != that1.PossibleKeys {
		return false
	}
	if this.Key != that1.Key {
		return false
	}
	if this.KeyLen != that1.KeyLen {
		return false
	}
	if this.Ref != that1.Ref {
		return false
	}
	if this.Rows != that1.Rows {
		return false
	}
	if this.Filtered != that1.Filtered {
		return false
	}
	if this.Extra != that1.Extra {
		return false
	}
	return true
}
func (this *GetIndexInfoReq) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*GetIndexInfoReq)
	if !ok {
		that2, ok := that.(GetIndexInfoReq)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TableName != that1.TableName {
		return false
	}
	if this.DB != that1.DB {
		return false
	}
	if this.Command != that1.Command {
		return false
	}
	return true
}
func (this *GetIndexInfoResp) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*GetIndexInfoResp)
	if !ok {
		that2, ok := that.(GetIndexInfoResp)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.TableIndexInfo) != len(that1.TableIndexInfo) {
		return false
	}
	for i := range this.TableIndexInfo {
		if !this.TableIndexInfo[i].Equal(that1.TableIndexInfo[i]) {
			return false
		}
	}
	return true
}
func (this *TableIndexInfo) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TableIndexInfo)
	if !ok {
		that2, ok := that.(TableIndexInfo)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TableName != that1.TableName {
		return false
	}
	if this.IndexName != that1.IndexName {
		return false
	}
	if this.Nullable != that1.Nullable {
		return false
	}
	if this.SeqInIndex != that1.SeqInIndex {
		return false
	}
	if this.IndexType != that1.IndexType {
		return false
	}
	if this.ColumnName != that1.ColumnName {
		return false
	}
	if this.SubPart != that1.SubPart {
		return false
	}
	if this.IndexComment != that1.IndexComment {
		return false
	}
	return true
}
func (this *GetIndexValueReq) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*GetIndexValueReq)
	if !ok {
		that2, ok := that.(GetIndexValueReq)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.TableName != that1.TableName {
		return false
	}
	if !this.Source.Equal(that1.Source) {
		return false
	}
	if this.Command != that1.Command {
		return false
	}
	if len(this.Columns) != len(that1.Columns) {
		return false
	}
	for i := range this.Columns {
		if this.Columns[i] != that1.Columns[i] {
			return false
		}
	}
	return true
}
func (this *GetIndexValueResp) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*GetIndexValueResp)
	if !ok {
		that2, ok := that.(GetIndexValueResp)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if len(this.TableIndexValue) != len(that1.TableIndexValue) {
		return false
	}
	for i := range this.TableIndexValue {
		if !this.TableIndexValue[i].Equal(that1.TableIndexValue[i]) {
			return false
		}
	}
	return true
}
func (this *TableIndexValue) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*TableIndexValue)
	if !ok {
		that2, ok := that.(TableIndexValue)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.IndexName != that1.IndexName {
		return false
	}
	if this.IndexValue != that1.IndexValue {
		return false
	}
	return true
}
func (this *ExecVeDBDDLTicket) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ExecVeDBDDLTicket)
	if !ok {
		that2, ok := that.(ExecVeDBDDLTicket)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	if this.TenantID != that1.TenantID {
		return false
	}
	if this.UserID != that1.UserID {
		return false
	}
	if !this.Source.Equal(that1.Source) {
		return false
	}
	if this.TicketType != that1.TicketType {
		return false
	}
	if this.ExecuteType != that1.ExecuteType {
		return false
	}
	if this.SqlText != that1.SqlText {
		return false
	}
	if this.ExecutableStartTime != that1.ExecutableStartTime {
		return false
	}
	if this.ExecutableEndTime != that1.ExecutableEndTime {
		return false
	}
	return true
}
func (this *CreateShardingSubTicket) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*CreateShardingSubTicket)
	if !ok {
		that2, ok := that.(CreateShardingSubTicket)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	return true
}
func (this *ExecDataCleanTicket) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ExecDataCleanTicket)
	if !ok {
		that2, ok := that.(ExecDataCleanTicket)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	if this.TenantID != that1.TenantID {
		return false
	}
	if this.UserID != that1.UserID {
		return false
	}
	if !this.Source.Equal(that1.Source) {
		return false
	}
	if this.TicketType != that1.TicketType {
		return false
	}
	if this.ExecuteType != that1.ExecuteType {
		return false
	}
	if this.SqlText != that1.SqlText {
		return false
	}
	if this.ExecutableStartTime != that1.ExecutableStartTime {
		return false
	}
	if this.ExecutableEndTime != that1.ExecutableEndTime {
		return false
	}
	return true
}
func (this *ExecShardingFreeLockDDLTicket) Equal(that interface{}) bool {
	if that == nil {
		return this == nil
	}

	that1, ok := that.(*ExecShardingFreeLockDDLTicket)
	if !ok {
		that2, ok := that.(ExecShardingFreeLockDDLTicket)
		if ok {
			that1 = &that2
		} else {
			return false
		}
	}
	if that1 == nil {
		return this == nil
	} else if this == nil {
		return false
	}
	if this.BizContext != that1.BizContext {
		return false
	}
	if this.TenantID != that1.TenantID {
		return false
	}
	if this.UserID != that1.UserID {
		return false
	}
	if !this.Source.Equal(that1.Source) {
		return false
	}
	if this.TicketType != that1.TicketType {
		return false
	}
	if this.ExecuteType != that1.ExecuteType {
		return false
	}
	if this.SqlText != that1.SqlText {
		return false
	}
	if this.ExecutableStartTime != that1.ExecutableStartTime {
		return false
	}
	if this.ExecutableEndTime != that1.ExecutableEndTime {
		return false
	}
	return true
}
func (this *Ticket) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 36)
	s = append(s, "&shared.Ticket{")
	s = append(s, "TicketId: "+fmt.Sprintf("%#v", this.TicketId)+",\n")
	s = append(s, "FlowConfigId: "+fmt.Sprintf("%#v", this.FlowConfigId)+",\n")
	s = append(s, "WorkflowId: "+fmt.Sprintf("%#v", this.WorkflowId)+",\n")
	s = append(s, "TicketType: "+fmt.Sprintf("%#v", this.TicketType)+",\n")
	s = append(s, "TicketStatus: "+fmt.Sprintf("%#v", this.TicketStatus)+",\n")
	s = append(s, "FlowStep: "+fmt.Sprintf("%#v", this.FlowStep)+",\n")
	s = append(s, "ExecuteType: "+fmt.Sprintf("%#v", this.ExecuteType)+",\n")
	s = append(s, "CreateTime: "+fmt.Sprintf("%#v", this.CreateTime)+",\n")
	s = append(s, "UpdateTime: "+fmt.Sprintf("%#v", this.UpdateTime)+",\n")
	s = append(s, "CreateUserId: "+fmt.Sprintf("%#v", this.CreateUserId)+",\n")
	s = append(s, "TenantId: "+fmt.Sprintf("%#v", this.TenantId)+",\n")
	s = append(s, "CurrentUserIds: "+fmt.Sprintf("%#v", this.CurrentUserIds)+",\n")
	s = append(s, "InstanceType: "+fmt.Sprintf("%#v", this.InstanceType)+",\n")
	s = append(s, "InstanceId: "+fmt.Sprintf("%#v", this.InstanceId)+",\n")
	s = append(s, "SqlText: "+fmt.Sprintf("%#v", this.SqlText)+",\n")
	s = append(s, "Description: "+fmt.Sprintf("%#v", this.Description)+",\n")
	s = append(s, "DbName: "+fmt.Sprintf("%#v", this.DbName)+",\n")
	s = append(s, "ExecStartTime: "+fmt.Sprintf("%#v", this.ExecStartTime)+",\n")
	s = append(s, "ExecEndTime: "+fmt.Sprintf("%#v", this.ExecEndTime)+",\n")
	s = append(s, "TaskId: "+fmt.Sprintf("%#v", this.TaskId)+",\n")
	s = append(s, "BatchSize: "+fmt.Sprintf("%#v", this.BatchSize)+",\n")
	s = append(s, "IsReplicaDelayEnable: "+fmt.Sprintf("%#v", this.IsReplicaDelayEnable)+",\n")
	s = append(s, "ReplicaDelaySeconds: "+fmt.Sprintf("%#v", this.ReplicaDelaySeconds)+",\n")
	s = append(s, "SleepTimeMs: "+fmt.Sprintf("%#v", this.SleepTimeMs)+",\n")
	s = append(s, "ApprovalFlowId: "+fmt.Sprintf("%#v", this.ApprovalFlowId)+",\n")
	s = append(s, "ArchiveConfig: "+fmt.Sprintf("%#v", this.ArchiveConfig)+",\n")
	s = append(s, "CreateFrom: "+fmt.Sprintf("%#v", this.CreateFrom)+",\n")
	s = append(s, "Memo: "+fmt.Sprintf("%#v", this.Memo)+",\n")
	s = append(s, "Title: "+fmt.Sprintf("%#v", this.Title)+",\n")
	s = append(s, "AffectedRows: "+fmt.Sprintf("%#v", this.AffectedRows)+",\n")
	s = append(s, "CreateUserName: "+fmt.Sprintf("%#v", this.CreateUserName)+",\n")
	s = append(s, "Submitted: "+fmt.Sprintf("%#v", this.Submitted)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ExecTicket) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 12)
	s = append(s, "&shared.ExecTicket{")
	s = append(s, "TenantID: "+fmt.Sprintf("%#v", this.TenantID)+",\n")
	s = append(s, "UserID: "+fmt.Sprintf("%#v", this.UserID)+",\n")
	if this.Source != nil {
		s = append(s, "Source: "+fmt.Sprintf("%#v", this.Source)+",\n")
	}
	s = append(s, "TicketType: "+fmt.Sprintf("%#v", this.TicketType)+",\n")
	s = append(s, "ExecuteType: "+fmt.Sprintf("%#v", this.ExecuteType)+",\n")
	s = append(s, "ExecutableStartTime: "+fmt.Sprintf("%#v", this.ExecutableStartTime)+",\n")
	s = append(s, "ExecutableEndTime: "+fmt.Sprintf("%#v", this.ExecutableEndTime)+",\n")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *StopTicket) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 9)
	s = append(s, "&shared.StopTicket{")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "TicketId: "+fmt.Sprintf("%#v", this.TicketId)+",\n")
	s = append(s, "TaskId: "+fmt.Sprintf("%#v", this.TaskId)+",\n")
	s = append(s, "TenantId: "+fmt.Sprintf("%#v", this.TenantId)+",\n")
	s = append(s, "InstanceId: "+fmt.Sprintf("%#v", this.InstanceId)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TicketExecuted) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 9)
	s = append(s, "&shared.TicketExecuted{")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "TicketId: "+fmt.Sprintf("%#v", this.TicketId)+",\n")
	s = append(s, "UserId: "+fmt.Sprintf("%#v", this.UserId)+",\n")
	s = append(s, "Code: "+fmt.Sprintf("%#v", this.Code)+",\n")
	s = append(s, "Message: "+fmt.Sprintf("%#v", this.Message)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *StopTicketResp) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.StopTicketResp{")
	s = append(s, "TicketId: "+fmt.Sprintf("%#v", this.TicketId)+",\n")
	s = append(s, "ErrCode: "+fmt.Sprintf("%#v", this.ErrCode)+",\n")
	s = append(s, "ErrMessage: "+fmt.Sprintf("%#v", this.ErrMessage)+",\n")
	s = append(s, "Status: "+fmt.Sprintf("%#v", this.Status)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ExecFreeLockDMLTicket) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 15)
	s = append(s, "&shared.ExecFreeLockDMLTicket{")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "TenantID: "+fmt.Sprintf("%#v", this.TenantID)+",\n")
	s = append(s, "UserID: "+fmt.Sprintf("%#v", this.UserID)+",\n")
	if this.Source != nil {
		s = append(s, "Source: "+fmt.Sprintf("%#v", this.Source)+",\n")
	}
	s = append(s, "TicketType: "+fmt.Sprintf("%#v", this.TicketType)+",\n")
	s = append(s, "ExecuteType: "+fmt.Sprintf("%#v", this.ExecuteType)+",\n")
	s = append(s, "SqlText: "+fmt.Sprintf("%#v", this.SqlText)+",\n")
	s = append(s, "ExecutableStartTime: "+fmt.Sprintf("%#v", this.ExecutableStartTime)+",\n")
	s = append(s, "ExecutableEndTime: "+fmt.Sprintf("%#v", this.ExecutableEndTime)+",\n")
	s = append(s, "CreateFrom: "+fmt.Sprintf("%#v", this.CreateFrom)+",\n")
	s = append(s, "Hint: "+fmt.Sprintf("%#v", this.Hint)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ExecShardingFreeLockDMLTicket) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.ExecShardingFreeLockDMLTicket{")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	if this.Source != nil {
		s = append(s, "Source: "+fmt.Sprintf("%#v", this.Source)+",\n")
	}
	s = append(s, "SqlText: "+fmt.Sprintf("%#v", this.SqlText)+",\n")
	s = append(s, "TableName: "+fmt.Sprintf("%#v", this.TableName)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ExplainCommandReq) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.ExplainCommandReq{")
	s = append(s, "DB: "+fmt.Sprintf("%#v", this.DB)+",\n")
	s = append(s, "SQLText: "+fmt.Sprintf("%#v", this.SQLText)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ExplainCommandResp) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.ExplainCommandResp{")
	if this.CommandRes != nil {
		s = append(s, "CommandRes: "+fmt.Sprintf("%#v", this.CommandRes)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ExplainCommandResult) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 16)
	s = append(s, "&shared.ExplainCommandResult{")
	s = append(s, "Id: "+fmt.Sprintf("%#v", this.Id)+",\n")
	s = append(s, "SelectType: "+fmt.Sprintf("%#v", this.SelectType)+",\n")
	s = append(s, "Table: "+fmt.Sprintf("%#v", this.Table)+",\n")
	s = append(s, "Partitions: "+fmt.Sprintf("%#v", this.Partitions)+",\n")
	s = append(s, "Type: "+fmt.Sprintf("%#v", this.Type)+",\n")
	s = append(s, "PossibleKeys: "+fmt.Sprintf("%#v", this.PossibleKeys)+",\n")
	s = append(s, "Key: "+fmt.Sprintf("%#v", this.Key)+",\n")
	s = append(s, "KeyLen: "+fmt.Sprintf("%#v", this.KeyLen)+",\n")
	s = append(s, "Ref: "+fmt.Sprintf("%#v", this.Ref)+",\n")
	s = append(s, "Rows: "+fmt.Sprintf("%#v", this.Rows)+",\n")
	s = append(s, "Filtered: "+fmt.Sprintf("%#v", this.Filtered)+",\n")
	s = append(s, "Extra: "+fmt.Sprintf("%#v", this.Extra)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *GetIndexInfoReq) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 7)
	s = append(s, "&shared.GetIndexInfoReq{")
	s = append(s, "TableName: "+fmt.Sprintf("%#v", this.TableName)+",\n")
	s = append(s, "DB: "+fmt.Sprintf("%#v", this.DB)+",\n")
	s = append(s, "Command: "+fmt.Sprintf("%#v", this.Command)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *GetIndexInfoResp) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.GetIndexInfoResp{")
	if this.TableIndexInfo != nil {
		s = append(s, "TableIndexInfo: "+fmt.Sprintf("%#v", this.TableIndexInfo)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TableIndexInfo) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 12)
	s = append(s, "&shared.TableIndexInfo{")
	s = append(s, "TableName: "+fmt.Sprintf("%#v", this.TableName)+",\n")
	s = append(s, "IndexName: "+fmt.Sprintf("%#v", this.IndexName)+",\n")
	s = append(s, "Nullable: "+fmt.Sprintf("%#v", this.Nullable)+",\n")
	s = append(s, "SeqInIndex: "+fmt.Sprintf("%#v", this.SeqInIndex)+",\n")
	s = append(s, "IndexType: "+fmt.Sprintf("%#v", this.IndexType)+",\n")
	s = append(s, "ColumnName: "+fmt.Sprintf("%#v", this.ColumnName)+",\n")
	s = append(s, "SubPart: "+fmt.Sprintf("%#v", this.SubPart)+",\n")
	s = append(s, "IndexComment: "+fmt.Sprintf("%#v", this.IndexComment)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *GetIndexValueReq) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 8)
	s = append(s, "&shared.GetIndexValueReq{")
	s = append(s, "TableName: "+fmt.Sprintf("%#v", this.TableName)+",\n")
	if this.Source != nil {
		s = append(s, "Source: "+fmt.Sprintf("%#v", this.Source)+",\n")
	}
	s = append(s, "Command: "+fmt.Sprintf("%#v", this.Command)+",\n")
	s = append(s, "Columns: "+fmt.Sprintf("%#v", this.Columns)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *GetIndexValueResp) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.GetIndexValueResp{")
	if this.TableIndexValue != nil {
		s = append(s, "TableIndexValue: "+fmt.Sprintf("%#v", this.TableIndexValue)+",\n")
	}
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *TableIndexValue) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 6)
	s = append(s, "&shared.TableIndexValue{")
	s = append(s, "IndexName: "+fmt.Sprintf("%#v", this.IndexName)+",\n")
	s = append(s, "IndexValue: "+fmt.Sprintf("%#v", this.IndexValue)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ExecVeDBDDLTicket) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 13)
	s = append(s, "&shared.ExecVeDBDDLTicket{")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "TenantID: "+fmt.Sprintf("%#v", this.TenantID)+",\n")
	s = append(s, "UserID: "+fmt.Sprintf("%#v", this.UserID)+",\n")
	if this.Source != nil {
		s = append(s, "Source: "+fmt.Sprintf("%#v", this.Source)+",\n")
	}
	s = append(s, "TicketType: "+fmt.Sprintf("%#v", this.TicketType)+",\n")
	s = append(s, "ExecuteType: "+fmt.Sprintf("%#v", this.ExecuteType)+",\n")
	s = append(s, "SqlText: "+fmt.Sprintf("%#v", this.SqlText)+",\n")
	s = append(s, "ExecutableStartTime: "+fmt.Sprintf("%#v", this.ExecutableStartTime)+",\n")
	s = append(s, "ExecutableEndTime: "+fmt.Sprintf("%#v", this.ExecutableEndTime)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *CreateShardingSubTicket) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 5)
	s = append(s, "&shared.CreateShardingSubTicket{")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ExecDataCleanTicket) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 13)
	s = append(s, "&shared.ExecDataCleanTicket{")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "TenantID: "+fmt.Sprintf("%#v", this.TenantID)+",\n")
	s = append(s, "UserID: "+fmt.Sprintf("%#v", this.UserID)+",\n")
	if this.Source != nil {
		s = append(s, "Source: "+fmt.Sprintf("%#v", this.Source)+",\n")
	}
	s = append(s, "TicketType: "+fmt.Sprintf("%#v", this.TicketType)+",\n")
	s = append(s, "ExecuteType: "+fmt.Sprintf("%#v", this.ExecuteType)+",\n")
	s = append(s, "SqlText: "+fmt.Sprintf("%#v", this.SqlText)+",\n")
	s = append(s, "ExecutableStartTime: "+fmt.Sprintf("%#v", this.ExecutableStartTime)+",\n")
	s = append(s, "ExecutableEndTime: "+fmt.Sprintf("%#v", this.ExecutableEndTime)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func (this *ExecShardingFreeLockDDLTicket) GoString() string {
	if this == nil {
		return "nil"
	}
	s := make([]string, 0, 13)
	s = append(s, "&shared.ExecShardingFreeLockDDLTicket{")
	s = append(s, "BizContext: "+fmt.Sprintf("%#v", this.BizContext)+",\n")
	s = append(s, "TenantID: "+fmt.Sprintf("%#v", this.TenantID)+",\n")
	s = append(s, "UserID: "+fmt.Sprintf("%#v", this.UserID)+",\n")
	if this.Source != nil {
		s = append(s, "Source: "+fmt.Sprintf("%#v", this.Source)+",\n")
	}
	s = append(s, "TicketType: "+fmt.Sprintf("%#v", this.TicketType)+",\n")
	s = append(s, "ExecuteType: "+fmt.Sprintf("%#v", this.ExecuteType)+",\n")
	s = append(s, "SqlText: "+fmt.Sprintf("%#v", this.SqlText)+",\n")
	s = append(s, "ExecutableStartTime: "+fmt.Sprintf("%#v", this.ExecutableStartTime)+",\n")
	s = append(s, "ExecutableEndTime: "+fmt.Sprintf("%#v", this.ExecutableEndTime)+",\n")
	s = append(s, "}")
	return strings.Join(s, "")
}
func valueToGoStringWorkflow(v interface{}, typ string) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("func(v %v) *%v { return &v } ( %#v )", typ, typ, pv)
}
func (m *Ticket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Ticket) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Ticket) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Submitted != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.Submitted))
		i--
		dAtA[i] = 0x2
		i--
		dAtA[i] = 0x80
	}
	if len(m.CreateUserName) > 0 {
		i -= len(m.CreateUserName)
		copy(dAtA[i:], m.CreateUserName)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.CreateUserName)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xfa
	}
	if len(m.AffectedRows) > 0 {
		i -= len(m.AffectedRows)
		copy(dAtA[i:], m.AffectedRows)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.AffectedRows)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xf2
	}
	if len(m.Title) > 0 {
		i -= len(m.Title)
		copy(dAtA[i:], m.Title)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Title)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xea
	}
	if len(m.Memo) > 0 {
		i -= len(m.Memo)
		copy(dAtA[i:], m.Memo)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Memo)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xe2
	}
	if len(m.CreateFrom) > 0 {
		i -= len(m.CreateFrom)
		copy(dAtA[i:], m.CreateFrom)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.CreateFrom)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xda
	}
	if len(m.ArchiveConfig) > 0 {
		i -= len(m.ArchiveConfig)
		copy(dAtA[i:], m.ArchiveConfig)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.ArchiveConfig)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xd2
	}
	if m.ApprovalFlowId != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ApprovalFlowId))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xc8
	}
	if m.SleepTimeMs != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.SleepTimeMs))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xc0
	}
	if m.ReplicaDelaySeconds != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ReplicaDelaySeconds))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xb8
	}
	if m.IsReplicaDelayEnable {
		i--
		if m.IsReplicaDelayEnable {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xb0
	}
	if m.BatchSize != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.BatchSize))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa8
	}
	if len(m.TaskId) > 0 {
		i -= len(m.TaskId)
		copy(dAtA[i:], m.TaskId)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.TaskId)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0xa2
	}
	if m.ExecEndTime != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecEndTime))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x98
	}
	if m.ExecStartTime != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecStartTime))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x90
	}
	if len(m.DbName) > 0 {
		i -= len(m.DbName)
		copy(dAtA[i:], m.DbName)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.DbName)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x8a
	}
	if len(m.Description) > 0 {
		i -= len(m.Description)
		copy(dAtA[i:], m.Description)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Description)))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x82
	}
	if len(m.SqlText) > 0 {
		i -= len(m.SqlText)
		copy(dAtA[i:], m.SqlText)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.SqlText)))
		i--
		dAtA[i] = 0x7a
	}
	if len(m.InstanceId) > 0 {
		i -= len(m.InstanceId)
		copy(dAtA[i:], m.InstanceId)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.InstanceId)))
		i--
		dAtA[i] = 0x72
	}
	if m.InstanceType != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.InstanceType))
		i--
		dAtA[i] = 0x68
	}
	if len(m.CurrentUserIds) > 0 {
		i -= len(m.CurrentUserIds)
		copy(dAtA[i:], m.CurrentUserIds)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.CurrentUserIds)))
		i--
		dAtA[i] = 0x62
	}
	if len(m.TenantId) > 0 {
		i -= len(m.TenantId)
		copy(dAtA[i:], m.TenantId)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.TenantId)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.CreateUserId) > 0 {
		i -= len(m.CreateUserId)
		copy(dAtA[i:], m.CreateUserId)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.CreateUserId)))
		i--
		dAtA[i] = 0x52
	}
	if m.UpdateTime != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.UpdateTime))
		i--
		dAtA[i] = 0x48
	}
	if m.CreateTime != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.CreateTime))
		i--
		dAtA[i] = 0x40
	}
	if m.ExecuteType != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecuteType))
		i--
		dAtA[i] = 0x38
	}
	if m.FlowStep != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.FlowStep))
		i--
		dAtA[i] = 0x30
	}
	if m.TicketStatus != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.TicketStatus))
		i--
		dAtA[i] = 0x28
	}
	if m.TicketType != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.TicketType))
		i--
		dAtA[i] = 0x20
	}
	if m.WorkflowId != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.WorkflowId))
		i--
		dAtA[i] = 0x18
	}
	if m.FlowConfigId != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.FlowConfigId))
		i--
		dAtA[i] = 0x10
	}
	if m.TicketId != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.TicketId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *ExecTicket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExecTicket) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExecTicket) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0x42
	}
	if m.ExecutableEndTime != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecutableEndTime))
		i--
		dAtA[i] = 0x38
	}
	if m.ExecutableStartTime != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecutableStartTime))
		i--
		dAtA[i] = 0x30
	}
	if m.ExecuteType != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecuteType))
		i--
		dAtA[i] = 0x28
	}
	if m.TicketType != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.TicketType))
		i--
		dAtA[i] = 0x20
	}
	if m.Source != nil {
		{
			size, err := m.Source.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintWorkflow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if len(m.UserID) > 0 {
		i -= len(m.UserID)
		copy(dAtA[i:], m.UserID)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.UserID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.TenantID) > 0 {
		i -= len(m.TenantID)
		copy(dAtA[i:], m.TenantID)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.TenantID)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *StopTicket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StopTicket) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StopTicket) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.InstanceId) > 0 {
		i -= len(m.InstanceId)
		copy(dAtA[i:], m.InstanceId)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.InstanceId)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.TenantId) > 0 {
		i -= len(m.TenantId)
		copy(dAtA[i:], m.TenantId)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.TenantId)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.TaskId) > 0 {
		i -= len(m.TaskId)
		copy(dAtA[i:], m.TaskId)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.TaskId)))
		i--
		dAtA[i] = 0x1a
	}
	if m.TicketId != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.TicketId))
		i--
		dAtA[i] = 0x10
	}
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TicketExecuted) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TicketExecuted) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TicketExecuted) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Message) > 0 {
		i -= len(m.Message)
		copy(dAtA[i:], m.Message)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Message)))
		i--
		dAtA[i] = 0x2a
	}
	if m.Code != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.Code))
		i--
		dAtA[i] = 0x20
	}
	if len(m.UserId) > 0 {
		i -= len(m.UserId)
		copy(dAtA[i:], m.UserId)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.UserId)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.TicketId) > 0 {
		i -= len(m.TicketId)
		copy(dAtA[i:], m.TicketId)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.TicketId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *StopTicketResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StopTicketResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StopTicketResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Status) > 0 {
		i -= len(m.Status)
		copy(dAtA[i:], m.Status)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Status)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.ErrMessage) > 0 {
		i -= len(m.ErrMessage)
		copy(dAtA[i:], m.ErrMessage)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.ErrMessage)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.ErrCode) > 0 {
		i -= len(m.ErrCode)
		copy(dAtA[i:], m.ErrCode)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.ErrCode)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.TicketId) > 0 {
		i -= len(m.TicketId)
		copy(dAtA[i:], m.TicketId)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.TicketId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ExecFreeLockDMLTicket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExecFreeLockDMLTicket) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExecFreeLockDMLTicket) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Hint) > 0 {
		i -= len(m.Hint)
		copy(dAtA[i:], m.Hint)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Hint)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.CreateFrom) > 0 {
		i -= len(m.CreateFrom)
		copy(dAtA[i:], m.CreateFrom)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.CreateFrom)))
		i--
		dAtA[i] = 0x52
	}
	if m.ExecutableEndTime != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecutableEndTime))
		i--
		dAtA[i] = 0x48
	}
	if m.ExecutableStartTime != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecutableStartTime))
		i--
		dAtA[i] = 0x40
	}
	if len(m.SqlText) > 0 {
		i -= len(m.SqlText)
		copy(dAtA[i:], m.SqlText)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.SqlText)))
		i--
		dAtA[i] = 0x3a
	}
	if m.ExecuteType != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecuteType))
		i--
		dAtA[i] = 0x30
	}
	if m.TicketType != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.TicketType))
		i--
		dAtA[i] = 0x28
	}
	if m.Source != nil {
		{
			size, err := m.Source.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintWorkflow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if len(m.UserID) > 0 {
		i -= len(m.UserID)
		copy(dAtA[i:], m.UserID)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.UserID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.TenantID) > 0 {
		i -= len(m.TenantID)
		copy(dAtA[i:], m.TenantID)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.TenantID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ExecShardingFreeLockDMLTicket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExecShardingFreeLockDMLTicket) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExecShardingFreeLockDMLTicket) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TableName) > 0 {
		i -= len(m.TableName)
		copy(dAtA[i:], m.TableName)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.TableName)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.SqlText) > 0 {
		i -= len(m.SqlText)
		copy(dAtA[i:], m.SqlText)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.SqlText)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Source != nil {
		{
			size, err := m.Source.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintWorkflow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ExplainCommandReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExplainCommandReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExplainCommandReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.SQLText) > 0 {
		i -= len(m.SQLText)
		copy(dAtA[i:], m.SQLText)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.SQLText)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.DB) > 0 {
		i -= len(m.DB)
		copy(dAtA[i:], m.DB)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.DB)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ExplainCommandResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExplainCommandResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExplainCommandResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.CommandRes) > 0 {
		for iNdEx := len(m.CommandRes) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.CommandRes[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintWorkflow(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ExplainCommandResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExplainCommandResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExplainCommandResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Extra) > 0 {
		i -= len(m.Extra)
		copy(dAtA[i:], m.Extra)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Extra)))
		i--
		dAtA[i] = 0x62
	}
	if len(m.Filtered) > 0 {
		i -= len(m.Filtered)
		copy(dAtA[i:], m.Filtered)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Filtered)))
		i--
		dAtA[i] = 0x5a
	}
	if len(m.Rows) > 0 {
		i -= len(m.Rows)
		copy(dAtA[i:], m.Rows)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Rows)))
		i--
		dAtA[i] = 0x52
	}
	if len(m.Ref) > 0 {
		i -= len(m.Ref)
		copy(dAtA[i:], m.Ref)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Ref)))
		i--
		dAtA[i] = 0x4a
	}
	if len(m.KeyLen) > 0 {
		i -= len(m.KeyLen)
		copy(dAtA[i:], m.KeyLen)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.KeyLen)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.PossibleKeys) > 0 {
		i -= len(m.PossibleKeys)
		copy(dAtA[i:], m.PossibleKeys)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.PossibleKeys)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.Type) > 0 {
		i -= len(m.Type)
		copy(dAtA[i:], m.Type)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Type)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.Partitions) > 0 {
		i -= len(m.Partitions)
		copy(dAtA[i:], m.Partitions)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Partitions)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Table) > 0 {
		i -= len(m.Table)
		copy(dAtA[i:], m.Table)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Table)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.SelectType) > 0 {
		i -= len(m.SelectType)
		copy(dAtA[i:], m.SelectType)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.SelectType)))
		i--
		dAtA[i] = 0x12
	}
	if m.Id != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetIndexInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIndexInfoReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetIndexInfoReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Command) > 0 {
		i -= len(m.Command)
		copy(dAtA[i:], m.Command)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Command)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.DB) > 0 {
		i -= len(m.DB)
		copy(dAtA[i:], m.DB)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.DB)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.TableName) > 0 {
		i -= len(m.TableName)
		copy(dAtA[i:], m.TableName)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.TableName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetIndexInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIndexInfoResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetIndexInfoResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TableIndexInfo) > 0 {
		for iNdEx := len(m.TableIndexInfo) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.TableIndexInfo[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintWorkflow(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *TableIndexInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TableIndexInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TableIndexInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.IndexComment) > 0 {
		i -= len(m.IndexComment)
		copy(dAtA[i:], m.IndexComment)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.IndexComment)))
		i--
		dAtA[i] = 0x42
	}
	if len(m.SubPart) > 0 {
		i -= len(m.SubPart)
		copy(dAtA[i:], m.SubPart)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.SubPart)))
		i--
		dAtA[i] = 0x3a
	}
	if len(m.ColumnName) > 0 {
		i -= len(m.ColumnName)
		copy(dAtA[i:], m.ColumnName)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.ColumnName)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.IndexType) > 0 {
		i -= len(m.IndexType)
		copy(dAtA[i:], m.IndexType)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.IndexType)))
		i--
		dAtA[i] = 0x2a
	}
	if m.SeqInIndex != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.SeqInIndex))
		i--
		dAtA[i] = 0x20
	}
	if len(m.Nullable) > 0 {
		i -= len(m.Nullable)
		copy(dAtA[i:], m.Nullable)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Nullable)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.IndexName) > 0 {
		i -= len(m.IndexName)
		copy(dAtA[i:], m.IndexName)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.IndexName)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.TableName) > 0 {
		i -= len(m.TableName)
		copy(dAtA[i:], m.TableName)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.TableName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetIndexValueReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIndexValueReq) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetIndexValueReq) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Columns) > 0 {
		for iNdEx := len(m.Columns) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Columns[iNdEx])
			copy(dAtA[i:], m.Columns[iNdEx])
			i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Columns[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.Command) > 0 {
		i -= len(m.Command)
		copy(dAtA[i:], m.Command)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.Command)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Source != nil {
		{
			size, err := m.Source.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintWorkflow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.TableName) > 0 {
		i -= len(m.TableName)
		copy(dAtA[i:], m.TableName)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.TableName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetIndexValueResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIndexValueResp) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetIndexValueResp) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TableIndexValue) > 0 {
		for iNdEx := len(m.TableIndexValue) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.TableIndexValue[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintWorkflow(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *TableIndexValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TableIndexValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TableIndexValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.IndexValue) > 0 {
		i -= len(m.IndexValue)
		copy(dAtA[i:], m.IndexValue)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.IndexValue)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.IndexName) > 0 {
		i -= len(m.IndexName)
		copy(dAtA[i:], m.IndexName)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.IndexName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ExecVeDBDDLTicket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExecVeDBDDLTicket) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExecVeDBDDLTicket) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ExecutableEndTime != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecutableEndTime))
		i--
		dAtA[i] = 0x48
	}
	if m.ExecutableStartTime != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecutableStartTime))
		i--
		dAtA[i] = 0x40
	}
	if len(m.SqlText) > 0 {
		i -= len(m.SqlText)
		copy(dAtA[i:], m.SqlText)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.SqlText)))
		i--
		dAtA[i] = 0x3a
	}
	if m.ExecuteType != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecuteType))
		i--
		dAtA[i] = 0x30
	}
	if m.TicketType != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.TicketType))
		i--
		dAtA[i] = 0x28
	}
	if m.Source != nil {
		{
			size, err := m.Source.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintWorkflow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if len(m.UserID) > 0 {
		i -= len(m.UserID)
		copy(dAtA[i:], m.UserID)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.UserID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.TenantID) > 0 {
		i -= len(m.TenantID)
		copy(dAtA[i:], m.TenantID)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.TenantID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CreateShardingSubTicket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateShardingSubTicket) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CreateShardingSubTicket) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ExecDataCleanTicket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExecDataCleanTicket) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExecDataCleanTicket) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ExecutableEndTime != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecutableEndTime))
		i--
		dAtA[i] = 0x48
	}
	if m.ExecutableStartTime != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecutableStartTime))
		i--
		dAtA[i] = 0x40
	}
	if len(m.SqlText) > 0 {
		i -= len(m.SqlText)
		copy(dAtA[i:], m.SqlText)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.SqlText)))
		i--
		dAtA[i] = 0x3a
	}
	if m.ExecuteType != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecuteType))
		i--
		dAtA[i] = 0x30
	}
	if m.TicketType != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.TicketType))
		i--
		dAtA[i] = 0x28
	}
	if m.Source != nil {
		{
			size, err := m.Source.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintWorkflow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if len(m.UserID) > 0 {
		i -= len(m.UserID)
		copy(dAtA[i:], m.UserID)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.UserID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.TenantID) > 0 {
		i -= len(m.TenantID)
		copy(dAtA[i:], m.TenantID)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.TenantID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ExecShardingFreeLockDDLTicket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExecShardingFreeLockDDLTicket) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ExecShardingFreeLockDDLTicket) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ExecutableEndTime != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecutableEndTime))
		i--
		dAtA[i] = 0x48
	}
	if m.ExecutableStartTime != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecutableStartTime))
		i--
		dAtA[i] = 0x40
	}
	if len(m.SqlText) > 0 {
		i -= len(m.SqlText)
		copy(dAtA[i:], m.SqlText)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.SqlText)))
		i--
		dAtA[i] = 0x3a
	}
	if m.ExecuteType != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.ExecuteType))
		i--
		dAtA[i] = 0x30
	}
	if m.TicketType != 0 {
		i = encodeVarintWorkflow(dAtA, i, uint64(m.TicketType))
		i--
		dAtA[i] = 0x28
	}
	if m.Source != nil {
		{
			size, err := m.Source.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintWorkflow(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if len(m.UserID) > 0 {
		i -= len(m.UserID)
		copy(dAtA[i:], m.UserID)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.UserID)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.TenantID) > 0 {
		i -= len(m.TenantID)
		copy(dAtA[i:], m.TenantID)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.TenantID)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.BizContext) > 0 {
		i -= len(m.BizContext)
		copy(dAtA[i:], m.BizContext)
		i = encodeVarintWorkflow(dAtA, i, uint64(len(m.BizContext)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintWorkflow(dAtA []byte, offset int, v uint64) int {
	offset -= sovWorkflow(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *Ticket) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TicketId != 0 {
		n += 1 + sovWorkflow(uint64(m.TicketId))
	}
	if m.FlowConfigId != 0 {
		n += 1 + sovWorkflow(uint64(m.FlowConfigId))
	}
	if m.WorkflowId != 0 {
		n += 1 + sovWorkflow(uint64(m.WorkflowId))
	}
	if m.TicketType != 0 {
		n += 1 + sovWorkflow(uint64(m.TicketType))
	}
	if m.TicketStatus != 0 {
		n += 1 + sovWorkflow(uint64(m.TicketStatus))
	}
	if m.FlowStep != 0 {
		n += 1 + sovWorkflow(uint64(m.FlowStep))
	}
	if m.ExecuteType != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecuteType))
	}
	if m.CreateTime != 0 {
		n += 1 + sovWorkflow(uint64(m.CreateTime))
	}
	if m.UpdateTime != 0 {
		n += 1 + sovWorkflow(uint64(m.UpdateTime))
	}
	l = len(m.CreateUserId)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.TenantId)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.CurrentUserIds)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.InstanceType != 0 {
		n += 1 + sovWorkflow(uint64(m.InstanceType))
	}
	l = len(m.InstanceId)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.SqlText)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Description)
	if l > 0 {
		n += 2 + l + sovWorkflow(uint64(l))
	}
	l = len(m.DbName)
	if l > 0 {
		n += 2 + l + sovWorkflow(uint64(l))
	}
	if m.ExecStartTime != 0 {
		n += 2 + sovWorkflow(uint64(m.ExecStartTime))
	}
	if m.ExecEndTime != 0 {
		n += 2 + sovWorkflow(uint64(m.ExecEndTime))
	}
	l = len(m.TaskId)
	if l > 0 {
		n += 2 + l + sovWorkflow(uint64(l))
	}
	if m.BatchSize != 0 {
		n += 2 + sovWorkflow(uint64(m.BatchSize))
	}
	if m.IsReplicaDelayEnable {
		n += 3
	}
	if m.ReplicaDelaySeconds != 0 {
		n += 2 + sovWorkflow(uint64(m.ReplicaDelaySeconds))
	}
	if m.SleepTimeMs != 0 {
		n += 2 + sovWorkflow(uint64(m.SleepTimeMs))
	}
	if m.ApprovalFlowId != 0 {
		n += 2 + sovWorkflow(uint64(m.ApprovalFlowId))
	}
	l = len(m.ArchiveConfig)
	if l > 0 {
		n += 2 + l + sovWorkflow(uint64(l))
	}
	l = len(m.CreateFrom)
	if l > 0 {
		n += 2 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Memo)
	if l > 0 {
		n += 2 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Title)
	if l > 0 {
		n += 2 + l + sovWorkflow(uint64(l))
	}
	l = len(m.AffectedRows)
	if l > 0 {
		n += 2 + l + sovWorkflow(uint64(l))
	}
	l = len(m.CreateUserName)
	if l > 0 {
		n += 2 + l + sovWorkflow(uint64(l))
	}
	if m.Submitted != 0 {
		n += 2 + sovWorkflow(uint64(m.Submitted))
	}
	return n
}

func (m *ExecTicket) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TenantID)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.UserID)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.Source != nil {
		l = m.Source.Size()
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.TicketType != 0 {
		n += 1 + sovWorkflow(uint64(m.TicketType))
	}
	if m.ExecuteType != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecuteType))
	}
	if m.ExecutableStartTime != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecutableStartTime))
	}
	if m.ExecutableEndTime != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecutableEndTime))
	}
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	return n
}

func (m *StopTicket) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.TicketId != 0 {
		n += 1 + sovWorkflow(uint64(m.TicketId))
	}
	l = len(m.TaskId)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.TenantId)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.InstanceId)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	return n
}

func (m *TicketExecuted) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.TicketId)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.UserId)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.Code != 0 {
		n += 1 + sovWorkflow(uint64(m.Code))
	}
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	return n
}

func (m *StopTicketResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TicketId)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.ErrCode)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.ErrMessage)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Status)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	return n
}

func (m *ExecFreeLockDMLTicket) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.TenantID)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.UserID)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.Source != nil {
		l = m.Source.Size()
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.TicketType != 0 {
		n += 1 + sovWorkflow(uint64(m.TicketType))
	}
	if m.ExecuteType != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecuteType))
	}
	l = len(m.SqlText)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.ExecutableStartTime != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecutableStartTime))
	}
	if m.ExecutableEndTime != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecutableEndTime))
	}
	l = len(m.CreateFrom)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Hint)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	return n
}

func (m *ExecShardingFreeLockDMLTicket) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.Source != nil {
		l = m.Source.Size()
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.SqlText)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.TableName)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	return n
}

func (m *ExplainCommandReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.DB)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.SQLText)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	return n
}

func (m *ExplainCommandResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.CommandRes) > 0 {
		for _, e := range m.CommandRes {
			l = e.Size()
			n += 1 + l + sovWorkflow(uint64(l))
		}
	}
	return n
}

func (m *ExplainCommandResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovWorkflow(uint64(m.Id))
	}
	l = len(m.SelectType)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Table)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Partitions)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Type)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.PossibleKeys)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.KeyLen)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Ref)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Rows)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Filtered)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Extra)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	return n
}

func (m *GetIndexInfoReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TableName)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.DB)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Command)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	return n
}

func (m *GetIndexInfoResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.TableIndexInfo) > 0 {
		for _, e := range m.TableIndexInfo {
			l = e.Size()
			n += 1 + l + sovWorkflow(uint64(l))
		}
	}
	return n
}

func (m *TableIndexInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TableName)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.IndexName)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Nullable)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.SeqInIndex != 0 {
		n += 1 + sovWorkflow(uint64(m.SeqInIndex))
	}
	l = len(m.IndexType)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.ColumnName)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.SubPart)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.IndexComment)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	return n
}

func (m *GetIndexValueReq) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TableName)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.Source != nil {
		l = m.Source.Size()
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.Command)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if len(m.Columns) > 0 {
		for _, s := range m.Columns {
			l = len(s)
			n += 1 + l + sovWorkflow(uint64(l))
		}
	}
	return n
}

func (m *GetIndexValueResp) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.TableIndexValue) > 0 {
		for _, e := range m.TableIndexValue {
			l = e.Size()
			n += 1 + l + sovWorkflow(uint64(l))
		}
	}
	return n
}

func (m *TableIndexValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.IndexName)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.IndexValue)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	return n
}

func (m *ExecVeDBDDLTicket) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.TenantID)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.UserID)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.Source != nil {
		l = m.Source.Size()
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.TicketType != 0 {
		n += 1 + sovWorkflow(uint64(m.TicketType))
	}
	if m.ExecuteType != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecuteType))
	}
	l = len(m.SqlText)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.ExecutableStartTime != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecutableStartTime))
	}
	if m.ExecutableEndTime != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecutableEndTime))
	}
	return n
}

func (m *CreateShardingSubTicket) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	return n
}

func (m *ExecDataCleanTicket) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.TenantID)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.UserID)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.Source != nil {
		l = m.Source.Size()
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.TicketType != 0 {
		n += 1 + sovWorkflow(uint64(m.TicketType))
	}
	if m.ExecuteType != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecuteType))
	}
	l = len(m.SqlText)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.ExecutableStartTime != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecutableStartTime))
	}
	if m.ExecutableEndTime != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecutableEndTime))
	}
	return n
}

func (m *ExecShardingFreeLockDDLTicket) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.BizContext)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.TenantID)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	l = len(m.UserID)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.Source != nil {
		l = m.Source.Size()
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.TicketType != 0 {
		n += 1 + sovWorkflow(uint64(m.TicketType))
	}
	if m.ExecuteType != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecuteType))
	}
	l = len(m.SqlText)
	if l > 0 {
		n += 1 + l + sovWorkflow(uint64(l))
	}
	if m.ExecutableStartTime != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecutableStartTime))
	}
	if m.ExecutableEndTime != 0 {
		n += 1 + sovWorkflow(uint64(m.ExecutableEndTime))
	}
	return n
}

func sovWorkflow(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozWorkflow(x uint64) (n int) {
	return sovWorkflow(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (this *Ticket) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&Ticket{`,
		`TicketId:` + fmt.Sprintf("%v", this.TicketId) + `,`,
		`FlowConfigId:` + fmt.Sprintf("%v", this.FlowConfigId) + `,`,
		`WorkflowId:` + fmt.Sprintf("%v", this.WorkflowId) + `,`,
		`TicketType:` + fmt.Sprintf("%v", this.TicketType) + `,`,
		`TicketStatus:` + fmt.Sprintf("%v", this.TicketStatus) + `,`,
		`FlowStep:` + fmt.Sprintf("%v", this.FlowStep) + `,`,
		`ExecuteType:` + fmt.Sprintf("%v", this.ExecuteType) + `,`,
		`CreateTime:` + fmt.Sprintf("%v", this.CreateTime) + `,`,
		`UpdateTime:` + fmt.Sprintf("%v", this.UpdateTime) + `,`,
		`CreateUserId:` + fmt.Sprintf("%v", this.CreateUserId) + `,`,
		`TenantId:` + fmt.Sprintf("%v", this.TenantId) + `,`,
		`CurrentUserIds:` + fmt.Sprintf("%v", this.CurrentUserIds) + `,`,
		`InstanceType:` + fmt.Sprintf("%v", this.InstanceType) + `,`,
		`InstanceId:` + fmt.Sprintf("%v", this.InstanceId) + `,`,
		`SqlText:` + fmt.Sprintf("%v", this.SqlText) + `,`,
		`Description:` + fmt.Sprintf("%v", this.Description) + `,`,
		`DbName:` + fmt.Sprintf("%v", this.DbName) + `,`,
		`ExecStartTime:` + fmt.Sprintf("%v", this.ExecStartTime) + `,`,
		`ExecEndTime:` + fmt.Sprintf("%v", this.ExecEndTime) + `,`,
		`TaskId:` + fmt.Sprintf("%v", this.TaskId) + `,`,
		`BatchSize:` + fmt.Sprintf("%v", this.BatchSize) + `,`,
		`IsReplicaDelayEnable:` + fmt.Sprintf("%v", this.IsReplicaDelayEnable) + `,`,
		`ReplicaDelaySeconds:` + fmt.Sprintf("%v", this.ReplicaDelaySeconds) + `,`,
		`SleepTimeMs:` + fmt.Sprintf("%v", this.SleepTimeMs) + `,`,
		`ApprovalFlowId:` + fmt.Sprintf("%v", this.ApprovalFlowId) + `,`,
		`ArchiveConfig:` + fmt.Sprintf("%v", this.ArchiveConfig) + `,`,
		`CreateFrom:` + fmt.Sprintf("%v", this.CreateFrom) + `,`,
		`Memo:` + fmt.Sprintf("%v", this.Memo) + `,`,
		`Title:` + fmt.Sprintf("%v", this.Title) + `,`,
		`AffectedRows:` + fmt.Sprintf("%v", this.AffectedRows) + `,`,
		`CreateUserName:` + fmt.Sprintf("%v", this.CreateUserName) + `,`,
		`Submitted:` + fmt.Sprintf("%v", this.Submitted) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ExecTicket) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ExecTicket{`,
		`TenantID:` + fmt.Sprintf("%v", this.TenantID) + `,`,
		`UserID:` + fmt.Sprintf("%v", this.UserID) + `,`,
		`Source:` + strings.Replace(fmt.Sprintf("%v", this.Source), "DataSource", "DataSource", 1) + `,`,
		`TicketType:` + fmt.Sprintf("%v", this.TicketType) + `,`,
		`ExecuteType:` + fmt.Sprintf("%v", this.ExecuteType) + `,`,
		`ExecutableStartTime:` + fmt.Sprintf("%v", this.ExecutableStartTime) + `,`,
		`ExecutableEndTime:` + fmt.Sprintf("%v", this.ExecutableEndTime) + `,`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`}`,
	}, "")
	return s
}
func (this *StopTicket) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&StopTicket{`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`TicketId:` + fmt.Sprintf("%v", this.TicketId) + `,`,
		`TaskId:` + fmt.Sprintf("%v", this.TaskId) + `,`,
		`TenantId:` + fmt.Sprintf("%v", this.TenantId) + `,`,
		`InstanceId:` + fmt.Sprintf("%v", this.InstanceId) + `,`,
		`}`,
	}, "")
	return s
}
func (this *TicketExecuted) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&TicketExecuted{`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`TicketId:` + fmt.Sprintf("%v", this.TicketId) + `,`,
		`UserId:` + fmt.Sprintf("%v", this.UserId) + `,`,
		`Code:` + fmt.Sprintf("%v", this.Code) + `,`,
		`Message:` + fmt.Sprintf("%v", this.Message) + `,`,
		`}`,
	}, "")
	return s
}
func (this *StopTicketResp) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&StopTicketResp{`,
		`TicketId:` + fmt.Sprintf("%v", this.TicketId) + `,`,
		`ErrCode:` + fmt.Sprintf("%v", this.ErrCode) + `,`,
		`ErrMessage:` + fmt.Sprintf("%v", this.ErrMessage) + `,`,
		`Status:` + fmt.Sprintf("%v", this.Status) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ExecFreeLockDMLTicket) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ExecFreeLockDMLTicket{`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`TenantID:` + fmt.Sprintf("%v", this.TenantID) + `,`,
		`UserID:` + fmt.Sprintf("%v", this.UserID) + `,`,
		`Source:` + strings.Replace(fmt.Sprintf("%v", this.Source), "DataSource", "DataSource", 1) + `,`,
		`TicketType:` + fmt.Sprintf("%v", this.TicketType) + `,`,
		`ExecuteType:` + fmt.Sprintf("%v", this.ExecuteType) + `,`,
		`SqlText:` + fmt.Sprintf("%v", this.SqlText) + `,`,
		`ExecutableStartTime:` + fmt.Sprintf("%v", this.ExecutableStartTime) + `,`,
		`ExecutableEndTime:` + fmt.Sprintf("%v", this.ExecutableEndTime) + `,`,
		`CreateFrom:` + fmt.Sprintf("%v", this.CreateFrom) + `,`,
		`Hint:` + fmt.Sprintf("%v", this.Hint) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ExecShardingFreeLockDMLTicket) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ExecShardingFreeLockDMLTicket{`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`Source:` + strings.Replace(fmt.Sprintf("%v", this.Source), "DataSource", "DataSource", 1) + `,`,
		`SqlText:` + fmt.Sprintf("%v", this.SqlText) + `,`,
		`TableName:` + fmt.Sprintf("%v", this.TableName) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ExplainCommandReq) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ExplainCommandReq{`,
		`DB:` + fmt.Sprintf("%v", this.DB) + `,`,
		`SQLText:` + fmt.Sprintf("%v", this.SQLText) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ExplainCommandResp) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForCommandRes := "[]*ExplainCommandResult{"
	for _, f := range this.CommandRes {
		repeatedStringForCommandRes += strings.Replace(f.String(), "ExplainCommandResult", "ExplainCommandResult", 1) + ","
	}
	repeatedStringForCommandRes += "}"
	s := strings.Join([]string{`&ExplainCommandResp{`,
		`CommandRes:` + repeatedStringForCommandRes + `,`,
		`}`,
	}, "")
	return s
}
func (this *ExplainCommandResult) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ExplainCommandResult{`,
		`Id:` + fmt.Sprintf("%v", this.Id) + `,`,
		`SelectType:` + fmt.Sprintf("%v", this.SelectType) + `,`,
		`Table:` + fmt.Sprintf("%v", this.Table) + `,`,
		`Partitions:` + fmt.Sprintf("%v", this.Partitions) + `,`,
		`Type:` + fmt.Sprintf("%v", this.Type) + `,`,
		`PossibleKeys:` + fmt.Sprintf("%v", this.PossibleKeys) + `,`,
		`Key:` + fmt.Sprintf("%v", this.Key) + `,`,
		`KeyLen:` + fmt.Sprintf("%v", this.KeyLen) + `,`,
		`Ref:` + fmt.Sprintf("%v", this.Ref) + `,`,
		`Rows:` + fmt.Sprintf("%v", this.Rows) + `,`,
		`Filtered:` + fmt.Sprintf("%v", this.Filtered) + `,`,
		`Extra:` + fmt.Sprintf("%v", this.Extra) + `,`,
		`}`,
	}, "")
	return s
}
func (this *GetIndexInfoReq) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&GetIndexInfoReq{`,
		`TableName:` + fmt.Sprintf("%v", this.TableName) + `,`,
		`DB:` + fmt.Sprintf("%v", this.DB) + `,`,
		`Command:` + fmt.Sprintf("%v", this.Command) + `,`,
		`}`,
	}, "")
	return s
}
func (this *GetIndexInfoResp) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForTableIndexInfo := "[]*TableIndexInfo{"
	for _, f := range this.TableIndexInfo {
		repeatedStringForTableIndexInfo += strings.Replace(f.String(), "TableIndexInfo", "TableIndexInfo", 1) + ","
	}
	repeatedStringForTableIndexInfo += "}"
	s := strings.Join([]string{`&GetIndexInfoResp{`,
		`TableIndexInfo:` + repeatedStringForTableIndexInfo + `,`,
		`}`,
	}, "")
	return s
}
func (this *TableIndexInfo) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&TableIndexInfo{`,
		`TableName:` + fmt.Sprintf("%v", this.TableName) + `,`,
		`IndexName:` + fmt.Sprintf("%v", this.IndexName) + `,`,
		`Nullable:` + fmt.Sprintf("%v", this.Nullable) + `,`,
		`SeqInIndex:` + fmt.Sprintf("%v", this.SeqInIndex) + `,`,
		`IndexType:` + fmt.Sprintf("%v", this.IndexType) + `,`,
		`ColumnName:` + fmt.Sprintf("%v", this.ColumnName) + `,`,
		`SubPart:` + fmt.Sprintf("%v", this.SubPart) + `,`,
		`IndexComment:` + fmt.Sprintf("%v", this.IndexComment) + `,`,
		`}`,
	}, "")
	return s
}
func (this *GetIndexValueReq) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&GetIndexValueReq{`,
		`TableName:` + fmt.Sprintf("%v", this.TableName) + `,`,
		`Source:` + strings.Replace(fmt.Sprintf("%v", this.Source), "DataSource", "DataSource", 1) + `,`,
		`Command:` + fmt.Sprintf("%v", this.Command) + `,`,
		`Columns:` + fmt.Sprintf("%v", this.Columns) + `,`,
		`}`,
	}, "")
	return s
}
func (this *GetIndexValueResp) String() string {
	if this == nil {
		return "nil"
	}
	repeatedStringForTableIndexValue := "[]*TableIndexValue{"
	for _, f := range this.TableIndexValue {
		repeatedStringForTableIndexValue += strings.Replace(f.String(), "TableIndexValue", "TableIndexValue", 1) + ","
	}
	repeatedStringForTableIndexValue += "}"
	s := strings.Join([]string{`&GetIndexValueResp{`,
		`TableIndexValue:` + repeatedStringForTableIndexValue + `,`,
		`}`,
	}, "")
	return s
}
func (this *TableIndexValue) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&TableIndexValue{`,
		`IndexName:` + fmt.Sprintf("%v", this.IndexName) + `,`,
		`IndexValue:` + fmt.Sprintf("%v", this.IndexValue) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ExecVeDBDDLTicket) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ExecVeDBDDLTicket{`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`TenantID:` + fmt.Sprintf("%v", this.TenantID) + `,`,
		`UserID:` + fmt.Sprintf("%v", this.UserID) + `,`,
		`Source:` + strings.Replace(fmt.Sprintf("%v", this.Source), "DataSource", "DataSource", 1) + `,`,
		`TicketType:` + fmt.Sprintf("%v", this.TicketType) + `,`,
		`ExecuteType:` + fmt.Sprintf("%v", this.ExecuteType) + `,`,
		`SqlText:` + fmt.Sprintf("%v", this.SqlText) + `,`,
		`ExecutableStartTime:` + fmt.Sprintf("%v", this.ExecutableStartTime) + `,`,
		`ExecutableEndTime:` + fmt.Sprintf("%v", this.ExecutableEndTime) + `,`,
		`}`,
	}, "")
	return s
}
func (this *CreateShardingSubTicket) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&CreateShardingSubTicket{`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ExecDataCleanTicket) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ExecDataCleanTicket{`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`TenantID:` + fmt.Sprintf("%v", this.TenantID) + `,`,
		`UserID:` + fmt.Sprintf("%v", this.UserID) + `,`,
		`Source:` + strings.Replace(fmt.Sprintf("%v", this.Source), "DataSource", "DataSource", 1) + `,`,
		`TicketType:` + fmt.Sprintf("%v", this.TicketType) + `,`,
		`ExecuteType:` + fmt.Sprintf("%v", this.ExecuteType) + `,`,
		`SqlText:` + fmt.Sprintf("%v", this.SqlText) + `,`,
		`ExecutableStartTime:` + fmt.Sprintf("%v", this.ExecutableStartTime) + `,`,
		`ExecutableEndTime:` + fmt.Sprintf("%v", this.ExecutableEndTime) + `,`,
		`}`,
	}, "")
	return s
}
func (this *ExecShardingFreeLockDDLTicket) String() string {
	if this == nil {
		return "nil"
	}
	s := strings.Join([]string{`&ExecShardingFreeLockDDLTicket{`,
		`BizContext:` + fmt.Sprintf("%v", this.BizContext) + `,`,
		`TenantID:` + fmt.Sprintf("%v", this.TenantID) + `,`,
		`UserID:` + fmt.Sprintf("%v", this.UserID) + `,`,
		`Source:` + strings.Replace(fmt.Sprintf("%v", this.Source), "DataSource", "DataSource", 1) + `,`,
		`TicketType:` + fmt.Sprintf("%v", this.TicketType) + `,`,
		`ExecuteType:` + fmt.Sprintf("%v", this.ExecuteType) + `,`,
		`SqlText:` + fmt.Sprintf("%v", this.SqlText) + `,`,
		`ExecutableStartTime:` + fmt.Sprintf("%v", this.ExecutableStartTime) + `,`,
		`ExecutableEndTime:` + fmt.Sprintf("%v", this.ExecutableEndTime) + `,`,
		`}`,
	}, "")
	return s
}
func valueToStringWorkflow(v interface{}) string {
	rv := reflect.ValueOf(v)
	if rv.IsNil() {
		return "nil"
	}
	pv := reflect.Indirect(rv).Interface()
	return fmt.Sprintf("*%v", pv)
}
func (m *Ticket) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Ticket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Ticket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TicketId", wireType)
			}
			m.TicketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TicketId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FlowConfigId", wireType)
			}
			m.FlowConfigId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FlowConfigId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field WorkflowId", wireType)
			}
			m.WorkflowId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WorkflowId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TicketType", wireType)
			}
			m.TicketType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TicketType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TicketStatus", wireType)
			}
			m.TicketStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TicketStatus |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FlowStep", wireType)
			}
			m.FlowStep = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FlowStep |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecuteType", wireType)
			}
			m.ExecuteType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecuteType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreateUserId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CreateUserId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CurrentUserIds", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CurrentUserIds = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 13:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstanceType", wireType)
			}
			m.InstanceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InstanceType |= DataSourceType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstanceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstanceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 15:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SqlText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SqlText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Description", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Description = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 17:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DbName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DbName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 18:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecStartTime", wireType)
			}
			m.ExecStartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecStartTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 19:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecEndTime", wireType)
			}
			m.ExecEndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecEndTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 20:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TaskId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 21:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field BatchSize", wireType)
			}
			m.BatchSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BatchSize |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 22:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsReplicaDelayEnable", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsReplicaDelayEnable = bool(v != 0)
		case 23:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReplicaDelaySeconds", wireType)
			}
			m.ReplicaDelaySeconds = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReplicaDelaySeconds |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 24:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SleepTimeMs", wireType)
			}
			m.SleepTimeMs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SleepTimeMs |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 25:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ApprovalFlowId", wireType)
			}
			m.ApprovalFlowId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApprovalFlowId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 26:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ArchiveConfig", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ArchiveConfig = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 27:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreateFrom", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CreateFrom = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 28:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Memo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Memo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 29:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 30:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field AffectedRows", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.AffectedRows = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 31:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreateUserName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CreateUserName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 32:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Submitted", wireType)
			}
			m.Submitted = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Submitted |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExecTicket) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExecTicket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExecTicket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Source == nil {
				m.Source = &DataSource{}
			}
			if err := m.Source.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TicketType", wireType)
			}
			m.TicketType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TicketType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecuteType", wireType)
			}
			m.ExecuteType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecuteType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecutableStartTime", wireType)
			}
			m.ExecutableStartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecutableStartTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecutableEndTime", wireType)
			}
			m.ExecutableEndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecutableEndTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StopTicket) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StopTicket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StopTicket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TicketId", wireType)
			}
			m.TicketId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TicketId |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TaskId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InstanceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.InstanceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TicketExecuted) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TicketExecuted: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TicketExecuted: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TicketId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TicketId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= ExecutedError(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StopTicketResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StopTicketResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StopTicketResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TicketId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TicketId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ErrCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrMessage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ErrMessage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExecFreeLockDMLTicket) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExecFreeLockDMLTicket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExecFreeLockDMLTicket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Source == nil {
				m.Source = &DataSource{}
			}
			if err := m.Source.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TicketType", wireType)
			}
			m.TicketType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TicketType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecuteType", wireType)
			}
			m.ExecuteType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecuteType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SqlText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SqlText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecutableStartTime", wireType)
			}
			m.ExecutableStartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecutableStartTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecutableEndTime", wireType)
			}
			m.ExecutableEndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecutableEndTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreateFrom", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CreateFrom = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Hint", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Hint = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExecShardingFreeLockDMLTicket) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExecShardingFreeLockDMLTicket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExecShardingFreeLockDMLTicket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Source == nil {
				m.Source = &DataSource{}
			}
			if err := m.Source.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SqlText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SqlText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExplainCommandReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExplainCommandReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExplainCommandReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DB", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DB = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SQLText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SQLText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExplainCommandResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExplainCommandResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExplainCommandResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommandRes", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommandRes = append(m.CommandRes, &ExplainCommandResult{})
			if err := m.CommandRes[len(m.CommandRes)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExplainCommandResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExplainCommandResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExplainCommandResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SelectType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SelectType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Table", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Table = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Partitions", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Partitions = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Type = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PossibleKeys", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PossibleKeys = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field KeyLen", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.KeyLen = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ref", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Ref = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Rows", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Rows = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Filtered", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Filtered = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Extra", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Extra = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIndexInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetIndexInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetIndexInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DB", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DB = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Command", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Command = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIndexInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetIndexInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetIndexInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableIndexInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableIndexInfo = append(m.TableIndexInfo, &TableIndexInfo{})
			if err := m.TableIndexInfo[len(m.TableIndexInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TableIndexInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TableIndexInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TableIndexInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IndexName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.IndexName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Nullable", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Nullable = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SeqInIndex", wireType)
			}
			m.SeqInIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SeqInIndex |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IndexType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.IndexType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ColumnName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ColumnName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SubPart", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SubPart = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IndexComment", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.IndexComment = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIndexValueReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetIndexValueReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetIndexValueReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Source == nil {
				m.Source = &DataSource{}
			}
			if err := m.Source.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Command", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Command = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Columns", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Columns = append(m.Columns, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIndexValueResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetIndexValueResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetIndexValueResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TableIndexValue", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TableIndexValue = append(m.TableIndexValue, &TableIndexValue{})
			if err := m.TableIndexValue[len(m.TableIndexValue)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TableIndexValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TableIndexValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TableIndexValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IndexName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.IndexName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IndexValue", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.IndexValue = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExecVeDBDDLTicket) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExecVeDBDDLTicket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExecVeDBDDLTicket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Source == nil {
				m.Source = &DataSource{}
			}
			if err := m.Source.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TicketType", wireType)
			}
			m.TicketType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TicketType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecuteType", wireType)
			}
			m.ExecuteType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecuteType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SqlText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SqlText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecutableStartTime", wireType)
			}
			m.ExecutableStartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecutableStartTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecutableEndTime", wireType)
			}
			m.ExecutableEndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecutableEndTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateShardingSubTicket) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateShardingSubTicket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateShardingSubTicket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExecDataCleanTicket) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExecDataCleanTicket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExecDataCleanTicket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Source == nil {
				m.Source = &DataSource{}
			}
			if err := m.Source.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TicketType", wireType)
			}
			m.TicketType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TicketType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecuteType", wireType)
			}
			m.ExecuteType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecuteType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SqlText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SqlText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecutableStartTime", wireType)
			}
			m.ExecutableStartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecutableStartTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecutableEndTime", wireType)
			}
			m.ExecutableEndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecutableEndTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExecShardingFreeLockDDLTicket) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ExecShardingFreeLockDDLTicket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ExecShardingFreeLockDDLTicket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BizContext", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.BizContext = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TenantID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TenantID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserID", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserID = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Source == nil {
				m.Source = &DataSource{}
			}
			if err := m.Source.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TicketType", wireType)
			}
			m.TicketType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TicketType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecuteType", wireType)
			}
			m.ExecuteType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecuteType |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SqlText", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWorkflow
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWorkflow
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SqlText = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecutableStartTime", wireType)
			}
			m.ExecutableStartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecutableStartTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExecutableEndTime", wireType)
			}
			m.ExecutableEndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExecutableEndTime |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipWorkflow(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthWorkflow
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipWorkflow(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowWorkflow
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowWorkflow
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthWorkflow
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupWorkflow
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthWorkflow
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthWorkflow        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowWorkflow          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupWorkflow = fmt.Errorf("proto: unexpected end of group")
)
