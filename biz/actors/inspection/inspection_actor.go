package inspection

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"os"
	"runtime"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"time"

	"code.byted.org/gopkg/thrift"
	"code.byted.org/infcs/dbw-mgr/biz/service/full_sql"
	"code.byted.org/infcs/dbw-mgr/biz/service/space_analysis"

	"github.com/ahmetb/go-linq/v3"

	"github.com/volcengine/volc-sdk-golang/service/tls"
	"github.com/volcengine/volc-sdk-golang/service/tls/pb"
	"go.uber.org/dig"

	"code.byted.org/infcs/dbw-mgr/biz/actors/inspection/pdf"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	biz_model "code.byted.org/infcs/dbw-mgr/biz/model"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	crossauth "code.byted.org/infcs/dbw-mgr/biz/service/cross_service_authorization"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/mysql"
	"code.byted.org/infcs/dbw-mgr/biz/service/diagnosis"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	inspection3 "code.byted.org/infcs/dbw-mgr/biz/service/inspection"
	"code.byted.org/infcs/dbw-mgr/biz/service/message"
	slowTls "code.byted.org/infcs/dbw-mgr/biz/service/tls"
	"code.byted.org/infcs/dbw-mgr/biz/service/tos"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	dbw_utils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	rdsModel_v2_new "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2022-01-01/kitex_gen/model/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
)

type TaskActorIn struct {
	dig.In
	Conf              config.ConfigProvider
	Repo              repository.InspectionRepo
	IdgenSvc          idgen.Service
	Ds                datasource.DataSourceService
	RootDiagItem      diagnosis.RootDiagItem
	ActorClient       cli.ActorClient
	C3ConfProvider    c3.ConfigProvider
	Loca              location.Location
	InspectionService inspection3.InspectionService
	CrossAuthSvc      crossauth.CrossServiceAuthorizationService
	MsgService        message.MsgService
	SpaceAnalysis     space_analysis.SqlSpaceAnalysisService
	FullSqlService    full_sql.FullSqlService
}

// NewTaskActor 执行巡检的任务actor
//func NewTaskActor(p TaskActorIn) types.VirtualPersistenceProducer {
//	return types.VirtualPersistenceProducer{
//		Kind: consts.InspectionActorKind,
//		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
//			return &TaskActor{
//				state:        newTaskActorState(state),
//				cnf:          p.Conf,
//				repo:         p.Repo,
//				idgenSvc:     p.IdgenSvc,
//				ds:           p.Ds,
//				rootDiagItem: p.RootDiagItem,
//				actorClient:  p.ActorClient,
//			}
//		}),
//	}
//}

func NewTaskActor(p TaskActorIn) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.InspectionActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			return &TaskActor{
				state:             newTaskActorState([]byte{}),
				cnf:               p.Conf,
				repo:              p.Repo,
				idgenSvc:          p.IdgenSvc,
				ds:                p.Ds,
				rootDiagItem:      p.RootDiagItem,
				actorClient:       p.ActorClient,
				c3ConfProvider:    p.C3ConfProvider,
				loca:              p.Loca,
				inspectionService: p.InspectionService,
				crossAuthSvc:      p.CrossAuthSvc,
				msgService:        p.MsgService,
				spaceAnalysis:     p.SpaceAnalysis,
				fullSqlService:    p.FullSqlService,
			}
		}),
	}
}

type TaskActor struct {
	state             *TaskActorState
	cnf               config.ConfigProvider
	repo              repository.InspectionRepo
	idgenSvc          idgen.Service
	ds                datasource.DataSourceService
	rootDiagItem      diagnosis.RootDiagItem
	actorClient       cli.ActorClient
	inspectionResult  *entity.InspectionResultInfo
	status            shared.InspectionActorStatus
	c3ConfProvider    c3.ConfigProvider
	loca              location.Location
	inspectionService inspection3.InspectionService
	crossAuthSvc      crossauth.CrossServiceAuthorizationService
	msgService        message.MsgService
	spaceAnalysis     space_analysis.SqlSpaceAnalysisService
	fullSqlService    full_sql.FullSqlService
}

type TaskActorState struct {
	TenantId string
	RiskNum  int
}

func newTaskActorState(bytes []byte) *TaskActorState {
	ts := &TaskActorState{}
	err := json.Unmarshal(bytes, ts)
	if err != nil {
		return nil
	}
	return ts
}

func (t *TaskActor) GetState() []byte {
	state, _ := json.Marshal(t.state)
	return state
}

func (t *TaskActor) Process(ctx types.Context) {
	if t.state == nil {
		t.state = new(TaskActorState)
	}
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		// 启动时候做的事情
		t.protectUserCall(ctx, func() {
			t.OnStart(ctx)
		})
	case *shared.CreateInspectionTask:
		t.protectUserCall(ctx, func() {
			t.createInspectionTask(ctx, msg)
		})
	case *actor.ReceiveTimeout:
		t.protectUserCall(ctx, func() {
			t.checkTimeout(ctx)
		})
	case *shared.GetInspectionActorStatusReq:
		t.protectUserCall(ctx, func() {
			t.getInspectionActorStatus(ctx)
		})
	}
}

func (t *TaskActor) checkTimeout(ctx types.Context) {
	if t.status == shared.Doing {
		return
	}
	// 过个2s再检查一下，如果不是doing就stop掉
	time.Sleep(2 * time.Second)
	if t.status != shared.Doing {
		ctx.Stop(ctx.Self())
	}
}

func (t *TaskActor) getInspectionActorStatus(ctx types.Context) {
	ctx.Respond(&shared.GetInspectionActorStatusResp{Status: t.status})
}

func (t *TaskActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call TaskActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

// OnStart 这里做一些状态修复的动作
func (t *TaskActor) OnStart(ctx types.Context) {
	t.inspectionResult = &entity.InspectionResultInfo{
		Basic: &entity.InspectionBasicInfo{
			InspectAgg: &model.InspectAgg{},
		},
		Report: &entity.InspectionReportInfo{
			ScoreDetail: []*model.MetricScore{},
			DataMetrics: []*model.InspectionDataMetric{},
		},
		SlowLogs:                  []*model.InspectionSlowLog{},
		InspectionNodeResultInfos: map[string]*entity.InspectionNodeResultInfo{},
		InspectionNodeItems:       map[string]*model.InspectionNodeItem{},
		LastOneNodeAgg:            &model.InspectAgg{},
	}
	t.status = shared.Undo
	log.Info(ctx, "TaskActor %s start", ctx.GetName())
}

func (t *TaskActor) retryCreateTask(ctx types.Context, msg *shared.CreateInspectionTask) {
	// 不是自动任务，不重试
	if msg.InspectionType != int32(model.InspectionType_Auto) {
		ctx.Stop(ctx.Self())
		return
	}
	time.Sleep(10 * time.Second)
	log.Info(ctx, "taskId:%d, start to retry inspection", msg.TaskId)
	t.status = shared.Finished
	// 重试调用方法，改为发送消息
	ctx.Send(ctx.Self(), msg)
}

func (t *TaskActor) createInspectionTask(ctx types.Context, msg *shared.CreateInspectionTask) {
	t.status = shared.Doing
	t.inspectionResult.Basic.InspectionExecuteTime = time.Now().UnixMilli()
	t.state.TenantId = msg.TenantId
	ctx.WithValue("biz-context", &fwctx.BizContext{
		TenantID: msg.TenantId,
	})
	log.Info(ctx, "inspection: begin to create inspection task,msg is %s", utils.Show(msg))
	// 0.更新历史任务的实例名称,为了解决用户在MySQL列表修改了实例名字之后,找不到历史巡检记录的bug
	log.Info(ctx, "inspection: update task status")
	t.updateInstanceNameByInstanceID(ctx, msg.InstanceType, msg.InstanceId, msg.TenantId)

	// 1.检查有没有超时，如果超时了，就不做事，直接结束
	if msg.InspectionType == int32(model.InspectionType_Auto) && t.isOutTime(msg) {
		log.Warn(ctx, "inspection: InstanceId:%s Inspection time out", msg.InstanceId)
		ctx.Stop(ctx.Self())
		return
	}
	// 2.检查实例状态是否为可用状态
	if !t.isInstanceRunning(ctx, msg.InstanceType, msg.InstanceId, msg.TenantId) {
		t.retryCreateTask(ctx, msg)
	}
	// 3.写数据库(insert ignore)，如果不能insert ignore，需要把插入数据库上移，移动到调用actor之前
	log.Info(ctx, "inspection: insert one task")
	err := t.insertOneTask(ctx, msg)
	if err != nil {
		log.Warn(ctx, "inspection: insert one task error, err:%v", err)
		t.retryCreateTask(ctx, msg)
		return
	}
	// 4.发起巡检
	t.inspectionResult.TaskId = msg.TaskId
	// 更新任务状态
	err = t.updateTaskStatus(ctx, int(model.InspectionStatus_InspectionRunning), msg.TaskId)
	if err != nil {
		log.Warn(ctx, "inspection: update task status error:%s", err.Error())
		_ = t.updateTaskStatus(ctx, int(model.InspectionStatus_InspectionFailed), msg.TaskId)
		t.retryCreateTask(ctx, msg)
		return
	}
	log.Info(ctx, "inspection: do inspection")
	err = t.doInspection(ctx, msg)
	if err != nil {
		log.Warn(ctx, "inspection: do inspection error: %s", err.Error())
		_ = t.updateTaskStatus(ctx, int(model.InspectionStatus_InspectionFailed), msg.TaskId)
		t.retryCreateTask(ctx, msg)
		return
	}
	// 5.更新结果
	log.Info(ctx, "inspection: update result,msg is %s", utils.Show(msg))
	err = t.updateResult(ctx, msg)
	if err != nil {
		log.Warn(ctx, "inspection: update result error")
		_ = t.updateTaskStatus(ctx, int(model.InspectionStatus_InspectionFailed), msg.TaskId)
		t.retryCreateTask(ctx, msg)
		return
	}
	t.CreateAndSavePdf(ctx)
	// 执行完，关闭actor
	t.status = shared.Finished
	t.sendMessage(ctx)
	ctx.Stop(ctx.Self())
}

func (t *TaskActor) CreateAndSavePdf(ctx types.Context) {
	if err := pdf.CreatePdfFile(ctx, t.inspectionResult); err != nil {
		log.Warn(ctx, "CreatePdfFile error:%s", err.Error())
		return
	}
	tosConfig, err := t.createTosConfig(ctx)
	if err != nil {
		log.Warn(ctx, "createTosConfig error:%s", err.Error())
		return
	}
	if err = pdf.UploadPdfToTos(ctx, t.inspectionResult, tosConfig); err != nil {
		log.Warn(ctx, "UploadPdfToTos error:%s", err.Error())
	}
	// 删除文件
	t.DeletePdfFile(ctx)
}

func (t *TaskActor) checkAndGetMessageReceivers(ctx types.Context) (bool, []string) {
	//1.隔离，哪些租户需要发
	//2.租户需要发给哪些user
	isTenantSendMessage := false
	var receivers []string

	// messageReceiver := []*MessageReceiver{{TenantId: "2100067216", UserIds: []string{"1624798"}}}
	var messageReceiver []*MessageReceiver
	inspectionReceivers := t.cnf.Get(ctx).InspectionReceivers
	err := json.Unmarshal([]byte(inspectionReceivers), &messageReceiver)
	if err != nil {
		log.Warn(ctx, "unmarshal InspectionReceivers error:%s ", err.Error())
		return false, []string{}
	}

	for _, receiver := range messageReceiver {
		if strings.TrimSpace(receiver.TenantId) == strings.TrimSpace(t.state.TenantId) {
			receivers = receiver.UserIds
			isTenantSendMessage = true
			break
		}
	}
	return isTenantSendMessage, receivers
}

func (t *TaskActor) sendMessage(ctx types.Context) {
	// 检查当前租户是否在配置里
	isTenantSendMessage, receivers := t.checkAndGetMessageReceivers(ctx)
	if !isTenantSendMessage {
		return
	}
	// 获取文件的下载地址
	url, err := t.GetPdfTosUrl(ctx)
	if err != nil {
		log.Warn(ctx, "GetPdfTosUrl error:%s", err.Error())
		return
	}
	req := &biz_model.SendMessageByEventReq{
		EventName: message.EventByInspectionReport,
		EventDetail: biz_model.EventDetail{InspectionReport: biz_model.InspectionReport{
			InstanceType: t.inspectionResult.Report.InstanceType,
			InstanceId:   t.inspectionResult.Report.InstanceId,
			ReportUrl:    url,
			RiskItemNum:  t.state.RiskNum,
		}},
		ReceiverList: receivers,
	}
	err = t.msgService.SendMessageByEvent(ctx, req)
	if err != nil {
		log.Warn(ctx, "send report message error:%s", err.Error())
	}
}

func (t *TaskActor) GetPdfTosUrl(ctx types.Context) (string, error) {

	tosClient, err := t.initTOSConnection(ctx)
	if err != nil {
		log.Warn(ctx, "inti tos connection error:%s", err.Error())
		return "", err
	}
	c3cfg := t.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	bucketName := c3cfg.TOSBucketName
	objectName := fmt.Sprintf("%s/%s/%d.pdf", "dbw-report", t.inspectionResult.Report.InstanceId, t.inspectionResult.TaskId)
	url, err := tosClient.DescribeDownloadUrl(ctx, bucketName, objectName)
	if err != nil {
		log.Warn(ctx, "DescribeDownloadUrl error:%s", err.Error())
		return "", err
	}
	cleanedUrl := strings.TrimPrefix(url, "https://")
	return cleanedUrl, nil
}

func (t *TaskActor) initTOSConnection(ctx context.Context) (tos.Client, error) {
	var connectionInfo *tos.ConnectionInfo
	c3cfg := t.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	tosAK := c3cfg.TOSServiceAccessKey
	tosSK := c3cfg.TOSServiceSecretKey
	tosRegion := c3cfg.TOSServiceRegion
	tosEndpoint := c3cfg.TOSServiceEndpoint
	tosEndpoint = strings.Replace(tosEndpoint, "-s3", "", 1)
	connectionInfo = &tos.ConnectionInfo{
		Endpoint:        tosEndpoint,
		AccessKeyID:     tosAK,
		AccessKeySecret: tosSK,
		Region:          tosRegion,
	}

	client := tos.NewTOSClient(ctx, connectionInfo)
	return client, nil
}

func (t *TaskActor) DeletePdfFile(ctx types.Context) {
	dir := pdf.GetInspectionFilePath(t.inspectionResult)
	// 使用 os.RemoveAll 删除文件夹及其内容
	err := os.RemoveAll(dir)
	if err != nil {
		log.Warn(ctx, "delete %s error:%s", dir, err.Error())
	}
}

func (t *TaskActor) createTosConfig(ctx types.Context) (*pdf.TosConfig, error) {
	c3cfg := t.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	tenantId := c3cfg.DbwAccountId
	conf := t.cnf.Get(ctx)
	credential, err := t.crossAuthSvc.AssumeRoleBy(ctx, tenantId, conf.ServiceLinkRuleDataMigration)
	if err != nil {
		log.Warn(ctx, "Get temp credential failed %v", err)
		return nil, consts.ErrorOf(model.ErrorCode_GetTempCredentialFailed)
	}

	tos := &pdf.TosConfig{
		InstanceId: t.inspectionResult.Report.InstanceId,
		Region:     c3cfg.TOSServiceRegion,
		Endpoint:   c3cfg.TOSServiceEndpoint,
		Id:         credential.GetAK(),
		Secret:     credential.GetSK(),
		Token:      credential.GetToken(),
		Bucket:     c3cfg.TOSBucketName,
	}
	return tos, nil
}

func (t *TaskActor) isInstanceRunning(ctx types.Context, instanceType shared.DataSourceType, instanceId string, tenantId string) bool {
	// 将ctx的租户id设置为传入的租户id
	c := fwctx.GetBizContext(ctx)
	c.TenantID = tenantId
	inst, err := t.ds.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
		InstanceId: instanceId,
		Type:       instanceType,
	})
	if err != nil {
		log.Warn(ctx, "inspection: rds api err %s", err.Error())
		return false
	}
	if inst != nil && inst.InstanceStatus == rdsModel_v2_new.InstanceStatus_Running.String() {
		return true
	}
	return false
}

func (t *TaskActor) isOutTime(msg *shared.CreateInspectionTask) bool {
	hour := time.Now().Hour()
	minute := time.Now().Minute()
	second := time.Now().Second()
	currentTime := int64(hour*3600+minute*60+second) * 1000
	return currentTime < msg.ExecuteStartTime || currentTime > msg.ExecuteEndTime
}

func (t *TaskActor) insertOneTask(ctx types.Context, msg *shared.CreateInspectionTask) error {
	err := t.repo.CreateTask(ctx, &entity.InspectionTask{
		ID:                    msg.TaskId,
		InstanceType:          msg.InstanceType.String(),
		InstanceId:            msg.InstanceId,
		InstanceName:          msg.InstanceName,
		InspectionStatus:      model.InspectionStatus_InspectionUnStart,
		InspectionType:        model.InspectionType(msg.InspectionType),
		InspectionExecuteTime: t.inspectionResult.Basic.InspectionExecuteTime,
		InspectionStartTime:   msg.InspectionStartTime,
		InspectionEndTime:     msg.InspectionEndTime,
		LastOneNodeAgg:        msg.LastOneNodeAgg,
		TenantId:              msg.TenantId,
		RegionId:              msg.RegionId,
		CreatedAt:             time.Now().UnixMilli(),
		UpdatedAt:             time.Now().UnixMilli(),
		Deleted:               0,
	})
	if err != nil {
		log.Warn(ctx, "inspection: insert inspection task to db error：%v ", err)
		return err
	}
	return nil
}

type inspectItemFunc func(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error

func (t *TaskActor) ChooseInspectPlan(typ shared.DataSourceType) []inspectItemFunc {
	switch typ {
	case shared.MySQL:
		return []inspectItemFunc{
			// metric
			t.inspectionCpuReportInfo,
			t.inspectionMemReportInfo,
			t.inspectionDiskReportInfo,
			t.inspectionConnectedReportInfo,
			t.inspectionConnRatioReportInfo,
			t.inspectionQpsReportInfo,
			t.inspectionTpsReportInfo,
			t.inspectionBpHitInfo,
			// other
			t.inspectionSlowLogInfo,           // 获取慢日志信息
			t.inspectDescribeAggregateDialogs, // TOP SQL（基于聚合会话）
			t.inspectDescribeFullSql,          // TOP SQL（基于全量SQL洞察）
			t.inspectDescribeTableSpace,       // TOP 表（基于空间分析)
			t.inspectionTableSpaceAutoIncr,    // TOP10 自增列使用情况
			t.inspectionSlowLogsMetric,        // 获取慢日志metric
			t.inspectNoPrimaryKeyTables,       // 无主键表
		}
	case shared.VeDBMySQL:
		return []inspectItemFunc{
			// metric
			t.inspectionCpuReportInfo,
			t.inspectionMemReportInfo,
			t.inspectionDiskReportInfo,
			t.inspectionConnectedReportInfo,
			t.inspectionConnRatioReportInfo,
			t.inspectionQpsReportInfo,
			t.inspectionTpsReportInfo,
			t.inspectionBpHitInfo,
			// other
			t.inspectionSlowLogInfo,           // 获取慢日志信息
			t.inspectDescribeAggregateDialogs, // TOP SQL（基于聚合会话）
			t.inspectDescribeFullSql,          // TOP SQL（基于全量SQL洞察）
			t.inspectDescribeTableSpace,       // TOP 表（基于空间分析)
			t.inspectionTableSpaceAutoIncr,    // TOP10 自增列使用情况
			t.inspectionSlowLogsMetric,        // 获取慢日志metric
			t.inspectNoPrimaryKeyTables,       // 无主键表
		}
	case shared.Postgres:
		return []inspectItemFunc{
			// metric
			t.inspectionCpuReportInfo,
			t.inspectionMemReportInfo,
			t.inspectionDiskReportInfo,
			t.inspectionConnectedReportInfo,
			t.inspectionConnRatioReportInfo,
			t.inspectionQpsReportInfo,
			t.inspectionTpsReportInfo,
			t.inspectionBpHitInfo,
			// other
			t.inspectionSlowLogInfo,           // 获取慢日志信息
			t.inspectDescribeAggregateDialogs, // TOP SQL（基于聚合会话）
			t.inspectDescribeFullSql,          // TOP SQL（基于全量SQL洞察）
			t.inspectionTableSpaceAutoIncr,    // TOP10 自增列使用情况
			t.inspectionSlowLogsMetric,        // 获取慢日志metric
		}
	case shared.Redis:
		return []inspectItemFunc{
			// metric
			t.inspectionCpuReportInfo,
			t.inspectionMemReportInfo,
			t.inspectionConnRatioReportInfo,
			t.inspectionQpsReportInfo,
			t.inspectionInputMetric,
			t.inspectionOutputMetric,
			// other
			t.inspectionRedisInspectionBigKeysByLen,     // Top10 获取BigKeys （长度）
			t.inspectionRedisInspectionBigKeysBySize,    // Top10 获取BigKeys （Size）
			t.inspectionRedisInspectionHotKeysItemsInfo, // 获取HotKeys
		}
	default:
		panic("unknown type")
	}
}

func (t *TaskActor) doInspectionTask(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	// 获取实例的基本信息
	err := t.formatInstanceBasicInfo(ctx, msg, info)
	if err != nil {
		log.Warn(ctx, "inspection: formatInstanceBasicInfo err:%s", err.Error())
		return err
	}
	// 获取基本的检查信息
	info.GetBasic().InspectionExecuteTime = time.Now().UnixMilli()
	info.GetBasic().InspectionStartTime = msg.InspectionStartTime
	info.GetBasic().InspectionEndTime = msg.InspectionEndTime
	// 巡检plan
	for index, itemFunc := range t.ChooseInspectPlan(msg.InstanceType) {
		err = dbw_utils.PanicSafety(ctx, func() error {
			return itemFunc(ctx, msg, info)
		})
		if err != nil {
			log.Warn(ctx, "inspection: id %d error:%s", index, err.Error())
		}
	}
	if msg.InstanceType != shared.Redis && msg.NodeId == "" {
		// 当前巡检数据库类型，实例级别巡检，不巡检指标
		return nil
	}
	// 健康得分
	info.GetBasic().InspectAgg.HealthScore, err = t.getHealthScore(ctx, msg)
	if err != nil {
		log.Warn(ctx, "inspection: getHealthScore error:%s", err.Error())
	}
	log.Info(ctx, "inspection: health score is %d", info.GetBasic().InspectAgg.HealthScore)
	// 获取扣分详情
	err = t.getInspectionScore(ctx, msg, info)
	if err != nil {
		log.Warn(ctx, "inspection: getInspectionScore err:%s", err.Error())
	}
	return nil
}

func doInspectionWrapper(e interface{}) (result interface{}, err error) {
	if e == nil {
		return
	}
	req := e.(*doInspectionWrapperReq)

	defer func() {
		if r := recover(); r != nil || err != nil {
			const size = 64 << 10
			buf := make([]byte, size)
			buf = buf[:runtime.Stack(buf, false)]
			log.Warn(req.ctx, "doInspectionWrapper panic %v %s req %s", r, buf, utils.Show(req))
			req.info.SetStatus(model.InspectionStatus_InspectionFailed)
			if req.item != nil {
				req.item.TaskStatus = model.InspectionStatus_InspectionFailed
				req.item.ErrMsg = consts.ErrorWithParam(model.ErrorCode_SystemError).Error()
			}
		} else {
			if req.item != nil {
				req.item.TaskStatus = model.InspectionStatus_InspectionSuccess
			}
		}
	}()

	err = req.t.doInspectionTask(req.ctx, req.msg, req.info)
	if err != nil {
		req.info.SetStatus(model.InspectionStatus_InspectionFailed)
	}
	req.info.SetStatus(model.InspectionStatus_InspectionSuccess)
	if req.item != nil {
		req.item.Agg = req.info.GetBasic().InspectAgg
	}
	return
}

type doInspectionWrapperReq struct {
	msg  *shared.CreateInspectionTask
	ctx  types.Context
	t    *TaskActor
	info entity.InspectResultInfo
	item *model.InspectionNodeItem
}

func (t *TaskActor) doInspection(ctx types.Context, msg *shared.CreateInspectionTask) error {
	// 获取实例信息
	resp, err := t.ds.ListInstanceNodes(ctx, &datasource.ListInstanceNodesReq{
		DSType:     conv.ToSharedType(model.DSType(msg.InstanceType)),
		InstanceId: msg.InstanceId,
	})
	if err != nil {
		log.Warn(ctx, "failed to list instance nodes, err=%v", err)
		return err
	}
	pool := dbw_utils.RoutinePool{}
	pool.InitPool(3, 20, doInspectionWrapper, func(i interface{}) {
		if i != nil {
			err = i.(error)
		}
	})
	pool.Start(ctx)

	if msg.NodeId == "" {
		// 如果没指定NodeId巡检，就先巡检下实例级别
		pool.AddTask(&doInspectionWrapperReq{
			msg:  msg,
			ctx:  ctx,
			t:    t,
			info: t.inspectionResult,
		})
	}

	// 以下为节点级别的巡检，无需节点级别巡检的实例，直接返回
	if msg.InstanceType == shared.Redis {
		pool.Stop()
		t.inspectionResult.LastOneNodeAgg = t.inspectionResult.Basic.InspectAgg
		return nil
	}

	for _, node := range resp.Nodes {
		if msg.NodeId != "" && msg.NodeId != node.NodeId {
			continue
		}
		t.inspectionResult.InspectionNodeResultInfos[node.NodeId] = &entity.InspectionNodeResultInfo{
			TaskId: t.inspectionResult.TaskId,
			NodeId: node.NodeId,
			Status: model.InspectionStatus_InspectionRunning,
			Basic: &entity.InspectionBasicInfo{
				InspectAgg: &model.InspectAgg{},
			},
			Report: &entity.InspectionReportInfo{
				ScoreDetail: []*model.MetricScore{},
				DataMetrics: []*model.InspectionDataMetric{},
			},
		}
		t.inspectionResult.InspectionNodeItems[node.NodeId] = &model.InspectionNodeItem{
			TaskId:              fmt.Sprintf("%d", t.inspectionResult.TaskId),
			NodeTaskId:          node.NodeId,
			NodeInfoObject:      node,
			InstanceId:          msg.InstanceId,
			TaskStatus:          model.InspectionStatus_InspectionRunning,
			ExecuteTime:         fmt.Sprintf("%d", msg.ExecuteTime),
			TaskType:            model.InspectionType(msg.InspectionType),
			InspectionStartTime: fmt.Sprintf("%d", msg.InspectionStartTime),
			InspectionEndTime:   fmt.Sprintf("%d", msg.InspectionEndTime),
			Agg:                 &model.InspectAgg{},
		}
		nodeMsg := &shared.CreateInspectionTask{}
		nodeMsg.InstanceId = msg.InstanceId
		nodeMsg.InstanceName = msg.InstanceName
		nodeMsg.InspectionStartTime = msg.InspectionStartTime
		nodeMsg.InspectionEndTime = msg.InspectionEndTime
		nodeMsg.InspectionType = msg.InspectionType
		nodeMsg.InstanceType = msg.InstanceType
		nodeMsg.ExecuteStartTime = msg.ExecuteStartTime
		nodeMsg.ExecuteEndTime = msg.ExecuteEndTime
		nodeMsg.ExecuteTime = msg.ExecuteTime
		nodeMsg.TaskId = msg.TaskId
		nodeMsg.TenantId = msg.TenantId
		nodeMsg.RegionId = msg.RegionId
		nodeMsg.NodeId = node.NodeId
		pool.AddTask(&doInspectionWrapperReq{
			msg:  nodeMsg,
			ctx:  ctx,
			t:    t,
			info: t.inspectionResult.InspectionNodeResultInfos[node.NodeId],
			item: t.inspectionResult.InspectionNodeItems[node.NodeId],
		})
	}
	pool.Stop()

	// 计算最差实例统计信息
	if len(t.inspectionResult.InspectionNodeItems) <= 0 {
		return nil
	}
	var items []*model.InspectionNodeItem
	for _, item := range t.inspectionResult.InspectionNodeItems {
		items = append(items, item)
	}
	linq.From(items).OrderByT(func(item *model.InspectionNodeItem) int32 {
		return item.Agg.HealthScore
	}).ToSlice(&items)

	for _, item := range items {
		if item.TaskStatus != model.InspectionStatus_InspectionSuccess {
			continue
		}
		t.inspectionResult.LastOneNodeAgg = item.Agg
		t.inspectionResult.Basic.InspectAgg = item.Agg
		break
	}
	return nil
}

func (t *TaskActor) updateResult(ctx types.Context, msg *shared.CreateInspectionTask) error {
	// 1.tls插入报告表数据
	client := t.createTlsClient(ctx)
	_, err := client.PutLogs(t.formatTlsLogs(ctx))
	if err != nil {
		log.Warn(ctx, "inspection: write tls error:%s", err.Error())
		return err
	}
	// 2.更新任务表的结果+状态
	if err := t.updateInspectionResult(ctx); err != nil {
		log.Warn(ctx, "inspection: updateInspectionResult error:%s", err.Error())
		return err
	}
	return nil
}

func (t *TaskActor) createTlsClient(ctx types.Context) tls.Client {
	c3cfg := t.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)

	Ak := c3cfg.TLSServiceAccessKey
	Sk := c3cfg.TLSServiceSecretKey
	regionId := t.loca.RegionID()
	tlsEndpoint := fmt.Sprintf(consts.TLSEndpointTemplate, regionId)
	return tls.NewClient(tlsEndpoint, Ak, Sk, "", regionId)
}

func (t *TaskActor) formatTlsLogs(ctx types.Context) *tls.PutLogsRequest {
	inspectionReportJsonBytes, _ := json.Marshal(t.inspectionResult)
	inspectorLog := &pb.Log{
		Time: t.inspectionResult.Basic.InspectionExecuteTime / 1000,
		Contents: []*pb.LogContent{
			{Key: "inspectionId", Value: fmt.Sprintf("%d", t.inspectionResult.TaskId)},
			{Key: "inspectionValue", Value: string(inspectionReportJsonBytes)},
		},
	}
	logs := []*pb.Log{inspectorLog}
	logGroup := &pb.LogGroup{
		Logs: logs,
	}
	cnf := t.cnf.Get(ctx)
	request := &tls.PutLogsRequest{
		TopicID: cnf.InspectionTopicId,
		// TopicID: "5fc8990f-838b-4b0f-963a-c9a565455a90",
		LogBody: &pb.LogGroupList{LogGroups: []*pb.LogGroup{logGroup}},
	}
	return request
}

func (t *TaskActor) overWriteIfHasNodeInspect(entity *entity.InspectionTask) {
	if t.inspectionResult.LastOneNodeAgg == nil {
		return
	}
	entity.HealthScore = int8(t.inspectionResult.LastOneNodeAgg.HealthScore)
	setValIfNotErr(t.inspectionResult.LastOneNodeAgg.Vals, entity)
}

func setValIfNotErr(vals []*model.InspectVal, entity *entity.InspectionTask) {
	valMap := cvtInspectionMetricListToMap(vals)
	if one, ok := valMap[model.InspectionMetricName_CpuUsage]; ok {
		if one.ErrMsg == "" {
			entity.CpuUsage = one.Val
		}
	}
	if one, ok := valMap[model.InspectionMetricName_MemUsage]; ok {
		if one.ErrMsg == "" {
			entity.MemUsage = one.Val
		}
	}
	if one, ok := valMap[model.InspectionMetricName_DiskUsage]; ok {
		if one.ErrMsg == "" {
			entity.DiskUsage = one.Val
		}
	}
	if one, ok := valMap[model.InspectionMetricName_ConnUsage]; ok {
		if one.ErrMsg == "" {
			entity.ConnUsage = one.Val
		}
	}
	if one, ok := valMap[model.InspectionMetricName_QPS]; ok {
		if one.ErrMsg == "" {
			entity.Qps = one.Val
		}
	}
	if one, ok := valMap[model.InspectionMetricName_TPS]; ok {
		if one.ErrMsg == "" {
			entity.Tps = one.Val
		}
	}
	if one, ok := valMap[model.InspectionMetricName_SlowLogNum]; ok {
		if one.ErrMsg == "" {
			entity.SlowLog = int32(one.Val)
		}
	}
}

func (t *TaskActor) updateInspectionResult(ctx types.Context) error {
	entity := &entity.InspectionTask{
		ID:                    t.inspectionResult.TaskId,
		InspectionExecuteTime: t.inspectionResult.Basic.InspectionExecuteTime,
		HealthScore:           int8(t.inspectionResult.Basic.InspectAgg.HealthScore),
		InspectionStatus:      model.InspectionStatus_InspectionSuccess,
	}
	setValIfNotErr(t.inspectionResult.Basic.InspectAgg.Vals, entity)
	t.overWriteIfHasNodeInspect(entity)
	err := t.repo.UpdateTaskInspectionValue(ctx, entity)
	if err != nil {
		log.Warn(ctx, "taskId:%d, UpdateTaskInspectionValue error, err:%v", t.inspectionResult.TaskId, err)
	}
	return err
}

func cvtInspectionMetricListToMap(metrics []*model.InspectVal) map[model.InspectionMetricName]*model.InspectVal {
	ret := make(map[model.InspectionMetricName]*model.InspectVal)
	for _, metric := range metrics {
		ret[metric.DiagItem] = metric
	}
	return ret
}

func (t *TaskActor) formatMetricUsageReq(msg *shared.CreateInspectionTask) *datasource.GetMetricUsageReq {
	start := time.UnixMilli(msg.InspectionStartTime)
	end := time.UnixMilli(msg.InspectionEndTime)
	// 这里对时间做一下处理
	req := &datasource.GetMetricUsageReq{
		DSType:     msg.InstanceType,
		InstanceId: msg.InstanceId,
		TenantId:   msg.TenantId,
		StartTime:  start,
		EndTime:    end,
	}
	if msg.NodeId != "" {
		req.NodeId = &msg.NodeId
	}
	return req
}

func (t *TaskActor) getHealthScore(ctx types.Context, msg *shared.CreateInspectionTask) (int32, error) {
	resScore := 100
	t.state.RiskNum = 0
	isRedis := msg.InstanceType == shared.Redis
	for itemName, diagItem := range t.rootDiagItem.Items {
		if isRedis && !(diagItem.Name() == "CPU使用率" || diagItem.Name() == "内存使用率" || diagItem.Name() == "连接使用率") {
			continue
		}
		log.Info(ctx, "inspection: taskId:%d, startTime:%d, endTime:%d", msg.TaskId, int32(msg.InspectionStartTime/1000), int32(msg.InspectionEndTime/1000))
		req := &model.DescribeDBHealthScoreReq{
			InstanceType: model.InstanceType(msg.InstanceType),
			InstanceId:   msg.InstanceId,
			StartTime:    int32(msg.InspectionStartTime / 1000), // 数据库里面的毫秒转化成秒
			EndTime:      int32(msg.InspectionEndTime / 1000),
			RegionId:     msg.RegionId,
		}
		if msg.NodeId != "" {
			req.NodeId = &msg.NodeId
		}
		score, err := diagItem.Score(ctx, req)
		if err != nil {
			log.Warn(ctx, "inspection: %s 获取得分详情失败，err:%s", itemName, err.Error())
		}
		if score != 0 {
			t.state.RiskNum++
		}
		resScore -= score
	}
	return int32(resScore), nil
}

func (t *TaskActor) formatInstanceBasicInfo(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	report := info.GetReport()
	if report.InstanceSpecification != "" {
		// 如果InstanceSpecification不为空，说明采集过了，不重复执行
		return nil
	}
	instanceDetail, err := t.ds.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
		Type:       msg.InstanceType,
		InstanceId: msg.InstanceId,
	})
	log.Info(ctx, "inspection-instanceDetail is %s", utils.Show(instanceDetail))
	if err != nil || instanceDetail == nil {
		log.Warn(ctx, "TaskId:%d 获取实例详情失败, err:%v", msg.TaskId, err)
		return err
	}
	report.InstanceType = msg.InstanceType.String()
	report.InstanceId = msg.InstanceId
	report.InstanceVersion = instanceDetail.DBEngineVersion
	report.InstanceSpecification = instanceDetail.NodeSpec
	log.Info(ctx, "inspection-formatInstanceBasicInfo is %s", utils.Show(report))
	return nil
}

func (t *TaskActor) formatMetricData(metricData *model.ItemDataResult_, msg *shared.CreateInspectionTask, err error) *model.InspectionDataMetric {
	inspectionItemMetric := &model.InspectionDataMetric{
		Unit:        metricData.Unit,
		Description: metricData.Group,
		Min:         math.MaxFloat64,
		NodeId:      metricData.NodeId,
	}
	if err != nil {
		inspectionItemMetric.ErrMsg = err.Error()
	}
	if metricData.DataPoints == nil || len(metricData.DataPoints) == 0 {
		inspectionItemMetric.Min = 0
	} else {
		sum := float64(0)
		// 前后数据点有时间上的错误，可能超过也可能不够，但因为是近似值，本身点也是按照interval进行拆分的
		// 所以我们将第一个点的时间设置为起始时间
		metricData.DataPoints[0].TimeStamp = int32(msg.InspectionStartTime / 1000)
		for _, value := range metricData.DataPoints {
			if value.TimeStamp < int32(msg.InspectionStartTime/1000) || value.TimeStamp > int32(msg.InspectionEndTime/1000) {
				continue
			}
			sum += value.Value
			inspectionItemMetric.Min = math.Min(value.Value, inspectionItemMetric.Min)
			inspectionItemMetric.Max = math.Max(value.Value, inspectionItemMetric.Max)
			inspectionItemMetric.DataPoints = append(inspectionItemMetric.DataPoints, value)
		}

		inspectionItemMetric.Avg = t.decimalFloat(sum / float64(len(inspectionItemMetric.DataPoints)))

	}
	return inspectionItemMetric
}

func (t *TaskActor) isMetricNotNeedInspected(metricGroup string, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) bool {
	if msg.InstanceType != shared.Redis && msg.NodeId == "" {
		// 当前巡检数据库类型，实例级别巡检，不巡检指标
		return true
	}
	for _, value := range info.GetReport().DataMetrics {
		// 已经巡检过的指标，不再重复巡检
		if value.Description == metricGroup {
			return true
		}
	}
	return false
}

func (t *TaskActor) inspectionCpuReportInfo(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	if t.isMetricNotNeedInspected(mysql.CpuUsage, msg, info) {
		return nil
	}
	req := t.formatMetricUsageReq(msg)
	// cpu
	cpuResp, err := t.ds.GetMaxCpuUsage(ctx, req)
	if err != nil {
		log.Warn(ctx, "inspection: GetMaxCpuUsage error:%s", err.Error())
	} else {
		log.Info(ctx, "inspection: GetMaxCpuUsage is %s", utils.Show(cpuResp))
		info.GetBasic().InspectAgg.Vals = append(info.GetBasic().InspectAgg.Vals, &model.InspectVal{
			DiagItem: model.InspectionMetricName_CpuUsage,
			Val:      cpuResp.Max,
		})
	}
	cpuInfo, err := t.ds.GetInspectionCpuMetric(ctx, req)
	info.GetReport().DataMetrics = append(info.GetReport().DataMetrics, t.formatMetricData(cpuInfo, msg, err))
	return err

}
func (t *TaskActor) inspectionMemReportInfo(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	if t.isMetricNotNeedInspected(mysql.MemUsage, msg, info) {
		return nil
	}
	req := t.formatMetricUsageReq(msg)
	// 内存
	memResp, err := t.ds.GetMaxMemUsage(ctx, req)
	if err != nil {
		log.Warn(ctx, "inspection: GetMaxMemUsage error:%s", err.Error())
	} else {
		log.Info(ctx, "inspection: GetMaxMemUsage is %s", utils.Show(memResp))
		info.GetBasic().InspectAgg.Vals = append(info.GetBasic().InspectAgg.Vals, &model.InspectVal{
			DiagItem: model.InspectionMetricName_MemUsage,
			Val:      memResp.Max,
		})
	}
	memInfo, err := t.ds.GetInspectionMemMetric(ctx, req)
	info.GetReport().DataMetrics = append(info.GetReport().DataMetrics, t.formatMetricData(memInfo, msg, err))
	return err
}
func (t *TaskActor) inspectionDiskReportInfo(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	if t.isMetricNotNeedInspected(mysql.DiskUsage, msg, info) {
		return nil
	}
	req := t.formatMetricUsageReq(msg)
	// 磁盘
	diskResp, err := t.ds.GetMaxDiskUsage(ctx, req)
	if err != nil {
		log.Warn(ctx, "inspection: GetMaxDiskUsage error:%s", err.Error())
	} else {
		log.Info(ctx, "inspection: GetMaxDiskUsage is %s", utils.Show(diskResp))
		info.GetBasic().InspectAgg.Vals = append(info.GetBasic().InspectAgg.Vals, &model.InspectVal{
			DiagItem: model.InspectionMetricName_DiskUsage,
			Val:      diskResp.Max,
		})
	}
	diskInfo, err := t.ds.GetInspectionDiskMetric(ctx, req)
	info.GetReport().DataMetrics = append(info.GetReport().DataMetrics, t.formatMetricData(diskInfo, msg, err))
	return err
}
func (t *TaskActor) inspectionConnectedReportInfo(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	if t.isMetricNotNeedInspected(mysql.Connected, msg, info) {
		return nil
	}
	req := t.formatMetricUsageReq(msg)
	// 连接数
	connectionResp, err := t.ds.GetMaxConnectionUsage(ctx, req)
	if err != nil {
		log.Warn(ctx, "inspection: GetMaxConnectionUsage error:%s", err.Error())
	} else {
		log.Info(ctx, "inspection: GetMaxConnectionUsage is %s", utils.Show(connectionResp))
		info.GetBasic().InspectAgg.Vals = append(info.GetBasic().InspectAgg.Vals, &model.InspectVal{
			DiagItem: model.InspectionMetricName_ConnUsage,
			Val:      connectionResp.Max,
		})
	}
	connectedInfo, err := t.ds.GetInspectionConnectedMetric(ctx, req)
	if err != nil {
		log.Warn(ctx, "GetInspectionDiskMetric error:%s", utils.Show(err))
	}
	info.GetReport().DataMetrics = append(info.GetReport().DataMetrics, t.formatMetricData(connectedInfo, msg, err))
	return nil
}
func (t *TaskActor) inspectionConnRatioReportInfo(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	if t.isMetricNotNeedInspected(mysql.ConnectedRatio, msg, info) {
		return nil
	}
	req := t.formatMetricUsageReq(msg)
	connRatioInfo, err := t.ds.GetInspectionConnRatioMetric(ctx, req)
	if err != nil {
		log.Warn(ctx, "GetInspectionDiskMetric error:%s", utils.Show(err))
	}
	info.GetReport().DataMetrics = append(info.GetReport().DataMetrics, t.formatMetricData(connRatioInfo, msg, err))
	return nil
}
func (t *TaskActor) inspectionTpsReportInfo(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	if t.isMetricNotNeedInspected(tps, msg, info) {
		return nil
	}
	req := t.formatMetricUsageReq(msg)
	// TPS
	tpsResp, err := t.ds.GetTpsUsage(ctx, req)
	if err != nil {
		log.Warn(ctx, "inspection: GetTpsUsage error:%s", err.Error())
	} else if tpsResp != nil {
		log.Info(ctx, "inspection: GetTpsUsage is %s", utils.Show(tpsResp))
		info.GetBasic().InspectAgg.Vals = append(info.GetBasic().InspectAgg.Vals, &model.InspectVal{
			DiagItem: model.InspectionMetricName_TPS,
			Val:      tpsResp.Max,
		})
	}
	tpsInfo, err := t.ds.GetInspectionTpsMetric(ctx, req)
	if err != nil {
		log.Warn(ctx, "GetInspectionDiskMetric error:%s", utils.Show(err))
	}
	info.GetReport().DataMetrics = append(info.GetReport().DataMetrics, t.formatMetricData(tpsInfo, msg, err))
	return nil
}
func (t *TaskActor) inspectionQpsReportInfo(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	if t.isMetricNotNeedInspected(qps, msg, info) {
		return nil
	}
	req := t.formatMetricUsageReq(msg)
	// QPS
	qpsResp, err := t.ds.GetQpsUsage(ctx, req)
	if err != nil {
		log.Warn(ctx, "inspection: GetQpsUsage error:%s", err.Error())
	} else if qpsResp != nil {
		log.Info(ctx, "inspection: GetQpsUsage is %s", utils.Show(qpsResp))
		info.GetBasic().InspectAgg.Vals = append(info.GetBasic().InspectAgg.Vals, &model.InspectVal{
			DiagItem: model.InspectionMetricName_QPS,
			Val:      qpsResp.Max,
		})
	}
	qpsInfo, err := t.ds.GetInspectionQpsMetric(ctx, req)
	if err != nil {
		log.Warn(ctx, "GetInspectionDiskMetric error:%s", utils.Show(err))
	}
	info.GetReport().DataMetrics = append(info.GetReport().DataMetrics, t.formatMetricData(qpsInfo, msg, err))
	return nil
}
func (t *TaskActor) inspectionBpHitInfo(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	if t.isMetricNotNeedInspected(mysql.InnodbBufferPoolUsage, msg, info) {
		return nil
	}
	req := t.formatMetricUsageReq(msg)
	qpsInfo, err := t.ds.GetInspectionBpHitMetric(ctx, req)
	if err != nil {
		log.Warn(ctx, "GetInspectionDiskMetric error:%s", utils.Show(err))
		return err
	}
	if qpsInfo == nil {
		return nil
	}
	info.GetReport().DataMetrics = append(info.GetReport().DataMetrics, t.formatMetricData(qpsInfo, msg, err))
	return nil
}
func (t *TaskActor) inspectionOutputMetric(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	if t.isMetricNotNeedInspected(datasource.ThroughOutputMetric, msg, info) {
		return nil
	}
	req := t.formatMetricUsageReq(msg)
	tpsInfo, err := t.ds.GetInspectionOutputMetric(ctx, req)
	if err != nil {
		log.Warn(ctx, "GetInspectionDiskMetric error:%s", utils.Show(err))
	}
	info.GetReport().DataMetrics = append(info.GetReport().DataMetrics, t.formatMetricData(tpsInfo, msg, err))
	return nil
}
func (t *TaskActor) inspectionInputMetric(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	if t.isMetricNotNeedInspected(datasource.ThroughInputMetric, msg, info) {
		return nil
	}
	req := t.formatMetricUsageReq(msg)
	tpsInfo, err := t.ds.GetInspectionInputMetric(ctx, req)
	if err != nil {
		log.Warn(ctx, "GetInspectionDiskMetric error:%s", utils.Show(err))
	}
	info.GetReport().DataMetrics = append(info.GetReport().DataMetrics, t.formatMetricData(tpsInfo, msg, err))
	return nil
}

func (t *TaskActor) inspectionSlowLogsMetric(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) (err error) {
	if t.isMetricNotNeedInspected(slowLog, msg, info) {
		return nil
	}
	var resp *shared.DescribeSlowLogTimeSeriesStatsResp
	slowLogMetric := t.initDefaultSlowLogDataMetric(msg)
	defer func() {
		if err != nil {
			slowLogMetric.ErrMsg = err.Error()
		}
		t.formatSlowLogResult(resp, slowLogMetric)
		info.GetReport().DataMetrics = append(info.GetReport().DataMetrics, slowLogMetric)
	}()
	analysisActorMsg := t.formatSlowLogsReportReq(msg)
	var slowLogClient slowTls.SlowLogClient
	slowLogClient, err = slowTls.GetSlowClient(ctx, t.cnf, t.c3ConfProvider, msg.InstanceType.String(), msg.InstanceId)
	if err != nil {
		log.Warn(ctx, "DescribeSlowLogTimeSeriesStats return actor error is %v", err)
		return err
	}
	resp, err = slowLogClient.DescribeSlogLogTimeSeriesStats(ctx, analysisActorMsg)
	if err != nil || resp == nil {
		log.Warn(ctx, "DescribeSlowLogTimeSeriesStats return actor error is %v", err)
		return err
	}
	return
}

type SlowLogDecrement []*shared.SlowLogCount

func (s SlowLogDecrement) Len() int { return len(s) }

func (s SlowLogDecrement) Swap(i, j int) { s[i], s[j] = s[j], s[i] }

func (s SlowLogDecrement) Less(i, j int) bool { return s[i].Timestamp < s[j].Timestamp }

func (t *TaskActor) formatSlowLogResult(resp *shared.DescribeSlowLogTimeSeriesStatsResp, slowLogMetric *model.InspectionDataMetric) {
	if resp == nil {
		return
	}
	sort.Sort(SlowLogDecrement(resp.SlowLogCountStats))
	sum := float64(0)
	if resp.SlowLogCountStats == nil || len(resp.GetSlowLogCountStats()) == 0 {
		slowLogMetric.Min = 0
	} else {
		slowLogRespIdx := 0
		metricIdx := 0
		for slowLogRespIdx < len(resp.SlowLogCountStats) && metricIdx < len(slowLogMetric.DataPoints) {
			if metricIdx == len(slowLogMetric.DataPoints)-1 {
				slowLogCount := t.decimalFloat(float64(resp.SlowLogCountStats[slowLogRespIdx].Count))
				sum += slowLogCount
				slowLogMetric.DataPoints[metricIdx].Value += slowLogCount
				slowLogMetric.Min = math.Min(slowLogMetric.Min, slowLogMetric.DataPoints[metricIdx].Value)
				slowLogMetric.Max = math.Max(slowLogMetric.Max, slowLogMetric.DataPoints[metricIdx].Value)
				break
			}
			if int32(resp.SlowLogCountStats[slowLogRespIdx].Timestamp) >= slowLogMetric.DataPoints[metricIdx].TimeStamp &&
				int32(resp.SlowLogCountStats[slowLogRespIdx].Timestamp) < slowLogMetric.DataPoints[metricIdx+1].TimeStamp {
				slowLogCount := t.decimalFloat(float64(resp.SlowLogCountStats[slowLogRespIdx].Count))
				sum += slowLogCount
				slowLogMetric.DataPoints[metricIdx].Value += slowLogCount
				slowLogMetric.Min = math.Min(slowLogMetric.Min, slowLogMetric.DataPoints[metricIdx].Value)
				slowLogMetric.Max = math.Max(slowLogMetric.Max, slowLogMetric.DataPoints[metricIdx].Value)
				slowLogRespIdx++
				continue
			}
			slowLogMetric.Min = math.Min(slowLogMetric.Min, slowLogMetric.DataPoints[metricIdx].Value)
			metricIdx++
		}
		slowLogMetric.Avg = t.decimalFloat(sum / float64(len(slowLogMetric.DataPoints)))
	}
}

func (t *TaskActor) initDefaultSlowLogDataMetric(msg *shared.CreateInspectionTask) *model.InspectionDataMetric {
	slowLogMetric := &model.InspectionDataMetric{
		DataPoints:  []*model.DataPoint{},
		Description: slowLog,
		Unit:        "",
		Min:         math.MaxFloat64,
	}
	if msg.NodeId != "" {
		slowLogMetric.NodeId = &msg.NodeId
	}
	// 补点，如果没有慢日志，慢日志接口不会返回数据
	// 举例：如果10:00 - 10:01 没有慢日志，返回的列表是空的，并不是{time:10:00, value:0}
	// 所以，我们先进行补点，将data补到240个，然后根据返回结果进行赋值
	interval := (msg.InspectionEndTime - msg.InspectionStartTime) / (240 * 1000)
	for i := msg.InspectionStartTime / 1000; i <= msg.InspectionEndTime/1000; i += interval {
		dataPoint := &model.DataPoint{
			TimeStamp: int32(i),
			Value:     0,
		}
		slowLogMetric.DataPoints = append(slowLogMetric.DataPoints, dataPoint)
	}
	return slowLogMetric
}

func (t *TaskActor) getInspectionScore(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	task := &entity.InspectionTask{
		InstanceType:        msg.InstanceType.String(),
		InstanceId:          msg.InstanceId,
		RegionId:            msg.RegionId,
		InspectionStartTime: msg.InspectionStartTime,
		InspectionEndTime:   msg.InspectionEndTime,
	}
	if msg.NodeId != "" {
		task.NodeId = &msg.NodeId
	}
	res, err := t.inspectionService.GetDBInspectionScores(ctx, task)
	if err != nil {
		return err
	}
	info.GetReport().ScoreDetail = res
	return nil
}

func (t *TaskActor) inspectionSlowLogInfo(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	// if len(t.inspectionResult.SlowLogs) == 0 || len(t.inspectionResult.SlowLogs) != 0 {
	if len(*info.GetSlowLogs()) != 0 {
		// 如果慢日志条数不为0，那么就认为慢日志已经采集过了，不进行重复采集
		// （慢日志本身可能就是0条，我们尽可能的减少重复动作就可以了）
		return nil
	}
	var (
		err              error
		slowLogClient    slowTls.SlowLogClient
		describeSlowlogs *shared.DescribeAggregateSlowLogsResp
		seriesStats      *shared.DescribeSlowLogTimeSeriesStatsResp
	)
	seriesStatsMap := make(map[string]*shared.DescribeSlowLogTimeSeriesStatsResp)

	defer func() {
		t.formatSlowLogsResp(ctx, msg.InstanceId, describeSlowlogs, seriesStatsMap, info, err)
	}()
	slowLogClient, err = slowTls.GetSlowClient(ctx, t.cnf, t.c3ConfProvider, msg.InstanceType.String(), msg.InstanceId)
	if err != nil {
		log.Warn(ctx, "get slow log info error:%v", err)
		return err
	}
	describeSlowlogs, err = slowLogClient.DescribeAggregateSlowLogs(ctx, &shared.DescribeAggregateSlowLogsReq{
		NodeId:         msg.NodeId,
		RegionId:       msg.RegionId,
		InstanceId:     msg.InstanceId,
		DataSourceType: msg.InstanceType,
		StartTime:      msg.InspectionStartTime / 1000,
		EndTime:        msg.InspectionEndTime / 1000,
		SortBy:         shared.DESC,
		OrderBy:        shared.ExecuteCount,
		Offset:         0,
		Limit:          10,
		SearchParam:    &shared.AggregateSlowLogSearchParam{},
	})
	if err != nil || describeSlowlogs == nil {
		e := consts.ErrorWithParam(model.ErrorCode_InternalError, "DescribeSlowLogsDetail error", err.Error())
		log.Warn(ctx, "DescribeSlowLogsDetail actor return error is %v", err)
		return e
	}
	for _, aggregateSlowLog := range describeSlowlogs.AggregateSlowLogs {
		log.Info(ctx, "inspection: DescribeSlogLogTimeSeriesStats slowlog is %s", utils.Show(aggregateSlowLog))
		seriesStats, err = slowLogClient.DescribeSlogLogTimeSeriesStats(
			ctx, &shared.DescribeSlowLogTimeSeriesStatsReq{
				RegionId:       msg.RegionId,
				InstanceId:     msg.InstanceId,
				DataSourceType: msg.InstanceType,
				StartTime:      msg.InspectionStartTime/1000 - 30*24*3600,
				EndTime:        msg.InspectionEndTime / 1000,
				Interval:       24 * 3600,
				SearchParam: &shared.SlowLogSearchParam{
					SQLTemplateID: aggregateSlowLog.SQLTemplateID,
				},
				Limit:  30,
				NodeId: msg.NodeId,
			},
		)
		if err != nil {
			log.Warn(ctx, "DescribeSlowLogTimeSeriesStats actor return error is %v", err)
		} else {
			log.Info(ctx, "inspection: slowlog seriesStats is %s", utils.Show(seriesStats))
			seriesStatsMap[aggregateSlowLog.SQLTemplateID] = seriesStats
		}
	}

	return nil
}

func (t *TaskActor) formatSlowLogsResp(ctx types.Context, InstanceId string, resp *shared.DescribeAggregateSlowLogsResp, seriesStatsResp map[string]*shared.DescribeSlowLogTimeSeriesStatsResp, info entity.InspectResultInfo, err error) {
	log.Info(ctx, "inspection: slowlog resp is %s", utils.Show(resp))
	if resp == nil || resp.AggregateSlowLogs == nil {
		return
	}
	val := &model.InspectVal{
		Val:      float64(resp.Total),
		DiagItem: model.InspectionMetricName_SlowLogNum,
	}
	if err != nil {
		val.ErrMsg = err.Error()
	}
	info.GetBasic().InspectAgg.Vals = append(info.GetBasic().InspectAgg.Vals, val)
	idx := int32(1)
	for _, value := range resp.AggregateSlowLogs {
		slowLogInfo := &model.InspectionSlowLog{TaskId: t.inspectionResult.TaskId}
		slowLogInfo.NumberId = idx
		slowLogInfo.InstanceId = InstanceId
		slowLogInfo.SqlTemplate = t.interceptStr10k(value.SQLTemplate)
		slowLogInfo.DBName = value.DB
		slowLogInfo.ExecuteUser = value.User
		slowLogInfo.ExecuteCount = int32(value.ExecuteCount)
		slowLogInfo.TotalQueryTime = value.QueryTimeStats.Total
		slowLogInfo.AvgQueryTime = value.QueryTimeStats.Average
		slowLogInfo.AvgLockTime = value.LockTimeStats.Average
		slowLogInfo.AvgRowsExamined = value.RowsExaminedStats.Average
		slowLogInfo.QueryTimeStats = conv.CvtStatisticResultSharedToModel(value.QueryTimeStats)

		var SQLKeepNums int32
		if _, ok := seriesStatsResp[value.SQLTemplateID]; ok {
			for _, stat := range seriesStatsResp[value.SQLTemplateID].SlowLogCountStats {
				if stat != nil && stat.Count > 0 {
					SQLKeepNums++
				}
			}
		}
		slowLogInfo.SQLKeepDays = SQLKeepNums
		*info.GetSlowLogs() = append(*info.GetSlowLogs(), slowLogInfo)
		idx++
	}
}

func (t *TaskActor) inspectionTableSpaceAutoIncr(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	var resp *shared.DescribeTableSpaceAutoIncrResp
	var err error
	defer func() {
		var res *model.DescribeTableSpaceAutoIncrResp
		if resp != nil {
			res = &model.DescribeTableSpaceAutoIncrResp{
				Total:             resp.Total,
				TableStatAutoIncr: conv.ConvertTableStatAutoIncr(resp.TableStatAutoIncr),
			}
		}
		info.SetDescribeTableSpaceAutoIncrResp(res, err)
	}()
	describeTableSpace := &shared.DescribeTableSpace{
		Product:    int64(msg.InstanceType),
		InstanceId: msg.InstanceId,
		RegionId:   msg.RegionId,
		PageNumber: 1,
		PageSize:   10,
		OrderItem:  "TableSpace",
		OrderRule:  "DESC",
	}

	resp, err = t.spaceAnalysis.DescribeTableSpaceAutoIncr(ctx, describeTableSpace)
	if err != nil || resp == nil {
		log.Warn(ctx, "DescribeTableSpaceAutoIncr error:%v", err)
		return err
	}
	log.Info(ctx, "DescribeTableSpaceAutoIncr-listTables-resp %s", utils.Show(resp))
	return nil
}

func (t *TaskActor) inspectDescribeTableSpace(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	var err error
	var res *model.DescribeTableSpaceResp
	defer func() {
		info.SetDescribeTableSpaceResp(res, err)
	}()
	describeTableSpace := shared.DescribeTableSpace{
		Product:    int64(msg.InstanceType),
		InstanceId: msg.InstanceId,
		RegionId:   msg.RegionId,
		PageNumber: 1,
		PageSize:   10,
		OrderItem:  "TableSpace",
		OrderRule:  "DESC",
	}

	var resp *shared.DescribeTableSpaceResp
	resp, err = t.spaceAnalysis.DescribeDataBaseTableSpace(ctx, &describeTableSpace)
	if err != nil || resp == nil{
		log.Warn(ctx, "DescribeTableSpace-enter2-failed %s", err)
		return err
	}
	res = t.ds.ConvertTableSpaceToModel(ctx, msg.InstanceType, resp)
	return nil
}

func (t *TaskActor) inspectNoPrimaryKeyTables(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	var err error
	var res *model.NoPrimaryKeyTableResp
	defer func() {
		info.SetNoPrimaryKeyTableResp(res, err)
	}()
	res = &model.NoPrimaryKeyTableResp{}
	var tables *datasource.ListTablesResp
	tables, err = t.spaceAnalysis.DescribeTableNoPrimaryKey(
		ctx, &model.DescribeDataBaseTablesReq{
			InstanceType: conv.ToModelInstanceType(msg.InstanceType),
			InstanceId:   msg.InstanceId,
			PageNumber:   thrift.Int32Ptr(1),
			PageSize:     thrift.Int32Ptr(10),
			RegionId:     &msg.RegionId,
		},
	)
	if err != nil || tables == nil {
		log.Warn(ctx, "DescribeTableNoPrimaryKey error:%s", err)
		return err
	}
	for _, table := range tables.Tables {
		describeTableSpace := shared.DescribeTableSpace{
			Product:    int64(msg.InstanceType),
			InstanceId: msg.InstanceId,
			RegionId:   msg.RegionId,
			PageNumber: 1,
			PageSize:   1,
			OrderItem:  "TableSpace",
			OrderRule:  "DESC",
			Database:   table.Schema,
			TableName:  table.Name,
		}
		var ret *shared.DescribeTableSpaceResp
		ret, err = t.spaceAnalysis.DescribeDataBaseTableSpace(ctx, &describeTableSpace)
		if err != nil || ret == nil {
			log.Warn(ctx, "DescribeTableSpace-enter2-failed %s", err)
			return err
		}
		stat := t.ds.ConvertTableSpaceToModel(ctx, msg.InstanceType, ret).TableStats[0]
		res.NoPrimaryKeyTables = append(res.NoPrimaryKeyTables, &model.NoPrimaryKeyTable{
			DB:              table.Schema,
			Name:            table.Name,
			TableSpace:      stat.TableSpace,
			TableSpaceRatio: stat.TableSpaceRatio,
			Engine:          stat.Engine,
			TableRows:       stat.TableRows,
		})

	}
	return nil
}

func (t *TaskActor) inspectDescribeAggregateDialogs(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	var resp *slowTls.DescribeAggregateDialogsResp
	var err error
	defer func() {
		var res *model.DescribeAggregateDialogsResp
		if resp != nil {
			res = &model.DescribeAggregateDialogsResp{
				AggregateDialogs: resp.Details,
				Total:            int32(resp.Total),
			}
		}
		info.SetDescribeAggregateDialogsResp(res, err)
	}()
	var dbInternalUsers map[string]string
	internalUsers := make([]string, 0)
	err = json.Unmarshal([]byte(t.cnf.Get(ctx).DBInternalUsers), &dbInternalUsers)
	if err != nil {
		log.Warn(ctx, "parse json str failed %+v", err)
		return consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
	} else {
		internalUsers = strings.Split(dbInternalUsers[msg.InstanceType.String()], ",")
	}
	rreq := &slowTls.DescribeAggregateDialogsReq{
		TenantId:      fwctx.GetTenantID(ctx),
		InstanceId:    msg.InstanceId,
		InstanceType:  msg.InstanceType.String(),
		Component:     model.Component_DBEngine.String(),
		NodeId:        msg.NodeId,
		InternalUsers: internalUsers,
		SearchParam:   &model.AggregateDialogFilter{},
		OrderBy:       model.OrderByForAggregateDialog_TotalStateKeepTime,
		SortBy:        model.SortBy_DESC,
		StartTime:     msg.InspectionStartTime / 1000,
		EndTime:       msg.InspectionEndTime / 1000,
		PageNumber:    1,
		PageSize:      10,
	}
	if msg.NodeId != "" {
		rreq.NodeIds = []string{msg.NodeId}
	}
	var ak string
	var sk string
	var regionId string
	var tlsEndpoint string
	var tlsDialogDetailTopic map[string]string
	if t.cnf.Get(ctx).EnableByteRDS {
		c3Cfg := t.cnf.Get(ctx).C3Config
		ak = c3Cfg.TLSServiceAccessKey
		sk = c3Cfg.TLSServiceSecretKey
		regionId = c3Cfg.ByteInnerTLSServiceRegion
		tlsEndpoint = c3Cfg.ByteInnerTLSServiceEndpoint
		err = json.Unmarshal([]byte(c3Cfg.TLSDialogDetailTopicV2), &tlsDialogDetailTopic)
		if err != nil {
			log.Warn(ctx, "parse json TLSDialogDetailTopic str failed %+v", err)
			return consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
		}
	} else {
		cnf := t.cnf.Get(ctx)
		regionId = cnf.TlsZone
		c3 := t.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
		ak = c3.TLSServiceAccessKey
		sk = c3.TLSServiceSecretKey
		tlsEndpoint = cnf.TlsServiceEndpoint
		err = json.Unmarshal([]byte(t.cnf.Get(ctx).TLSDialogDetailTopicV2), &tlsDialogDetailTopic)
		if err != nil {
			log.Warn(ctx, "parse json TLSDialogDetailTopic str failed %+v", err)
			return consts.ErrorOf(model.ErrorCode_JsonUnmarshalFailed)
		}
	}

	tlsCli := slowTls.NewTLSClient(&slowTls.ConnectionInfo{
		Endpoint:        tlsEndpoint,
		AccessKeyID:     ak,
		AccessKeySecret: sk,
		Region:          regionId,
		TopicId:         tlsDialogDetailTopic[msg.InstanceType.String()],
	}, t.cnf)

	if resp, err = tlsCli.DescribeAggregateDialogs(ctx, rreq); err != nil {
		log.Warn(ctx, "Get aggregateDialog failed %s", err)
		return err
	}
	return nil
}

func (t *TaskActor) inspectDescribeFullSql(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	var err error
	var status *model.DescribeFullSqlStatusResp
	defer func() {
		info.SetDescribeFullSqlStatusResp(status, err)
	}()
	var dsType model.DSType
	dsType, err = model.DSTypeFromString(msg.InstanceType.String())
	if err != nil {
		log.Warn(ctx, "DSTypeFromString failed %s", err)
		return err
	}
	status, err = t.fullSqlService.DescribeFullSqlStatus(
		ctx, &model.DescribeFullSqlStatusReq{
			FollowInstanceID: msg.InstanceId,
			DSType:           dsType,
		},
	)
	if err != nil || status == nil {
		log.Warn(ctx, "DescribeFullSqlStatus failed %s", err)
		return err
	}

	if status.DetailStatus != model.FullSqlFuncStatus_RUN {
		return nil
	}
	err = t.inspectDescribeSqlTemplatesContrast(ctx, msg, info)
	if err != nil {
		log.Warn(ctx, "inspectDescribeSqlTemplatesContrast failed %s", err)
		return err
	}
	return nil
}

func (t *TaskActor) inspectDescribeSqlTemplatesContrast(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	var err error
	var instanceType model.InstanceType
	var res *model.DescribeSqlTemplatesContrastResp
	defer func() {
		info.SetDescribeSqlTemplatesContrastResp(res, err)
	}()
	instanceType, err = model.InstanceTypeFromString(msg.InstanceType.String())
	if err != nil {
		log.Warn(ctx, "InstanceTypeFromString failed %s", err)
		return err
	}
	req := &model.DescribeSqlTemplatesContrastReq{
		CollectSqlFingerprint: thrift.BoolPtr(false),
		InstanceId:            msg.InstanceId,
		InstanceType:          instanceType,
		OrderBy:               model.OrderByForSqlItemPtr(model.OrderByForSqlItem_ExecTime),
		SortBy:                model.SortByPtr(model.SortBy_DESC),
		FirstStartTime:        int32(msg.InspectionStartTime / 1000),
		FirstEndTime:          int32(msg.InspectionEndTime / 1000),
		SecondStartTime:       int32(msg.InspectionStartTime / 1000),
		SecondEndTime:         int32(msg.InspectionEndTime / 1000),
	}
	if msg.NodeId != "" {
		req.NodeId = &msg.NodeId
	}
	res, err = t.fullSqlService.GetSqlTemplatesContrasts(ctx, req)
	if err != nil || res == nil {
		log.Warn(ctx, "get sql execTime distribution detail error:%s", err.Error())
		return err
	}
	// 截断 只保留Top10
	if len(res.Details) > 10 {
		res.Details = res.Details[:10]
	}
	return nil
}

func (t *TaskActor) inspectionRedisInspectionBigKeysByLen(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	req := &datasource.DescribeBigKeysReq{
		Type:           msg.InstanceType,
		InstanceId:     &msg.InstanceId,
		QueryStartTime: thrift.StringPtr(time.UnixMilli(msg.InspectionStartTime).UTC().Format(time.RFC3339)),
		QueryEndTime:   thrift.StringPtr(time.UnixMilli(msg.InspectionEndTime).UTC().Format(time.RFC3339)),
		PageSize:       thrift.Int32Ptr(10),
		OrderBy:        thrift.StringPtr("ValueLen"),
		//OrderType:      thrift.StringPtr("DESC"), 加了会报错，现在默认就是DESC
	}
	result, err := t.ds.DescribeBigKeys(ctx, req)
	if err != nil || result == nil {
		log.Warn(ctx, "get slow log info error:%v", err)
		return err
	}
	for _, key := range result.BigKey {
		t.inspectionResult.RedisInspectionBigKeysItemItemsValueLen = append(t.inspectionResult.RedisInspectionBigKeysItemItemsValueLen, &entity.BigKeyInfo{
			InstanceId: key.InstanceId,
			DBName:     key.DBName,
			KeyType:    key.KeyType,
			KeyInfo:    key.KeyInfo,
			ValueLen:   key.ValueLen,
			ValueSize:  key.ValueSize,
		})
	}
	return nil
}

func (t *TaskActor) inspectionRedisInspectionBigKeysBySize(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	result, err := t.ds.DescribeBigKeys(ctx, &datasource.DescribeBigKeysReq{
		Type:           msg.InstanceType,
		InstanceId:     &msg.InstanceId,
		QueryStartTime: thrift.StringPtr(time.UnixMilli(msg.InspectionStartTime).UTC().Format(time.RFC3339)),
		QueryEndTime:   thrift.StringPtr(time.UnixMilli(msg.InspectionEndTime).UTC().Format(time.RFC3339)),
		PageSize:       thrift.Int32Ptr(10),
		OrderBy:        thrift.StringPtr("ValueSize"),
		//OrderType:      thrift.StringPtr("DESC"), 加了会报错，现在默认就是DESC
	})
	if err != nil || result == nil {
		log.Warn(ctx, "get slow log info error:%v", err)
		return err
	}
	for _, key := range result.BigKey {
		t.inspectionResult.RedisInspectionBigKeysItemItemsValueSize = append(t.inspectionResult.RedisInspectionBigKeysItemItemsValueSize, &entity.BigKeyInfo{
			InstanceId: key.InstanceId,
			DBName:     key.DBName,
			KeyType:    key.KeyType,
			KeyInfo:    key.KeyInfo,
			ValueLen:   key.ValueLen,
			ValueSize:  key.ValueSize,
		})
	}
	return nil
}

func (t *TaskActor) inspectionRedisInspectionHotKeysItemsInfo(ctx types.Context, msg *shared.CreateInspectionTask, info entity.InspectResultInfo) error {
	result, err := t.ds.DescribeHotKeys(ctx, &datasource.DescribeHotKeysReq{
		Type:           msg.InstanceType,
		InstanceId:     &msg.InstanceId,
		QueryStartTime: thrift.StringPtr(time.UnixMilli(msg.InspectionStartTime).UTC().Format(time.RFC3339)),
		QueryEndTime:   thrift.StringPtr(time.UnixMilli(msg.InspectionEndTime).UTC().Format(time.RFC3339)),
		PageSize:       thrift.Int32Ptr(10),
	})
	if err != nil || result == nil {
		log.Warn(ctx, "get slow log info error:%v", err)
		return err
	}
	for _, key := range result.HotKey {
		t.inspectionResult.RedisInspectionHotKeysItemItems = append(t.inspectionResult.RedisInspectionHotKeysItemItems, &entity.HotKeysInfo{
			InstanceId: key.InstanceId,
			DBName:     key.DBName,
			KeyType:    key.KeyType,
			KeyInfo:    key.KeyInfo,
			QueryCount: key.QueryCount,
		})
	}
	return nil
}

func (t *TaskActor) decimalFloat(value float64) float64 {
	num, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", value), 64)
	return num
}

func (t *TaskActor) interceptStr10k(str string) string {
	if len(str) > 10*1024 {
		return str[0 : 10*1023]
	} else {
		return str
	}
}

func (t *TaskActor) buildActorName(regionId string, dsType string, instanceId string) string {
	return strings.Join([]string{regionId, dsType, instanceId}, ">")
}

func (t *TaskActor) formatSlowLogsReportReq(msg *shared.CreateInspectionTask) *shared.DescribeSlowLogTimeSeriesStatsReq {
	interval := (msg.InspectionEndTime - msg.InspectionStartTime) / (240 * 1000)
	return &shared.DescribeSlowLogTimeSeriesStatsReq{
		RegionId:       msg.RegionId,
		InstanceId:     msg.InstanceId,
		DataSourceType: msg.InstanceType,
		NodeId:         msg.NodeId,
		StartTime:      msg.InspectionStartTime / 1000,
		EndTime:        msg.InspectionEndTime / 1000,
		Interval:       interval,
		Limit:          240,
	}
}

func (t *TaskActor) updateTaskStatus(ctx context.Context, status int, taskId int64) error {
	err := t.repo.UpdateTaskStatus(ctx, status, taskId)
	if err != nil {
		log.Warn(ctx, "inspection: taskId:%d updateTaskStatus fail,err:%v", taskId, err.Error())
		return err
	}
	return nil
}

//func (t *TaskActor) changeEnToCnVersion(version string) string {
//	switch version {
//	case "MySQL_5_7":
//		return "MySQL 5.7"
//	case "MySQL_8_0":
//		return "MySQL 8.0"
//	case "MySQL_5_6":
//		return "MySQL 5.6"
//	case "SQLServer_2019_Ent":
//		return "SQLServer 2019.Ent"
//	case "SQLServer_2019_Std":
//		return "SQLServer 2019.Std"
//	case "SQLServer_2019_Web":
//		return "SQLServer 2019.Web"
//	}
//	return version
//}

func (t *TaskActor) updateInstanceNameByInstanceID(ctx types.Context, instanceType shared.DataSourceType, instanceId string, tenantId string) error {
	// 将ctx的租户id设置为传入的租户id
	c := fwctx.GetBizContext(ctx)
	c.TenantID = tenantId
	inst, err := t.ds.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
		Type:       instanceType,
		InstanceId: instanceId,
	})
	if err != nil {
		log.Warn(ctx, "inspection: rds api err %s", err.Error())
		return err
	}
	if inst != nil && inst.InstanceStatus != rdsModel_v2_new.InstanceStatus_Running.String() {
		log.Warn(ctx, "inspection: instance not found %s", instanceId)
		return fmt.Errorf("inspection: instance not found or instance status is not running")
	}
	if err = t.repo.UpdateInstanceNameByInstanceID(ctx, inst.InstanceName, instanceId, tenantId); err != nil {
		log.Warn(ctx, "inspection: update inspection name to db error：%v,instanceName is %v,instanceId is %v ,tenantId is %v ",
			err, inst.InstanceName, instanceId, tenantId)
		return err
	}
	return nil
}
