package dbw_ticket

import (
	dbw_ticket_service "code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"encoding/json"
)

// DbwTicketActorState 简化数据库存储，只保留两个东西一个状态机，一个工单id
//
//	所有信息保存在工单表中，actor不存冗余数据
type DbwTicketActorState struct {
	TicketStatus int
	// TicketId     string
	RunningInfo *dbw_ticket_service.RunningInfo
	IsRunning   bool
}

type DbwInstanceTicketActorState struct {
	InstanceId string
}

func newDbwTicketActorState(bytes []byte) *DbwTicketActorState {
	ts := &DbwTicketActorState{}
	err := json.Unmarshal(bytes, ts)
	if err != nil {
		log.Warn(context.Background(), "newDbwTicketActorState error:%s", err.Error())
		return ts
	}
	return ts
}

func newDbwInstanceTicketActorState(bytes []byte) *DbwInstanceTicketActorState {
	ts := &DbwInstanceTicketActorState{}
	err := json.Unmarshal(bytes, ts)
	if err != nil {
		return ts
	}
	return ts
}
