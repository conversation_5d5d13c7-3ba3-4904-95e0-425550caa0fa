package online_ddl

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	dbw_ticket_service "code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket"
	"code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket/online_ddl/runtime"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/tenant"
	dbwutils "code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	ddl_model "code.byted.org/infcs/dbw-mgr/gen/ddl-agent/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	dslibutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"fmt"
	"github.com/qjpcpu/fp"
	"go.uber.org/dig"
	"runtime/debug"
	"strings"
	"time"
)

type OnlineDDLActorIn struct {
	dig.In
	Conf                config.ConfigProvider
	ActorClient         cli.ActorClient
	TicketRepo          repository.TicketRepo
	DsSources           datasource.DataSourceService
	TicketService       dbw_ticket_service.DbwTicketService
	DbwInstanceDal      dal.DbwInstanceDAL
	TicketCommonService dbw_ticket_service.TicketCommonService
	PodRuntime          runtime.Runtime
	Ds                  datasource.DataSourceService
	C3ConfProvider      c3.ConfigProvider
}

func NewOnlineDDLActor(p OnlineDDLActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.OnlineDDLActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &OnlineDDLActor{
				state:               newOnlineDDLActorState(state),
				cnf:                 p.Conf,
				actorClient:         p.ActorClient,
				ticketRepo:          p.TicketRepo,
				dsSources:           p.DsSources,
				ticketService:       p.TicketService,
				dbwInstanceDal:      p.DbwInstanceDal,
				ticketCommonService: p.TicketCommonService,
				podRuntime:          p.PodRuntime,
				ds:                  p.Ds,
				c3ConfProvider:      p.C3ConfProvider,
			}
		}),
	}
}

type OnlineDDLActor struct {
	state               *OnlineDDLActorState
	cnf                 config.ConfigProvider
	actorClient         cli.ActorClient
	ticketRepo          repository.TicketRepo
	dsSources           datasource.DataSourceService
	ticketService       dbw_ticket_service.DbwTicketService
	dbwInstanceDal      dal.DbwInstanceDAL
	ticketCommonService dbw_ticket_service.TicketCommonService
	podRuntime          runtime.Runtime
	ds                  datasource.DataSourceService
	c3ConfProvider      c3.ConfigProvider

	ticket    *entity.Ticket
	ddlClient *DDLClient
}

func (d *OnlineDDLActor) GetState() []byte {
	state, _ := json.Marshal(d.state)
	return state
}

// protectUserCall 防止panic之后程序挂掉
func (d *OnlineDDLActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call OnlineDDLActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func (d *OnlineDDLActor) Process(ctx types.Context) {
	ctx.SetReceiveTimeout(time.Duration(10) * time.Second)
	d.BuildCtx(ctx)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		d.protectUserCall(ctx, func() {
			d.OnStart(ctx)
		})
	case *shared.ExecuteDbwTicket:
		d.protectUserCall(ctx, func() {
			d.ExecuteTask(ctx, msg)
		})
	case *shared.StopDbwTicket:
		d.protectUserCall(ctx, func() {
			d.StopTask(ctx)
		})
	case *shared.PauseSubTask:
		d.protectUserCall(ctx, func() {
			d.PauseSubTask(ctx)
		})
	case *shared.ResumeSubTask:
		d.protectUserCall(ctx, func() {
			d.ResumeSubTask(ctx)
		})
	case *actor.ReceiveTimeout:
		d.protectUserCall(ctx, func() {
			d.CheckTask(ctx)
		})
	}
}

func (d *OnlineDDLActor) OnStart(ctx types.Context) {
	err := d.initBasic(ctx)
	if err != nil {
		log.Warn(ctx, "ticketId:%s is not null, but find ticket info error: %v", d.state.TicketId, err)
		// 没有找到工单，就关闭自己
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	ctx.SetReceiveTimeout(time.Duration(10) * time.Second)
	log.Info(ctx, "OnlineDDLActor %s start", ctx.GetName())
}

func (d *OnlineDDLActor) BuildCtx(ctx types.Context) {
	// var logID, tenantID, userID string
	var tenantID string
	if d.ticket != nil && d.ticket.TenantId != "" {
		tenantID = d.ticket.TenantId
	}
	ctx.WithValue("biz-context", &fwctx.BizContext{
		TenantID: tenantID,
	})
}

func (d *OnlineDDLActor) initBasic(ctx types.Context) error {
	if d.ticket == nil && d.state.TicketId != "" {
		ticket, err := d.ticketRepo.GetByID(ctx, d.state.TicketId)
		if err != nil {
			log.Warn(ctx, "get ticket error:%v", err)
			return err
		}
		d.ticket = ticket
	}
	d.BuildCtx(ctx)
	d.ddlClient = NewDDLClient(d.state.PodAddress, d.state.TicketId, ctx.GetName())
	return nil
}

func (d *OnlineDDLActor) ExecuteTask(ctx types.Context, msg *shared.ExecuteDbwTicket) {
	if d.state.Status > Undo {
		log.Warn(ctx, "current task is running, can't run others")
		return
	}
	d.state.TicketId = msg.TicketId
	d.state.TableName = msg.TableName
	d.state.SqlText = msg.SqlText
	if err := d.initBasic(ctx); err != nil {
		d.finishedTask(ctx, false, "get ticket info error")
		return
	}
	if d.ticketCommonService.IsDirectExecuteDDLSql(d.state.SqlText) || d.ticket.TicketConfig.OnlineDDlTicketConfig.UseLock {
		// 直接执行
		log.Info(ctx, "direct execute sql: %s", d.state.SqlText)
		d.directExecute(ctx)
		return
	}
	// 拉一个pod起来
	if err := d.createOnlineDDLPod(ctx); err != nil {
		log.Warn(ctx, "create online ddl agent pod error: %v", err.Error())
		d.finishedTask(ctx, false, "inner error: create online ddl agent pod error")
		return
	}
	d.state.Status = CreatePod
	ctx.SaveState()
	d.state.PodCreated = true
}

func (d *OnlineDDLActor) directExecute(ctx types.Context) {
	err := d.doExecuteSql(ctx, d.state.SqlText)
	if err != nil {
		d.state.Status = Error
		ctx.SaveState()
		log.Warn(ctx, "execute sql error: %v", err.Error())
		d.finishedTask(ctx, false, fmt.Sprintf("execute sql error: %v", err.Error()))
		return
	}
	d.state.Status = Finished
	ctx.SaveState()
	d.finishedTask(ctx, true, "")
}

func (d *OnlineDDLActor) dropCopyTable(ctx types.Context) error {

	switch d.ticket.TicketConfig.OnlineDDlTicketConfig.ChangeTableStrategy {
	case int64(model.ChangeTableStrategy_DoNothing):
		return nil
	case int64(model.ChangeTableStrategy_DELETE):
		return d.doDropCopyTable(ctx)
	}
	return nil
}

func (d *OnlineDDLActor) doDropCopyTable(ctx types.Context) error {
	dropSql := ""
	copyTableName := d.getCopyTableName(ctx)
	if d.ticket.InstanceType == shared.VeDBMySQL.String() || d.ticket.InstanceType == shared.MySQL.String() {
		// 将表名中的`转为``，进行转义，然后用``包裹起来
		dropSql = fmt.Sprintf("drop table if exists `%s`", strings.ReplaceAll(copyTableName, "`", "``"))
	}
	if dropSql == "" || !EndsWithDel(copyTableName) {
		// 兜底，免得出意外
		return nil
	}

	// 执行删除语句
	err := d.doExecuteSql(ctx, dropSql)
	if err != nil {
		log.Warn(ctx, "drop table, drop error, sql:%s, detail: %v", dropSql, err.Error())
		d.finishedTask(ctx, false, fmt.Sprintf("ddl is finished, but drop copy table error, copy table name: %s", d.getCopyTableName(ctx)))
	}
	return nil
}

func (d *OnlineDDLActor) doExecuteSql(ctx types.Context, sql string) error {
	ticketInfo := dbw_ticket_service.TicketBasicInfo{
		InstanceId:   d.ticket.InstanceId,
		InstanceType: shared.DataSourceType(shared.DataSourceType_value[d.ticket.InstanceType]),
		DbName:       d.ticket.DbName,
		TenantId:     d.ticket.TenantId,
	}
	source, err := d.ticketCommonService.GetDBDataSource(ctx, ticketInfo)
	if err != nil {
		log.Warn(ctx, "get datasource error: %v", err.Error())
		return err
	}

	err = d.ds.ExecuteSql(ctx, &datasource.ExecuteReq{Source: source, Type: shared.DataSourceType(shared.DataSourceType_value[d.ticket.InstanceType]), ExecuteSql: sql})
	if err != nil {
		log.Warn(ctx, "sql:%s, detail: %v", sql, err.Error())
		return err
	}
	return nil
}

//func (d *OnlineDDLActor) doMoveCopyTable(ctx types.Context) error {
//	return nil
//}

func (d *OnlineDDLActor) getCopyTableName(ctx types.Context) string {
	// 表名 node_task  影子表  _node_task_9099be_del
	return fmt.Sprintf("_%s_%s_del", d.state.TableName, d.ticketCommonService.Generate6MD5(ctx.GetName()))
}

func (d *OnlineDDLActor) startTask(ctx types.Context, msg *shared.ExecuteDbwTicket) {
	reTry := int8(d.ticket.TicketConfig.OnlineDDlTicketConfig.ChangeTableRetryCount)
	showGIPK := true
	startTaskReq := &ddl_model.StartTaskReq{TaskId: ctx.GetName(), Params: &ddl_model.StartTaskParams{
		MaxLagMillis:              d.ticket.TicketConfig.OnlineDDlTicketConfig.MaxLagMillis, // 1500
		ChunkSize:                 d.getChunkSize(ctx, msg, d.ticket.TicketConfig.OnlineDDlTicketConfig),
		Database:                  d.ticket.DbName,
		CutOverLockTimeoutSeconds: d.ticket.TicketConfig.OnlineDDlTicketConfig.ChangeTableLockTimeout,
		CutOverRetryTimes:         &reTry,
		CutOverTimeWindow:         d.covertTimeFormat(d.ticket.TicketConfig.OnlineDDlTicketConfig),
		ShowGIPK:                  &showGIPK,
		Statement:                 msg.SqlText,
		MagicTableNameSuffix:      d.ticketCommonService.Generate6MD5(ctx.GetName()),
		DMLBatchSize:              d.ticket.TicketConfig.OnlineDDlTicketConfig.DMLBatchSize,
		LongSqlKill:               &d.ticket.TicketConfig.OnlineDDlTicketConfig.IsOpenKillLongTransaction,
		LongTransactionTime:       &d.ticket.TicketConfig.OnlineDDlTicketConfig.KillLongTransactionTimeout,
	}}
	load, err := d.formatLoad(ctx)
	if err == nil {
		startTaskReq.Params.MaxLoad = &load
	}
	replicas, err := d.getThrottleControlReplicas(ctx)
	if err != nil {
		log.Warn(ctx, "get instance replicas error: %v", err)
		d.finishedTask(ctx, false, "inner error: Get instance replicas error")
		return
	}
	startTaskReq.Params.ThrottleControlReplicas = replicas
	if err := d.formatStartTaskDbAddress(ctx, startTaskReq.Params); err != nil {
		log.Warn(ctx, "get db address info error: %v", err)
		d.finishedTask(ctx, false, "get db address info error")
		return
	}
	startTaskResp := &ddl_model.StartTaskResp{}
	instance, err := d.dbwInstanceDal.Get(ctx, d.ticket.InstanceId, d.ticket.InstanceType, "", d.ticket.TenantId, model.ControlMode_Management.String())
	if err != nil || instance == nil {
		log.Warn(ctx, "get instance error, or instance is not in database, detail: %v", err)
		d.finishedTask(ctx, false, "instance is not in security control")
		return
	}
	startTaskReq.Params.UserName = instance.DatabaseUser
	startTaskReq.Params.Password = dbwutils.DecryptData(instance.DatabasePassword, instance.InstanceId)

	if d.ticket.InstanceType == model.InstanceType_VeDBMySQL.String() && (tenant.IsRDSMultiCloudPlatform(ctx, d.cnf) || d.ticket.TenantId == "**********") {
		d.setAdminAccount(ctx, startTaskReq)
	}

	err = d.ddlClient.Call(ctx, ActionStartTask, startTaskReq, startTaskResp)
	if err != nil {
		log.Warn(ctx, "send start message to agent error: %v", err.Error())
		d.finishedTask(ctx, false, "send startTask to agent error:"+err.Error())
		return
	}
	log.Info(ctx, "start task req:%s, resp:%v", utils.Show(startTaskReq), utils.Show(startTaskResp))
	d.state.Status = Execute
	ctx.SaveState()
}

func (d *OnlineDDLActor) setAdminAccount(ctx types.Context, req *ddl_model.StartTaskReq) {

	c3Cfg := d.c3ConfProvider.GetNamespace(ctx, consts.C3ApplicationNamespace)
	// 更换账号为dbw_admin的账号
	req.Params.UserName = c3Cfg.DBWAccountName
	req.Params.Password = d.ticketCommonService.GetAdminAccountPassword(c3Cfg.DbwAccountPasswordGenKey, d.ticket.InstanceId)
}

func (d *OnlineDDLActor) getThrottleControlReplicas(ctx types.Context) (*string, error) {
	res, err := d.GetSlaveList(ctx)
	if err != nil {
		log.Warn(ctx, "get instance slave node error: %s", err.Error())
	}
	if len(res) == 0 {
		return nil, nil
	}
	replicas := fp.StreamOf(res).JoinStrings(",")
	return &replicas, nil
}

func (d *OnlineDDLActor) GetSlaveList(ctx types.Context) ([]string, error) {
	if d.ticket.InstanceType == shared.VeDBMySQL.String() {
		return []string{}, nil
	}

	dsType := shared.DataSourceType(shared.DataSourceType_value[d.ticket.InstanceType])

	GetInstanceSlaveAddressReq := &datasource.GetInstanceSlaveAddressReq{InstanceId: d.ticket.InstanceId, Type: dsType}

	GetInstanceSlaveAddressResp, err := d.dsSources.GetInstanceSlaveAddress(ctx, GetInstanceSlaveAddressReq)

	if err != nil {
		log.Warn(ctx, "get slave address error: %v", err.Error())
		return []string{}, err
	}

	return GetInstanceSlaveAddressResp.Address, nil
}

func (d *OnlineDDLActor) formatLoad(ctx types.Context) (string, error) {
	load := "threads_running=%d,threads_connected=%d"
	//req := &datasource.GetMaxConnectionsReq{DSType: shared.DataSourceType(shared.DataSourceType_value[d.ticket.InstanceType]), InstanceId: d.ticket.InstanceId}
	ticketInfo := dbw_ticket_service.TicketBasicInfo{
		InstanceId:   d.ticket.InstanceId,
		InstanceType: shared.DataSourceType(shared.DataSourceType_value[d.ticket.InstanceType]),
		DbName:       d.ticket.DbName,
		TenantId:     d.ticket.TenantId,
	}
	source, err := d.ticketCommonService.GetDBDataSource(ctx, ticketInfo)
	if err != nil {
		log.Warn(ctx, "get datasource error: %v", err.Error())
		return "", err
	}

	maxConn, err := d.dsSources.GetCurrentMaxConnections(ctx, &datasource.GetCurrentMaxConnectionsReq{Source: source, Type: shared.DataSourceType(shared.DataSourceType_value[d.ticket.InstanceType])})
	if err != nil {
		log.Warn(ctx, "get user max connections error:%s", err.Error())
		return "", err
	}
	maxConn = int(float64(maxConn) * 0.8)
	return fmt.Sprintf(load, maxConn, maxConn), nil
}

func (d *OnlineDDLActor) getChunkSize(ctx types.Context, msg *shared.ExecuteDbwTicket, onlineDDlTicketConfig entity.OnlineDDlTicketConfig) int32 {
	if d.ticketCommonService.IsCreateTableSql(msg.SqlText) {
		return DefaultChunkSize
	}
	if !onlineDDlTicketConfig.IsTableCopyDMSAuto && onlineDDlTicketConfig.TableCopySize != 0 {
		return onlineDDlTicketConfig.TableCopySize
	}
	ticketInfo := dbw_ticket_service.TicketBasicInfo{
		InstanceId:   d.ticket.InstanceId,
		InstanceType: shared.DataSourceType(shared.DataSourceType_value[d.ticket.InstanceType]),
		DbName:       d.ticket.DbName,
		TicketId:     dslibutils.MustStrToInt64(d.ticket.TicketId),
		SqlText:      msg.SqlText,
		TenantId:     d.ticket.TenantId,
	}

	sessionID, err := d.ticketCommonService.GetSessionId(ctx, ticketInfo)
	if err != nil {
		log.Warn(ctx, "get session error:%s", err.Error())
		return DefaultChunkSize
	}
	defer d.ticketCommonService.GiveBackInstanceSession(ctx, ticketInfo.InstanceId, sessionID)

	explainSql := fmt.Sprintf("select count(*) from %s.%s", d.ticket.DbName, msg.TableName)
	resp, err := d.actorClient.KindOf(consts.SessionActorKind).Call(ctx, sessionID, &shared.ExplainCommandReq{SQLText: explainSql, DB: d.ticket.DbName})
	if err != nil {
		log.Warn(ctx, "get explain ticket sqlText error:%s ", err.Error())
		return DefaultChunkSize
	}
	rowsAffect := int64(0)
	switch rsp := resp.(type) {
	case *shared.ExplainCommandResp:
		for _, val := range rsp.CommandRes {
			rowsAffect += dslibutils.MustStrToInt64(val.Rows)
		}
	case *shared.DataSourceOpFailed:
		log.Warn(ctx, "failed to obtain the SQL statement for the ticket that affects the number of rows:%s", rsp.ErrorMessage)
		return DefaultChunkSize
	default:
		log.Warn(ctx, "failed to obtain the SQL statement for the ticket that affects the number of rows")
		return DefaultChunkSize
	}
	// 按1000个batch算
	chunkSize := rowsAffect / 1000
	// 最大10000，最小1000
	if chunkSize < DefaultChunkSize {
		chunkSize = DefaultChunkSize
	}
	if chunkSize > MaxChunkSize {
		chunkSize = MaxChunkSize
	}
	return int32(chunkSize)
}

func (d *OnlineDDLActor) covertTimeFormat(onlineDDlTicketConfig entity.OnlineDDlTicketConfig) string {
	if onlineDDlTicketConfig.IsExecuteImmediately {
		return ""
	}
	t := time.Unix(int64(onlineDDlTicketConfig.ExecuteTime), 0)
	// 格式化为 hh:mm:ss
	formattedTime := t.Format("15:04:05")
	return formattedTime
}

func (d *OnlineDDLActor) formatStartTaskDbAddress(ctx types.Context, startParams *ddl_model.StartTaskParams) error {
	var resp *datasource.DescribeInstanceAddressResp
	var err error
	describeInstanceAddressReq := &datasource.DescribeInstanceAddressReq{
		Type:       shared.DataSourceType(shared.DataSourceType_value[d.ticket.InstanceType]),
		InstanceId: d.ticket.InstanceId,
		LinkType:   shared.Volc,
		NodeType:   model.NodeType_Primary.String(),
	}
	if d.ticket.InstanceType == shared.VeDBMySQL.String() {
		resp, err = d.dsSources.DescribeInstancePodAddress(ctx, describeInstanceAddressReq)
		if err != nil {
			log.Warn(ctx, "DescribeInstanceProxyAddress error:%v", err)
			return err
		}
		log.Info(ctx, "online ddl VeDB instance, use proxy, instanceId:%s, ip:%s port:%d", d.ticket.InstanceId, resp.IP, resp.Port)
	} else {
		resp, err = d.dsSources.DescribeInstanceAddress(ctx, describeInstanceAddressReq)
		if err != nil {
			log.Warn(ctx, "DescribeInstanceAddress error:%v", err)
			return err
		}
	}
	startParams.Host = resp.IP
	startParams.Port = resp.Port
	return nil
}

func (d *OnlineDDLActor) finishedTask(ctx types.Context, success bool, errMsg string) {
	if success {
		d.state.Status = Finished
		ctx.SaveState()
	} else {
		d.state.Status = Error
		ctx.SaveState()
	}

	d.destroyDDLPod(ctx)

	err := d.actorClient.KindOf(consts.DbwTicketActorKind).Send(ctx, d.state.TicketId, &shared.UpdateDbwSubTaskInfo{Finished: true, Success: success, Msg: errMsg})
	if err != nil {
		log.Warn(ctx, "finished online ddl sub task error:%v", err)
	}

	ctx.Send(ctx.Self(), &proto.SuicideMessage{})
}

func (d *OnlineDDLActor) getPodName(ctx types.Context) string {
	// 实例名+库名的md5
	return "ddl-agent-" + d.ticket.InstanceId + "-" + d.ticketCommonService.Generate6MD5(d.ticket.DbName)
}

func (d *OnlineDDLActor) createOnlineDDLPod(ctx types.Context) error {

	conf := d.cnf.Get(ctx)
	DDLEnvs := make(map[string]string)
	DDLEnvs["TicketID"] = d.ticket.TicketId
	DDLEnvs["InstanceID"] = d.ticket.InstanceId
	DDLEnvs["TaskID"] = ctx.GetName()
	DDLEnvs["TenantID"] = d.ticket.TenantId

	var variables = map[string]interface{}{
		"PodName":    d.getPodName(ctx),
		"Image":      conf.OnlineDDLAgentImage,
		"TicketID":   d.ticket.TicketId,
		"InstanceID": d.ticket.InstanceId,
		"TaskID":     ctx.GetName(),
		"Cpu":        conf.OnlineDDLAgentPodCpu,
		"Memory":     conf.OnlineDDLAgentPodMem,
		"DDLEnvs":    DDLEnvs,
	}

	err := d.podRuntime.Create(ctx, runtime.CreateReq{Variables: variables, RuntimeType: shared.K8sRuntimeType})
	if err != nil {
		log.Warn(ctx, "create Req: %s", utils.Show(variables))
		log.Warn(ctx, "create online ddl runtime error:", err.Error())
		return err
	}
	return nil
}

func (d *OnlineDDLActor) destroyDDLPod(ctx types.Context) {
	log.Info(ctx, "ticket:%s start destroy online ddl pod", d.state.TicketId)
	if d.state.Status == Undo || !d.state.PodCreated {
		// 没有创建出来就不用管
		return
	}
	if d.state.Status != Finished {
		time.Sleep(1 * time.Minute)
	}
	err := d.podRuntime.Delete(ctx, runtime.DeleteReq{Name: d.getPodName(ctx), RuntimeType: shared.K8sRuntimeType})
	if err != nil {
		log.Warn(ctx, "destroy online ddl runtime error:", err.Error())
	}

	for true {
		// 先休息一会，避免没删完，就重复创建
		time.Sleep(10 * time.Second)
		_, err = d.podRuntime.IsReady(ctx, runtime.IsReadyReq{Name: d.getPodName(ctx), RuntimeType: shared.K8sRuntimeType})
		if err != nil {
			// 报错就认为查不到了
			// 避免没有删除干净，有时候会抓不住，这里退出了，还是在deleting，不晓得为啥
			time.Sleep(10 * time.Second)
			return
		}
	}
	log.Info(ctx, "ticket:%s finished destroy online ddl pod", d.state.TicketId)
}

func (d *OnlineDDLActor) StopTask(ctx types.Context) {
	stopTaskReq := &ddl_model.StopTaskReq{}
	stopTaskResp := &ddl_model.StopTaskReq{}

	if strings.TrimSpace(d.state.PodAddress) != "" {
		err := d.ddlClient.Call(ctx, ActionStopTask, stopTaskReq, stopTaskResp)
		if err != nil {
			log.Warn(ctx, "send stop message to agent error: %v", err.Error())
			// ctx.Respond(&shared.DbwTicketResponse{Success: false, ErrMsg: "send stop message to agent error"})
			// return
		}
	}

	d.dropGhcTable(ctx)
	d.dropGhoTable(ctx)
	ctx.Respond(&shared.DbwTicketResponse{Success: true, ErrMsg: ""})
	d.destroyDDLPod(ctx)
	ctx.Send(ctx.Self(), &proto.SuicideMessage{})
}

func (d *OnlineDDLActor) PauseSubTask(ctx types.Context) {
	pauseTaskReq := &ddl_model.PauseTaskReq{}
	pauseTaskResp := &ddl_model.PauseTaskResp{}
	err := d.ddlClient.Call(ctx, ActionPauseTask, pauseTaskReq, pauseTaskResp)
	if err != nil {
		log.Warn(ctx, "send pause message to agent error: %v", err.Error())
		return
	}
	d.state.Status = Pause
	ctx.SaveState()
}

func (d *OnlineDDLActor) ResumeSubTask(ctx types.Context) {
	resumeTaskReq := &ddl_model.ResumeTaskReq{}
	resumeTaskResp := &ddl_model.ResumeTaskResp{}
	err := d.ddlClient.Call(ctx, ActionResumeTask, resumeTaskReq, resumeTaskResp)
	if err != nil {
		log.Warn(ctx, "send resume message to agent error: %v", err.Error())
		return
	}
	d.state.Status = Execute
	ctx.SaveState()
}

func (d *OnlineDDLActor) CheckTask(ctx types.Context) {

	if d.state.Status == Undo {

	}

	if d.state.Status == CreatePod {
		d.checkCreatePod(ctx)
		return
	}

	if d.state.Status == Pause {
		// 暂停中，检查实例状态，是否具备可运行条件，具备就恢复任务
		//if d.CheckInstanceStatus(ctx) {
		//	ctx.Send(ctx.Self(), &shared.ResumeSubTask{})
		//}
		//return
	}
	if d.state.Status == Execute {
		d.CheckExecuteTaskStatus(ctx)
	}
}

func (d *OnlineDDLActor) checkCreatePod(ctx types.Context) {

	isCreateFinished, err := d.podRuntime.IsReady(ctx, runtime.IsReadyReq{Name: d.getPodName(ctx), RuntimeType: shared.K8sRuntimeType})
	if err != nil {
		log.Warn(ctx, "get create pod info error:%s", err.Error())
		d.finishedTask(ctx, false, "inner error, create pod error")
		return
	}
	if !isCreateFinished {
		log.Info(ctx, "pod is creating")
		return
	}

	address, err := d.podRuntime.GetAddress(ctx, runtime.GetAddressReq{Name: d.getPodName(ctx), RuntimeType: shared.K8sRuntimeType})
	if err != nil {
		log.Warn(ctx, "get online ddl runtime address error:", err.Error())
		d.finishedTask(ctx, false, "inner error, create pod error")
		return
	}
	d.state.PodAddress = fmt.Sprintf("%s:%d", address, PodPort)
	d.ddlClient.ddlAgentAddr = fmt.Sprintf("%s:%d", address, PodPort)
	ctx.SaveState()
	startMsg := &shared.ExecuteDbwTicket{
		SqlText:   d.state.SqlText,
		TableName: d.state.TableName,
		TicketId:  d.state.TicketId,
	}
	d.startTask(ctx, startMsg)
}

func (d *OnlineDDLActor) dropGhcTable(ctx types.Context) {
	ghcTableName := fmt.Sprintf("~%s_%s_ghc", d.state.TableName, d.ticketCommonService.Generate6MD5(ctx.GetName()))
	dropSql := fmt.Sprintf("drop table if exists `%s`", strings.ReplaceAll(ghcTableName, "`", "``"))
	// 执行删除语句
	err := d.doExecuteSql(ctx, dropSql)
	if err != nil {
		log.Warn(ctx, "drop table, drop error, sql:%s, detail: %v", dropSql, err.Error())
	}
}

func (d *OnlineDDLActor) dropGhoTable(ctx types.Context) {
	ghcTableName := fmt.Sprintf("~%s_%s_gho", d.state.TableName, d.ticketCommonService.Generate6MD5(ctx.GetName()))
	dropSql := fmt.Sprintf("drop table if exists `%s`", strings.ReplaceAll(ghcTableName, "`", "``"))
	// 执行删除语句
	err := d.doExecuteSql(ctx, dropSql)
	if err != nil {
		log.Warn(ctx, "drop table, drop error, sql:%s, detail: %v", dropSql, err.Error())
	}
}

func (d *OnlineDDLActor) CheckExecuteTaskStatus(ctx types.Context) {
	getProgressReq := &ddl_model.GetProgressReq{}
	getProgressResp := &ddl_model.GetProgressResp{}
	err := d.ddlClient.Call(ctx, ActionGetProgress, getProgressReq, getProgressResp)
	if err != nil {
		log.Warn(ctx, "send get progress message to agent error: %v", err.Error())
		d.finishedTask(ctx, false, fmt.Sprintf("execute error ,errorDetail: %s", err.Error()))
		d.dropGhcTable(ctx)
		d.dropGhoTable(ctx)
		return
	}

	log.Info(ctx, "Ticket:%s ProgressInfo:%s", d.state.TicketId, utils.Show(getProgressResp))
	d.updateSubTaskRunningDetail(ctx, getProgressResp)
	if getProgressResp.ProgressPercent-100 >= 0 && getProgressResp.State == ddl_model.State_Finished {
		if err = d.dropCopyTable(ctx); err != nil {
			log.Warn(ctx, "drop copy table error: %v", err)
			return
		}
		d.finishedTask(ctx, true, "")
		return
	}

	// 正在执行则，检查实例信息，不具备可行条件就暂停任务
	// TODO 先不做这个检查了，后续有需求再说
	//if !d.CheckInstanceStatus(ctx) {
	//	ctx.Send(ctx.Self(), &shared.PauseSubTask{})
	//}
}

func (d *OnlineDDLActor) updateSubTaskRunningDetail(ctx types.Context, getProgressResp *ddl_model.GetProgressResp) {
	progress := int(getProgressResp.ProgressPercent)
	if progress >= 100 && getProgressResp.State != ddl_model.State_Finished {
		progress = 99
	}

	err := d.actorClient.KindOf(consts.DbwTicketActorKind).Send(ctx, d.state.TicketId, &shared.UpdateDbwSubTaskInfo{
		Success:  true,
		Msg:      utils.Show(getProgressResp),
		Finished: false,
		Progress: int32(progress),
	})
	if err != nil {
		log.Warn(ctx, "finished online ddl sub task error:%v", err)
	}
}

func (d *OnlineDDLActor) CheckInstanceStatus(ctx types.Context) bool {
	if d.ticket.InstanceType == shared.VeDBMySQL.String() {
		// VeDB不用检查空间大小
		return true
	}
	dsType := shared.DataSourceType(shared.DataSourceType_value[d.ticket.InstanceType])
	// 查磁盘利用率
	diskUsage, err := d.dsSources.GetLatestDiskUsage(ctx, &datasource.GetLatestDiskUsageReq{InstanceId: d.ticket.InstanceId, DSType: dsType})
	if err != nil {
		log.Warn(ctx, "GetLatestDiskUsage error:%v", err)
		return true
	}
	if int(diskUsage) > MaxDiskUsage {
		return false
	}
	return true
}
