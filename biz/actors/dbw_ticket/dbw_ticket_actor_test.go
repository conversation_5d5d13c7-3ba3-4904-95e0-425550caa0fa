package dbw_ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	dbw_ticket_service "code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/repository"
	"code.byted.org/infcs/ds-lib/common/log"
	"fmt"
	"github.com/bytedance/mockey"
	"testing"
	"time"
)

func TestStopTicket(t *testing.T) {
	actor := &DbwTicketActor{
		ticketRepo: &repository.MockTicketRepo{},
	}
	actor.state = &DbwTicketActorState{}
	actor.ticket = &entity.Ticket{}

	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	mock1 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*repository.MockTicketRepo).UpdateRunningInfo).Return(nil).Build()
	defer mock4.UnPatch()

	actor.StopTicket(&mocks.MockContext{})
}

func TestUpdateSubTicketProgress(t *testing.T) {
	actor := &DbwTicketActor{
		ticketRepo: &repository.MockTicketRepo{},
	}
	actor.state = &DbwTicketActorState{}
	actor.ticket = &entity.Ticket{}

	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*repository.MockTicketRepo).GetTicketRunningDetail).Return(entity.TicketRunningDetail{}, fmt.Errorf("test")).Build()
	actor.UpdateSubTicketProgress(&mocks.MockContext{}, &shared.UpdateDbwSubTaskInfo{})
	mock1.UnPatch()
	time.Sleep(50 * time.Millisecond)

	mock2 := mockey.Mock((*repository.MockTicketRepo).GetTicketRunningDetail).Return(entity.TicketRunningDetail{}, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*repository.MockTicketRepo).UpdateTicketRunningDetail).Return(fmt.Errorf("test")).Build()
	defer mock3.UnPatch()
	actor.UpdateSubTicketProgress(&mocks.MockContext{}, &shared.UpdateDbwSubTaskInfo{})
}

func TestUpdateTicketRunningDetail(t *testing.T) {
	actor := &DbwTicketActor{
		ticketRepo: &repository.MockTicketRepo{},
	}
	actor.state = &DbwTicketActorState{RunningInfo: &dbw_ticket_service.RunningInfo{OnlineDDlRunningInfo: &dbw_ticket_service.OnlineDDlRunningInfo{TaskInfos: []*dbw_ticket_service.OnlineTaskInfo{{}}}}}
	actor.ticket = &entity.Ticket{}

	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*repository.MockTicketRepo).GetTicketRunningDetail).Return(entity.TicketRunningDetail{}, fmt.Errorf("test")).Build()
	actor.UpdateTicketRunningDetail(&mocks.MockContext{})
	mock1.UnPatch()
	time.Sleep(50 * time.Millisecond)

	mock2 := mockey.Mock((*repository.MockTicketRepo).GetTicketRunningDetail).Return(entity.TicketRunningDetail{}, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*repository.MockTicketRepo).UpdateTicketRunningDetail).Return(fmt.Errorf("test")).Build()
	defer mock3.UnPatch()
	actor.UpdateTicketRunningDetail(&mocks.MockContext{})
}
