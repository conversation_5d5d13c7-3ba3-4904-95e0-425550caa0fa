package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/com"
	ycnf "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/service/audit"
	"code.byted.org/infcs/dbw-mgr/biz/service/billing"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/cross_service_authorization"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/project"
	"code.byted.org/infcs/dbw-mgr/biz/service/tag"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls/tls_enhance"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	local_dal "code.byted.org/infcs/dbw-mgr/biz/test/dal"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	config2 "code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	mock_dal "code.byted.org/infcs/dbw-mgr/biz/test/mocks/dal"
	mocks_audit "code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/audit"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/bill"
	mocks_fullsql "code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/full_sql"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/log_collector"
	mocks_project "code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/project"
	mocks_tag "code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/tag"
	mock_zkconfig "code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/zkconfig"
	mock_tls "code.byted.org/infcs/dbw-mgr/biz/test/mocks/tlsc"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	rdsModel_v2 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2022-01-01/kitex_gen/model/v2"
	rdsModel "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/lib-mgr-common/volc/iam"
	volctrade "code.byted.org/infcs/lib-mgr-common/volc/trade"
	"code.byted.org/infcs/protoactor-go/actor"
	volc_tag "code.byted.org/videoarch/cloud-volc_sdk_go/tag"
	"code.byted.org/videoarch/cloud-volc_sdk_go/tradeapi"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	. "github.com/bytedance/mockey"
	"github.com/coocood/freecache"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	tls_sdk "github.com/volcengine/volc-sdk-golang/service/tls"
	"os"
	"reflect"
	"runtime/debug"
	"strings"
	"testing"
	"time"
)

type PgAuditCreateActorSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *PgAuditCreateActorSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}
func (suite *PgAuditCreateActorSuite) TearDownTest() {
	suite.ctrl.Finish()
}
func TestPgAuditCreateActorSuite(t *testing.T) {
	//	suite.Run(t, new(PgAuditCreateActorSuite))
}

func (suite *PgAuditCreateActorSuite) TestStep_CreateMethod() {
	type fields struct {
		BaseStep BaseStep
	}
	type args struct {
		ctx types.Context
		a   *AuditLifecycleActor
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "",
			fields: fields{
				BaseStep: BaseStep{},
			},
			args: args{
				ctx: MockActorContext(suite.ctrl),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		suite.T().Run(tt.name, func(t *testing.T) {
			var pass bool
			var err error
			allStep := make(map[string]IdempotentStep)
			for name, step := range createSteps {
				allStep[name] = step
			}
			for name, step := range pgCreateSteps {
				allStep[name] = step
			}
			for name, step := range auditCreateGCSteps {
				allStep[name] = step
			}
			for name, step := range createFullSqlSteps {
				allStep[name] = step
			}
			allStep["BaseStep"] = BaseStep{}
			for stepName, step := range allStep {
				PatchConvey(stepName, suite.T(), func() {
					_, cfg, c3Cnf, local, auditSvc, publishEventSvc := MockBillNeed(suite.ctrl)
					mockCrossAuthService := MockCrossAuthService(suite.ctrl)
					ds := MockDS(suite.ctrl)
					tagSvc := MockTag(suite.ctrl)
					proj := MockProject(suite.ctrl)
					lcSvc := MockLogCollector(suite.ctrl)

					resp := &tls_sdk.CreateRuleResponse{
						CommonResponse: tls_sdk.CommonResponse{},
						RuleID:         "rule-xxxxid",
					}
					Mock(tls_enhance.CreateRule).Return(resp, nil).Build()
					Mock(tls_enhance.DeleteScheduleSqlTask).Return(&tls_enhance.DeleteScheduleSqlTaskResponse{}, nil).Build()
					Mock(tls_enhance.CreateScheduleSqlTask).Return(&tls_enhance.CreateScheduleSqlTaskResponse{}, nil).Build()
					Mock(tls_sdk.NewClient).Return(MockTlsClient(suite.ctrl)).Build()
					MockStepTlsClient(suite)
					fullsqlZkConf := mock_zkconfig.NewMockFullSqlConfigService(suite.ctrl)
					fullsqlZkConf.EXPECT().PipeConfig(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					fullsqlZkConf.EXPECT().PipeDeleteConfig(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					fullsqlZkConf.EXPECT().ConfigStatisticTls(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					fullsqlZkConf.EXPECT().ConfigDeleteStatisticTls(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					fullsqlZkConf.EXPECT().ClearInstanceCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

					tradeNewMsg := &volctrade.TradeMessage{}
					_ = json.Unmarshal([]byte("{\"MsgID\":\"delivery_suborder_req_7187313654622441772\",\"Type\":\"New\",\"Product\":\"DBW\",\"IsResourcePack\":0,\"DeductBy\":0,\"SubjectCode\":\"3423\",\"OrderNO\":\"Order7187312971627237676\",\"SuborderNO\":\"Suborder7187312971627270444\",\"InstanceNO\":\"asdf-asdf-asdf-asdf002\",\"InstanceType\":1,\"AccountID\":**********,\"DistributorID\":0,\"EventTime\":\"2023-01-11T16:48:12+08:00\",\"BeginTime\":\"2023-01-11T16:48:12+08:00\",\"EndTime\":\"2200-01-01T00:00:00+08:00\",\"SubOrder\":\"{\\\"ID\\\":0,\\\"ParentOrderNO\\\":\\\"Order7187312971627237676\\\",\\\"Product\\\":\\\"DBW\\\",\\\"AccountID\\\":**********,\\\"UserID\\\":-1,\\\"SubOrderNO\\\":\\\"Suborder7187312971627270444\\\",\\\"SubjectCode\\\":\\\"3423\\\",\\\"OrderType\\\":1,\\\"OrderCategory\\\":1,\\\"PriceVersion\\\":\\\"********\\\",\\\"ConfigurationCode\\\":\\\"log_collect\\\",\\\"InstanceNO\\\":\\\"asdf-asdf-asdf-asdf002\\\",\\\"OriginalAmount\\\":0,\\\"DiscountAmount\\\":0,\\\"PayableAmount\\\":0,\\\"PaidAmount\\\":0,\\\"BalanceAmount\\\":0,\\\"TtypayAmount\\\":0,\\\"RefundAmount\\\":0,\\\"PayType\\\":\\\"post\\\",\\\"PayTranscationNO\\\":\\\"\\\",\\\"RefundTranscationNO\\\":\\\"\\\",\\\"BeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"EndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"PaidTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"Period\\\":\\\"\\\",\\\"Times\\\":0,\\\"WriteBackStatus\\\":0,\\\"Status\\\":1,\\\"CreatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"UpdatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ConfigItems\\\":[],\\\"EventTime\\\":0,\\\"Area\\\":\\\"\\\",\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"Zone\\\":\\\"\\\",\\\"ConfigDescription\\\":\\\"\\\",\\\"ConfigurationName\\\":\\\"日志采集费\\\",\\\"Source\\\":2,\\\"BillingType\\\":2,\\\"DiscountInfos\\\":[],\\\"TerminateOrderExtraInfos\\\":[],\\\"IsResourcePack\\\":0,\\\"DeductBy\\\":0,\\\"PreOrderNo\\\":\\\"*********************\\\",\\\"CouponAmount\\\":0,\\\"ChargeByNaturalMonth\\\":false,\\\"Instance\\\":null,\\\"IsBillPayment\\\":false,\\\"AccountInfo\\\":null,\\\"SettlementPeriod\\\":3,\\\"InheritedPaidAmount\\\":0,\\\"InheritedCouponAmount\\\":0,\\\"CarriedForwardPaidAmount\\\":0,\\\"ActualBeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ActualEndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"RefundDisable\\\":true,\\\"RefundRule\\\":\\\"\\\",\\\"CouponDisable\\\":false,\\\"IsGift\\\":false,\\\"OriginalSubOrderNO\\\":\\\"\\\",\\\"CreditAmount\\\":0,\\\"Organization\\\":\\\"\\\",\\\"IsAtomized\\\":false,\\\"ProviderCode\\\":\\\"\\\",\\\"IncomeType\\\":\\\"\\\",\\\"OpenCallback\\\":0,\\\"DisplaySetting\\\":\\\"\\\",\\\"TerminateType\\\":0,\\\"ErrMsg\\\":\\\"\\\",\\\"FloatTimes\\\":0,\\\"Version\\\":\\\"v2\\\",\\\"OriginalParentOrderNO\\\":\\\"\\\",\\\"IgnoreInteraction\\\":false,\\\"InsDiscountAmount\\\":0,\\\"ChargeFree\\\":\\\"\\\",\\\"BuyerID\\\":**********,\\\"PayerID\\\":**********,\\\"BusinessMode\\\":0,\\\"BuyerInfo\\\":null,\\\"PayerInfo\\\":null,\\\"SellerInfo\\\":null,\\\"SellerID\\\":3423,\\\"OwnerID\\\":**********,\\\"SiteCode\\\":\\\"660086\\\",\\\"QuotaAmount\\\":0,\\\"SettlementMode\\\":0,\\\"SellerRelSubOrderNO\\\":\\\"\\\",\\\"SellingMode\\\":0,\\\"BizType\\\":0,\\\"RealAmount\\\":0}\",\"Attribute\":\"{\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"DSType\\\":\\\"MySQL\\\",\\\"TlsTtl\\\":7,\\\"LabelType\\\":\\\"ProxyFront\\\",\\\"StartAfterPaid\\\":false,\\\"ProductType\\\":\\\"AuditLogCollectTrafficCharges\\\"}\",\"Region\":\"cn-nanjing-bbit\"}"),
						tradeNewMsg)

					Mock((*volctrade.VolcTradeClient).CallbackInstanceStatus).Return(nil).Build()
					billSvc := bill.NewMockBillingService(suite.ctrl)
					billSvc.EXPECT().GetInstance(gomock.Any(), gomock.Any()).Return(&tradeapi.Instance{BusinessStatus: int32(model.BusinessStatus_InstanceBusinessPending)}, nil).AnyTimes()
					billSvc.EXPECT().GetMultiClient().Return(&volctrade.VolcTradeClient{}).AnyTimes()

					aa := &AuditLifecycleActor{
						state: &AuditLifeState{
							LifeState:        LifeState{},
							TenantId:         "**********",
							Region:           "cn-chongqing-sdv",
							FollowInstanceId: "Postgres-00000001",
							TlsTopic:         "ab68a6d1-b401-4ae6-b574-d98cb78e0763",
							TlsProject:       "373977ae-1d6d-4069-9a28-af8dc603c5e4",
							TlsRegion:        "cn-chongqing-sdv",
							TlsEndpoint:      "https://tls-cn-guilin-boe-inner.ivolces.com",
							TlsTopicTTL:      1,
							DSType:           model.DSType_Postgres,
							MsgID:            "",
							Message:          tradeNewMsg,
							AzClusterMap:     map[string]string{"compute-a": "compute-a", "compute-b": "compute-b"},
							SqlTemplateTls: &TlsConfig{
								TlsId:       1,
								TlsEndpoint: "",
								TlsRegion:   "",
								TlsProject:  "",
								TlsTopic:    "",
								TlsTopicTTL: 1,
								TlsDataType: 5,
							},
							SqlCountTls: &TlsConfig{
								TlsId:       2,
								TlsEndpoint: "",
								TlsRegion:   "",
								TlsProject:  "",
								TlsTopic:    "",
								TlsTopicTTL: 2,
								TlsDataType: 6,
							},
							SqlTableAggrTls: &TlsConfig{
								TlsId:       3,
								TlsEndpoint: "",
								TlsRegion:   "",
								TlsProject:  "",
								TlsTopic:    "",
								TlsTopicTTL: 3,
								TlsDataType: 7,
							},
							SqlTopTls: &TlsConfig{
								TlsId:       4,
								TlsEndpoint: "",
								TlsRegion:   "",
								TlsProject:  "",
								TlsTopic:    "",
								TlsTopicTTL: 4,
								TlsDataType: 8,
							},
							LogProductType:    model.LogProductType_FullSqlLog,
							CapabilitiesFlags: 0,
						},
						auditService:         auditSvc,
						auditTlsDAL:          local.AuditDAL,
						fullSqlInstDAL:       local.FullSqlDAL,
						tlsDAL:               local.TlsDAL,
						statisticSqlTlsDAL:   local.StatisticSqlTlsDAL,
						statisticSqlTaskDAL:  local.StatisticSqlTaskDAL,
						instanceExtraTlsDAL:  local.InstanceExtraTlsDAL,
						crossAuthSvc:         mockCrossAuthService,
						conf:                 cfg,
						c3Conf:               c3Cnf,
						publishEventSvc:      publishEventSvc,
						source:               ds,
						tagSvc:               tagSvc,
						projectSvc:           proj,
						billSvc:              billSvc,
						lcSvc:                lcSvc,
						fullSqlConfigService: fullsqlZkConf,
					}
					if step.GetStepName() == "WaitCollectorReadyStep" {
						atDal := mock_dal.NewMockAuditTlsDAL(suite.ctrl)
						auditTls := &dao.AuditTls{
							ID:               1,
							TlsId:            1,
							InstanceID:       "1",
							FollowInstanceID: "1",
							DeployType:       model.LabelType_ProxyFront.String(),
							TenantID:         "1",
							DbType:           model.DSType_MySQL.String(),
							Status:           2,
							Deleted:          0,
						}
						atDal.EXPECT().GetByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(auditTls, nil).AnyTimes()
						aa.auditTlsDAL = atDal
					}

					aa.initCtx(tt.args.ctx)
					fmt.Printf("test stepName:%s\n", stepName)

					if _, err = step.PreCheck(tt.args.ctx, aa); err != nil {
						t.Errorf("%s PreCheck() error = %v, pass %t", step.GetStepName(), err, pass)
					}
					if err := step.ProtectExec(tt.args.ctx, aa); err != nil {
						t.Errorf("%s ProtectExec() error = %v", step.GetStepName(), err)
					}
					if step.GetStepName() != stepName {
						t.Errorf("%s GetStepName() error, stepName:%s got:%s", step.GetStepName(), stepName, step.GetStepName())
					}
					if step.MaxExecRetry() < 1 {
						t.Errorf("%s max exec less than 1", step.GetStepName())
					}
					if step.ErrorWait() < 1 {
						t.Errorf("%s error wait less than 1", step.GetStepName())
					}
					step.NextStep(tt.args.ctx, aa)
					step.Trace(tt.args.ctx, aa, "are you ok")
				})
			}
		})
	}
}

func (suite *PgAuditCreateActorSuite) TestStep_CreateRedisMethod() {
	type fields struct {
		BaseStep BaseStep
	}
	type args struct {
		ctx types.Context
		a   *AuditLifecycleActor
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "",
			fields: fields{
				BaseStep: BaseStep{},
			},
			args: args{
				ctx: MockActorContext(suite.ctrl),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		suite.T().Run(tt.name, func(t *testing.T) {
			var pass bool
			var err error
			allStep := make(map[string]IdempotentStep)
			for name, step := range redisCreateSteps {
				allStep[name] = step
			}
			allStep["BaseStep"] = BaseStep{}
			for stepName, step := range allStep {
				PatchConvey(stepName, suite.T(), func() {
					_, cfg, c3Cnf, local, auditSvc, publishEventSvc := MockBillNeed(suite.ctrl)
					mockCrossAuthService := MockCrossAuthService(suite.ctrl)
					ds := MockDS(suite.ctrl)
					tagSvc := MockTag(suite.ctrl)
					proj := MockProject(suite.ctrl)
					lcSvc := MockLogCollector(suite.ctrl)

					resp := &tls_sdk.CreateRuleResponse{
						CommonResponse: tls_sdk.CommonResponse{},
						RuleID:         "rule-xxxxid",
					}
					Mock(tls_enhance.CreateRule).Return(resp, nil).Build()
					Mock(tls_enhance.DeleteScheduleSqlTask).Return(&tls_enhance.DeleteScheduleSqlTaskResponse{}, nil).Build()
					Mock(tls_enhance.CreateScheduleSqlTask).Return(&tls_enhance.CreateScheduleSqlTaskResponse{}, nil).Build()
					Mock(tls_sdk.NewClient).Return(MockTlsClient(suite.ctrl)).Build()
					MockStepTlsClient(suite)

					fullsqlZkConf := mock_zkconfig.NewMockFullSqlConfigService(suite.ctrl)
					fullsqlZkConf.EXPECT().PipeConfig(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					fullsqlZkConf.EXPECT().PipeDeleteConfig(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					fullsqlZkConf.EXPECT().ConfigStatisticTls(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					fullsqlZkConf.EXPECT().ConfigDeleteStatisticTls(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					fullsqlZkConf.EXPECT().ClearInstanceCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					fullsqlZkConf.EXPECT().PipeConfigX(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					fullsqlZkConf.EXPECT().PipeDeleteConfigX(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					fullsqlZkConf.EXPECT().ConfigStatisticTlsX(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
					fullsqlZkConf.EXPECT().ConfigDeleteStatisticTlsX(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

					tradeNewMsg := &volctrade.TradeMessage{}
					_ = json.Unmarshal([]byte("{\"MsgID\":\"delivery_suborder_req_7187313654622441772\",\"Type\":\"New\",\"Product\":\"DBW\",\"IsResourcePack\":0,\"DeductBy\":0,\"SubjectCode\":\"3423\",\"OrderNO\":\"Order7187312971627237676\",\"SuborderNO\":\"Suborder7187312971627270444\",\"InstanceNO\":\"asdf-asdf-asdf-asdf002\",\"InstanceType\":1,\"AccountID\":**********,\"DistributorID\":0,\"EventTime\":\"2023-01-11T16:48:12+08:00\",\"BeginTime\":\"2023-01-11T16:48:12+08:00\",\"EndTime\":\"2200-01-01T00:00:00+08:00\",\"SubOrder\":\"{\\\"ID\\\":0,\\\"ParentOrderNO\\\":\\\"Order7187312971627237676\\\",\\\"Product\\\":\\\"DBW\\\",\\\"AccountID\\\":**********,\\\"UserID\\\":-1,\\\"SubOrderNO\\\":\\\"Suborder7187312971627270444\\\",\\\"SubjectCode\\\":\\\"3423\\\",\\\"OrderType\\\":1,\\\"OrderCategory\\\":1,\\\"PriceVersion\\\":\\\"********\\\",\\\"ConfigurationCode\\\":\\\"log_collect\\\",\\\"InstanceNO\\\":\\\"asdf-asdf-asdf-asdf002\\\",\\\"OriginalAmount\\\":0,\\\"DiscountAmount\\\":0,\\\"PayableAmount\\\":0,\\\"PaidAmount\\\":0,\\\"BalanceAmount\\\":0,\\\"TtypayAmount\\\":0,\\\"RefundAmount\\\":0,\\\"PayType\\\":\\\"post\\\",\\\"PayTranscationNO\\\":\\\"\\\",\\\"RefundTranscationNO\\\":\\\"\\\",\\\"BeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"EndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"PaidTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"Period\\\":\\\"\\\",\\\"Times\\\":0,\\\"WriteBackStatus\\\":0,\\\"Status\\\":1,\\\"CreatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"UpdatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ConfigItems\\\":[],\\\"EventTime\\\":0,\\\"Area\\\":\\\"\\\",\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"Zone\\\":\\\"\\\",\\\"ConfigDescription\\\":\\\"\\\",\\\"ConfigurationName\\\":\\\"日志采集费\\\",\\\"Source\\\":2,\\\"BillingType\\\":2,\\\"DiscountInfos\\\":[],\\\"TerminateOrderExtraInfos\\\":[],\\\"IsResourcePack\\\":0,\\\"DeductBy\\\":0,\\\"PreOrderNo\\\":\\\"*********************\\\",\\\"CouponAmount\\\":0,\\\"ChargeByNaturalMonth\\\":false,\\\"Instance\\\":null,\\\"IsBillPayment\\\":false,\\\"AccountInfo\\\":null,\\\"SettlementPeriod\\\":3,\\\"InheritedPaidAmount\\\":0,\\\"InheritedCouponAmount\\\":0,\\\"CarriedForwardPaidAmount\\\":0,\\\"ActualBeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ActualEndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"RefundDisable\\\":true,\\\"RefundRule\\\":\\\"\\\",\\\"CouponDisable\\\":false,\\\"IsGift\\\":false,\\\"OriginalSubOrderNO\\\":\\\"\\\",\\\"CreditAmount\\\":0,\\\"Organization\\\":\\\"\\\",\\\"IsAtomized\\\":false,\\\"ProviderCode\\\":\\\"\\\",\\\"IncomeType\\\":\\\"\\\",\\\"OpenCallback\\\":0,\\\"DisplaySetting\\\":\\\"\\\",\\\"TerminateType\\\":0,\\\"ErrMsg\\\":\\\"\\\",\\\"FloatTimes\\\":0,\\\"Version\\\":\\\"v2\\\",\\\"OriginalParentOrderNO\\\":\\\"\\\",\\\"IgnoreInteraction\\\":false,\\\"InsDiscountAmount\\\":0,\\\"ChargeFree\\\":\\\"\\\",\\\"BuyerID\\\":**********,\\\"PayerID\\\":**********,\\\"BusinessMode\\\":0,\\\"BuyerInfo\\\":null,\\\"PayerInfo\\\":null,\\\"SellerInfo\\\":null,\\\"SellerID\\\":3423,\\\"OwnerID\\\":**********,\\\"SiteCode\\\":\\\"660086\\\",\\\"QuotaAmount\\\":0,\\\"SettlementMode\\\":0,\\\"SellerRelSubOrderNO\\\":\\\"\\\",\\\"SellingMode\\\":0,\\\"BizType\\\":0,\\\"RealAmount\\\":0}\",\"Attribute\":\"{\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"DSType\\\":\\\"MySQL\\\",\\\"TlsTtl\\\":7,\\\"LabelType\\\":\\\"ProxyFront\\\",\\\"StartAfterPaid\\\":false,\\\"ProductType\\\":\\\"AuditLogCollectTrafficCharges\\\"}\",\"Region\":\"cn-nanjing-bbit\"}"),
						tradeNewMsg)

					Mock((*volctrade.VolcTradeClient).CallbackInstanceStatus).Return(nil).Build()
					billSvc := bill.NewMockBillingService(suite.ctrl)
					billSvc.EXPECT().GetInstance(gomock.Any(), gomock.Any()).Return(&tradeapi.Instance{BusinessStatus: int32(model.BusinessStatus_InstanceBusinessPending)}, nil).AnyTimes()
					billSvc.EXPECT().GetMultiClient().Return(&volctrade.VolcTradeClient{}).AnyTimes()

					aa := &AuditLifecycleActor{
						state: &AuditLifeState{
							LifeState:        LifeState{},
							TenantId:         "**********",
							Region:           "cn-chongqing-sdv",
							FollowInstanceId: "Postgres-00000001",
							TlsTopic:         "ab68a6d1-b401-4ae6-b574-d98cb78e0763",
							TlsProject:       "373977ae-1d6d-4069-9a28-af8dc603c5e4",
							TlsRegion:        "cn-chongqing-sdv",
							TlsEndpoint:      "https://tls-cn-guilin-boe-inner.ivolces.com",
							TlsTopicTTL:      1,
							DSType:           model.DSType_Redis,
							MsgID:            "",
							Message:          tradeNewMsg,
							AzClusterMap:     map[string]string{"compute-a": "compute-a", "compute-b": "compute-b"},
							SqlTemplateTls: &TlsConfig{
								TlsId:       1,
								TlsEndpoint: "",
								TlsRegion:   "",
								TlsProject:  "",
								TlsTopic:    "",
								TlsTopicTTL: 1,
								TlsDataType: 5,
							},
							SqlCountTls: &TlsConfig{
								TlsId:       2,
								TlsEndpoint: "",
								TlsRegion:   "",
								TlsProject:  "",
								TlsTopic:    "",
								TlsTopicTTL: 2,
								TlsDataType: 6,
							},
							SqlTableAggrTls: &TlsConfig{
								TlsId:       3,
								TlsEndpoint: "",
								TlsRegion:   "",
								TlsProject:  "",
								TlsTopic:    "",
								TlsTopicTTL: 3,
								TlsDataType: 7,
							},
							SqlTopTls: &TlsConfig{
								TlsId:       4,
								TlsEndpoint: "",
								TlsRegion:   "",
								TlsProject:  "",
								TlsTopic:    "",
								TlsTopicTTL: 4,
								TlsDataType: 8,
							},
							CapabilitiesFlags: 1,
						},
						auditService:         auditSvc,
						auditTlsDAL:          local.AuditDAL,
						fullSqlInstDAL:       local.FullSqlDAL,
						tlsDAL:               local.TlsDAL,
						statisticSqlTlsDAL:   local.StatisticSqlTlsDAL,
						statisticSqlTaskDAL:  local.StatisticSqlTaskDAL,
						instanceExtraTlsDAL:  local.InstanceExtraTlsDAL,
						crossAuthSvc:         mockCrossAuthService,
						conf:                 cfg,
						c3Conf:               c3Cnf,
						publishEventSvc:      publishEventSvc,
						source:               ds,
						tagSvc:               tagSvc,
						projectSvc:           proj,
						billSvc:              billSvc,
						lcSvc:                lcSvc,
						fullSqlConfigService: fullsqlZkConf,
					}
					if step.GetStepName() == "WaitCollectorReadyStep" {
						atDal := mock_dal.NewMockAuditTlsDAL(suite.ctrl)
						auditTls := &dao.AuditTls{
							ID:               1,
							TlsId:            1,
							InstanceID:       "1",
							FollowInstanceID: "1",
							DeployType:       model.LabelType_ProxyFront.String(),
							TenantID:         "1",
							DbType:           model.DSType_MySQL.String(),
							Status:           2,
							Deleted:          0,
						}
						atDal.EXPECT().GetByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(auditTls, nil).AnyTimes()
						aa.auditTlsDAL = atDal
					}

					aa.initCtx(tt.args.ctx)
					fmt.Printf("test stepName:%s\n", stepName)

					if _, err = step.PreCheck(tt.args.ctx, aa); err != nil {
						t.Errorf("%s PreCheck() error = %v, pass %t", step.GetStepName(), err, pass)
					}
					if err := step.ProtectExec(tt.args.ctx, aa); err != nil {
						t.Errorf("%s ProtectExec() error = %v", step.GetStepName(), err)
					}
					if step.GetStepName() != stepName {
						t.Errorf("%s GetStepName() error, stepName:%s got:%s", step.GetStepName(), stepName, step.GetStepName())
					}
					if step.MaxExecRetry() < 1 {
						t.Errorf("%s max exec less than 1", step.GetStepName())
					}
					if step.ErrorWait() < 1 {
						t.Errorf("%s error wait less than 1", step.GetStepName())
					}
					step.NextStep(tt.args.ctx, aa)
					step.Trace(tt.args.ctx, aa, "are you ok")
				})
			}
		})
	}
}

func MockStepTlsClient(suite *PgAuditCreateActorSuite) {
	Mock(BaseStep.GetInnerAccountTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(BaseStep.GetTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(CreateInnerTLSRuleStep.GetTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(CreateInnerTLSRuleStep.GetInnerAccountTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(PrepareInnerTLSIndexStep.GetTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(PrepareInnerTLSIndexStep.GetInnerAccountTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(ApplyRuleToInnerHostGroupsStep.GetTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(ApplyRuleToInnerHostGroupsStep.GetInnerAccountTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(UpdateTLSHostGroupStep.GetTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(UpdateTLSHostGroupStep.GetInnerAccountTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(CreateInnerTLSHostGroupStep.GetTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(CreateInnerTLSHostGroupStep.GetInnerAccountTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(FetchInnerProjectStep.GetTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(FetchInnerProjectStep.GetInnerAccountTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(FetchInnerProjectSqlTableStep.GetTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(FetchInnerProjectSqlTableStep.GetInnerAccountTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(FetchInnerProjectSqlTemplateStep.GetTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(FetchInnerProjectSqlTemplateStep.GetInnerAccountTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(PrepareFullSqlTLSIndexSqlCountStep.GetTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(PrepareFullSqlTLSIndexSqlCountStep.GetInnerAccountTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(CreateInnerTopicStep.GetTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(CreateInnerTopicStep.GetInnerAccountTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(PrepareFullSqlTLSIndexSqlTableStep.GetTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(PrepareFullSqlTLSIndexSqlTableStep.GetInnerAccountTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	Mock(GetInnerAccountTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	//Mock(.GetTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()
	//Mock(.GetInnerAccountTlsClient).Return(MockTlsClient(suite.ctrl), nil).Build()

}

func (suite *PgAuditCreateActorSuite) TestStep_DeleteMethod() {
	type fields struct {
		BaseStep BaseStep
	}
	type args struct {
		ctx types.Context
		a   *AuditLifecycleActor
	}

	tt := struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		name: "",
		fields: fields{
			BaseStep: BaseStep{},
		},
		args: args{
			ctx: MockActorContext(suite.ctrl),
		},
		wantErr: false,
	}
	suite.T().Run(tt.name, func(t *testing.T) {
		var pass bool
		var err error
		allStep := make(map[string]IdempotentStep)

		for name, step := range deleteSteps {
			allStep[name] = step
		}

		for stepName, step := range allStep {
			PatchConvey(stepName, suite.T(), func() {
				_, cfg, c3Cnf, local, auditSvc, publishEventSvc := MockBillNeed(suite.ctrl)
				mockCrossAuthService := MockCrossAuthService(suite.ctrl)
				ds := MockDS(suite.ctrl)
				tagSvc := MockTag(suite.ctrl)
				proj := MockProject(suite.ctrl)
				lcSvc := MockLogCollector(suite.ctrl)

				Mock((*volctrade.VolcTradeClient).CallbackInstanceStatus).Return(nil).Build()
				billSvc := bill.NewMockBillingService(suite.ctrl)
				billSvc.EXPECT().GetInstance(gomock.Any(), gomock.Any()).Return(&tradeapi.Instance{BusinessStatus: int32(model.BusinessStatus_InstanceBusinessTerminating)}, nil).AnyTimes()
				billSvc.EXPECT().GetMultiClient().Return(&volctrade.VolcTradeClient{}).AnyTimes()

				resp := &tls_sdk.CreateRuleResponse{
					CommonResponse: tls_sdk.CommonResponse{},
					RuleID:         "rule-xxxxid",
				}
				Mock(tls_enhance.CreateRule).Return(resp, nil).Build()
				Mock(tls_enhance.CleanTagTopic).Return(&tls_sdk.CommonResponse{}, nil).Build()
				Mock(tls_enhance.CreateScheduleSqlTask).Return(&tls_enhance.CreateScheduleSqlTaskResponse{}, nil).Build()
				Mock(tls_enhance.DeleteScheduleSqlTask).Return(&tls_enhance.DeleteScheduleSqlTaskResponse{}, nil).Build()
				Mock(tls_enhance.ModifyScheduleSqlTask).Return(&tls_enhance.ModifyScheduleSqlTaskResponse{}, nil).Build()
				Mock(tls_sdk.NewClient).Return(MockTlsClient(suite.ctrl)).Build()

				MockStepTlsClient(suite)

				fullsqlZkConf := mock_zkconfig.NewMockFullSqlConfigService(suite.ctrl)
				fullsqlZkConf.EXPECT().PipeConfig(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().PipeDeleteConfig(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().ConfigStatisticTls(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().ConfigDeleteStatisticTls(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().ClearInstanceCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().PipeConfigX(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().PipeDeleteConfigX(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().ConfigStatisticTlsX(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().ConfigDeleteStatisticTlsX(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				tradeNewMsg := &volctrade.TradeMessage{}
				_ = json.Unmarshal([]byte("{\"MsgID\":\"delivery_suborder_req_7187313654622441772\",\"Type\":\"New\",\"Product\":\"DBW\",\"IsResourcePack\":0,\"DeductBy\":0,\"SubjectCode\":\"3423\",\"OrderNO\":\"Order7187312971627237676\",\"SuborderNO\":\"Suborder7187312971627270444\",\"InstanceNO\":\"asdf-asdf-asdf-asdf002\",\"InstanceType\":1,\"AccountID\":**********,\"DistributorID\":0,\"EventTime\":\"2023-01-11T16:48:12+08:00\",\"BeginTime\":\"2023-01-11T16:48:12+08:00\",\"EndTime\":\"2200-01-01T00:00:00+08:00\",\"SubOrder\":\"{\\\"ID\\\":0,\\\"ParentOrderNO\\\":\\\"Order7187312971627237676\\\",\\\"Product\\\":\\\"DBW\\\",\\\"AccountID\\\":**********,\\\"UserID\\\":-1,\\\"SubOrderNO\\\":\\\"Suborder7187312971627270444\\\",\\\"SubjectCode\\\":\\\"3423\\\",\\\"OrderType\\\":1,\\\"OrderCategory\\\":1,\\\"PriceVersion\\\":\\\"********\\\",\\\"ConfigurationCode\\\":\\\"log_collect\\\",\\\"InstanceNO\\\":\\\"asdf-asdf-asdf-asdf002\\\",\\\"OriginalAmount\\\":0,\\\"DiscountAmount\\\":0,\\\"PayableAmount\\\":0,\\\"PaidAmount\\\":0,\\\"BalanceAmount\\\":0,\\\"TtypayAmount\\\":0,\\\"RefundAmount\\\":0,\\\"PayType\\\":\\\"post\\\",\\\"PayTranscationNO\\\":\\\"\\\",\\\"RefundTranscationNO\\\":\\\"\\\",\\\"BeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"EndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"PaidTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"Period\\\":\\\"\\\",\\\"Times\\\":0,\\\"WriteBackStatus\\\":0,\\\"Status\\\":1,\\\"CreatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"UpdatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ConfigItems\\\":[],\\\"EventTime\\\":0,\\\"Area\\\":\\\"\\\",\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"Zone\\\":\\\"\\\",\\\"ConfigDescription\\\":\\\"\\\",\\\"ConfigurationName\\\":\\\"日志采集费\\\",\\\"Source\\\":2,\\\"BillingType\\\":2,\\\"DiscountInfos\\\":[],\\\"TerminateOrderExtraInfos\\\":[],\\\"IsResourcePack\\\":0,\\\"DeductBy\\\":0,\\\"PreOrderNo\\\":\\\"*********************\\\",\\\"CouponAmount\\\":0,\\\"ChargeByNaturalMonth\\\":false,\\\"Instance\\\":null,\\\"IsBillPayment\\\":false,\\\"AccountInfo\\\":null,\\\"SettlementPeriod\\\":3,\\\"InheritedPaidAmount\\\":0,\\\"InheritedCouponAmount\\\":0,\\\"CarriedForwardPaidAmount\\\":0,\\\"ActualBeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ActualEndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"RefundDisable\\\":true,\\\"RefundRule\\\":\\\"\\\",\\\"CouponDisable\\\":false,\\\"IsGift\\\":false,\\\"OriginalSubOrderNO\\\":\\\"\\\",\\\"CreditAmount\\\":0,\\\"Organization\\\":\\\"\\\",\\\"IsAtomized\\\":false,\\\"ProviderCode\\\":\\\"\\\",\\\"IncomeType\\\":\\\"\\\",\\\"OpenCallback\\\":0,\\\"DisplaySetting\\\":\\\"\\\",\\\"TerminateType\\\":0,\\\"ErrMsg\\\":\\\"\\\",\\\"FloatTimes\\\":0,\\\"Version\\\":\\\"v2\\\",\\\"OriginalParentOrderNO\\\":\\\"\\\",\\\"IgnoreInteraction\\\":false,\\\"InsDiscountAmount\\\":0,\\\"ChargeFree\\\":\\\"\\\",\\\"BuyerID\\\":**********,\\\"PayerID\\\":**********,\\\"BusinessMode\\\":0,\\\"BuyerInfo\\\":null,\\\"PayerInfo\\\":null,\\\"SellerInfo\\\":null,\\\"SellerID\\\":3423,\\\"OwnerID\\\":**********,\\\"SiteCode\\\":\\\"660086\\\",\\\"QuotaAmount\\\":0,\\\"SettlementMode\\\":0,\\\"SellerRelSubOrderNO\\\":\\\"\\\",\\\"SellingMode\\\":0,\\\"BizType\\\":0,\\\"RealAmount\\\":0}\",\"Attribute\":\"{\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"DSType\\\":\\\"MySQL\\\",\\\"TlsTtl\\\":7,\\\"LabelType\\\":\\\"ProxyFront\\\",\\\"StartAfterPaid\\\":false,\\\"ProductType\\\":\\\"AuditLogCollectTrafficCharges\\\"}\",\"Region\":\"cn-nanjing-bbit\"}"),
					tradeNewMsg)

				aa := &AuditLifecycleActor{
					state: &AuditLifeState{
						LifeState:        LifeState{},
						TenantId:         "**********",
						Region:           "cn-chongqing-sdv",
						FollowInstanceId: "Postgres-00000001",
						TlsTopic:         "ab68a6d1-b401-4ae6-b574-d98cb78e0763",
						TlsProject:       "373977ae-1d6d-4069-9a28-af8dc603c5e4",
						TlsRegion:        "cn-chongqing-sdv",
						TlsEndpoint:      "https://tls-cn-guilin-boe-inner.ivolces.com",
						TlsTopicTTL:      1,
						DSType:           model.DSType_Postgres,
						MsgID:            "",
						Message:          tradeNewMsg,
						AzClusterMap:     map[string]string{"compute-a": "compute-a", "compute-b": "compute-b"},
					},
					auditService:         auditSvc,
					auditTlsDAL:          local.AuditDAL,
					tlsDAL:               local.TlsDAL,
					statisticSqlTlsDAL:   local.StatisticSqlTlsDAL,
					statisticSqlTaskDAL:  local.StatisticSqlTaskDAL,
					instanceExtraTlsDAL:  local.InstanceExtraTlsDAL,
					crossAuthSvc:         mockCrossAuthService,
					conf:                 cfg,
					c3Conf:               c3Cnf,
					publishEventSvc:      publishEventSvc,
					source:               ds,
					tagSvc:               tagSvc,
					projectSvc:           proj,
					billSvc:              billSvc,
					lcSvc:                lcSvc,
					fullSqlConfigService: fullsqlZkConf,
				}
				aa.initCtx(tt.args.ctx)
				fmt.Printf("test stepName:%s\n", stepName)
				_, err = step.PreCheck(tt.args.ctx, aa)
				if err != nil {
					t.Errorf("%s PreCheck() error = %v, pass %t", step.GetStepName(), err, pass)
				}
				if err := step.ProtectExec(tt.args.ctx, aa); err != nil {
					t.Errorf("%s ProtectExec() error = %v", step.GetStepName(), err)
				}
				if step.GetStepName() != stepName {
					t.Errorf("test stepName:%s got:%s", stepName, step.GetStepName())
				}
				if step.MaxExecRetry() < 1 {
					t.Errorf("%s max exec less than 1", step.GetStepName())
				}
				if step.ErrorWait() < 1 {
					t.Errorf("%s error wait less than 1", step.GetStepName())
				}
				step.NextStep(tt.args.ctx, aa)
				step.Trace(tt.args.ctx, aa, "are you ok")
			})
		}
	})
}

func (suite *PgAuditCreateActorSuite) TestStep_DeleteFullMethod() {
	type fields struct {
		BaseStep BaseStep
	}
	type args struct {
		ctx types.Context
		a   *AuditLifecycleActor
	}

	tt := struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		name: "",
		fields: fields{
			BaseStep: BaseStep{},
		},
		args: args{
			ctx: MockActorContext(suite.ctrl),
		},
		wantErr: false,
	}
	suite.T().Run(tt.name, func(t *testing.T) {
		var pass bool
		var err error
		allStep := make(map[string]IdempotentStep)

		for name, step := range deleteFullSqlSteps {
			allStep[name] = step
		}

		for name, step := range CreateFullSqlGCSteps {
			allStep[name] = step
		}

		for stepName, step := range allStep {
			PatchConvey(stepName, suite.T(), func() {
				_, cfg, c3Cnf, local, auditSvc, publishEventSvc := MockBillNeed(suite.ctrl)
				mockCrossAuthService := MockCrossAuthService(suite.ctrl)
				ds := MockDS(suite.ctrl)
				tagSvc := MockTag(suite.ctrl)
				proj := MockProject(suite.ctrl)
				lcSvc := MockLogCollector(suite.ctrl)

				Mock((*volctrade.VolcTradeClient).CallbackInstanceStatus).Return(nil).Build()
				billSvc := bill.NewMockBillingService(suite.ctrl)
				billSvc.EXPECT().GetInstance(gomock.Any(), gomock.Any()).Return(&tradeapi.Instance{BusinessStatus: int32(model.BusinessStatus_InstanceBusinessTerminating)}, nil).AnyTimes()
				billSvc.EXPECT().GetMultiClient().Return(&volctrade.VolcTradeClient{}).AnyTimes()

				resp := &tls_sdk.CreateRuleResponse{
					CommonResponse: tls_sdk.CommonResponse{},
					RuleID:         "rule-xxxxid",
				}
				Mock(tls_enhance.CreateRule).Return(resp, nil).Build()
				Mock(tls_enhance.CleanTagTopic).Return(&tls_sdk.CommonResponse{}, nil).Build()
				Mock(tls_enhance.CreateScheduleSqlTask).Return(&tls_enhance.CreateScheduleSqlTaskResponse{}, nil).Build()
				Mock(tls_enhance.DeleteScheduleSqlTask).Return(&tls_enhance.DeleteScheduleSqlTaskResponse{}, nil).Build()
				Mock(tls_enhance.ModifyScheduleSqlTask).Return(&tls_enhance.ModifyScheduleSqlTaskResponse{}, nil).Build()
				Mock(tls_sdk.NewClient).Return(MockTlsClient(suite.ctrl)).Build()

				fullsqlSvc := mocks_fullsql.NewMockFullSqlService(suite.ctrl)
				fullsqlSvc.EXPECT().DeleteFullSqlResource(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlSvc.EXPECT().DeleteTopic(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlSvc.EXPECT().DeleteAllFullSqlCollectionFingerprint(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				fullsqlZkConf := mock_zkconfig.NewMockFullSqlConfigService(suite.ctrl)
				fullsqlZkConf.EXPECT().PipeConfig(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().PipeDeleteConfig(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().ConfigStatisticTls(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().ConfigDeleteStatisticTls(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().ClearInstanceCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().PipeConfigX(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().PipeDeleteConfigX(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().ConfigStatisticTlsX(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().ConfigDeleteStatisticTlsX(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				tradeNewMsg := &volctrade.TradeMessage{}
				_ = json.Unmarshal([]byte("{\"MsgID\":\"delivery_suborder_req_7187313654622441772\",\"Type\":\"New\",\"Product\":\"DBW\",\"IsResourcePack\":0,\"DeductBy\":0,\"SubjectCode\":\"3423\",\"OrderNO\":\"Order7187312971627237676\",\"SuborderNO\":\"Suborder7187312971627270444\",\"InstanceNO\":\"asdf-asdf-asdf-asdf002\",\"InstanceType\":1,\"AccountID\":**********,\"DistributorID\":0,\"EventTime\":\"2023-01-11T16:48:12+08:00\",\"BeginTime\":\"2023-01-11T16:48:12+08:00\",\"EndTime\":\"2200-01-01T00:00:00+08:00\",\"SubOrder\":\"{\\\"ID\\\":0,\\\"ParentOrderNO\\\":\\\"Order7187312971627237676\\\",\\\"Product\\\":\\\"DBW\\\",\\\"AccountID\\\":**********,\\\"UserID\\\":-1,\\\"SubOrderNO\\\":\\\"Suborder7187312971627270444\\\",\\\"SubjectCode\\\":\\\"3423\\\",\\\"OrderType\\\":1,\\\"OrderCategory\\\":1,\\\"PriceVersion\\\":\\\"********\\\",\\\"ConfigurationCode\\\":\\\"log_collect\\\",\\\"InstanceNO\\\":\\\"asdf-asdf-asdf-asdf002\\\",\\\"OriginalAmount\\\":0,\\\"DiscountAmount\\\":0,\\\"PayableAmount\\\":0,\\\"PaidAmount\\\":0,\\\"BalanceAmount\\\":0,\\\"TtypayAmount\\\":0,\\\"RefundAmount\\\":0,\\\"PayType\\\":\\\"post\\\",\\\"PayTranscationNO\\\":\\\"\\\",\\\"RefundTranscationNO\\\":\\\"\\\",\\\"BeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"EndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"PaidTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"Period\\\":\\\"\\\",\\\"Times\\\":0,\\\"WriteBackStatus\\\":0,\\\"Status\\\":1,\\\"CreatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"UpdatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ConfigItems\\\":[],\\\"EventTime\\\":0,\\\"Area\\\":\\\"\\\",\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"Zone\\\":\\\"\\\",\\\"ConfigDescription\\\":\\\"\\\",\\\"ConfigurationName\\\":\\\"日志采集费\\\",\\\"Source\\\":2,\\\"BillingType\\\":2,\\\"DiscountInfos\\\":[],\\\"TerminateOrderExtraInfos\\\":[],\\\"IsResourcePack\\\":0,\\\"DeductBy\\\":0,\\\"PreOrderNo\\\":\\\"*********************\\\",\\\"CouponAmount\\\":0,\\\"ChargeByNaturalMonth\\\":false,\\\"Instance\\\":null,\\\"IsBillPayment\\\":false,\\\"AccountInfo\\\":null,\\\"SettlementPeriod\\\":3,\\\"InheritedPaidAmount\\\":0,\\\"InheritedCouponAmount\\\":0,\\\"CarriedForwardPaidAmount\\\":0,\\\"ActualBeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ActualEndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"RefundDisable\\\":true,\\\"RefundRule\\\":\\\"\\\",\\\"CouponDisable\\\":false,\\\"IsGift\\\":false,\\\"OriginalSubOrderNO\\\":\\\"\\\",\\\"CreditAmount\\\":0,\\\"Organization\\\":\\\"\\\",\\\"IsAtomized\\\":false,\\\"ProviderCode\\\":\\\"\\\",\\\"IncomeType\\\":\\\"\\\",\\\"OpenCallback\\\":0,\\\"DisplaySetting\\\":\\\"\\\",\\\"TerminateType\\\":0,\\\"ErrMsg\\\":\\\"\\\",\\\"FloatTimes\\\":0,\\\"Version\\\":\\\"v2\\\",\\\"OriginalParentOrderNO\\\":\\\"\\\",\\\"IgnoreInteraction\\\":false,\\\"InsDiscountAmount\\\":0,\\\"ChargeFree\\\":\\\"\\\",\\\"BuyerID\\\":**********,\\\"PayerID\\\":**********,\\\"BusinessMode\\\":0,\\\"BuyerInfo\\\":null,\\\"PayerInfo\\\":null,\\\"SellerInfo\\\":null,\\\"SellerID\\\":3423,\\\"OwnerID\\\":**********,\\\"SiteCode\\\":\\\"660086\\\",\\\"QuotaAmount\\\":0,\\\"SettlementMode\\\":0,\\\"SellerRelSubOrderNO\\\":\\\"\\\",\\\"SellingMode\\\":0,\\\"BizType\\\":0,\\\"RealAmount\\\":0}\",\"Attribute\":\"{\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"DSType\\\":\\\"MySQL\\\",\\\"TlsTtl\\\":7,\\\"LabelType\\\":\\\"ProxyFront\\\",\\\"StartAfterPaid\\\":false,\\\"ProductType\\\":\\\"AuditLogCollectTrafficCharges\\\"}\",\"Region\":\"cn-nanjing-bbit\"}"),
					tradeNewMsg)

				aa := &AuditLifecycleActor{
					state: &AuditLifeState{
						LifeState:        LifeState{},
						TenantId:         "**********",
						Region:           "cn-chongqing-sdv",
						FollowInstanceId: "Postgres-00000001",
						TlsTopic:         "ab68a6d1-b401-4ae6-b574-d98cb78e0763",
						TlsProject:       "373977ae-1d6d-4069-9a28-af8dc603c5e4",
						TlsRegion:        "cn-chongqing-sdv",
						TlsEndpoint:      "https://tls-cn-guilin-boe-inner.ivolces.com",
						TlsTopicTTL:      1,
						DSType:           model.DSType_Postgres,
						MsgID:            "",
						Message:          tradeNewMsg,
						AzClusterMap:     map[string]string{"compute-a": "compute-a", "compute-b": "compute-b"},
						SqlTemplateTls: &TlsConfig{
							TlsId:       1,
							TlsEndpoint: "",
							TlsRegion:   "",
							TlsProject:  "",
							TlsTopic:    "",
							TlsTopicTTL: 1,
							TlsDataType: 5,
						},
						SqlCountTls: &TlsConfig{
							TlsId:       2,
							TlsEndpoint: "",
							TlsRegion:   "",
							TlsProject:  "",
							TlsTopic:    "",
							TlsTopicTTL: 2,
							TlsDataType: 6,
						},
						SqlTableAggrTls: &TlsConfig{
							TlsId:       3,
							TlsEndpoint: "",
							TlsRegion:   "",
							TlsProject:  "",
							TlsTopic:    "",
							TlsTopicTTL: 3,
							TlsDataType: 7,
						},
						SqlTopTls: &TlsConfig{
							TlsId:       4,
							TlsEndpoint: "",
							TlsRegion:   "",
							TlsProject:  "",
							TlsTopic:    "",
							TlsTopicTTL: 4,
							TlsDataType: 8,
						},
					},
					auditService:         auditSvc,
					auditTlsDAL:          local.AuditDAL,
					tlsDAL:               local.TlsDAL,
					statisticSqlTlsDAL:   local.StatisticSqlTlsDAL,
					statisticSqlTaskDAL:  local.StatisticSqlTaskDAL,
					instanceExtraTlsDAL:  local.InstanceExtraTlsDAL,
					crossAuthSvc:         mockCrossAuthService,
					conf:                 cfg,
					c3Conf:               c3Cnf,
					publishEventSvc:      publishEventSvc,
					source:               ds,
					tagSvc:               tagSvc,
					projectSvc:           proj,
					billSvc:              billSvc,
					lcSvc:                lcSvc,
					fullSqlSvc:           fullsqlSvc,
					fullSqlInstDAL:       local.FullSqlDAL,
					fullSqlConfigService: fullsqlZkConf,
				}
				aa.initCtx(tt.args.ctx)
				fmt.Printf("test stepName:%s\n", stepName)
				_, err = step.PreCheck(tt.args.ctx, aa)
				if err != nil {
					if strings.Contains(err.Error(), "unexpected business statue") {
						// pass
					} else {
						t.Errorf("%s PreCheck() error = %v, pass %t", step.GetStepName(), err, pass)
					}
				}
				if err := step.ProtectExec(tt.args.ctx, aa); err != nil {
					t.Errorf("%s ProtectExec() error = %v", step.GetStepName(), err)
				}
				if step.GetStepName() != stepName {
					t.Errorf("test stepName:%s got:%s", stepName, step.GetStepName())
				}
				if step.MaxExecRetry() < 1 {
					t.Errorf("%s max exec less than 1", step.GetStepName())
				}
				if step.ErrorWait() < 1 {
					t.Errorf("%s error wait less than 1", step.GetStepName())
				}
				step.NextStep(tt.args.ctx, aa)
				step.Trace(tt.args.ctx, aa, "are you ok")
			})
		}
	})
}

func (suite *PgAuditCreateActorSuite) TestStep_FetchInnerProjectStep() {
	type fields struct {
		BaseStep BaseStep
	}
	type args struct {
		ctx types.Context
		a   *AuditLifecycleActor
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "",
			fields: fields{
				BaseStep: BaseStep{},
			},
			args: args{
				ctx: MockActorContext(suite.ctrl),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		suite.T().Run(tt.name, func(t *testing.T) {
			var pass bool
			var err error
			step := FetchInnerProjectStep{}
			PatchConvey(step.GetStepName(), suite.T(), func() {
				_, cfg, c3Cnf, local, auditSvc, publishEventSvc := MockBillNeed(suite.ctrl)
				mockCrossAuthService := MockCrossAuthService(suite.ctrl)
				ds := MockDS(suite.ctrl)
				tagSvc := MockTag(suite.ctrl)
				proj := MockProject(suite.ctrl)
				lcSvc := MockLogCollector(suite.ctrl)

				Mock((*volctrade.VolcTradeClient).CallbackInstanceStatus).Return(nil).Build()
				billSvc := bill.NewMockBillingService(suite.ctrl)
				billSvc.EXPECT().GetInstance(gomock.Any(), gomock.Any()).Return(&tradeapi.Instance{BusinessStatus: int32(model.BusinessStatus_InstanceBusinessTerminating)}, nil).AnyTimes()
				billSvc.EXPECT().GetMultiClient().Return(&volctrade.VolcTradeClient{}).AnyTimes()

				resp := &tls_sdk.CreateRuleResponse{
					CommonResponse: tls_sdk.CommonResponse{},
					RuleID:         "rule-xxxxid",
				}
				Mock(tls_enhance.CreateRule).Return(resp, nil).Build()
				Mock(tls_enhance.CleanTagTopic).Return(&tls_sdk.CommonResponse{}, nil).Build()
				Mock(tls_enhance.CreateScheduleSqlTask).Return(&tls_enhance.CreateScheduleSqlTaskResponse{}, nil).Build()
				Mock(tls_enhance.DeleteScheduleSqlTask).Return(&tls_enhance.DeleteScheduleSqlTaskResponse{}, nil).Build()
				Mock(tls_enhance.ModifyScheduleSqlTask).Return(&tls_enhance.ModifyScheduleSqlTaskResponse{}, nil).Build()
				Mock(tls_sdk.NewClient).Return(MockTlsClient(suite.ctrl)).Build()
				MockStepTlsClient(suite)
				fullsqlSvc := mocks_fullsql.NewMockFullSqlService(suite.ctrl)
				fullsqlSvc.EXPECT().DeleteFullSqlResource(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf := mock_zkconfig.NewMockFullSqlConfigService(suite.ctrl)
				fullsqlZkConf.EXPECT().PipeConfig(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().PipeDeleteConfig(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().ConfigStatisticTls(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().ConfigDeleteStatisticTls(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().ClearInstanceCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().PipeConfigX(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().PipeDeleteConfigX(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().ConfigStatisticTlsX(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				fullsqlZkConf.EXPECT().ConfigDeleteStatisticTlsX(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				tradeNewMsg := &volctrade.TradeMessage{}
				_ = json.Unmarshal([]byte("{\"MsgID\":\"delivery_suborder_req_7187313654622441772\",\"Type\":\"New\",\"Product\":\"DBW\",\"IsResourcePack\":0,\"DeductBy\":0,\"SubjectCode\":\"3423\",\"OrderNO\":\"Order7187312971627237676\",\"SuborderNO\":\"Suborder7187312971627270444\",\"InstanceNO\":\"asdf-asdf-asdf-asdf002\",\"InstanceType\":1,\"AccountID\":**********,\"DistributorID\":0,\"EventTime\":\"2023-01-11T16:48:12+08:00\",\"BeginTime\":\"2023-01-11T16:48:12+08:00\",\"EndTime\":\"2200-01-01T00:00:00+08:00\",\"SubOrder\":\"{\\\"ID\\\":0,\\\"ParentOrderNO\\\":\\\"Order7187312971627237676\\\",\\\"Product\\\":\\\"DBW\\\",\\\"AccountID\\\":**********,\\\"UserID\\\":-1,\\\"SubOrderNO\\\":\\\"Suborder7187312971627270444\\\",\\\"SubjectCode\\\":\\\"3423\\\",\\\"OrderType\\\":1,\\\"OrderCategory\\\":1,\\\"PriceVersion\\\":\\\"********\\\",\\\"ConfigurationCode\\\":\\\"log_collect\\\",\\\"InstanceNO\\\":\\\"asdf-asdf-asdf-asdf002\\\",\\\"OriginalAmount\\\":0,\\\"DiscountAmount\\\":0,\\\"PayableAmount\\\":0,\\\"PaidAmount\\\":0,\\\"BalanceAmount\\\":0,\\\"TtypayAmount\\\":0,\\\"RefundAmount\\\":0,\\\"PayType\\\":\\\"post\\\",\\\"PayTranscationNO\\\":\\\"\\\",\\\"RefundTranscationNO\\\":\\\"\\\",\\\"BeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"EndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"PaidTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"Period\\\":\\\"\\\",\\\"Times\\\":0,\\\"WriteBackStatus\\\":0,\\\"Status\\\":1,\\\"CreatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"UpdatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ConfigItems\\\":[],\\\"EventTime\\\":0,\\\"Area\\\":\\\"\\\",\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"Zone\\\":\\\"\\\",\\\"ConfigDescription\\\":\\\"\\\",\\\"ConfigurationName\\\":\\\"日志采集费\\\",\\\"Source\\\":2,\\\"BillingType\\\":2,\\\"DiscountInfos\\\":[],\\\"TerminateOrderExtraInfos\\\":[],\\\"IsResourcePack\\\":0,\\\"DeductBy\\\":0,\\\"PreOrderNo\\\":\\\"*********************\\\",\\\"CouponAmount\\\":0,\\\"ChargeByNaturalMonth\\\":false,\\\"Instance\\\":null,\\\"IsBillPayment\\\":false,\\\"AccountInfo\\\":null,\\\"SettlementPeriod\\\":3,\\\"InheritedPaidAmount\\\":0,\\\"InheritedCouponAmount\\\":0,\\\"CarriedForwardPaidAmount\\\":0,\\\"ActualBeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ActualEndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"RefundDisable\\\":true,\\\"RefundRule\\\":\\\"\\\",\\\"CouponDisable\\\":false,\\\"IsGift\\\":false,\\\"OriginalSubOrderNO\\\":\\\"\\\",\\\"CreditAmount\\\":0,\\\"Organization\\\":\\\"\\\",\\\"IsAtomized\\\":false,\\\"ProviderCode\\\":\\\"\\\",\\\"IncomeType\\\":\\\"\\\",\\\"OpenCallback\\\":0,\\\"DisplaySetting\\\":\\\"\\\",\\\"TerminateType\\\":0,\\\"ErrMsg\\\":\\\"\\\",\\\"FloatTimes\\\":0,\\\"Version\\\":\\\"v2\\\",\\\"OriginalParentOrderNO\\\":\\\"\\\",\\\"IgnoreInteraction\\\":false,\\\"InsDiscountAmount\\\":0,\\\"ChargeFree\\\":\\\"\\\",\\\"BuyerID\\\":**********,\\\"PayerID\\\":**********,\\\"BusinessMode\\\":0,\\\"BuyerInfo\\\":null,\\\"PayerInfo\\\":null,\\\"SellerInfo\\\":null,\\\"SellerID\\\":3423,\\\"OwnerID\\\":**********,\\\"SiteCode\\\":\\\"660086\\\",\\\"QuotaAmount\\\":0,\\\"SettlementMode\\\":0,\\\"SellerRelSubOrderNO\\\":\\\"\\\",\\\"SellingMode\\\":0,\\\"BizType\\\":0,\\\"RealAmount\\\":0}\",\"Attribute\":\"{\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"DSType\\\":\\\"MySQL\\\",\\\"TlsTtl\\\":7,\\\"LabelType\\\":\\\"ProxyFront\\\",\\\"StartAfterPaid\\\":false,\\\"ProductType\\\":\\\"AuditLogCollectTrafficCharges\\\"}\",\"Region\":\"cn-nanjing-bbit\"}"),
					tradeNewMsg)

				aa := &AuditLifecycleActor{
					state: &AuditLifeState{
						LifeState:        LifeState{},
						TenantId:         "**********",
						Region:           "cn-chongqing-sdv",
						FollowInstanceId: "Postgres-00000001",
						TlsTopic:         "",
						TlsProject:       "",
						TlsRegion:        "cn-chongqing-sdv",
						TlsEndpoint:      "https://tls-cn-guilin-boe-inner.ivolces.com",
						TlsTopicTTL:      1,
						DSType:           model.DSType_Postgres,
						MsgID:            "",
						AzClusterMap:     map[string]string{"compute-a": "compute-a", "compute-b": "compute-b"},
					},
					auditService:        auditSvc,
					auditTlsDAL:         local.AuditDAL,
					tlsDAL:              local.TlsDAL,
					statisticSqlTlsDAL:  local.StatisticSqlTlsDAL,
					statisticSqlTaskDAL: local.StatisticSqlTaskDAL,
					instanceExtraTlsDAL: local.InstanceExtraTlsDAL,
					crossAuthSvc:        mockCrossAuthService,
					conf:                cfg,
					c3Conf:              c3Cnf,
					publishEventSvc:     publishEventSvc,
					source:              ds,
					tagSvc:              tagSvc,
					projectSvc:          proj,
					billSvc:             billSvc,
					lcSvc:               lcSvc,
					fullSqlSvc:          fullsqlSvc,
					fullSqlInstDAL:      local.FullSqlDAL,
				}

				aa.initCtx(tt.args.ctx)
				fmt.Printf("test stepName:%s\n", step.GetStepName())

				if _, err = step.PreCheck(tt.args.ctx, aa); err != nil {
					t.Errorf("%s PreCheck() error = %v, pass %t", step.GetStepName(), err, pass)
				}
				if err := step.ProtectExec(tt.args.ctx, aa); err != nil {
					t.Errorf("%s ProtectExec() error = %v", step.GetStepName(), err)
				}
				structName := reflect.TypeOf(step).Name()
				if step.GetStepName() != structName {
					t.Errorf("%s GetStepName() error, stepName:%s got:%s", step.GetStepName(), structName, step.GetStepName())
				}
				if step.MaxExecRetry() < 1 {
					t.Errorf("%s max exec less than 1", step.GetStepName())
				}
				if step.ErrorWait() < 1 {
					t.Errorf("%s error wait less than 1", step.GetStepName())
				}
				step.NextStep(tt.args.ctx, aa)
				step.Trace(tt.args.ctx, aa, "are you ok")
			})
		})
	}
}

func (suite *PgAuditCreateActorSuite) TestActorNew() {
	suite.T().Run("", func(t *testing.T) {
		PatchConvey("", suite.T(), func() {
			_, cfg, c3Cnf, local, auditSvc, publishEventSvc := MockBillNeed(suite.ctrl)
			mockCrossAuthService := MockCrossAuthService(suite.ctrl)
			ds := MockDS(suite.ctrl)
			tagSvc := MockTag(suite.ctrl)
			proj := MockProject(suite.ctrl)
			lcSvc := MockLogCollector(suite.ctrl)

			Mock((*volctrade.VolcTradeClient).CallbackInstanceStatus).Return(nil).Build()
			billSvc := bill.NewMockBillingService(suite.ctrl)
			billSvc.EXPECT().GetInstance(gomock.Any(), gomock.Any()).Return(&tradeapi.Instance{BusinessStatus: int32(model.BusinessStatus_InstanceBusinessTerminating)}, nil).AnyTimes()
			billSvc.EXPECT().GetMultiClient().Return(&volctrade.VolcTradeClient{}).AnyTimes()

			resp := &tls_sdk.CreateRuleResponse{
				CommonResponse: tls_sdk.CommonResponse{},
				RuleID:         "rule-xxxxid",
			}
			Mock(tls_enhance.CreateRule).Return(resp, nil).Build()

			tradeNewMsg := &volctrade.TradeMessage{}
			_ = json.Unmarshal([]byte("{\"MsgID\":\"delivery_suborder_req_7187313654622441772\",\"Type\":\"New\",\"Product\":\"DBW\",\"IsResourcePack\":0,\"DeductBy\":0,\"SubjectCode\":\"3423\",\"OrderNO\":\"Order7187312971627237676\",\"SuborderNO\":\"Suborder7187312971627270444\",\"InstanceNO\":\"asdf-asdf-asdf-asdf002\",\"InstanceType\":1,\"AccountID\":**********,\"DistributorID\":0,\"EventTime\":\"2023-01-11T16:48:12+08:00\",\"BeginTime\":\"2023-01-11T16:48:12+08:00\",\"EndTime\":\"2200-01-01T00:00:00+08:00\",\"SubOrder\":\"{\\\"ID\\\":0,\\\"ParentOrderNO\\\":\\\"Order7187312971627237676\\\",\\\"Product\\\":\\\"DBW\\\",\\\"AccountID\\\":**********,\\\"UserID\\\":-1,\\\"SubOrderNO\\\":\\\"Suborder7187312971627270444\\\",\\\"SubjectCode\\\":\\\"3423\\\",\\\"OrderType\\\":1,\\\"OrderCategory\\\":1,\\\"PriceVersion\\\":\\\"********\\\",\\\"ConfigurationCode\\\":\\\"log_collect\\\",\\\"InstanceNO\\\":\\\"asdf-asdf-asdf-asdf002\\\",\\\"OriginalAmount\\\":0,\\\"DiscountAmount\\\":0,\\\"PayableAmount\\\":0,\\\"PaidAmount\\\":0,\\\"BalanceAmount\\\":0,\\\"TtypayAmount\\\":0,\\\"RefundAmount\\\":0,\\\"PayType\\\":\\\"post\\\",\\\"PayTranscationNO\\\":\\\"\\\",\\\"RefundTranscationNO\\\":\\\"\\\",\\\"BeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"EndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"PaidTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"Period\\\":\\\"\\\",\\\"Times\\\":0,\\\"WriteBackStatus\\\":0,\\\"Status\\\":1,\\\"CreatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"UpdatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ConfigItems\\\":[],\\\"EventTime\\\":0,\\\"Area\\\":\\\"\\\",\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"Zone\\\":\\\"\\\",\\\"ConfigDescription\\\":\\\"\\\",\\\"ConfigurationName\\\":\\\"日志采集费\\\",\\\"Source\\\":2,\\\"BillingType\\\":2,\\\"DiscountInfos\\\":[],\\\"TerminateOrderExtraInfos\\\":[],\\\"IsResourcePack\\\":0,\\\"DeductBy\\\":0,\\\"PreOrderNo\\\":\\\"*********************\\\",\\\"CouponAmount\\\":0,\\\"ChargeByNaturalMonth\\\":false,\\\"Instance\\\":null,\\\"IsBillPayment\\\":false,\\\"AccountInfo\\\":null,\\\"SettlementPeriod\\\":3,\\\"InheritedPaidAmount\\\":0,\\\"InheritedCouponAmount\\\":0,\\\"CarriedForwardPaidAmount\\\":0,\\\"ActualBeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ActualEndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"RefundDisable\\\":true,\\\"RefundRule\\\":\\\"\\\",\\\"CouponDisable\\\":false,\\\"IsGift\\\":false,\\\"OriginalSubOrderNO\\\":\\\"\\\",\\\"CreditAmount\\\":0,\\\"Organization\\\":\\\"\\\",\\\"IsAtomized\\\":false,\\\"ProviderCode\\\":\\\"\\\",\\\"IncomeType\\\":\\\"\\\",\\\"OpenCallback\\\":0,\\\"DisplaySetting\\\":\\\"\\\",\\\"TerminateType\\\":0,\\\"ErrMsg\\\":\\\"\\\",\\\"FloatTimes\\\":0,\\\"Version\\\":\\\"v2\\\",\\\"OriginalParentOrderNO\\\":\\\"\\\",\\\"IgnoreInteraction\\\":false,\\\"InsDiscountAmount\\\":0,\\\"ChargeFree\\\":\\\"\\\",\\\"BuyerID\\\":**********,\\\"PayerID\\\":**********,\\\"BusinessMode\\\":0,\\\"BuyerInfo\\\":null,\\\"PayerInfo\\\":null,\\\"SellerInfo\\\":null,\\\"SellerID\\\":3423,\\\"OwnerID\\\":**********,\\\"SiteCode\\\":\\\"660086\\\",\\\"QuotaAmount\\\":0,\\\"SettlementMode\\\":0,\\\"SellerRelSubOrderNO\\\":\\\"\\\",\\\"SellingMode\\\":0,\\\"BizType\\\":0,\\\"RealAmount\\\":0}\",\"Attribute\":\"{\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"DSType\\\":\\\"MySQL\\\",\\\"TlsTtl\\\":7,\\\"LabelType\\\":\\\"ProxyFront\\\",\\\"StartAfterPaid\\\":false,\\\"ProductType\\\":\\\"AuditLogCollectTrafficCharges\\\"}\",\"Region\":\"cn-nanjing-bbit\"}"),
				tradeNewMsg)

			aa := &AuditLifecycleActor{
				state: &AuditLifeState{
					LifeState:        LifeState{},
					TenantId:         "**********",
					Region:           "cn-chongqing-sdv",
					FollowInstanceId: "Postgres-00000001",
					TlsTopic:         "ab68a6d1-b401-4ae6-b574-d98cb78e0763",
					TlsProject:       "373977ae-1d6d-4069-9a28-af8dc603c5e4",
					TlsRegion:        "cn-chongqing-sdv",
					TlsEndpoint:      "https://tls-cn-guilin-boe-inner.ivolces.com",
					TlsTopicTTL:      1,
					DSType:           model.DSType_Postgres,
					MsgID:            "",
					Message:          tradeNewMsg,
					AzClusterMap:     map[string]string{"compute-a": "compute-a", "compute-b": "compute-b"},
				},
				auditService:        auditSvc,
				auditTlsDAL:         local.AuditDAL,
				tlsDAL:              local.TlsDAL,
				statisticSqlTlsDAL:  local.StatisticSqlTlsDAL,
				statisticSqlTaskDAL: local.StatisticSqlTaskDAL,
				instanceExtraTlsDAL: local.InstanceExtraTlsDAL,
				crossAuthSvc:        mockCrossAuthService,
				conf:                cfg,
				c3Conf:              c3Cnf,
				publishEventSvc:     publishEventSvc,
				source:              ds,
				tagSvc:              tagSvc,
				projectSvc:          proj,
				billSvc:             billSvc,
				lcSvc:               lcSvc,
			}
			createCtx := MockActorCtx(suite.ctrl, &shared.CreateAuditFlowRequest{
				InstanceID: "aaa",
				Msg:        "{\"MsgID\":\"delivery_suborder_req_7187313654622441772\",\"Type\":\"New\",\"Product\":\"DBW\",\"IsResourcePack\":0,\"DeductBy\":0,\"SubjectCode\":\"3423\",\"OrderNO\":\"Order7187312971627237676\",\"SuborderNO\":\"Suborder7187312971627270444\",\"InstanceNO\":\"asdf-asdf-asdf-asdf002\",\"InstanceType\":1,\"AccountID\":**********,\"DistributorID\":0,\"EventTime\":\"2023-01-11T16:48:12+08:00\",\"BeginTime\":\"2023-01-11T16:48:12+08:00\",\"EndTime\":\"2200-01-01T00:00:00+08:00\",\"SubOrder\":\"{\\\"ID\\\":0,\\\"ParentOrderNO\\\":\\\"Order7187312971627237676\\\",\\\"Product\\\":\\\"DBW\\\",\\\"AccountID\\\":**********,\\\"UserID\\\":-1,\\\"SubOrderNO\\\":\\\"Suborder7187312971627270444\\\",\\\"SubjectCode\\\":\\\"3423\\\",\\\"OrderType\\\":1,\\\"OrderCategory\\\":1,\\\"PriceVersion\\\":\\\"********\\\",\\\"ConfigurationCode\\\":\\\"log_collect\\\",\\\"InstanceNO\\\":\\\"asdf-asdf-asdf-asdf002\\\",\\\"OriginalAmount\\\":0,\\\"DiscountAmount\\\":0,\\\"PayableAmount\\\":0,\\\"PaidAmount\\\":0,\\\"BalanceAmount\\\":0,\\\"TtypayAmount\\\":0,\\\"RefundAmount\\\":0,\\\"PayType\\\":\\\"post\\\",\\\"PayTranscationNO\\\":\\\"\\\",\\\"RefundTranscationNO\\\":\\\"\\\",\\\"BeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"EndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"PaidTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"Period\\\":\\\"\\\",\\\"Times\\\":0,\\\"WriteBackStatus\\\":0,\\\"Status\\\":1,\\\"CreatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"UpdatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ConfigItems\\\":[],\\\"EventTime\\\":0,\\\"Area\\\":\\\"\\\",\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"Zone\\\":\\\"\\\",\\\"ConfigDescription\\\":\\\"\\\",\\\"ConfigurationName\\\":\\\"日志采集费\\\",\\\"Source\\\":2,\\\"BillingType\\\":2,\\\"DiscountInfos\\\":[],\\\"TerminateOrderExtraInfos\\\":[],\\\"IsResourcePack\\\":0,\\\"DeductBy\\\":0,\\\"PreOrderNo\\\":\\\"*********************\\\",\\\"CouponAmount\\\":0,\\\"ChargeByNaturalMonth\\\":false,\\\"Instance\\\":null,\\\"IsBillPayment\\\":false,\\\"AccountInfo\\\":null,\\\"SettlementPeriod\\\":3,\\\"InheritedPaidAmount\\\":0,\\\"InheritedCouponAmount\\\":0,\\\"CarriedForwardPaidAmount\\\":0,\\\"ActualBeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ActualEndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"RefundDisable\\\":true,\\\"RefundRule\\\":\\\"\\\",\\\"CouponDisable\\\":false,\\\"IsGift\\\":false,\\\"OriginalSubOrderNO\\\":\\\"\\\",\\\"CreditAmount\\\":0,\\\"Organization\\\":\\\"\\\",\\\"IsAtomized\\\":false,\\\"ProviderCode\\\":\\\"\\\",\\\"IncomeType\\\":\\\"\\\",\\\"OpenCallback\\\":0,\\\"DisplaySetting\\\":\\\"\\\",\\\"TerminateType\\\":0,\\\"ErrMsg\\\":\\\"\\\",\\\"FloatTimes\\\":0,\\\"Version\\\":\\\"v2\\\",\\\"OriginalParentOrderNO\\\":\\\"\\\",\\\"IgnoreInteraction\\\":false,\\\"InsDiscountAmount\\\":0,\\\"ChargeFree\\\":\\\"\\\",\\\"BuyerID\\\":**********,\\\"PayerID\\\":**********,\\\"BusinessMode\\\":0,\\\"BuyerInfo\\\":null,\\\"PayerInfo\\\":null,\\\"SellerInfo\\\":null,\\\"SellerID\\\":3423,\\\"OwnerID\\\":**********,\\\"SiteCode\\\":\\\"660086\\\",\\\"QuotaAmount\\\":0,\\\"SettlementMode\\\":0,\\\"SellerRelSubOrderNO\\\":\\\"\\\",\\\"SellingMode\\\":0,\\\"BizType\\\":0,\\\"RealAmount\\\":0}\",\"Attribute\":\"{\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"DSType\\\":\\\"MySQL\\\",\\\"TlsTtl\\\":7,\\\"LabelType\\\":\\\"ProxyFront\\\",\\\"StartAfterPaid\\\":false,\\\"ProductType\\\":\\\"AuditLogCollectTrafficCharges\\\"}\",\"Region\":\"cn-nanjing-bbit\"}",
			})
			aa.Process(createCtx)

			deleteCtx := MockActorCtx(suite.ctrl, &shared.DeleteAuditFlowRequest{
				InstanceID: "aaa",
				Msg:        "{\"MsgID\":\"delivery_suborder_req_7187313654622441772\",\"Type\":\"New\",\"Product\":\"DBW\",\"IsResourcePack\":0,\"DeductBy\":0,\"SubjectCode\":\"3423\",\"OrderNO\":\"Order7187312971627237676\",\"SuborderNO\":\"Suborder7187312971627270444\",\"InstanceNO\":\"asdf-asdf-asdf-asdf002\",\"InstanceType\":1,\"AccountID\":**********,\"DistributorID\":0,\"EventTime\":\"2023-01-11T16:48:12+08:00\",\"BeginTime\":\"2023-01-11T16:48:12+08:00\",\"EndTime\":\"2200-01-01T00:00:00+08:00\",\"SubOrder\":\"{\\\"ID\\\":0,\\\"ParentOrderNO\\\":\\\"Order7187312971627237676\\\",\\\"Product\\\":\\\"DBW\\\",\\\"AccountID\\\":**********,\\\"UserID\\\":-1,\\\"SubOrderNO\\\":\\\"Suborder7187312971627270444\\\",\\\"SubjectCode\\\":\\\"3423\\\",\\\"OrderType\\\":1,\\\"OrderCategory\\\":1,\\\"PriceVersion\\\":\\\"********\\\",\\\"ConfigurationCode\\\":\\\"log_collect\\\",\\\"InstanceNO\\\":\\\"asdf-asdf-asdf-asdf002\\\",\\\"OriginalAmount\\\":0,\\\"DiscountAmount\\\":0,\\\"PayableAmount\\\":0,\\\"PaidAmount\\\":0,\\\"BalanceAmount\\\":0,\\\"TtypayAmount\\\":0,\\\"RefundAmount\\\":0,\\\"PayType\\\":\\\"post\\\",\\\"PayTranscationNO\\\":\\\"\\\",\\\"RefundTranscationNO\\\":\\\"\\\",\\\"BeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"EndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"PaidTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"Period\\\":\\\"\\\",\\\"Times\\\":0,\\\"WriteBackStatus\\\":0,\\\"Status\\\":1,\\\"CreatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"UpdatedTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ConfigItems\\\":[],\\\"EventTime\\\":0,\\\"Area\\\":\\\"\\\",\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"Zone\\\":\\\"\\\",\\\"ConfigDescription\\\":\\\"\\\",\\\"ConfigurationName\\\":\\\"日志采集费\\\",\\\"Source\\\":2,\\\"BillingType\\\":2,\\\"DiscountInfos\\\":[],\\\"TerminateOrderExtraInfos\\\":[],\\\"IsResourcePack\\\":0,\\\"DeductBy\\\":0,\\\"PreOrderNo\\\":\\\"*********************\\\",\\\"CouponAmount\\\":0,\\\"ChargeByNaturalMonth\\\":false,\\\"Instance\\\":null,\\\"IsBillPayment\\\":false,\\\"AccountInfo\\\":null,\\\"SettlementPeriod\\\":3,\\\"InheritedPaidAmount\\\":0,\\\"InheritedCouponAmount\\\":0,\\\"CarriedForwardPaidAmount\\\":0,\\\"ActualBeginTime\\\":\\\"2023-01-11 16:48:12\\\",\\\"ActualEndTime\\\":\\\"2200-01-01 00:00:00\\\",\\\"RefundDisable\\\":true,\\\"RefundRule\\\":\\\"\\\",\\\"CouponDisable\\\":false,\\\"IsGift\\\":false,\\\"OriginalSubOrderNO\\\":\\\"\\\",\\\"CreditAmount\\\":0,\\\"Organization\\\":\\\"\\\",\\\"IsAtomized\\\":false,\\\"ProviderCode\\\":\\\"\\\",\\\"IncomeType\\\":\\\"\\\",\\\"OpenCallback\\\":0,\\\"DisplaySetting\\\":\\\"\\\",\\\"TerminateType\\\":0,\\\"ErrMsg\\\":\\\"\\\",\\\"FloatTimes\\\":0,\\\"Version\\\":\\\"v2\\\",\\\"OriginalParentOrderNO\\\":\\\"\\\",\\\"IgnoreInteraction\\\":false,\\\"InsDiscountAmount\\\":0,\\\"ChargeFree\\\":\\\"\\\",\\\"BuyerID\\\":**********,\\\"PayerID\\\":**********,\\\"BusinessMode\\\":0,\\\"BuyerInfo\\\":null,\\\"PayerInfo\\\":null,\\\"SellerInfo\\\":null,\\\"SellerID\\\":3423,\\\"OwnerID\\\":**********,\\\"SiteCode\\\":\\\"660086\\\",\\\"QuotaAmount\\\":0,\\\"SettlementMode\\\":0,\\\"SellerRelSubOrderNO\\\":\\\"\\\",\\\"SellingMode\\\":0,\\\"BizType\\\":0,\\\"RealAmount\\\":0}\",\"Attribute\":\"{\\\"Region\\\":\\\"cn-nanjing-bbit\\\",\\\"DSType\\\":\\\"MySQL\\\",\\\"TlsTtl\\\":7,\\\"LabelType\\\":\\\"ProxyFront\\\",\\\"StartAfterPaid\\\":false,\\\"ProductType\\\":\\\"AuditLogCollectTrafficCharges\\\"}\",\"Region\":\"cn-nanjing-bbit\"}",
			})
			aa.Process(deleteCtx)

			NewAuditCreateLifecycleActor(NewAuditCreateLifecycleActorIn{})
			NewAuditDeleteLifecycleActor(NewAuditCreateLifecycleActorIn{})
		})
	})
}

func MockActorCtx(ctrl *gomock.Controller, message interface{}) types.Context {
	tctx := mocks.NewMockContext(ctrl)
	tctx.EXPECT().Value(gomock.Any()).AnyTimes()
	tctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	tctx.EXPECT().Message().Return(message).Times(1)
	tctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	tctx.EXPECT().Send(gomock.Any(), gomock.Any()).Return().AnyTimes()
	tctx.EXPECT().Sender().Return(&actor.PID{}).AnyTimes()
	tctx.EXPECT().GetName().Return("a|b|c|d").AnyTimes()
	tctx.EXPECT().GetKind().Return(consts.PgAuditCreateActorKind).AnyTimes()
	tctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
	tctx.EXPECT().Self().Return(&actor.PID{}).AnyTimes()
	return tctx
}

func MockLogCollector(ctrl *gomock.Controller) *log_collector.MockLogCollectorInterface {
	lcSvc := log_collector.NewMockLogCollectorInterface(ctrl)
	lcSvc.EXPECT().DeleteTenantFromLogCollector(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	lcSvc.EXPECT().DeployLogCollector(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	lcSvc.EXPECT().ModifyLogCollectorImage(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	lcSvc.EXPECT().DeployInnerLogCollector(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	return lcSvc
}

// MockTlsClientSTABLE Stable TLS接口调试专用
func MockTlsClientSTABLE() tls_sdk.Client {
	serviceAK := "AKLTODY2M2EzMzRlNTdhNGU3ZDhhZTlhNDgxYzA3MjkyODY"
	ServiceSK := "WVdSak1UQTNNR1F6TURjMk5ETTJZVGt6TldJNU5XTXpZemxqT1RoallUSQ=="
	client := iam.NewVolcIAMClient("boe_stable", serviceAK, ServiceSK)
	resp, err := client.AssumeRole("**********", "DBWAuditLogArchiveTLSRole")
	if err != nil {
		fmt.Println(err)
	}
	fmt.Printf("%s", resp)
	region := "cn-chongqing-sdv"
	os.Setenv("BDC_REGION_ID", region)
	tlsEndpoint := fmt.Sprintf("https://tls-%s.volces.com", region)
	tlsClient := tls_sdk.NewClient(tlsEndpoint, resp.AccessKeyId, resp.SecretAccessKey, resp.SessionToken, region)
	return tlsClient
}

func MockTlsClient(ctrl *gomock.Controller) tls_sdk.Client {
	tlsClient := mock_tls.NewMockClient(ctrl)
	tlsClient.EXPECT().CreateProject(gomock.Any()).Return(&tls_sdk.CreateProjectResponse{
		CommonResponse: tls_sdk.CommonResponse{},
		ProjectID:      "1",
	}, nil).AnyTimes()
	tlsClient.EXPECT().DescribeProject(gomock.Any()).Return(&tls_sdk.DescribeProjectResponse{
		CommonResponse: tls_sdk.CommonResponse{},
		ProjectInfo: tls_sdk.ProjectInfo{
			ProjectID:       "1",
			ProjectName:     "1",
			Description:     "1",
			CreateTimestamp: "",
			TopicCount:      0,
			InnerNetDomain:  "",
		},
	}, nil).AnyTimes()
	tlsClient.EXPECT().CreateTopic(gomock.Any()).Return(&tls_sdk.CreateTopicResponse{
		CommonResponse: tls_sdk.CommonResponse{},
		TopicID:        "1",
	}, nil).AnyTimes()
	tlsClient.EXPECT().CreateIndex(gomock.Any()).Return(&tls_sdk.CreateIndexResponse{
		CommonResponse: tls_sdk.CommonResponse{},
		TopicID:        "1",
	}, nil).AnyTimes()
	tlsClient.EXPECT().ModifyIndex(gomock.Any()).Return(&tls_sdk.CommonResponse{}, nil).AnyTimes()
	tlsClient.EXPECT().DeleteTopic(gomock.Any()).Return(&tls_sdk.CommonResponse{
		RequestID: "123",
	}, nil).AnyTimes()
	tlsClient.EXPECT().CreateDownloadTask(gomock.Any()).Return(&tls_sdk.CreateDownloadTaskResponse{
		CommonResponse: tls_sdk.CommonResponse{},
		TaskId:         "123",
	}, nil).AnyTimes()
	tlsClient.EXPECT().DescribeDownloadTasks(gomock.Any()).Return(&tls_sdk.DescribeDownloadTasksResponse{
		CommonResponse: tls_sdk.CommonResponse{},
		Tasks: []*tls_sdk.DownloadTaskResp{
			{
				TaskId:      "1",
				TaskName:    "1",
				TopicId:     "1",
				Query:       "1",
				StartTime:   "1",
				EndTime:     "1",
				LogCount:    1,
				LogSize:     1,
				Compression: "1",
				DataFormat:  "1",
				TaskStatus:  "1",
				CreateTime:  "1",
			},
		},
		Total: 0,
	}, nil).AnyTimes()
	tlsClient.EXPECT().DescribeDownloadUrl(gomock.Any()).Return(&tls_sdk.DescribeDownloadUrlResponse{
		CommonResponse: tls_sdk.CommonResponse{},
		DownloadUrl:    "http://baidu.com/123",
	}, nil).AnyTimes()
	tlsClient.EXPECT().SearchLogs(gomock.Any()).Return(&tls_sdk.SearchLogsResponse{
		CommonResponse: tls_sdk.CommonResponse{},
		Status:         "1",
		Analysis:       false,
		ListOver:       false,
		HitCount:       0,
		Count:          0,
		Limit:          0,
		Logs: []map[string]interface{}{
			{
				"a": "1",
				"b": "2",
			},
			{
				"c": "3",
				"d": "4",
			},
		},
		AnalysisResult: nil,
		Context:        "",
		HighLight:      nil,
	}, nil).AnyTimes()
	tlsClient.EXPECT().DescribeHostGroups(gomock.Any()).Return(&tls_sdk.DescribeHostGroupsResponse{
		CommonResponse:           tls_sdk.CommonResponse{},
		Total:                    0,
		HostGroupHostsRulesInfos: nil,
	}, nil).AnyTimes()
	tlsClient.EXPECT().CreateHostGroup(gomock.Any()).Return(&tls_sdk.CreateHostGroupResponse{
		CommonResponse: tls_sdk.CommonResponse{},
		HostGroupID:    "hostgroupid111",
	}, nil).AnyTimes()
	tlsClient.EXPECT().DescribeRule(gomock.Any()).Return(&tls_sdk.DescribeRuleResponse{
		CommonResponse: tls_sdk.CommonResponse{},
		ProjectID:      "1",
		ProjectName:    "1",
		TopicID:        "1",
		TopicName:      "1",
		RuleInfo:       nil,
		HostGroupInfos: nil,
	}, nil).AnyTimes()
	tlsClient.EXPECT().DescribeRules(gomock.Any()).Return(&tls_sdk.DescribeRulesResponse{
		CommonResponse: tls_sdk.CommonResponse{},
		Total:          0,
		RuleInfos:      nil,
	}, nil).AnyTimes()
	tlsClient.EXPECT().CreateRule(gomock.Any()).Return(&tls_sdk.CreateRuleResponse{
		CommonResponse: tls_sdk.CommonResponse{},
		RuleID:         "ruleid111",
	}, nil).AnyTimes()

	tlsClient.EXPECT().ApplyRuleToHostGroups(gomock.Any()).Return(&tls_sdk.CommonResponse{}, nil).AnyTimes()
	tlsClient.EXPECT().DescribeIndex(gomock.Any()).Return(&tls_sdk.DescribeIndexResponse{}, errors.New("IndexNotExists")).AnyTimes()
	//Mock(tls_sdk.NewClient).Return(tlsClient).Build()
	return tlsClient
}

func MockActorContext(ctrl *gomock.Controller) types.Context {
	actorCtx := mocks.NewMockContext(ctrl)
	actorCtx.EXPECT().Value(gomock.Any()).AnyTimes()
	actorCtx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes()
	actorCtx.EXPECT().GetName().Return("a|b|c|d").AnyTimes()
	actorCtx.EXPECT().GetKind().Return(consts.PgAuditCreateActorKind).AnyTimes()
	actorCtx.EXPECT().Self().Return(nil).AnyTimes()
	actorCtx.EXPECT().Send(gomock.Any(), gomock.Any()).Return().AnyTimes()
	actorCtx.EXPECT().Respond(gomock.Any()).Return().AnyTimes()
	kindClient := mocks.NewMockKindClient(ctrl)
	kindClient.EXPECT().Call(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	kindClient.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	actorCtx.EXPECT().ClientOf(gomock.Any()).Return(kindClient).AnyTimes()
	return actorCtx
}

type normalStep struct {
}

func (n normalStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (n normalStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	return nil
}

func (n normalStep) NextStep(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
	return &nextStep{}
}

func (n normalStep) MaxExecRetry() int {
	return 3
}

func (n normalStep) ErrorWait() time.Duration {
	return time.Millisecond
}

func (n normalStep) GetStepName() string {
	return "normalStep"
}

func (n normalStep) Trace(ctx types.Context, a *AuditLifecycleActor, comment ...string) {
	a.state.LifeState.HistoryStepsTrace = append(a.state.LifeState.HistoryStepsTrace, comment)
}

type jumpStep struct {
}

func (n jumpStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return true, nil
}

func (n jumpStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	return nil
}

func (n jumpStep) NextStep(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
	return &nextStep{}
}

func (n jumpStep) MaxExecRetry() int {
	return 3
}

func (n jumpStep) ErrorWait() time.Duration {
	return time.Millisecond
}

func (n jumpStep) GetStepName() string {
	return "jumpStep"
}

func (n jumpStep) Trace(ctx types.Context, a *AuditLifecycleActor, comment ...string) {
	a.state.LifeState.HistoryStepsTrace = append(a.state.LifeState.HistoryStepsTrace, comment)
}

type preErrorStep struct {
}

func (n preErrorStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, errors.New("pre check error")
}

func (n preErrorStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	return nil
}

func (n preErrorStep) NextStep(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
	return &nextStep{}
}

func (n preErrorStep) MaxExecRetry() int {
	return 3
}

func (n preErrorStep) ErrorWait() time.Duration {
	return time.Millisecond
}

func (n preErrorStep) GetStepName() string {
	return "preErrorStep"
}

func (n preErrorStep) Trace(ctx types.Context, a *AuditLifecycleActor, comment ...string) {
	a.state.LifeState.HistoryStepsTrace = append(a.state.LifeState.HistoryStepsTrace, comment)
}

type execErrorStep struct {
}

func (n execErrorStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (n execErrorStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	return errors.New("protect execute failed")
}

func (n execErrorStep) NextStep(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
	return &nextStep{}
}

func (n execErrorStep) MaxExecRetry() int {
	return 3
}

func (n execErrorStep) ErrorWait() time.Duration {
	return time.Millisecond
}

func (n execErrorStep) GetStepName() string {
	return "execErrorStep"
}

func (n execErrorStep) Trace(ctx types.Context, a *AuditLifecycleActor, comment ...string) {
	a.state.LifeState.HistoryStepsTrace = append(a.state.LifeState.HistoryStepsTrace, comment)
}

type finishStep struct {
}

func (n finishStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (n finishStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	return nil
}

func (n finishStep) NextStep(ctx types.Context, a *AuditLifecycleActor) IdempotentStep {
	return nil
}

func (n finishStep) MaxExecRetry() int {
	return 3
}

func (n finishStep) ErrorWait() time.Duration {
	return time.Millisecond
}

func (n finishStep) GetStepName() string {
	return "finishStep"
}

func (n finishStep) Trace(ctx types.Context, a *AuditLifecycleActor, comment ...string) {
	a.state.LifeState.HistoryStepsTrace = append(a.state.LifeState.HistoryStepsTrace, comment)
}

type nextStep struct {
	BaseStep
}

func (n nextStep) GetStepName() string {
	return "nextStep"
}

func (suite *PgAuditCreateActorSuite) TestAuditLifecycleActor_RunStep() {
	Mock(debug.Stack).Return([]byte("---------stack--------")).Build()
	type fields struct {
		state           *AuditLifeState
		idempotentSteps map[string]IdempotentStep
		source          datasource.DataSourceService
		crossAuthSvc    cross_service_authorization.CrossServiceAuthorizationService
		cache           *freecache.Cache
		conf            config.ConfigProvider
		c3Conf          c3.ConfigProvider
		loc             location.Location
		tagSvc          tag.TagService
		projectSvc      project.ProjectService
		auditService    audit.SqlAuditService
		auditTlsDAL     dal.AuditTlsDAL
		tlsDAL          dal.TlsDAL
		eventDAL        dal.EventDAL
		lcSvc           audit.LogCollectorInterface
		billSvc         billing.BillingService
		publishEventSvc com.PublishEventService
	}
	type args struct {
		ctx  types.Context
		step IdempotentStep
	}

	var tests = []struct {
		name      string
		fields    fields
		args      args
		wantState *AuditLifeState
	}{
		{
			name: "无报错流程",
			fields: fields{
				state: &AuditLifeState{
					LifeState: LifeState{
						HistoryStepsTrace: nil,
						CurrentStepName:   "",
					},
					FlowErrorHandleStep: "GCStep",
				},
			},
			args: args{
				ctx:  MockActorContext(suite.ctrl),
				step: &normalStep{},
			},
			//{"LifeState":{"LogID":"","HistoryStepsTrace":[["normalStep","PreCheck success"],["normalStep","ProtectExec success"]],"CurrentStepName":"nextStep"},"FlowType":"","FlowErrorHandleStep":"GCStep","TenantId":"","InstanceId":"","InstanceStatus":"OrderNew","FollowInstanceId":"","Region":"","DSType":"MySQL","DeployType":"InstanceFront","CloseType":"\u003cUNSET\u003e","OrderId":"","MsgID":"","Message":null,"TlsEndpoint":"","TlsRegion":"","TlsProject":"","TlsTopic":"","TlsTopicTTL":0,"TlsId":0,"TlsHostGroupId":"","TlsRuleId":"","ProxyPort":null,"ProxyCpuRequest":"","AzClusterMap":null}
			wantState: &AuditLifeState{
				LifeState: LifeState{
					HistoryStepsTrace: [][]string{
						{"normalStep", "PreCheck success"},
						{"normalStep", "ProtectExec success"},
					},
					CurrentStepName: "nextStep",
				},
				FlowErrorHandleStep: "GCStep",
			},
		},
		{
			name: "无报错流程,jump",
			fields: fields{
				state: &AuditLifeState{
					LifeState: LifeState{
						HistoryStepsTrace: nil,
						CurrentStepName:   "",
					},
					FlowErrorHandleStep: "GCStep",
				},
			},
			args: args{
				ctx:  MockActorContext(suite.ctrl),
				step: &jumpStep{},
			},
			//{"LifeState":{"LogID":"","HistoryStepsTrace":[["jumpStep","PreCheck success"],["jumpStep","ProtectExec jump"]],"CurrentStepName":"nextStep"},"FlowType":"","FlowErrorHandleStep":"GCStep","TenantId":"","InstanceId":"","InstanceStatus":"OrderNew","FollowInstanceId":"","Region":"","DSType":"MySQL","DeployType":"InstanceFront","CloseType":"\u003cUNSET\u003e","OrderId":"","MsgID":"","Message":null,"TlsEndpoint":"","TlsRegion":"","TlsProject":"","TlsTopic":"","TlsTopicTTL":0,"TlsId":0,"TlsHostGroupId":"","TlsRuleId":"","ProxyPort":null,"ProxyCpuRequest":"","AzClusterMap":null}
			wantState: &AuditLifeState{
				LifeState: LifeState{
					HistoryStepsTrace: [][]string{
						{"jumpStep", "PreCheck success"},
						{"jumpStep", "ProtectExec jump"},
					},
					CurrentStepName: "nextStep",
				},
				FlowErrorHandleStep: "GCStep",
			},
		},
		{
			name: "pre一直报错流程",
			fields: fields{
				state: &AuditLifeState{
					LifeState: LifeState{
						HistoryStepsTrace: nil,
						CurrentStepName:   "",
					},
					FlowErrorHandleStep: "GCStep",
				},
			},
			args: args{
				ctx:  MockActorContext(suite.ctrl),
				step: &preErrorStep{},
			},
			//{"LifeState":{"LogID":"","HistoryStepsTrace":[["preErrorStep","PreCheck err:pre check error, retry times:0"],["preErrorStep","PreCheck err:pre check error, retry times:1"],["preErrorStep","PreCheck err:pre check error, retry times:2"],["preErrorStep","Enter GC Flow, error:pre check error"]],"CurrentStepName":"GCStep"},"FlowType":"","FlowErrorHandleStep":"GCStep","TenantId":"","InstanceId":"","InstanceStatus":"OrderNew","FollowInstanceId":"","Region":"","DSType":"MySQL","DeployType":"InstanceFront","CloseType":"\u003cUNSET\u003e","OrderId":"","MsgID":"","Message":null,"TlsEndpoint":"","TlsRegion":"","TlsProject":"","TlsTopic":"","TlsTopicTTL":0,"TlsId":0,"TlsHostGroupId":"","TlsRuleId":"","ProxyPort":null,"ProxyCpuRequest":"","AzClusterMap":null}
			wantState: &AuditLifeState{
				LifeState: LifeState{
					HistoryStepsTrace: [][]string{
						{"preErrorStep", "PreCheck err:pre check error, retry times:0"},
						{"preErrorStep", "PreCheck err:pre check error, retry times:1"},
						{"preErrorStep", "PreCheck err:pre check error, retry times:2"},
						{"preErrorStep", "Enter GC Flow, error:pre check error"},
					},
					CurrentStepName: "GCStep",
				},
				FlowErrorHandleStep: "GCStep",
			},
		},
		{
			name: "exec一直报错流程",
			fields: fields{
				state: &AuditLifeState{
					LifeState: LifeState{
						HistoryStepsTrace: nil,
						CurrentStepName:   "",
					},
					FlowErrorHandleStep: "GCStep",
				},
			},
			args: args{
				ctx:  MockActorContext(suite.ctrl),
				step: &execErrorStep{},
			},
			//{"LifeState":{"LogID":"","HistoryStepsTrace":[["execErrorStep","PreCheck success"],["execErrorStep","ProtectExec err:protect execute failed, retry times:0"],["execErrorStep","PreCheck success"],["execErrorStep","ProtectExec err:protect execute failed, retry times:1"],["execErrorStep","PreCheck success"],["execErrorStep","ProtectExec err:protect execute failed, retry times:2"],["FinishStep","RunStep over"]],"CurrentStepName":"execErrorStep"},"FlowType":"","FlowErrorHandleStep":"","TenantId":"","InstanceId":"","InstanceStatus":"OrderNew","FollowInstanceId":"","Region":"","DSType":"MySQL","DeployType":"InstanceFront","CloseType":"\u003cUNSET\u003e","OrderId":"","MsgID":"","Message":null,"TlsEndpoint":"","TlsRegion":"","TlsProject":"","TlsTopic":"","TlsTopicTTL":0,"TlsId":0,"TlsHostGroupId":"","TlsRuleId":"","ProxyPort":null,"ProxyCpuRequest":"","AzClusterMap":null}
			wantState: &AuditLifeState{
				LifeState: LifeState{
					HistoryStepsTrace: [][]string{
						{"execErrorStep", "PreCheck success"},
						{"execErrorStep", "ProtectExec err:protect execute failed, retry times:0"},
						{"execErrorStep", "PreCheck success"},
						{"execErrorStep", "ProtectExec err:protect execute failed, retry times:1"},
						{"execErrorStep", "PreCheck success"},
						{"execErrorStep", "ProtectExec err:protect execute failed, retry times:2"},
						{"execErrorStep", "Enter GC Flow, error:protect execute failed"},
					},
					CurrentStepName: "GCStep",
				},
				FlowErrorHandleStep: "GCStep",
			},
		},
		{
			name: "exec一直报错流程,无GC",
			fields: fields{
				state: &AuditLifeState{
					LifeState: LifeState{
						HistoryStepsTrace: nil,
						CurrentStepName:   "",
					},
					FlowErrorHandleStep: "",
				},
			},
			args: args{
				ctx:  MockActorContext(suite.ctrl),
				step: &execErrorStep{},
			},
			//{"LifeState":{"LogID":"","HistoryStepsTrace":[["execErrorStep","PreCheck success"],["execErrorStep","ProtectExec err:protect execute failed, retry times:0"],["execErrorStep","PreCheck success"],["execErrorStep","ProtectExec err:protect execute failed, retry times:1"],["execErrorStep","PreCheck success"],["execErrorStep","ProtectExec err:protect execute failed, retry times:2"],["FinishStep","RunStep over"]],"CurrentStepName":"execErrorStep"},"FlowType":"","FlowErrorHandleStep":"","TenantId":"","InstanceId":"","InstanceStatus":"OrderNew","FollowInstanceId":"","Region":"","DSType":"MySQL","DeployType":"InstanceFront","CloseType":"\u003cUNSET\u003e","OrderId":"","MsgID":"","Message":null,"TlsEndpoint":"","TlsRegion":"","TlsProject":"","TlsTopic":"","TlsTopicTTL":0,"TlsId":0,"TlsHostGroupId":"","TlsRuleId":"","ProxyPort":null,"ProxyCpuRequest":"","AzClusterMap":null}
			wantState: &AuditLifeState{
				LifeState: LifeState{
					HistoryStepsTrace: [][]string{
						{"execErrorStep", "PreCheck success"},
						{"execErrorStep", "ProtectExec err:protect execute failed, retry times:0"},
						{"execErrorStep", "PreCheck success"},
						{"execErrorStep", "ProtectExec err:protect execute failed, retry times:1"},
						{"execErrorStep", "PreCheck success"},
						{"execErrorStep", "ProtectExec err:protect execute failed, retry times:2"},
						{"FinishStep", "RunStep over with error: protect execute failed"},
					},
					CurrentStepName: "",
				},
				FlowErrorHandleStep: "",
			},
		},
		{
			name: "无报错流程，nextstep是nil",
			fields: fields{
				state: &AuditLifeState{
					LifeState: LifeState{
						HistoryStepsTrace: nil,
						CurrentStepName:   "",
					},
					FlowErrorHandleStep: "GCStep",
				},
			},
			args: args{
				ctx:  MockActorContext(suite.ctrl),
				step: &finishStep{},
			},
			//{"LifeState":{"LogID":"","HistoryStepsTrace":[["finishStep","PreCheck success"],["finishStep","ProtectExec success"],["FinishStep","RunStep over"]],"CurrentStepName":"finishStep"},"FlowType":"","FlowErrorHandleStep":"GCStep","TenantId":"","InstanceId":"","InstanceStatus":"OrderNew","FollowInstanceId":"","Region":"","DSType":"MySQL","DeployType":"InstanceFront","CloseType":"\u003cUNSET\u003e","OrderId":"","MsgID":"","Message":null,"TlsEndpoint":"","TlsRegion":"","TlsProject":"","TlsTopic":"","TlsTopicTTL":0,"TlsId":0,"TlsHostGroupId":"","TlsRuleId":"","ProxyPort":null,"ProxyCpuRequest":"","AzClusterMap":null}
			wantState: &AuditLifeState{
				LifeState: LifeState{
					HistoryStepsTrace: [][]string{
						{"finishStep", "PreCheck success"},
						{"finishStep", "ProtectExec success"},
						{"FinishStep", "RunStep over"},
					},
					CurrentStepName: "",
				},
				FlowErrorHandleStep: "GCStep",
			},
		},
	}
	for _, tt := range tests {
		suite.T().Run(tt.name, func(t *testing.T) {
			a := &AuditLifecycleActor{
				state:           tt.fields.state,
				idempotentSteps: tt.fields.idempotentSteps,
				source:          tt.fields.source,
				crossAuthSvc:    tt.fields.crossAuthSvc,
				cache:           tt.fields.cache,
				conf:            tt.fields.conf,
				c3Conf:          tt.fields.c3Conf,
				loc:             tt.fields.loc,
				tagSvc:          tt.fields.tagSvc,
				projectSvc:      tt.fields.projectSvc,
				auditService:    tt.fields.auditService,
				auditTlsDAL:     tt.fields.auditTlsDAL,
				tlsDAL:          tt.fields.tlsDAL,
				lcSvc:           tt.fields.lcSvc,
				billSvc:         tt.fields.billSvc,
				publishEventSvc: tt.fields.publishEventSvc,
			}
			a.RunStep(tt.args.ctx, tt.args.step)
			marshal, _ := json.Marshal(a.state)
			fmt.Println(string(marshal))

			if a.state.LifeState.CurrentStepName != tt.wantState.LifeState.CurrentStepName {
				t.Errorf("CurrentStepName want:%s got:%s", tt.wantState.LifeState.CurrentStepName, a.state.LifeState.CurrentStepName)
			}
			for i, historys := range a.state.LifeState.HistoryStepsTrace {
				for j, history := range historys {
					if tt.wantState.LifeState.HistoryStepsTrace[i][j] != history {
						t.Errorf("history want:%s got:%s", tt.wantState.LifeState.HistoryStepsTrace[i][j], history)
					}
				}
			}
		})
	}
}

func MockBillNeed(ctrl *gomock.Controller) (
	context.Context,
	*config2.MockConfigProvider,
	*config2.MockC3ConfigProvider,
	*local_dal.LocalAudit,
	*mocks_audit.MockSqlAuditService,
	*mocks.MockPublishEventService,
) {
	// CONSUL_HTTP_HOST=************;CONSUL_HTTP_PORT=2280;RUNTIME_IDC_NAME=boe;SEC_TOKEN_STRING=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************.MqaypODNXw4CfSWQ1S5rLkR6R7LFqL82DpxA1Jixrl-BFZ8qn9L4zKX7ayDNDb37KlnRE1_REy6pZRQMqAZmUl47A-tAxbk_L2Vgzgzh876v6g_bygsnAyFzJEfhcn2DoucHizSB-oF0G8uOFOBsj2ARJfx6J8Y4ZKCjMg8BKKKq3rg68dSLkyeG2kz7VMIO5DVEj26P4MMHN3PCshySKOT5gUpZwpY62G8G1Q-OxOcYPvAFGpDijy2WQ4HXwJHYCp1nrrEkIY3YYLz5IBWHyW5BOLy-QMTPgA_SMysGkjGcflyyZ5DeZgLRpu80f78vnRPmH34tCwWx82CGI4FrfA
	os.Setenv("CONSUL_HTTP_HOST", "************")
	os.Setenv("CONSUL_HTTP_PORT", "2280")
	os.Setenv("RUNTIME_IDC_NAME", "boe-stable")
	os.Setenv("SEC_TOKEN_STRING", "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************.MqaypODNXw4CfSWQ1S5rLkR6R7LFqL82DpxA1Jixrl-BFZ8qn9L4zKX7ayDNDb37KlnRE1_REy6pZRQMqAZmUl47A-tAxbk_L2Vgzgzh876v6g_bygsnAyFzJEfhcn2DoucHizSB-oF0G8uOFOBsj2ARJfx6J8Y4ZKCjMg8BKKKq3rg68dSLkyeG2kz7VMIO5DVEj26P4MMHN3PCshySKOT5gUpZwpY62G8G1Q-OxOcYPvAFGpDijy2WQ4HXwJHYCp1nrrEkIY3YYLz5IBWHyW5BOLy-QMTPgA_SMysGkjGcflyyZ5DeZgLRpu80f78vnRPmH34tCwWx82CGI4FrfA")
	os.Setenv("BDC_REGION_ID", "cn-nanjing-bbit")
	ctx := context.TODO()
	ctx = context.WithValue(ctx, "biz-context", &fwctx.BizContext{TenantID: "**********"})
	cfg := config2.NewMockConfigProvider(ctrl)
	// boe-stable
	cfg.EXPECT().Get(gomock.Any()).Return(&ycnf.Config{
		VolcProfile:                "boe_stable",
		BillingAK:                  "MDgyYjM0NWRiYWY",
		BillingSK:                  "GMxZjk3MTczZTMxNGYzlhODg3NmRk",
		VolcServiceAccountAK:       "AKLTY2RmNGQ4NjhmZmJlNGI5YzhkZTM0ZjQyOWY0MTlmOTI",
		VolcServiceAccountSK:       "TURReU9UY3hOR05tTnpJME5HWmhZVGt4WVRZek5tUTVaRFprTm1RMk1EQQ==",
		BillingTopic:               "trade_instance",
		BillingCluster:             "rmq_test_new",
		BillingConsumerGroup:       "dbw_cn-nanjing-bbit",
		EnableBillingTenantIDList:  []string{"**********", "**********"},
		DisableBillingTenantIDList: []string{},
	}).AnyTimes()
	c3Cnf := config2.NewMockC3ConfigProvider(ctrl)
	c3Cnf.EXPECT().GetNamespace(gomock.Any(), gomock.Any()).Return(&ycnf.C3Config{
		Application: ycnf.Application{
			TOPServiceAccessKey: "AKLTODY2M2EzMzRlNTdhNGU3ZDhhZTlhNDgxYzA3MjkyODY",
			TOPServiceSecretKey: "WVdSak1UQTNNR1F6TURjMk5ETTJZVGt6TldJNU5XTXpZemxqT1RoallUSQ==",
			TLSServiceAccessKey: "AKLTMmMwNzM5ZWUxYjliNGUzY2FjNWZkMTg1YmZkZWQ0MTU",
			TLSServiceSecretKey: "TldWaVlUTXpNemszWlRJeE5ETTFNamhpTXpKaE56SmxPVE15TWpGbE5HSQ==",
		},
		Aksk: ycnf.Aksk{},
	}).AnyTimes()
	//Mock(fwctx.GetTenantID).Return("**********").Build()
	//Mock((*volctrade.VolcTradeClient).IsAccountRealNameAuthentication).Return(true).Build()
	//Mock((*volctrade.VolcTradeClient).PushInstanceMeasure).Return(&volctrade.TradeResInfo{}, nil).Build()

	mgrProv := mocks.NewMockMgrProvider(ctrl)
	mgrClient := mocks.NewMockMgrClient(ctrl)
	mgrProv.EXPECT().Get().Return(mgrClient).AnyTimes()

	nodedetail := &rdsModel_v2.DescribeDBInstanceDetailResp{}
	json.Unmarshal([]byte("{\"BasicInfo\":{\"InstanceId\":\"mysql-0844ce4826e0\",\"InstanceName\":\"dyh_80_勿删\",\"InstanceStatus\":\"Running\",\"RegionId\":\"cn-nanjing-bbit\",\"ZoneId\":\"cn-nanjing-bbit-a\",\"DBEngine\":\"Mysql\",\"DBEngineVersion\":\"MySQL_8_0\",\"InstanceType\":\"HA\",\"VCPU\":2,\"Memory\":8,\"NodeSpec\":\"rds.mysql.2c8g\",\"NodeNumber\":\"3\",\"CreateTime\":\"2022-11-16 12:01:36\",\"UpdateTime\":\"\",\"StorageUse\":4.17,\"StorageSpace\":100,\"StorageType\":\"LocalSSD\",\"BackupUse\":0.76,\"VpcId\":\"vpc-8gvvy8uzp3i88k0tze9vkak9\",\"TimeZone\":\"UTC +08:00\",\"DataSyncMode\":\"SemiSync\",\"ProjectName\":\"\",\"InnerVersion\":\"\",\"IsLatestVersion\":false,\"LowerCaseTableNames\":\"1\",\"SubnetId\":\"subnet-h0alcmc3e96o3kyadhv0ggix\",\"ShardNumber\":0,\"StorageDataSize\":**********,\"StorageLogSize\":********,\"StorageBinLogSize\":********,\"StorageErrorLogSize\":859390,\"StorageAuditLogSize\":4096,\"StorageSlowLogSize\":4220526,\"BackupDataSize\":*********,\"BackupLogSize\":*********,\"BackupBinLogSize\":*********,\"BackupErrorLogSize\":868625,\"BackupAuditLogSize\":0,\"BackupSlowLogSize\":7292344,\"PrimaryDBAccount\":\"\",\"AllowListVersion\":\"v2\",\"ServerCollation\":\"\",\"MaintenanceWindow\":{\"MaintenanceTime\":\"18:00Z-22:00Z\",\"DayKind\":\"Week\",\"DayOfWeek\":[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\",\"Sunday\"],\"DayOfMonth\":[]}},\"ConnectionInfo\":[{\"EndpointId\":\"mysql-0844ce4826e0-cluster\",\"EndpointType\":\"Cluster\",\"Description\":\"\",\"Address\":[{\"NetworkType\":\"Ingress\",\"Domain\":\"mysql0844ce4826e0.rds-boe.infcs.tob\",\"IPAddress\":\"\",\"Port\":\"25345\",\"SubnetId\":\"\",\"EipId\":\"\"},{\"NetworkType\":\"Private\",\"Domain\":\"mysql0844ce4826e0.rds-boe.ivolces.com\",\"IPAddress\":\"*************\",\"Port\":\"3306\",\"SubnetId\":\"subnet-h0alcmc3e96o3kyadhv0ggix\",\"EipId\":\"\"},{\"NetworkType\":\"Carma\",\"Domain\":\"mysql-0844ce4826e0-proxy-agent-hs.rds.svc.mix-panel-a.org\",\"IPAddress\":\"\",\"Port\":\"3679\",\"SubnetId\":\"\",\"EipId\":\"\"}],\"EnableReadWriteSplitting\":\"Disable\",\"EnableReadOnly\":\"Disable\",\"EndpointName\":\"\",\"ReadWriteMode\":\"ReadWrite\",\"ReadOnlyNodeWeight\":[{\"NodeType\":\"Primary\",\"NodeId\":\"\",\"Weight\":200},{\"NodeType\":\"ReadOnly\",\"NodeId\":\"mysql-0844ce4826e0-r9d3f\",\"Weight\":0}],\"AutoAddNewNodes\":\"Enable\",\"ReadOnlyNodeDistributionType\":\"Default\",\"ReadOnlyNodeMaxDelayTime\":30},{\"EndpointId\":\"mysql-0844ce4826e0-direct\",\"EndpointType\":\"Direct\",\"Description\":\"\",\"Address\":[{\"NetworkType\":\"Carma\",\"Domain\":\"mysql-0844ce4826e0-hs.rds.svc.mix-panel-a.org\",\"IPAddress\":\"\",\"Port\":\"3306\",\"SubnetId\":\"\",\"EipId\":\"\"}],\"EnableReadWriteSplitting\":\"Disable\",\"EnableReadOnly\":\"Disable\",\"EndpointName\":\"\",\"ReadWriteMode\":\"ReadWrite\",\"AutoAddNewNodes\":\"Disable\",\"ReadOnlyNodeDistributionType\":\"Default\"}],\"ChargeDetail\":{\"ChargeType\":\"PostPaid\",\"AutoRenew\":false,\"PeriodUnit\":\"Month\",\"Period\":1,\"Number\":1,\"ChargeStatus\":\"Normal\",\"ChargeStartTime\":\"\",\"ChargeEndTime\":\"\",\"OverdueTime\":\"\",\"OverdueReclaimTime\":\"\"},\"NodeDetailInfo\":[{\"InstanceId\":\"mysql-0844ce4826e0\",\"NodeId\":\"mysql-0844ce4826e0-0\",\"RegionId\":\"cn-nanjing-bbit\",\"ZoneId\":\"cn-nanjing-bbit-a\",\"NodeType\":\"Primary\",\"NodeStatus\":\"Running\",\"VCPU\":2,\"Memory\":8,\"NodeSpec\":\"rds.mysql.2c8g\",\"CreateTime\":\"2022-11-16 12:01:36\",\"UpdateTime\":\"\",\"ShardId\":\"\"},{\"InstanceId\":\"mysql-0844ce4826e0\",\"NodeId\":\"mysql-0844ce4826e0-1\",\"RegionId\":\"cn-nanjing-bbit\",\"ZoneId\":\"cn-nanjing-bbit-a\",\"NodeType\":\"Secondary\",\"NodeStatus\":\"Running\",\"VCPU\":2,\"Memory\":8,\"NodeSpec\":\"rds.mysql.2c8g\",\"CreateTime\":\"2022-11-16 12:01:36\",\"UpdateTime\":\"\",\"ShardId\":\"\"},{\"InstanceId\":\"mysql-0844ce4826e0\",\"NodeId\":\"mysql-0844ce4826e0-r9d3f\",\"RegionId\":\"cn-nanjing-bbit\",\"ZoneId\":\"cn-nanjing-bbit-a\",\"NodeType\":\"ReadOnly\",\"NodeStatus\":\"Running\",\"VCPU\":2,\"Memory\":8,\"NodeSpec\":\"rds.mysql.2c8g\",\"CreateTime\":\"2022-11-21 22:26:47\",\"UpdateTime\":\"2022-11-22 16:47:43\",\"ShardId\":\"\"}],\"ShardInfo\":null}"),
		nodedetail)
	mgrClient.EXPECT().Call(gomock.Any(), rdsModel_v2.Action_DescribeDBInstanceDetail.String(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(3, *nodedetail).Return(nil).AnyTimes()

	pods := &rdsModel.ListInstancePodsResp{}
	json.Unmarshal([]byte("{\"Total\":7,\"Datas\":[{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-m9c1a-ha-controller-0\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"200mC\",\"MemInfo\":\"200Mi\",\"DiskInfo\":\"\",\"RunningInfo\":\"Container:1/1\",\"LastestStartTime\":\"2022-11-23 10:31:40\",\"Resource\":\"mysql-bfb26ba0773e-m9c1a\",\"Containers\":[{\"Name\":\"ha-controller\",\"Port\":\"2338\",\"Image\":\"hub.byted.org/infcs/ha_controller:20221115.1718-epic_v1.1.10-1b9759\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"200Mi\"}],\"Component\":\"HA Controller\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-m9c1a-ha-controller-1\",\"NodeIP\":\"***********\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"200mC\",\"MemInfo\":\"200Mi\",\"DiskInfo\":\"\",\"RunningInfo\":\"Container:1/1\",\"LastestStartTime\":\"2022-11-23 10:31:40\",\"Resource\":\"mysql-bfb26ba0773e-m9c1a\",\"Containers\":[{\"Name\":\"ha-controller\",\"Port\":\"2338\",\"Image\":\"hub.byted.org/infcs/ha_controller:20221115.1718-epic_v1.1.10-1b9759\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"200Mi\"}],\"Component\":\"HA Controller\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-sbf4a-ha-controller-0\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"200mC\",\"MemInfo\":\"200Mi\",\"DiskInfo\":\"\",\"RunningInfo\":\"Container:1/1\",\"LastestStartTime\":\"2022-11-23 10:31:41\",\"Resource\":\"mysql-bfb26ba0773e-sbf4a\",\"Containers\":[{\"Name\":\"ha-controller\",\"Port\":\"2338\",\"Image\":\"hub.byted.org/infcs/ha_controller:20221115.1718-epic_v1.1.10-1b9759\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"200Mi\"}],\"Component\":\"HA Controller\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-m9c1a-0\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"1C 200mC\",\"MemInfo\":\"2458Mi500Mi\",\"DiskInfo\":\"20Gi\",\"RunningInfo\":\"Container:3/3\",\"LastestStartTime\":\"2022-11-23 10:31:26\",\"Resource\":\"mysql-bfb26ba0773e-m9c1a\",\"Containers\":[{\"Name\":\"mysql\",\"Port\":\"3306\",\"Image\":\"hub.byted.org/infcs/infcs_rds_mysql_server_5_7:1c16f3a2b1b86f0ab416221869608d41\",\"Status\":\"Ready\",\"Cpu\":\"1C\",\"Mem\":\"2458Mi\"},{\"Name\":\"backup-server\",\"Port\":\"8889\",\"Image\":\"hub.byted.org/infcs/rds_backup:20221109.2245-epic_v1.1.10-2c756b\",\"Status\":\"Ready\",\"Cpu\":\"\",\"Mem\":\"\"},{\"Name\":\"mysql-agent\",\"Port\":\"2335\",\"Image\":\"hub.byted.org/infcs/tob_rds_mysql_agent:20221116.1608-epic_v1.1.10-c1b154\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"500Mi\"}],\"Component\":\"MySQL\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-sbf4a-0\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"1C 200mC\",\"MemInfo\":\"2458Mi500Mi\",\"DiskInfo\":\"20Gi\",\"RunningInfo\":\"Container:3/3\",\"LastestStartTime\":\"2022-11-23 10:31:26\",\"Resource\":\"mysql-bfb26ba0773e-sbf4a\",\"Containers\":[{\"Name\":\"mysql\",\"Port\":\"3306\",\"Image\":\"hub.byted.org/infcs/infcs_rds_mysql_server_5_7:1c16f3a2b1b86f0ab416221869608d41\",\"Status\":\"Ready\",\"Cpu\":\"1C\",\"Mem\":\"2458Mi\"},{\"Name\":\"backup-server\",\"Port\":\"8889\",\"Image\":\"hub.byted.org/infcs/rds_backup:20221109.2245-epic_v1.1.10-2c756b\",\"Status\":\"Ready\",\"Cpu\":\"\",\"Mem\":\"\"},{\"Name\":\"mysql-agent\",\"Port\":\"2335\",\"Image\":\"hub.byted.org/infcs/tob_rds_mysql_agent:20221116.1608-epic_v1.1.10-c1b154\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"500Mi\"}],\"Component\":\"MySQL\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-m9c1a-proxy-agent-544dbc9ff7-fd4l6\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"500mC 200mC\",\"MemInfo\":\"2Gi500Mi\",\"DiskInfo\":\"\",\"RunningInfo\":\"Container:2/2\",\"LastestStartTime\":\"2022-11-23 10:31:48\",\"Resource\":\"mysql-bfb26ba0773e-m9c1a\",\"Containers\":[{\"Name\":\"proxy\",\"Port\":\"3679 23679 3680 23680 3681 23681 3682 23682 3683 23683 3684 23684 3685 23685 3686 23686 3687 23687 3688 23688 3689 23689 3690 23690 3691 23691 3692 23692 3693 23693 3694 23694 3695 23695\",\"Image\":\"hub.byted.org/bytendb2b/rds_proxy_sidecar:20221115.1403-epic_v1.1.10-e73d0b\",\"Status\":\"Ready\",\"Cpu\":\"500mC\",\"Mem\":\"2Gi\"},{\"Name\":\"proxy-agent\",\"Port\":\"2335\",\"Image\":\"hub.byted.org/bytendb2b/rds_proxy_agent_sidecar:07383ec561c92137e9b26bc326b5af06\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"500Mi\"}],\"Component\":\"Proxy\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-sbf4a-proxy-agent-bb476c4fb-4mblj\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"500mC 200mC\",\"MemInfo\":\"2Gi500Mi\",\"DiskInfo\":\"\",\"RunningInfo\":\"Container:2/2\",\"LastestStartTime\":\"2022-11-23 10:31:49\",\"Resource\":\"mysql-bfb26ba0773e-sbf4a\",\"Containers\":[{\"Name\":\"proxy\",\"Port\":\"3679 23679 3680 23680 3681 23681 3682 23682 3683 23683 3684 23684 3685 23685 3686 23686 3687 23687 3688 23688 3689 23689 3690 23690 3691 23691 3692 23692 3693 23693 3694 23694 3695 23695\",\"Image\":\"hub.byted.org/bytendb2b/rds_proxy_sidecar:20221115.1403-epic_v1.1.10-e73d0b\",\"Status\":\"Ready\",\"Cpu\":\"500mC\",\"Mem\":\"2Gi\"},{\"Name\":\"proxy-agent\",\"Port\":\"2335\",\"Image\":\"hub.byted.org/bytendb2b/rds_proxy_agent_sidecar:07383ec561c92137e9b26bc326b5af06\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"500Mi\"}],\"Component\":\"Proxy\"}]}"),
		pods)
	mgrClient.EXPECT().Call(gomock.Any(), rdsModel.Action_ListInstancePods.String(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(3, *pods).Return(nil).AnyTimes()

	local := &local_dal.LocalAudit{}
	local.Init(ctx, ctrl)

	auditSvc := mocks_audit.NewMockSqlAuditService(ctrl)
	auditSvc.EXPECT().TerminateSqlAuditOrderCallback(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	auditSvc.EXPECT().TerminateSqlAuditOrderCallback(gomock.Any(), gomock.Any(), gomock.Any()).Return(consts.ErrorOf(model.ErrorCode_InternalError)).AnyTimes()
	auditSvc.EXPECT().GetTenantTlsClient(gomock.Any(), gomock.Any()).Return(MockTlsClient(ctrl), nil).AnyTimes()
	auditSvc.EXPECT().GetInnerAccountTlsClient(gomock.Any()).Return(MockTlsClient(ctrl), nil).AnyTimes()
	auditSvc.EXPECT().GenCreateIndexRequest(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&tls_sdk.CreateIndexRequest{
		TopicID: "topic-id-xxx-xxx-xxx",
		KeyValue: &[]tls_sdk.KeyValueInfo{
			{
				Key: "content_timestamp",
				Value: tls_sdk.Value{
					ValueType:      "long",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "type",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "status",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "query",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "!@#%^&*()-_=\\\"', <>/?|;: \\n\\t\\r[]{}",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "method",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "path",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "params",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "!@#%^&*()-_=\\\"', <>/?|;: \\n\\t\\r[]{}",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "network_byte",
				Value: tls_sdk.Value{
					ValueType:      "long",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "network_session_id",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "server_bytes",
				Value: tls_sdk.Value{
					ValueType:      "long",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "client_ip",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "client_port",
				Value: tls_sdk.Value{
					ValueType:      "long",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "client_bytes",
				Value: tls_sdk.Value{
					ValueType:      "long",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "agent_id",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "event_start",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "event_end",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "event_duration",
				Value: tls_sdk.Value{
					ValueType:      "long",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "mysql_affected_rows",
				Value: tls_sdk.Value{
					ValueType:      "long",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "mysql_insert_id",
				Value: tls_sdk.Value{
					ValueType:      "long",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "mysql_num_rows",
				Value: tls_sdk.Value{
					ValueType:      "long",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "mysql_num_fields",
				Value: tls_sdk.Value{
					ValueType:      "long",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			// 统计
			{
				Key: "mysql_context_db",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "mysql_context_user",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "mysql_error_code",
				Value: tls_sdk.Value{
					ValueType:      "long",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "mysql_error_message",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "mysql_sql_template",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			// 统计
			{
				Key: "mysql_sql_table",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "mysql_sql_template_md5",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "mysql_log_level",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "mysql_log_type",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "node_id",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
		},
	}, nil).AnyTimes()
	auditSvc.EXPECT().DeleteAuditResource(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	auditSvc.EXPECT().CreatePodInCluster(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	auditSvc.EXPECT().GetTlsClient(gomock.Any(), gomock.Any()).Return(MockTlsClient(ctrl), nil).AnyTimes()

	publishEventSvc := mocks.NewMockPublishEventService(ctrl)
	publishEventSvc.EXPECT().PublishEvent(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	publishEventSvc.EXPECT().UpdateEventResult(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	return ctx, cfg, c3Cnf, local, auditSvc, publishEventSvc
}

func MockCrossAuthService(ctrl *gomock.Controller) *mocks.MockCrossServiceAuthorizationService {
	credential := mocks.NewMockCredentials(ctrl)
	credential.EXPECT().GetAK().Return("ak").AnyTimes()
	credential.EXPECT().GetSK().Return("sk").AnyTimes()
	credential.EXPECT().GetToken().Return("token").AnyTimes()
	crossAuthSvc := mocks.NewMockCrossServiceAuthorizationService(ctrl)
	crossAuthSvc.EXPECT().AssumeRoleBy(gomock.Any(), gomock.Any(), gomock.Any()).Return(credential, nil).AnyTimes()
	return crossAuthSvc
}

func MockDS(ctrl *gomock.Controller) datasource.DataSourceService {
	source := mocks.NewMockDataSourceService(ctrl)
	source.EXPECT().DescribeDBInstanceDetail(gomock.Any(), gomock.Any()).Return(&datasource.DescribeDBInstanceDetailResp{
		InstanceId:      "1",
		InstanceName:    "1",
		InstanceStatus:  rdsModel_v2.InstanceStatus_Running.String(),
		RegionId:        "cn-nanjing-bbit",
		ZoneId:          "az1",
		DBEngine:        "1",
		DBEngineVersion: "1",
		InstanceType:    "1",
		VCPU:            1,
		Memory:          2,
		ProjectName:     "projectname",
	}, nil).AnyTimes()
	source.EXPECT().DescribeAccounts(gomock.Any(), gomock.Any()).Return(&datasource.DescribeAccountResp{
		Total: 0,
		AccountsInfo: []*rdsModel_v2.AccountsInfoObject{
			{
				AccountName:           "abc",
				AccountType:           1,
				AccountStatus:         1,
				AccountPrivilegesInfo: nil,
			},
		},
	}, nil).AnyTimes()
	source.EXPECT().CreateAccount(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	source.EXPECT().GrantAccountPrivilege(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	source.EXPECT().DescribeDBProxyConfig(gomock.Any(), gomock.Any()).Return(&datasource.DescribeDBProxyConfigResp{
		IsProxyEnable: false,
	}, nil).AnyTimes()
	source.EXPECT().DescribeDBInstanceSSL(gomock.Any(), gomock.Any()).Return(&datasource.DescribeDBInstanceSSLResp{
		InstanceId:    "",
		SSLEnable:     false,
		IsValid:       false,
		SSLExpireTime: "",
	}, nil).AnyTimes()
	source.EXPECT().ListInstancePods(gomock.Any(), gomock.Any()).Return(&datasource.ListInstancePodsResp{
		Total: 1,
		Data: []*shared.KubePod{
			{
				Region:      "cn-nanjing-bbit",
				Zone:        "mix-panel-aza",
				KubeCluster: "xxx",
				NodePool:    "x",
				Name:        "proxy-x",
				NodeIP:      "x",
				PodIP:       "x",
				Containers: []*shared.KubeContainer{
					{
						Name: "proxy-",
						Port: "3306",
						Cpu:  "1",
						Mem:  "1",
					},
				},
				Role:      "x",
				Component: "Proxy",
			},
		},
	}, nil).AnyTimes()
	source.EXPECT().DescribeDBInstanceAuditCollectedPod(gomock.Any(), gomock.Any()).Return(&datasource.DescribeDBInstanceAuditCollectedPodResp{
		Port:       []string{"3306"},
		CpuRequest: "1C",
	}, nil).AnyTimes()
	source.EXPECT().DescribeDBInstanceCluster(gomock.Any(), gomock.Any()).Return(&datasource.DescribeDBInstanceClusterResp{
		MultiAZ: true,
		AzClusterMap: map[string]string{
			"compute-a": "compute-a",
			"compute-b": "compute-b",
		},
	}, nil).AnyTimes()

	source.EXPECT().OpenDBInstanceAuditLog(gomock.Any(), gomock.Any()).Return(&datasource.OpenDBInstanceAuditLogResp{}, nil).AnyTimes()

	source.EXPECT().DescribeFullSQLLogConfig(gomock.Any(), gomock.Any()).Return(&datasource.DescribeFullSQLLogConfigResp{
		SQLCollectorStatus: datasource.SQLCollectorStatus_Enable,
		TLSDomain:          "http://xxx.com",
		TLSProjectId:       "xxproject",
		TLSTopic:           "xxtopic",
	}, nil).AnyTimes()
	source.EXPECT().ModifyFullSQLLogConfig(gomock.Any(), gomock.Any()).Return(&datasource.ModifyFullSQLLogConfigResp{}, nil).AnyTimes()
	return source
}

func MockTag(ctrl *gomock.Controller) tag.TagService {
	tag := mocks_tag.NewMockTagService(ctrl)
	tag.EXPECT().CreateParentProjectTag(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	tag.EXPECT().GetTag(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*volc_tag.TagPair{
		{
			Key:   "a",
			Value: "a",
			Type:  "a",
		},
	}, nil).AnyTimes()
	tag.EXPECT().TagResource(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	tag.EXPECT().UnTagResource(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	tag.EXPECT().CleanAllTagResource(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	return tag
}

func MockProject(ctrl *gomock.Controller) project.ProjectService {
	proj := mocks_project.NewMockProjectService(ctrl)
	proj.EXPECT().RemoveResource(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	proj.EXPECT().AddOrphanResource(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	proj.EXPECT().GetResourceProjectName(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("projectname", nil).AnyTimes()
	proj.EXPECT().AddAuditToResource(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	return proj
}

func TestCheckLoopback(t *testing.T) {
	type args struct {
		ctx         types.Context
		stepName    string
		stepMaxRety int
		history     [][]string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "loop",
			args: args{
				ctx:         nil,
				stepName:    "b",
				stepMaxRety: 3,
				history: [][]string{
					{"a", "xx", "xx"},
					{"a", "xx", "xx"},
					{"a", "xx", "xx"},
					{"b", "xx", "xx"},
					{"b", "xx", "xx"},
					{"b", "xx", "xx"},
				},
			},
			wantErr: true,
		},
		{
			name: "loop",
			args: args{
				ctx:         nil,
				stepName:    "b",
				stepMaxRety: 3,
				history: [][]string{
					{"a", "xx", "xx"},
					{"a", "xx", "xx"},
					{"a", "xx", "xx"},
					{"b", "xx", "xx"},
					{"b", "xx", "xx"},
				},
			},
			wantErr: false,
		},
		{
			name: "loop",
			args: args{
				ctx:         nil,
				stepName:    "a",
				stepMaxRety: 3,
				history: [][]string{
					{"a", "xx", "xx"},
					{"a", "xx", "xx"},
					{"a", "xx", "xx"},
					{"b", "xx", "xx"},
					{"b", "xx", "xx"},
				},
			},
			wantErr: true,
		},
		{
			name: "loop",
			args: args{
				ctx:         nil,
				stepName:    "a",
				stepMaxRety: 3,
				history:     [][]string{},
			},
			wantErr: false,
		},
		{
			name: "loop",
			args: args{
				ctx:         nil,
				stepName:    "a",
				stepMaxRety: 3,
				history: [][]string{
					{"a", "xx", "xx"},
					{"b", "xx", "xx"},
					{"c", "xx", "xx"},
				},
			},
			wantErr: true,
		},
		{
			name: "loop",
			args: args{
				ctx:         nil,
				stepName:    "d",
				stepMaxRety: 3,
				history: [][]string{
					{"a", "xx", "xx"},
					{"b", "xx", "xx"},
					{"c", "xx", "xx"},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := CheckLoopback(tt.args.ctx, tt.args.stepName, tt.args.stepMaxRety, tt.args.history)
			if tt.wantErr != (err != nil) {
				t.Errorf("wantErr:%t got:%s", tt.wantErr, err)
			}
		})
	}
}
