package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/lib-mgr-common/volc"
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/service/audit/k8s"
	"code.byted.org/infcs/dbw-mgr/biz/service/billing"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/i18n"
	"code.byted.org/infcs/dbw-mgr/biz/service/tag"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls/tls_enhance"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"github.com/google/uuid"
	tls_sdk "github.com/volcengine/volc-sdk-golang/service/tls"
	"gorm.io/gorm"
	"k8s.io/utils/pointer"
)

type PrepareInstanceStateStep struct {
	BaseStep
}

func (i PrepareInstanceStateStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (i PrepareInstanceStateStep) ProtectExec(ctx types.Context, actor *AuditLifecycleActor) error {
	log.Info(ctx, "ProtectExec InstanceId: %s, OrderID: %s", actor.state.InstanceId, actor.state.OrderId)
	orderExtra := &billing.OrderExtra{}
	_ = libutils.Unmarshal([]byte(actor.state.Message.Attribute), orderExtra)
	actor.state.TlsTopicTTL = int64(orderExtra.TlsTtl)
	actor.state.InitTags = orderExtra.CustomTags
	audit, err := actor.auditTlsDAL.GetByID(ctx, actor.state.InstanceId, actor.state.TenantId)
	if err != nil {
		log.Error(ctx, "query audit fail, error:%s", err)
		return err
	}
	actor.state.InstanceStatus = model.AuditStatus(audit.Status)
	actor.state.FollowInstanceId = audit.FollowInstanceID
	actor.state.DSType, err = model.DSTypeFromString(audit.DbType)
	if err != nil {
		log.Error(ctx, "DSType fail, error:%s", err)
		return err
	}
	actor.state.DeployType, err = model.LabelTypeFromString(audit.DeployType)
	if err != nil {
		log.Error(ctx, "DeployType fail, error:%s", err)
		return err
	}
	site := i18n.GetSiteName(ctx, audit.TenantID)
	actor.state.Region = audit.Region
	tlsRegion, err := actor.conf.Get(ctx).GetTLSRegion(site)
	if err != nil {
		log.Error(ctx, "请检查运维面参数:TlsServiceEndpoint, tls region err:%s", err)
		return err
	}
	actor.state.TlsRegion = tlsRegion
	tlsEndpoint := actor.conf.Get(ctx).GetTlsRegionEndpoint(site)
	actor.state.TlsEndpoint = tlsEndpoint
	actor.state.TlsDataType = model.StatisticDataType_SqlDetail
	actor.state.TlsIndexVersion = 1
	actor.state.CapabilitiesFlags = uint64(audit.CapabilitiesFlags)
	actor.state.StorageSqlTypes = strings.Split(audit.StorageSqlTypes, ",")

	detail, err := actor.source.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{InstanceId: actor.state.FollowInstanceId, Type: shared.DataSourceType(actor.state.DSType)})
	if err != nil {
		log.Warn(ctx, "Action_DescribeDBInstanceDetail call error: %s", err)
		return err
	}
	if detail.InstanceStatus != model.InstanceStatus_Running.String() {
		log.Warn(ctx, "Please try later. RDS instance is not running, %s", detail.InstanceStatus)
		return err
	}
	clusterInfo, err := actor.source.DescribeDBInstanceCluster(ctx, &datasource.DescribeDBInstanceClusterReq{
		InstanceId: actor.state.FollowInstanceId,
		Type:       shared.DataSourceType(actor.state.DSType),
	})
	if err != nil {
		log.Warn(ctx, "DescribeDBInstanceCluster call error: %s", err)
		return err
	}

	actor.state.AzClusterMap = clusterInfo.AzClusterMap
	actor.state.NodePoolsToCluster = clusterInfo.NodePool2Cluster
	switch actor.state.DeployType {
	case model.LabelType_ProxyFront:
		collected, err := actor.source.DescribeDBInstanceAuditCollectedPod(ctx, &datasource.DescribeDBInstanceAuditCollectedPodReq{
			InstanceId: actor.state.FollowInstanceId,
			Type:       shared.DataSourceType(actor.state.DSType),
		})
		if err != nil {
			log.Warn(ctx, "DescribeDBInstanceAuditCollectedPod call error: %s", err)
			return err
		}
		if len(collected.Port) == 0 {
			err = errors.New("port not find")
			log.Warn(ctx, "DescribeDBInstanceAuditCollectedPod call error: %s", err)
			return err
		}
		if len(collected.CpuRequest) == 0 {
			err = errors.New("CpuRequest not find")
			log.Warn(ctx, "DescribeDBInstanceAuditCollectedPod call error: %s", err)
			return err
		}
		actor.state.CollectPort = collected.Port
		actor.state.CollectPodCpuRequest = collected.CpuRequest
	}

	switch actor.state.DSType {
	case model.DSType_Redis:
		actor.state.InnerTLSResource = true
		actor.state.TlsTenant = actor.conf.Get(ctx).FullSqlInnerAccountId
	case model.DSType_Postgres:
		actor.state.InnerTLSResource = false
		actor.state.TlsTenant = actor.state.TenantId
	case model.DSType_MySQL:
		actor.state.InnerTLSResource = false
		actor.state.TlsTenant = actor.state.TenantId
	case model.DSType_VeDBMySQL:
		actor.state.InnerTLSResource = false
		actor.state.TlsTenant = actor.state.TenantId
	case model.DSType_Mongo:
		actor.state.InnerTLSResource = true
		actor.state.TlsTenant = actor.conf.Get(ctx).FullSqlInnerAccountId
	default:
		return errors.New("unsupported DSType")
	}
	log.Info(ctx, fmt.Sprintf("Init params:[%s]", libutils.Show(actor.state)))
	return nil
}

func (i PrepareInstanceStateStep) MaxExecRetry() int {
	return 10
}

func (i PrepareInstanceStateStep) GetStepName() string {
	return "PrepareInstanceStateStep"
}

type UpdateInstanceStatusCreatingStep struct {
	BaseStep
}

func (u UpdateInstanceStatusCreatingStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	audit, err := a.auditTlsDAL.GetByID(ctx, a.state.InstanceId, a.state.TenantId)
	if err != nil {
		log.Error(ctx, "query audit fail, error:%s", err)
		return false, err
	}
	a.state.InstanceStatus = model.AuditStatus(audit.Status)
	if a.state.InstanceStatus == model.AuditStatus_OrderNew {
		return false, nil
	} else if a.state.InstanceStatus == model.AuditStatus_Creating {
		return true, nil
	}
	return false, errors.New(fmt.Sprintf("audit status is not expected, want: OrderNew, got:%s", model.AuditStatus(audit.Status).String()))
}

func (u UpdateInstanceStatusCreatingStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	err := a.auditTlsDAL.UpdateByFollowInstanceID(ctx, &dao.AuditTls{
		FollowInstanceID: a.state.FollowInstanceId,
		TenantID:         a.state.TenantId,
		Status:           int32(model.AuditStatus_Creating),
	})
	if err == nil {
		a.state.InstanceStatus = model.AuditStatus_Creating
		return nil
	}
	return err
}

func (u UpdateInstanceStatusCreatingStep) MaxExecRetry() int {
	return 10
}

func (u UpdateInstanceStatusCreatingStep) GetStepName() string {
	return "UpdateInstanceStatusCreatingStep"
}

type UpdateInstanceStatusDeletingStep struct {
	BaseStep
}

func (u UpdateInstanceStatusDeletingStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	audit, err := a.auditTlsDAL.GetByID(ctx, a.state.InstanceId, a.state.TenantId)
	if err != nil {
		log.Error(ctx, "query audit fail, error:%s", err)
		return err
	}
	if audit.Status < int32(model.AuditStatus_Deleting) {
		a.state.InstanceStatus = model.AuditStatus_Deleting
		audit.Status = int32(a.state.InstanceStatus)
		err := a.auditTlsDAL.UpdateByFollowInstanceID(ctx, audit)
		return err
	}
	return nil
}

func (u UpdateInstanceStatusDeletingStep) MaxExecRetry() int {
	return 10
}

func (u UpdateInstanceStatusDeletingStep) GetStepName() string {
	return "UpdateInstanceStatusDeletingStep"
}

type UpdateInstanceStatusStep struct {
	BaseStep
}

func (u UpdateInstanceStatusStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (u UpdateInstanceStatusStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	err := a.auditTlsDAL.UpdateByFollowInstanceID(ctx, &dao.AuditTls{
		FollowInstanceID: a.state.FollowInstanceId,
		TenantID:         a.state.TenantId,
		Status:           int32(a.state.InstanceStatus),
	})
	return err
}

func (u UpdateInstanceStatusStep) MaxExecRetry() int {
	return 10
}

func (u UpdateInstanceStatusStep) GetStepName() string {
	return "UpdateInstanceStatusStep"
}

type PrepareIAMProjectStep struct {
	BaseStep
}

func (p PrepareIAMProjectStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	_, err := a.projectSvc.GetResourceProjectName(context.Background(), a.state.Region, a.state.TenantId, a.state.InstanceId)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

func (p PrepareIAMProjectStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	err := a.projectSvc.AddAuditToResource(ctx, a.state.Region, a.state.TenantId, a.state.InstanceId, a.state.FollowInstanceId, a.state.DSType)
	if err != nil {
		return err
	}
	return nil
}

func (p PrepareIAMProjectStep) MaxExecRetry() int {
	return 10
}

func (p PrepareIAMProjectStep) GetStepName() string {
	return "PrepareIAMProjectStep"
}

type PrepareTLSProjectStep struct {
	BaseStep
}

func (p PrepareTLSProjectStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	if a.state.TlsProject != "" {
		return true, nil
	}
	// 如果实例与tls不在一个region的情况下会找不到project
	site := i18n.GetSiteName(ctx, a.state.TenantId)
	tlsRegion, err := a.conf.Get(ctx).GetTLSRegion(site)
	if err != nil {
		log.Error(ctx, "请检查运维面参数:TlsServiceEndpoint, tls region err:%s", err)
		return false, err
	}
	pls, err := a.tlsDAL.GetProjects(ctx, a.state.TenantId, tlsRegion)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Info(ctx, "project not exist in metadb, TenantId:%s, Region:%s", a.state.TenantId, a.state.Region)
			return false, nil
		} else {
			log.Warn(ctx, "failed to get tls project list, err=%v", err)
			return false, err
		}
	}
	client, err := p.GetTlsClient(ctx, a)
	if err != nil {
		return false, err
	}
	for _, pl := range pls {
		project, err := client.DescribeProject(&tls_sdk.DescribeProjectRequest{
			CommonRequest: tls_sdk.CommonRequest{},
			ProjectID:     pl.TlsProjectId,
		})
		if err != nil {
			if strings.Contains(err.Error(), "ProjectNotExists") {
				log.Info(ctx, "Project not exists:%s", err)
				continue
			} else {
				return false, err
			}
		}
		if project.TopicCount < 50 {
			log.Info(ctx, "project topic count less than 50")
			a.state.TlsProject = pl.TlsProjectId
			return true, nil
		}
	}
	log.Info(ctx, "not find valid project")
	return false, nil
}

func (p PrepareTLSProjectStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	tlsClient, err := p.GetTlsClient(ctx, a)
	if err != nil {
		return err
	}
	// 创建新project
	projectPrefix := "dbw-sql-audit-"
	createProjectRequest := &tls_sdk.CreateProjectRequest{
		ProjectName: projectPrefix + uuid.New().String(),
		Region:      a.state.TlsRegion,
		Description: consts.TLSDescription,
	}
	createProjectResponse, err := tlsClient.CreateProject(createProjectRequest)
	log.Info(ctx, "createProjectResponse:%+v", createProjectResponse)
	if err != nil {
		log.Info(ctx, "create project %s failed, error:%s", createProjectRequest.ProjectName, err.Error())
		return err
	}
	site := i18n.GetSiteName(ctx, a.state.TenantId)
	_, err = a.tlsDAL.Create(ctx, &dao.Tls{
		TlsProjectId: createProjectResponse.ProjectID,
		Region:       a.state.TlsRegion,
		TenantID:     a.state.TenantId,
		TlsEndpoint:  a.conf.Get(ctx).GetTlsRegionEndpoint(site),
	})
	if err != nil {
		log.Warn(ctx, "failed to create tls topic, err=%v", err)
	}
	a.state.TlsProject = createProjectResponse.ProjectID
	return nil
}

func (p PrepareTLSProjectStep) MaxExecRetry() int {
	return 10
}

func (p PrepareTLSProjectStep) GetStepName() string {
	return "PrepareTLSProjectStep"
}

type PrepareTLSTopicStep struct {
	BaseStep
}

func (p PrepareTLSTopicStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	if a.state.TlsTopic == "" {
		return false, nil
	} else {
		return true, nil
	}

}

func (p PrepareTLSTopicStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	tlsClient, err := p.GetTlsClient(ctx, a)
	if err != nil {
		return err
	}

	createTopicRequest := &tls_sdk.CreateTopicRequest{
		ProjectID:   a.state.TlsProject,
		TopicName:   a.state.FollowInstanceId + uuid.New().String(),
		Description: consts.TLSDescription,
		Ttl:         uint16(a.state.TlsTopicTTL),
		AutoSplit:   true,
		ShardCount:  4,
	}
	log.Info(ctx, "createTopicRequest:%s", createTopicRequest)
	createTopicResponse, err := tlsClient.CreateTopic(createTopicRequest)
	if err != nil {
		log.Info(ctx, "create topic %s failed, error:%s", createTopicRequest.TopicName, err)
		return err
	}
	log.Info(ctx, " create topic %s response:%+v", createTopicRequest.TopicName, createTopicResponse)
	a.state.TlsTopic = createTopicResponse.TopicID
	return nil
}

func (p PrepareTLSTopicStep) MaxExecRetry() int {
	return 10
}

func (p PrepareTLSTopicStep) GetStepName() string {
	return "PrepareTLSTopicStep"
}

type PrepareTLSIndexStep struct {
	BaseStep
}

func (p PrepareTLSIndexStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	tlsClient, err := p.GetTlsClient(ctx, a)
	if err != nil {
		return false, err
	}
	_, err = tlsClient.DescribeIndex(&tls_sdk.DescribeIndexRequest{
		CommonRequest: tls_sdk.CommonRequest{},
		TopicID:       a.state.TlsTopic,
	})
	if err != nil {
		if strings.Contains(err.Error(), "IndexNotExists") {
			return false, nil
		} else {
			return false, err
		}
	} else {
		return true, nil
	}
}

func (p PrepareTLSIndexStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	tlsClient, err := p.GetTlsClient(ctx, a)
	if err != nil {
		return err
	}

	log.Info(ctx, "create new topic id : %s", a.state.TlsTopic)
	createIndexRequest, err := a.genTlsSvc.GenCreateIndexRequest(ctx, a.state.TlsTopic, a.state.DSType, model.StatisticDataType_SqlDetail, a.state.TlsIndexVersion)
	if err != nil {
		log.Warn(ctx, "GenCreateIndexRequest error:%s", err)
		return err
	}
	log.Info(ctx, "createIndexRequest : %+v", createIndexRequest)
	createIndexResp, err := tlsClient.CreateIndex(createIndexRequest)
	if err != nil {
		log.Info(ctx, "create tls index error, topic:%s, error:%s", a.state.TlsTopic, err.Error())
		return err
	}
	log.Info(ctx, "createIndexResp : %+v", createIndexResp)
	return nil
}

func (p PrepareTLSIndexStep) MaxExecRetry() int {
	return 10
}

func (p PrepareTLSIndexStep) GetStepName() string {
	return "PrepareTLSIndexStep"
}

type SaveTLSInfoStep struct {
	BaseStep
}

func (s SaveTLSInfoStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (s SaveTLSInfoStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	if a.state.TlsId == 0 {
		tlsId, err := a.tlsDAL.Create(context.TODO(), &dao.Tls{
			TlsProjectId:   a.state.TlsProject,
			TlsTopicId:     a.state.TlsTopic,
			TlsEndpoint:    a.state.TlsEndpoint,
			Region:         a.state.TlsRegion,
			TenantID:       a.state.TlsTenant,
			TlsRuleId:      a.state.TlsRuleId,
			TlsHostGroupId: a.state.TlsHostGroupId,
		})
		if err != nil {
			log.Info(ctx, "create tls meta error:%s", err.Error())
			return err
		}
		a.state.TlsId = tlsId
	}

	err := a.auditTlsDAL.UpdateByFollowInstanceID(ctx, &dao.AuditTls{
		TlsId:            a.state.TlsId,
		FollowInstanceID: a.state.FollowInstanceId,
		TenantID:         a.state.TenantId,
	})
	if err != nil {
		log.Info(ctx, "update audit meta error:%s", err.Error())
		return err
	}
	return nil
}

func (s SaveTLSInfoStep) MaxExecRetry() int {
	return 10
}

func (s SaveTLSInfoStep) GetStepName() string {
	return "SaveTLSInfoStep"
}

type UpdateTLSHostGroupStep struct {
	BaseStep
}

func (s UpdateTLSHostGroupStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	if a.state.TlsId != 0 {
		err := a.tlsDAL.UpdateByID(context.TODO(), a.state.TlsTenant, &dao.Tls{
			ID:             a.state.TlsId,
			TlsProjectId:   a.state.TlsProject,
			TlsTopicId:     a.state.TlsTopic,
			TlsEndpoint:    a.state.TlsEndpoint,
			Region:         a.state.TlsRegion,
			TenantID:       a.state.TlsTenant,
			TlsRuleId:      a.state.TlsRuleId,
			TlsHostGroupId: a.state.TlsHostGroupId,
		})
		if err != nil {
			log.Info(ctx, "create tls meta error:%s", err.Error())
			return err
		}
	}
	return nil
}

func (s UpdateTLSHostGroupStep) MaxExecRetry() int {
	return 10
}

func (s UpdateTLSHostGroupStep) GetStepName() string {
	return "UpdateTLSHostGroupStep"
}

type UpdateTLSRuleStep struct {
	BaseStep
}

func (s UpdateTLSRuleStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	if a.state.TlsId != 0 {
		err := a.tlsDAL.UpdateByID(context.TODO(), a.state.TlsTenant, &dao.Tls{
			ID:             a.state.TlsId,
			TlsProjectId:   a.state.TlsProject,
			TlsTopicId:     a.state.TlsTopic,
			TlsEndpoint:    a.state.TlsEndpoint,
			Region:         a.state.TlsRegion,
			TenantID:       a.state.TlsTenant,
			TlsRuleId:      a.state.TlsRuleId,
			TlsHostGroupId: a.state.TlsHostGroupId,
		})
		if err != nil {
			log.Info(ctx, "create tls meta error:%s", err.Error())
			return err
		}
	}
	return nil
}

func (s UpdateTLSRuleStep) MaxExecRetry() int {
	return 10
}

func (s UpdateTLSRuleStep) GetStepName() string {
	return "UpdateTLSRuleStep"
}

type NotifyCreateOrderStep struct {
	BaseStep
}

func (n NotifyCreateOrderStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	instance, err := a.billSvc.GetInstance(ctx, a.state.InstanceId)
	if err != nil {
		return false, err
	}
	if instance == nil {
		return false, errors.New("InstanceNotExist")
	}
	switch model.BusinessStatus(instance.BusinessStatus) {
	case model.BusinessStatus_InstanceBusinessPending:
		return false, nil
	case model.BusinessStatus_InstanceBusinessRunning:
		return true, nil
	default:
		return false, errors.New("create notify but meet other business status")
	}

}

func (n NotifyCreateOrderStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	err := a.billSvc.CallbackInstanceStatus(ctx, a.state.Message, false, true)
	if err != nil {
		return err
	}
	log.Info(ctx, "create notify CallbackInstanceStatus %s. msg id : %s", true, a.state.Message.MsgID)
	eventResult := int32(model.EventResult__Success)
	err = a.publishEventSvc.UpdateEventResult(ctx, a.state.Message.MsgID, a.state.Message.InstanceNO, eventResult)
	if err != nil {
		log.Error(ctx, "UpdateEventResult fail, MsgID:%s InstanceNO:%s eventResult:%s", a.state.Message.MsgID, a.state.Message.InstanceNO, eventResult)
	}
	return nil
}

func (n NotifyCreateOrderStep) MaxExecRetry() int {
	return 10
}

func (n NotifyCreateOrderStep) GetStepName() string {
	return "NotifyCreateOrderStep"
}

type NotifyCreateOrderFailStep struct {
	BaseStep
}

func (n NotifyCreateOrderFailStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	log.Info(ctx, "audit instance resource gc, instanceId:%s", a.state.InstanceId)
	instance, err := a.billSvc.GetInstance(ctx, a.state.InstanceId)
	if err != nil {
		log.Warn(ctx, "bill service get instance err:%s", err)
		return false, err
	}
	log.Info(ctx, "audit instance bill status:%s business status:%s", model.AuditStatus(instance.Status).String(), model.BusinessStatus(instance.BusinessStatus).String())

	if instance != nil {
		switch model.BusinessStatus(instance.BusinessStatus) {
		case model.BusinessStatus_InstanceBusinessPending:
			return false, nil
		default:
			return false, errors.New(fmt.Sprintf("unexpected business statue:%s, it should be InstanceBusinessPending", model.BusinessStatus(instance.BusinessStatus)))
		}
	} else {
		return false, errors.New("instance not exist")
	}
}

func (n NotifyCreateOrderFailStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	err := a.billSvc.CallbackInstanceStatus(ctx, a.state.Message, false, false)
	if err != nil {
		log.Warn(ctx, "bill service CallbackInstanceStatus false err:%s", err)
		return err
	}
	log.Info(ctx, "create notify CallbackInstanceStatus %s. msg id : %s", false, a.state.Message.MsgID)
	eventResult := int32(model.EventResult__Success)
	err = a.publishEventSvc.UpdateEventResult(ctx, a.state.Message.MsgID, a.state.Message.InstanceNO, eventResult)
	if err != nil {
		log.Warn(ctx, "UpdateEventResult fail, MsgID:%s InstanceNO:%s eventResult:%s", a.state.Message.MsgID, a.state.Message.InstanceNO, eventResult)
		return err
	}

	return nil
}

func (n NotifyCreateOrderFailStep) MaxExecRetry() int {
	return 10
}

func (n NotifyCreateOrderFailStep) GetStepName() string {
	return "NotifyCreateOrderFailStep"
}

type AuditResourceGCStep struct {
	BaseStep
}

func (G AuditResourceGCStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	log.Info(ctx, "audit instance resource gc, instanceId:%s", a.state.InstanceId)
	var CloseType model.CloseType
	if a.state.CloseType == nil {
		CloseType = model.CloseType_CloseAuditCollectAndTls
	} else {
		// 删除实例的消息有这个值
		CloseType = *a.state.CloseType
	}
	err := a.auditService.DeleteAuditResource(ctx, a.state.FollowInstanceId, a.state.TenantId, CloseType, a.state.DSType)
	if err == nil {
		ctx.Send(ctx.Self(), &shared.RemoveAuditInstance{InstanceId: a.state.FollowInstanceId})
	}
	return err
}

func (G AuditResourceGCStep) MaxExecRetry() int {
	return 10
}

func (G AuditResourceGCStep) GetStepName() string {
	return "AuditResourceGCStep"
}

type PrepareIAMProjectTagStep struct {
	BaseStep
}

func (p PrepareIAMProjectTagStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	tags, err := a.tagSvc.GetTag(ctx, a.state.Region, a.state.TenantId, a.state.InstanceId, tag.SystemTag)
	if err != nil {
		return false, err
	}
	for _, pair := range tags {
		if pair.Key == consts.SystemRdsTagKey ||
			pair.Key == consts.SystemVeDBTagKey ||
			pair.Key == consts.SystemPGTagKey ||
			pair.Key == consts.SystemRedisTagKey ||
			pair.Key == consts.SystemMongoTagKey {
			return true, nil
		}
	}
	return false, nil

}

func (p PrepareIAMProjectTagStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	err := a.tagSvc.CreateParentProjectTag(ctx, a.state.Region, a.state.TenantId, a.state.InstanceId, a.state.FollowInstanceId, a.state.DSType)
	if err != nil {
		return err
	}
	return nil
}

func (p PrepareIAMProjectTagStep) MaxExecRetry() int {
	return 10
}

func (p PrepareIAMProjectTagStep) GetStepName() string {
	return "PrepareIAMProjectTagStep"
}

type CreateTLSHostGroupStep struct {
	BaseStep
}

func (p CreateTLSHostGroupStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	if a.state.TlsHostGroupId != "" {
		return true, nil
	} else {
		tlsClient, err := a.auditService.GetTlsClient(context.Background(), a.state.TlsTenant)
		if err != nil {
			return false, err
		}
		hostGroupName := k8s.GetHostIdentifier(a.state.TenantId, a.state.Region)
		req := &tls_sdk.DescribeHostGroupsRequest{
			HostGroupName: &hostGroupName,
		}
		log.Info(ctx, "req:%+v", req.HostGroupName)
		groups, err := tlsClient.DescribeHostGroups(req)
		log.Info(ctx, "resp:%+v, err:%s", groups, err)
		if err != nil {
			return false, err
		}
		if groups.HostGroupHostsRulesInfos != nil && len(groups.HostGroupHostsRulesInfos) > 0 {
			a.state.TlsHostGroupId = groups.HostGroupHostsRulesInfos[0].HostGroupInfo.HostGroupID
			return true, nil
		}
		return false, nil
	}
}

// GetHostIdentifier 机器组名与标识一样
//func GetHostIdentifier(tenantId, region string) string {
//	return fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf("%s|%s|%s", tenantId, region, "8yteD@n(e"))))
//}
//func GetRuleName(tenantId, region, followInstanceId string) string {
//	return fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf("%s|%s|%s|%s", tenantId, region, followInstanceId, "8y&eD@n(e"))))
//}

func (p CreateTLSHostGroupStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	tlsClient, err := a.auditService.GetTlsClient(context.Background(), a.state.TlsTenant)
	if err != nil {
		return err
	}
	// 机器组名与标识一样
	hostGroupName := k8s.GetHostIdentifier(a.state.TenantId, a.state.Region)
	hostIdentifier := k8s.GetHostIdentifier(a.state.TenantId, a.state.Region)
	createHostGroupReq := tls_sdk.CreateHostGroupRequest{
		CommonRequest:  tls_sdk.CommonRequest{Headers: IgnoreHeaders},
		HostGroupName:  hostGroupName,
		HostGroupType:  "Label",
		HostIdentifier: &hostIdentifier,
		AutoUpdate:     pointer.Bool(false),
	}
	hostGroup, err := tlsClient.CreateHostGroup(&createHostGroupReq)
	if err != nil {
		return err
	}
	a.state.TlsHostGroupId = hostGroup.HostGroupID
	return nil
}

func (p CreateTLSHostGroupStep) MaxExecRetry() int {
	return 10
}

func (p CreateTLSHostGroupStep) GetStepName() string {
	return "CreateTLSHostGroupStep"
}

type CreateTLSRuleStep struct {
	BaseStep
}

func (p CreateTLSRuleStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	if a.state.TlsRuleId != "" {
		return true, nil
	} else {
		tlsClient, err := p.GetTlsClient(ctx, a)
		if err != nil {
			return false, err
		}
		ruleName := k8s.GetRuleName(a.state.TenantId, a.state.Region, a.state.FollowInstanceId)
		rules, err := tlsClient.DescribeRules(&tls_sdk.DescribeRulesRequest{
			ProjectID: a.state.TlsProject,
			RuleName:  &ruleName,
		})
		if err != nil {
			return false, err
		}
		if rules.RuleInfos != nil && len(rules.RuleInfos) > 0 {
			a.state.TlsRuleId = rules.RuleInfos[0].RuleID
			return true, nil
		}
		return false, nil
	}
}

func (p CreateTLSRuleStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	tlsClient, err := p.GetTlsClient(ctx, a)
	if err != nil {
		return err
	}
	// TODO 插件
	logType := MultilineLog
	inputType := 2
	logSample := "2023-09-27 16:45:44.501 CST,\"postgres\",\"db1\",349031,\"[local]\",6513eb87.55367,35909,\"INSERT\",2023-09-27 16:44:55 CST,16/7850,91148,LOG,00000,\"duration: 0.035 ms  statement: INSERT INTO pgbench_history (tid, bid, aid, delta, mtime) VALUES (50, 3, 84513, -2344, CURRENT_TIMESTAMP);\",,,,,,,,\"exec_simple_query, postgres.c:1322\",\"pgbench\",1,0,0,0.000 ms,0.000 ms\n"
	createRuleReq := tls_enhance.CreateRuleEnhanceRequest{
		CommonRequest: tls_sdk.CommonRequest{Headers: IgnoreHeaders},
		TopicID:       a.state.TlsTopic,
		RuleName:      k8s.GetRuleName(a.state.TenantId, a.state.Region, a.state.FollowInstanceId),
		Paths:         &[]string{"/var/log/pg_log/postgresql-*.auditlog"},
		LogType:       &logType,
		ExtractRule: &tls_sdk.ExtractRule{
			BeginRegex:          PGLogRegex,
			UnMatchUpLoadSwitch: true,
			UnMatchLogKey:       "LogParseFailed",
			TimeKey:             "content_timestamp",
			TimeFormat:          "%s",
		},
		LogSample: &logSample,
		InputType: &inputType,
		ContainerRule: &tls_sdk.ContainerRule{
			ContainerNameRegex: PgContainerName,
			KubernetesRule: tls_sdk.KubernetesRule{
				NamespaceNameRegex: PgNameSpace,
				IncludePodLabelRegex: map[string]string{
					PgPodLabel: a.state.FollowInstanceId,
				},
			},
		},
		UserDefineRule: &tls_enhance.UserDefineRule{
			EnableRawLog: false,
			Plugin: map[string]interface{}{
				"processors": []interface{}{
					map[string]interface{}{
						"pg_auditlog_parser": map[string]interface{}{
							"name":                "pg_auditlog_parser",
							"sql_text_max_length": 31,
						},
					},
				},
			},
		},
	}
	log.Info(ctx, "createRuleReq:%+v", libutils.Show(createRuleReq))
	createRuleResp, err := tls_enhance.CreateRule(tlsClient, &createRuleReq)
	log.Info(ctx, "createRuleResp:%+v ,error:%s", libutils.Show(createRuleResp), err)
	if err != nil {
		log.Info(ctx, "createRule fail:%s", err)
		return err
	}
	a.state.TlsRuleId = createRuleResp.RuleID
	return nil
}

func (p CreateTLSRuleStep) MaxExecRetry() int {
	return 10
}

func (p CreateTLSRuleStep) GetStepName() string {
	return "CreateTLSRuleStep"
}

type ApplyRuleToHostGroupsStep struct {
	BaseStep
}

func (p ApplyRuleToHostGroupsStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	tlsClient, err := p.GetTlsClient(ctx, a)
	if err != nil {
		return false, err
	}
	rule, err := tlsClient.DescribeRule(&tls_sdk.DescribeRuleRequest{
		RuleID: a.state.TlsRuleId,
	})
	if err != nil {
		return false, err
	}
	if len(rule.HostGroupInfos) > 0 {
		if rule.HostGroupInfos[0].HostGroupID == a.state.TlsHostGroupId {
			return true, nil
		} else {
			return false, errors.New("wtf rule not bind with the host group")
		}
	}
	return false, nil
}

func (p ApplyRuleToHostGroupsStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	tlsClient, err := p.GetTlsClient(ctx, a)
	if err != nil {
		return err
	}
	applyRuleToHostGroupsRequest := &tls_sdk.ApplyRuleToHostGroupsRequest{
		RuleID:       a.state.TlsRuleId,
		HostGroupIDs: []string{a.state.TlsHostGroupId},
	}
	log.Info(ctx, "applyRuleToHostGroupsRequest:%+v", applyRuleToHostGroupsRequest)
	applyRuleToHostGroupsResp, err := tlsClient.ApplyRuleToHostGroups(applyRuleToHostGroupsRequest)
	log.Info(ctx, "applyRuleToHostGroupsResp:%+v", applyRuleToHostGroupsResp)
	if err != nil {
		log.Info(ctx, "applyRuleToHostGroups failed:%s", err)
		return err
	}
	return nil
}

func (p ApplyRuleToHostGroupsStep) MaxExecRetry() int {
	return 10
}

func (p ApplyRuleToHostGroupsStep) GetStepName() string {
	return "ApplyRuleToHostGroupsStep"
}

type CreateLogCollectorResourceStep struct {
	BaseStep
}

func (c CreateLogCollectorResourceStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (c CreateLogCollectorResourceStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	applicationC3Config := a.c3Conf.GetNamespace(ctx, consts.C3ApplicationNamespace)
	serviceAK := applicationC3Config.Application.InnerTOPServiceAccessKey
	serviceSK := applicationC3Config.Application.InnerTOPServiceSecretKey
	//Todo: nodePool
	for _, clusterName := range a.state.AzClusterMap {
		var nodePools []string
		for k, v := range a.state.NodePoolsToCluster {
			if v == clusterName {
				nodePools = append(nodePools, k)
			}
		}
		tlsRegion, _ := a.conf.Get(ctx).GetVolcTlsRegion()
		err := a.lcSvc.DeployLogCollector(ctx, &entity.ClusterInfo{ClusterName: clusterName}, nodePools, k8s.TenantConfig{
			Endpoint:     a.state.TlsEndpoint,
			SecretID:     serviceAK,
			SecretKey:    serviceSK,
			Region:       a.state.TlsRegion,
			RoleProvider: k8s.RoleProviderAssumeRoleProvider,
			RoleName:     a.conf.Get(ctx).ServiceLinkRuleAudit,
			VolcEndpoint: volc.GetInnerServiceEndpoint(a.conf.Get(ctx).IAMProfile, tlsRegion, volc.InnerServiceSTS, ""),
			AccountID:    a.state.TenantId,
			// 这里机器组标识
			ServiceName: k8s.GetHostIdentifier(a.state.TenantId, a.state.Region),
		})
		if err != nil {
			return err
		}
	}
	ctx.Send(ctx.Self(), &shared.NewAuditInstance{InstanceId: a.state.FollowInstanceId})
	return nil
}

func (c CreateLogCollectorResourceStep) MaxExecRetry() int {
	return 10
}

func (c CreateLogCollectorResourceStep) GetStepName() string {
	return "CreateLogCollectorResourceStep"
}

type PrepareDeleteInstanceStateStep struct {
	BaseStep
}

func (p PrepareDeleteInstanceStateStep) ProtectExec(ctx types.Context, actor *AuditLifecycleActor) error {
	audit, err := actor.auditTlsDAL.GetByID(ctx, actor.state.InstanceId, actor.state.TenantId)
	if err != nil {
		log.Error(ctx, "query audit fail, error:%s", err)
		return err
	}
	actor.state.InstanceStatus = model.AuditStatus(audit.Status)
	actor.state.FollowInstanceId = audit.FollowInstanceID
	actor.state.DSType, err = model.DSTypeFromString(audit.DbType)
	if err != nil {
		log.Error(ctx, "DSType fail, error:%s", err)
		return err
	}
	actor.state.Region = audit.Region
	tlsInfo, err := actor.tlsDAL.GetFromAdmin(ctx, strconv.FormatInt(audit.TlsId, 10))
	if err != nil {
		log.Error(ctx, "query table dbw_tls fail, error:%s", err)
		return err
	}
	actor.state.TlsRegion = tlsInfo.Region
	actor.state.TlsEndpoint = tlsInfo.TlsEndpoint
	actor.state.TlsTenant = tlsInfo.TenantID
	actor.state.TlsId = tlsInfo.ID
	actor.state.TlsProject = tlsInfo.TlsProjectId
	actor.state.TlsTopic = tlsInfo.TlsTopicId
	actor.state.TlsDataType = model.StatisticDataType_SqlDetail
	log.Info(ctx, fmt.Sprintf("Init params:[%s]", libutils.Show(actor.state)))
	return nil
}

func (p PrepareDeleteInstanceStateStep) MaxExecRetry() int {
	return 10
}

func (p PrepareDeleteInstanceStateStep) GetStepName() string {
	return "PrepareDeleteInstanceStateStep"
}

type NotifyDeleteOrderStep struct {
	BaseStep
}

func (n NotifyDeleteOrderStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	instance, err := a.billSvc.GetInstance(ctx, a.state.InstanceId)
	if err != nil {
		return false, err
	}
	if instance == nil {
		return false, err
	}
	switch model.OrderInstanceStatus(instance.Status) {
	case model.OrderInstanceStatus_InstanceStatusCreatedFail:
		return true, nil
	}
	switch model.BusinessStatus(instance.BusinessStatus) {
	case model.BusinessStatus_InstanceBusinessTerminating:
		return false, nil
	case model.BusinessStatus_InstanceBusinessReclaiming:
		return false, nil
	case model.BusinessStatus_InstanceBusinessExpiring:
		return false, nil
	case model.BusinessStatus_InstanceBusinessTerminateSuspending:
		return false, nil
	case model.BusinessStatus_InstanceBusinessOffDuty:
		return true, nil
	default:
		return false, errors.New("create notify but meet other business status")
	}

}

func (n NotifyDeleteOrderStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	err := a.billSvc.CallbackInstanceStatus(ctx, a.state.Message, false, true)
	if err != nil {
		return err
	}
	log.Info(ctx, "create notify CallbackInstanceStatus %s. msg id : %s", true, a.state.Message.MsgID)
	eventResult := int32(model.EventResult__Success)
	err = a.publishEventSvc.UpdateEventResult(ctx, a.state.Message.MsgID, a.state.Message.InstanceNO, eventResult)
	if err != nil {
		log.Error(ctx, "UpdateEventResult fail, MsgID:%s InstanceNO:%s eventResult:%s", a.state.Message.MsgID, a.state.Message.InstanceNO, eventResult)
	}
	return nil
}

func (n NotifyDeleteOrderStep) MaxExecRetry() int {
	return 10
}

func (n NotifyDeleteOrderStep) GetStepName() string {
	return "NotifyDeleteOrderStep"
}

type OpenDBInstanceAuditLogStep struct {
	BaseStep
}

func (n OpenDBInstanceAuditLogStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (n OpenDBInstanceAuditLogStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	_, err := a.source.OpenDBInstanceAuditLog(ctx, &datasource.OpenDBInstanceAuditLogReq{
		InstanceId:    a.state.FollowInstanceId,
		Type:          shared.DataSourceType(a.state.DSType),
		AuditLogTypes: a.state.StorageSqlTypes,
	})
	if err != nil {
		return err
	}
	return nil
}

func (n OpenDBInstanceAuditLogStep) MaxExecRetry() int {
	return 10
}

func (n OpenDBInstanceAuditLogStep) GetStepName() string {
	return "OpenDBInstanceAuditLogStep"
}

type RemoveResourceStep struct {
	BaseStep
}

func (n RemoveResourceStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	return false, nil
}

func (n RemoveResourceStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	// RemoveResource 删除不存在的资源不会报错
	err := a.projectSvc.RemoveResource(ctx, a.state.Region, utils.GetTenantIDX(a.state.TenantId, a.state.DSType, a.conf), a.state.InstanceId)
	if err != nil {
		log.Warn(ctx, "remove resource error: %s")
		return err
	}
	return nil
}

func (n RemoveResourceStep) MaxExecRetry() int {
	return 10
}

func (n RemoveResourceStep) GetStepName() string {
	return "RemoveResourceStep"
}

type TagResourceStep struct {
	BaseStep
}

func (n TagResourceStep) PreCheck(ctx types.Context, a *AuditLifecycleActor) (bool, error) {
	if len(a.state.InitTags) == 0 {
		log.Info(ctx, "no tags to operate")
		return true, nil
	}
	return false, nil
}

func (n TagResourceStep) ProtectExec(ctx types.Context, a *AuditLifecycleActor) error {
	err := a.tagSvc.TagResource(ctx, a.state.Region, a.state.TenantId, a.state.InstanceId, a.state.InitTags)
	if err != nil {
		log.Warn(ctx, "remove resource error: %s")
		return err
	}
	return nil
}

func (n TagResourceStep) MaxExecRetry() int {
	return 10
}

func (n TagResourceStep) GetStepName() string {
	return "TagResourceStep"
}
