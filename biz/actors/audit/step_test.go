package audit

import (
	biz_config "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	serviceaudit "code.byted.org/infcs/dbw-mgr/biz/service/audit"
	"code.byted.org/infcs/dbw-mgr/biz/service/audit/k8s"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"errors"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"testing"
)

// MockC3ConfigProvider is a mock for the c3.ConfigProvider interface.
type MockC3ConfigProvider struct {
	c3.ConfigProvider
}

// GetNamespace mocks the GetNamespace method.
func (m *MockC3ConfigProvider) GetNamespace(ctx context.Context, namespace string) *biz_config.C3Config {
	return &biz_config.C3Config{}
}

// MockConfigProvider is a mock for the service_config.ConfigProvider interface.

// Get mocks the Get method.

// MockLogCollectorSvc is a mock for the LogCollectorInterface interface.
type MockLogCollectorSvc struct {
	serviceaudit.LogCollectorInterface
}

// DeployLogCollector mocks the DeployLogCollector method.
func (m *MockLogCollectorSvc) DeployLogCollector(ctx context.Context, cluster *entity.ClusterInfo, nodePool []string, tenantConfig k8s.TenantConfig) error {
	return nil
}

// MockActorContext is a mock for the types.Context interface.
type MockActorContext1 struct {
	types.Context
}

// Self mocks the Self method.
func (m *MockActorContext1) Self() *actor.PID {
	return &actor.PID{}
}

// Send mocks the Send method.
func (m *MockActorContext1) Send(pid *actor.PID, message interface{}) {
}

func Test_CreateLogCollectorResourceStep_ProtectExec_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("Test_CreateLogCollectorResourceStep_ProtectExec", t, func() {
		// 数据准备
		step := CreateLogCollectorResourceStep{}
		ctx := &MockActorContext1{}
		a := &AuditLifecycleActor{
			state: &AuditLifeState{
				AzClusterMap: map[string]string{
					"az-1": "cluster-a",
					"az-2": "cluster-b",
				},
				NodePoolsToCluster: map[string]string{
					"nodepool-1": "cluster-a",
					"nodepool-2": "cluster-b",
					"nodepool-3": "cluster-b",
				},
				TlsEndpoint:      "test-tls-endpoint",
				TlsRegion:        "test-tls-region",
				TenantId:         "test-tenant-id",
				Region:           "test-region",
				FollowInstanceId: "test-follow-instance-id",
			},
			c3Conf: &MockC3ConfigProvider{},
			conf:   &MockConfigProvider{},
			lcSvc:  &MockLogCollectorSvc{},
		}
		mockC3Config := &biz_config.C3Config{
			Application: biz_config.Application{
				InnerTOPServiceAccessKey: "test-ak",
				InnerTOPServiceSecretKey: "test-sk",
			},
		}
		mockConfig := &biz_config.Config{
			ServiceLinkRuleAudit: "test-service-link-rule",
			IAMProfile:           "test-iam-profile",
		}

		mockey.PatchConvey("成功场景", func() {
			// 场景描述：当所有依赖都正常返回无异常情况下，验证目标函数正确执行，为每个集群部署日志采集器，并发送消息。
			// 数据构造：
			// - actor.state 包含多个集群和节点池映射。
			// - Mock c3Conf, conf, lcSvc, k8s, ctx 的相关方法均返回成功。
			// 逻辑链路：
			// 1. Mock a.c3Conf.GetNamespace 成功返回C3配置。
			// 2. Mock a.conf.Get 成功返回系统配置。
			// 3. Mock config.GetVolcTlsRegion 成功返回TLS Region。
			// 4. Mock k8s.GetHostIdentifier 成功返回主机标识。
			// 5. Mock a.lcSvc.DeployLogCollector 对所有集群均成功返回nil。
			// 6. Mock ctx.Self 和 ctx.Send 成功。
			// 7. 调用 ProtectExec 方法。
			// 8. 断言返回的 error 为 nil。

			mockey.Mock((*MockC3ConfigProvider).GetNamespace).Return(mockC3Config).Build()
			mockey.Mock((*MockConfigProvider).Get).Return(mockConfig).Build()
			mockey.Mock((*biz_config.Config).GetVolcTlsRegion).Return("test-volc-tls-region", nil).Build()
			mockey.Mock(k8s.GetHostIdentifier).Return("test-host-identifier").Build()
			mockey.Mock((*MockLogCollectorSvc).DeployLogCollector).Return(nil).Build()
			mockey.Mock((*MockActorContext1).Self).Return(&actor.PID{Address: "local", Id: "self"}).Build()
			mockey.Mock((*MockActorContext1).Send).Return().Build()

			err := step.ProtectExec(ctx, a)

			convey.So(err, convey.ShouldBeNil)
		})

		mockey.PatchConvey("失败场景 - DeployLogCollector返回错误", func() {
			// 场景描述：当日志采集器部署失败时，函数应立即返回错误。
			// 数据构造：
			// - actor.state 包含至少一个集群。
			// - Mock lcSvc.DeployLogCollector 返回一个错误。
			// 逻辑链路：
			// 1. Mock a.c3Conf.GetNamespace 成功返回C3配置。
			// 2. Mock a.conf.Get 成功返回系统配置。
			// 3. Mock config.GetVolcTlsRegion 成功返回TLS Region。
			// 4. Mock k8s.GetHostIdentifier 成功返回主机标识。
			// 5. Mock a.lcSvc.DeployLogCollector 返回一个非nil的error。
			// 6. 调用 ProtectExec 方法。
			// 7. 断言返回的 error 不为 nil，且包含预期的错误信息。

			deployErr := errors.New("failed to deploy log collector")
			mockey.Mock((*MockC3ConfigProvider).GetNamespace).Return(mockC3Config).Build()
			mockey.Mock((*MockConfigProvider).Get).Return(mockConfig).Build()
			mockey.Mock((*biz_config.Config).GetVolcTlsRegion).Return("test-volc-tls-region", nil).Build()
			mockey.Mock(k8s.GetHostIdentifier).Return("test-host-identifier").Build()
			mockey.Mock((*MockLogCollectorSvc).DeployLogCollector).Return(deployErr).Build()

			err := step.ProtectExec(ctx, a)

			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, deployErr.Error())
		})
	})
}
