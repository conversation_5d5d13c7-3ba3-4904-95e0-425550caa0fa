package audit

import (
	"code.byted.org/infcs/dbw-mgr/biz/com"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/service/audit"
	"code.byted.org/infcs/dbw-mgr/biz/service/billing"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	crossauth "code.byted.org/infcs/dbw-mgr/biz/service/cross_service_authorization"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/full_sql"
	"code.byted.org/infcs/dbw-mgr/biz/service/full_sql/zkconfig"
	"code.byted.org/infcs/dbw-mgr/biz/service/project"
	"code.byted.org/infcs/dbw-mgr/biz/service/tag"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls/index"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	volctrade "code.byted.org/infcs/lib-mgr-common/volc/trade"
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/coocood/freecache"
	"go.uber.org/dig"
	"runtime/debug"
	"strings"
	"sync"
	"time"
)

const (
	PgContainerName          = "database"
	PgPodLabel               = "pg-cluster"
	PgNameSpace              = "pg"
	PGLogRegex               = "\\d+-\\d+-\\d+\\s\\d+:\\d+:\\d+\\.\\d+\\s.*?"
	RedisNameSpace           = "redis"
	RedisMultiNameSpace      = "redis.*"
	RedisInstLabel           = "ResourceId"
	RedisFilterLabel         = "component"
	RedisFilterLabelVal      = "proxy"
	CreateAuditFlowEnterStep = "PrepareInstanceStateStep"
	CreateAuditFlow          = "CreateAuditFlow"
	DeleteAuditFlowEnterStep = "PrepareDeleteInstanceStateStep"
	DeleteAuditFlow          = "DeleteAuditFlow"
	ErrorEnterStep           = "NotifyCreateOrderFailStep"
	UpgradeInstanceFlow      = "UpgradeInstanceFlow"

	CreateFullSqlFlowEnterStep  = "PrepareFullSqlInstanceStateStep"
	DeleteFullSqlFlowEnterStep  = "PrepareDeleteFullSqlInstanceStateStep"
	FullSqlErrorStep            = "NotifyCreateOrderFailStep"
	UpgradeFullSqlFlowEnterStep = "PrepareFullSqlInstanceUpgradeStateStep"

	CreateFullSqlInnerFlowEnterStep = "PrepareInnerFullSqlInstanceStateStep"
	DeleteFullSqlInnerFlowEnterStep = "PrepareDeleteInnerFullSqlInstanceStateStep"

	MongoNameSpace     = "mongons"
	MongoInstLabel     = "ResourceId"
	MongoContainerName = "mongo"
)

var ErrorFlowStep = map[string]bool{
	"NotifyCreateOrderFailStep": true,
	"AuditResourceGCStep":       true,
}

var GlobalStepLock sync.Mutex

type NewAuditCreateLifecycleActorIn struct {
	dig.In
	Conf                 config.ConfigProvider
	C3Conf               c3.ConfigProvider
	Loc                  location.Location
	DataSource           datasource.DataSourceService
	CrossAuthSvc         crossauth.CrossServiceAuthorizationService
	AuditTlsDAL          dal.AuditTlsDAL
	FullSqlInstDAL       dal.FullSqlDAL
	TagSvc               tag.TagService
	ProjectSvc           project.ProjectService
	AuditService         audit.SqlAuditService
	GenTlsSvc            index.GenTlsService
	TlsDAL               dal.TlsDAL
	LCSvc                audit.LogCollectorInterface
	BillSvc              billing.BillingService
	PublishEventSvc      com.PublishEventService
	StatisticSqlTlsDAL   dal.StatisticSqlTlsDal
	StatisticSqlTaskDAL  dal.StatisticSqlTaskDal
	FullSqlSvc           full_sql.FullSqlService
	FullSqlConfigService zkconfig.FullSqlConfigService
	InstanceExtraTlsDAL  dal.InstanceExtraTlsDAL
	InstanceExtraNodeDAL dal.InstanceExtraNodeDAL
}

type NewInnerFullSqlLifecycleActorIn struct {
	dig.In
	Conf                 config.ConfigProvider
	C3Conf               c3.ConfigProvider
	Loc                  location.Location
	DataSource           datasource.DataSourceService
	CrossAuthSvc         crossauth.CrossServiceAuthorizationService
	GenTlsSvc            index.GenTlsService
	FullSqlInstDAL       dal.FullSqlDAL
	TlsDAL               dal.TlsDAL
	PublishEventSvc      com.PublishEventService
	FullSqlSvc           full_sql.FullSqlService
	FullSqlConfigService zkconfig.FullSqlConfigService
	InstanceExtraTlsDAL  dal.InstanceExtraTlsDAL
	InstanceExtraNodeDAL dal.InstanceExtraNodeDAL
}

func (a *AuditLifecycleActor) initCtx(ctx types.Context) {
	kind := ctx.GetKind()
	log.Info(ctx, "kind, %s, name:%s", kind, ctx.GetName())
	switch kind {
	case consts.PgAuditCreateActorKind:
		a.initOrderCallbackCtx(ctx)
	case consts.AuditDeleteActorKind:
		a.initOrderCallbackCtx(ctx)
	case consts.MysqlAuditCreateActorKind:
		a.initOrderCallbackCtx(ctx)
	case consts.MysqlFullSqlCreateActorKind:
		a.initOrderCallbackCtx(ctx)
	case consts.MysqlFullSqlDeleteActorKind:
		a.initOrderCallbackCtx(ctx)
	case consts.RedisAuditCreateActorKind:
		a.initOrderCallbackCtx(ctx)
	case consts.MongoAuditCreateActorKind:
		a.initOrderCallbackCtx(ctx)
	case consts.MysqlFullSqlUpgradeTableAggrActorKind:
		a.initCustomCtx(ctx)
	case consts.MysqlFullSqlDeleteResourceActorKind:
		a.initCustomCtx(ctx)
	case consts.MysqlFullSqlUpgradeIndexActorKind:
		a.initCustomCtx(ctx)
	case consts.InnerMysqlFullSqlCreateActorKind:
		a.initInnerCtx(ctx)
	case consts.InnerMysqlFullSqlDeleteActorKind:
		a.initInnerCtx(ctx)
	default:
		log.Error(ctx, "Unknown kind, %s, name:%s", kind, ctx.GetName())
	}
}

func (a *AuditLifecycleActor) initOrderCallbackCtx(ctx types.Context) {
	if a.state.MsgID == "" {
		values := strings.Split(ctx.GetName(), consts.ActorSplitSymbol)
		if len(values) != 4 {
			log.Error(ctx, "actor name error, %s", fmt.Errorf("the length of split of actor name %s expect %d, got %d", ctx.GetName(), 4, len(values)))
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		}
		a.state.TenantId = values[0]
		a.state.InstanceId = values[1]
		a.state.OrderId = values[2]
		a.state.MsgID = values[3]

		//解析传入的logid
		if a.state.LifeState.LogID == "" {
			a.state.LifeState.LogID = ctx.GetName()
			ctx.WithValue("biz-context", &fwctx.BizContext{
				LogID: a.state.LifeState.LogID,
			})
		}
	}
	if a.state.TenantId != "" {
		ctx.WithValue("biz-context", &fwctx.BizContext{
			TenantID: a.state.TenantId,
		})
	}
}

func (a *AuditLifecycleActor) initCustomCtx(ctx types.Context) {
	if a.state.InstanceId == "" {
		values := strings.Split(ctx.GetName(), consts.ActorSplitSymbol)
		if len(values) != 2 {
			log.Error(ctx, "actor name error, %s", fmt.Errorf("the length of split of actor name %s expect %d, got %d", ctx.GetName(), 2, len(values)))
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		}
		a.state.TenantId = values[0]
		a.state.InstanceId = values[1]

		//解析传入的logid
		if a.state.LifeState.LogID == "" {
			a.state.LifeState.LogID = ctx.GetName()
			ctx.WithValue("biz-context", &fwctx.BizContext{
				LogID: a.state.LifeState.LogID,
				Extra: make(map[string]string),
			})
		}
	}
	if a.state.TenantId != "" {
		ctx.WithValue("biz-context", &fwctx.BizContext{
			TenantID: a.state.TenantId,
			Extra:    make(map[string]string),
		})
	}
}

func (a *AuditLifecycleActor) initInnerCtx(ctx types.Context) {
	if a.state.InstanceId == "" {
		values := strings.Split(ctx.GetName(), consts.ActorSplitSymbol)
		if len(values) != 2 {
			log.Error(ctx, "actor name error, %s", fmt.Errorf("the length of split of actor name %s expect %d, got %d", ctx.GetName(), 2, len(values)))
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		}
		a.state.TenantId = values[0]
		a.state.InstanceId = values[1]

		//解析传入的logid
		if a.state.LifeState.LogID == "" {
			a.state.LifeState.LogID = ctx.GetName()
			ctx.WithValue("biz-context", &fwctx.BizContext{
				LogID: a.state.LifeState.LogID,
				Extra: make(map[string]string),
			})
		}
	}
	if a.state.TenantId != "" {
		ctx.WithValue("biz-context", &fwctx.BizContext{
			TenantID: a.state.TenantId,
			Extra:    make(map[string]string),
		})
	}
}

type AuditLifeState struct {
	LifeState           LifeState
	FlowType            string
	FlowErrorHandleStep string

	TenantId               string
	InstanceId             string
	InstanceStatus         model.AuditStatus
	FollowInstanceId       string
	Region                 string
	DSType                 model.DSType
	DeployType             model.LabelType
	CloseType              *model.CloseType
	LogProductType         model.LogProductType
	StorageSqlTypes        []string
	CapabilitiesFlags      uint64
	EnableFunctions        map[model.FullSqlFuncName]bool
	SqlDesensitizationType model.SqlDesensitizationType
	RateSimpling           int32
	IsOpsInstance          bool

	OrderId string
	MsgID   string
	Message *volctrade.TradeMessage

	TlsEndpoint     string
	TlsRegion       string
	TlsProject      string
	TlsTopic        string
	TlsTopicTTL     int64 // 初始化时设置
	TlsId           int64
	TlsHostGroupId  string
	TlsRuleId       string
	TlsDataType     model.StatisticDataType
	TlsIndexVersion int64

	TlsConsumerEndpoint   string
	TlsConsumerRegion     string
	TlsConsumerProject    string
	TlsConsumerTopic      string
	TlsConsumerAK         string
	TlsConsumerSK         string
	TlsConsumerGroup      string
	TlsConsumerConcurrent int64

	AzClusterMap       map[string]string
	NodePoolsToCluster map[string]string

	CollectPort          []string
	CollectPodCpuRequest string
	StatisticTlsMap      map[string]*StatisticTlsMeta

	SqlTemplateTls  *TlsConfig
	SqlCountTls     *TlsConfig
	SqlTableAggrTls *TlsConfig
	SqlTopTls       *TlsConfig

	InnerTLSResource bool
	TlsTenant        string

	InitTags map[string]string

	DBName       string
	Nodes        []string
	SimplingRate int32
	SecretLevel  int32
}

type TlsConfig struct {
	TlsId           int64
	TlsEndpoint     string
	TlsRegion       string
	TlsProject      string
	TlsTopic        string
	TlsTopicTTL     int64
	TlsDataType     model.StatisticDataType
	TlsIndexVersion int64
}

type LifeState struct {
	LogID             string
	HistoryStepsTrace [][]string
	CurrentStepName   string
}

type AuditLifecycleActor struct {
	state           *AuditLifeState
	idempotentSteps map[string]IdempotentStep

	source       datasource.DataSourceService
	crossAuthSvc crossauth.CrossServiceAuthorizationService
	cache        *freecache.Cache
	conf         config.ConfigProvider
	c3Conf       c3.ConfigProvider
	loc          location.Location

	tagSvc               tag.TagService
	projectSvc           project.ProjectService
	auditService         audit.SqlAuditService
	genTlsSvc            index.GenTlsService
	fullSqlSvc           full_sql.FullSqlService
	fullSqlConfigService zkconfig.FullSqlConfigService

	auditTlsDAL          dal.AuditTlsDAL
	fullSqlInstDAL       dal.FullSqlDAL
	tlsDAL               dal.TlsDAL
	statisticSqlTlsDAL   dal.StatisticSqlTlsDal
	statisticSqlTaskDAL  dal.StatisticSqlTaskDal
	instanceExtraTlsDAL  dal.InstanceExtraTlsDAL
	instanceExtraNodeDAL dal.InstanceExtraNodeDAL

	lcSvc           audit.LogCollectorInterface
	billSvc         billing.BillingService
	publishEventSvc com.PublishEventService
}

func (a *AuditLifecycleActor) GetState() []byte {
	state, err := json.Marshal(a.state)
	if err != nil {
		log.Error(context.Background(), "json marshal error:%s, ", err)
		return nil
	}
	return state
}

func newTaskState(bytes []byte) *AuditLifeState {
	ts := &AuditLifeState{}
	if len(bytes) == 0 {
		return ts
	}
	err := json.Unmarshal(bytes, ts)
	if err != nil {
		log.Error(context.Background(), "json unmarshal error:%s, state:%s", err, string(bytes))
		return ts
	}
	return ts
}

func (a *AuditLifecycleActor) RunStep(ctx types.Context, step IdempotentStep) {
	if step == nil {
		b := &BaseStep{}
		b.Trace(ctx, a, "UnknownStep", "RunStep over")
		log.Info(ctx, "\n%s", DrawStepTrace(a.state.LifeState.HistoryStepsTrace))
		return
	}
	defer func() {
		if err := recover(); err != nil {
			a.handleUnexpectedError(ctx, step, fmt.Sprint(err))
		}
	}()
	var err error
	a.state.LifeState.CurrentStepName = step.GetStepName()
	err = CheckLoopback(ctx, step.GetStepName(), step.MaxExecRetry(), a.state.LifeState.HistoryStepsTrace)
	if err != nil {
		log.Warn(ctx, "CheckLoopback error:%s, step name:%s", err, step.GetStepName())
		a.handleUnexpectedError(ctx, step, err.Error())
		return
	}
	maxExecRetry := step.MaxExecRetry()
	var jump bool
	for i := 0; i < maxExecRetry; i++ {
		jump, err = step.PreCheck(ctx, a)
		if err != nil {
			step.Trace(ctx, a, step.GetStepName(), fmt.Sprintf("PreCheck err:%s, retry times:%d", err.Error(), i))
			time.Sleep(step.ErrorWait())
			continue
		}
		step.Trace(ctx, a, step.GetStepName(), "PreCheck success")
		if jump {
			step.Trace(ctx, a, step.GetStepName(), "ProtectExec jump")
			break
		}
		err = step.ProtectExec(ctx, a)
		if err != nil {
			step.Trace(ctx, a, step.GetStepName(), fmt.Sprintf("ProtectExec err:%s, retry times:%d", err.Error(), i))
			time.Sleep(step.ErrorWait())
			continue
		}
		step.Trace(ctx, a, step.GetStepName(), fmt.Sprintf("ProtectExec success"))
		break
	}

	if err != nil {
		if ErrorFlowStep[a.state.LifeState.CurrentStepName] || a.state.FlowErrorHandleStep == "" {
			// 处理没有GC流程的报错、GC流程出现的报错
			a.handleUnexpectedError(ctx, step, err.Error())
			return
		} else {
			// 处理正常流程时报错进入GC错误处理流程
			step.Trace(ctx, a, step.GetStepName(), fmt.Sprintf("Enter GC Flow, error:%s", err))
			log.Warn(ctx, "%s exec err:%s", step.GetStepName(), err)
			a.state.LifeState.CurrentStepName = a.state.FlowErrorHandleStep
			ctx.Send(ctx.Self(), &shared.RunFlowRequest{})
			return
		}
	}
	nextStep := step.NextStep(ctx, a)
	if nextStep != nil {
		log.Info(ctx, "Current Step: %s, Next Step: %s", step.GetStepName(), nextStep.GetStepName())
		a.state.LifeState.CurrentStepName = nextStep.GetStepName()
		ctx.Send(ctx.Self(), &shared.RunFlowRequest{})
		return
	} else {
		step.Trace(ctx, a, "FinishStep", "RunStep over")
		log.Info(ctx, "%s", libutils.Show(a.state))
		log.Info(ctx, "\n%s", DrawStepTrace(a.state.LifeState.HistoryStepsTrace))
		a.state.LifeState.CurrentStepName = ""
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}

}

func (a *AuditLifecycleActor) handleUnexpectedError(ctx types.Context, step IdempotentStep, err string) {
	stack := string(debug.Stack())
	log.Error(ctx, "%s ErrorFlowStep err:%s, Stack:%s", step.GetStepName(), err, stack)
	step.Trace(ctx, a, "FinishStep", fmt.Sprintf("RunStep over with error: %s", err))
	log.Info(ctx, "%s", libutils.Show(a.state))
	log.Info(ctx, "\n%s", DrawStepTrace(a.state.LifeState.HistoryStepsTrace))
	a.state.LifeState.CurrentStepName = ""
	ctx.Send(ctx.Self(), &proto.SuicideMessage{})
}

func CheckLoopback(ctx types.Context, stepName string, stepMaxRety int, history [][]string) error {
	i := len(history) - 1
	for ; i >= 0; i-- {
		if stepName != history[i][0] {
			break
		}
	}
	if (len(history) - i - 1) >= stepMaxRety {
		return errors.New("max exec retry loopback")
	}
	for ; i >= 0; i-- {
		if stepName == history[i][0] {
			return errors.New("step loopback")
		}
	}
	return nil
}

// Process 处理订单的完整周期，具备恢复能力
// actor name is "$TenantId|$InstanceId|$OrderId|$MsgID"
func (a *AuditLifecycleActor) Process(ctx types.Context) {
	a.initCtx(ctx)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		log.Info(ctx, "actor started.")
		ctx.Send(ctx.Self(), &shared.RunFlowRequest{})
	case *shared.CreateAuditFlowRequest:
		log.Info(ctx, "CreateAuditFlowRequest:%s", libutils.Show(msg))
		ctx.Respond(&shared.OK{})
		if a.handleCreateAuditFlowReq(ctx, msg) {
			return
		}
		ctx.Send(ctx.Self(), &shared.RunFlowRequest{})
	case *shared.DeleteAuditFlowRequest:
		log.Info(ctx, "DeleteAuditFlowRequest:%s", libutils.Show(msg))
		ctx.Respond(&shared.OK{})
		if a.handleDeleteAuditFlowReq(ctx, msg) {
			return
		}
		ctx.Send(ctx.Self(), &shared.RunFlowRequest{})
	case *shared.UpgradeInstanceFlowRequest:
		log.Info(ctx, "UpgradeFullSqlFlowRequest:%s", libutils.Show(msg))
		ctx.Respond(&shared.OK{})
		if a.handleUpgradeInstanceFlowReq(ctx, msg) {
			return
		}
		ctx.Send(ctx.Self(), &shared.RunFlowRequest{})
	case *shared.DeleteResourceFlowRequest:
		log.Info(ctx, "DeleteAuditFlowRequest:%s", libutils.Show(msg))
		ctx.Respond(&shared.OK{})
		if a.handleDeleteResourceFlowReq(ctx, msg) {
			return
		}
		ctx.Send(ctx.Self(), &shared.RunFlowRequest{})
	case *shared.CreateInnerAuditFlowRequest:
		log.Info(ctx, "CreateInnerAuditFlowRequest:%s", libutils.Show(msg))
		ctx.Respond(&shared.OK{})
		if a.handleCreateInnerAuditFlowReq(ctx, msg) {
			return
		}
		ctx.Send(ctx.Self(), &shared.RunFlowRequest{})
	case *shared.DeleteInnerAuditFlowRequest:
		log.Info(ctx, "DeleteInnerAuditFlowRequest:%s", libutils.Show(msg))
		ctx.Respond(&shared.OK{})
		if a.handleDeleteInnerAuditFlowReq(ctx, msg) {
			return
		}
		ctx.Send(ctx.Self(), &shared.RunFlowRequest{})
	case *shared.RunFlowRequest:
		if a.state.LifeState.CurrentStepName != "" {
			if _, ok := a.idempotentSteps[a.state.LifeState.CurrentStepName]; !ok {
				log.Error(ctx, "Step not register:%s", a.state.LifeState.CurrentStepName)
			} else {
				a.RunStep(ctx, a.idempotentSteps[a.state.LifeState.CurrentStepName])
			}
		} else {
			log.Info(ctx, "current step name is nil")
		}
	case *actor.Stopped:
		log.Info(ctx, "taskActor %s stop", ctx.GetName())
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}
}

func (a *AuditLifecycleActor) handleCreateAuditFlowReq(ctx types.Context, msg *shared.CreateAuditFlowRequest) bool {
	if a.state.LifeState.CurrentStepName != "" {
		log.Warn(ctx, "exist flow in running")
		return true
	}

	message := &volctrade.TradeMessage{}
	libutils.MustUnmarshal([]byte(msg.Msg), message)
	a.state.Message = message
	a.state.FlowType = CreateAuditFlow
	a.state.LogProductType, _ = model.LogProductTypeFromString(msg.ProductType)

	if msg.ProductType == model.LogProductType_FullSqlLog.String() {
		// 设置流程GC入口
		a.state.FlowErrorHandleStep = FullSqlErrorStep
		// 设置创建全量sql入口
		a.state.LifeState.CurrentStepName = CreateFullSqlFlowEnterStep
		return false
	} else if msg.ProductType == model.LogProductType_AuditLog.String() {
		a.state.FlowErrorHandleStep = ErrorEnterStep
		a.state.LifeState.CurrentStepName = CreateAuditFlowEnterStep
		return false
	}
	log.Error(ctx, "unknown ")
	return true
}

func (a *AuditLifecycleActor) handleDeleteAuditFlowReq(ctx types.Context, msg *shared.DeleteAuditFlowRequest) bool {
	if a.state.LifeState.CurrentStepName != "" {
		log.Warn(ctx, "exist flow in running")
		return true
	}
	message := &volctrade.TradeMessage{}
	libutils.MustUnmarshal([]byte(msg.Msg), message)
	a.state.Message = message
	a.state.FlowType = DeleteAuditFlow
	a.state.LogProductType, _ = model.LogProductTypeFromString(msg.ProductType)
	CloseType, err := model.CloseTypeFromString(msg.CloseType)
	if err == nil {
		a.state.CloseType = &CloseType
	}
	if msg.ProductType == model.LogProductType_FullSqlLog.String() {
		a.state.LifeState.CurrentStepName = DeleteFullSqlFlowEnterStep
		return false
	}
	a.state.LifeState.CurrentStepName = DeleteAuditFlowEnterStep
	return false
}

func (a *AuditLifecycleActor) handleCreateInnerAuditFlowReq(ctx types.Context, msg *shared.CreateInnerAuditFlowRequest) bool {
	if a.state.LifeState.CurrentStepName != "" {
		log.Warn(ctx, "exist flow in running")
		return true
	}
	a.state.TlsTopicTTL = int64(msg.TTL)
	a.state.Nodes = msg.Nodes
	a.state.SimplingRate = msg.SimplingRate
	a.state.FlowType = CreateAuditFlow
	a.state.LogProductType, _ = model.LogProductTypeFromString(msg.ProductType)

	if msg.ProductType == model.LogProductType_FullSqlLog.String() {
		// TODO 设置流程GC入口
		a.state.FlowErrorHandleStep = ""
		// 设置创建全量sql入口
		a.state.LifeState.CurrentStepName = CreateFullSqlInnerFlowEnterStep
		return false
	}
	log.Error(ctx, "unknown ")
	return true
}

func (a *AuditLifecycleActor) handleDeleteInnerAuditFlowReq(ctx types.Context, msg *shared.DeleteInnerAuditFlowRequest) bool {
	if a.state.LifeState.CurrentStepName != "" {
		log.Warn(ctx, "exist flow in running")
		return true
	}

	a.state.FlowType = DeleteAuditFlow
	a.state.LogProductType, _ = model.LogProductTypeFromString(msg.ProductType)
	CloseType, err := model.CloseTypeFromString(msg.CloseType)
	if err == nil {
		a.state.CloseType = &CloseType
	}
	if msg.ProductType == model.LogProductType_FullSqlLog.String() {
		// TODO
		a.state.LifeState.CurrentStepName = DeleteFullSqlInnerFlowEnterStep
		return false
	}
	return false
}

func (a *AuditLifecycleActor) handleDeleteResourceFlowReq(ctx types.Context, msg *shared.DeleteResourceFlowRequest) bool {
	if a.state.LifeState.CurrentStepName != "" {
		log.Warn(ctx, "exist flow in running")
		return true
	}

	a.state.FlowType = DeleteAuditFlow
	a.state.LogProductType, _ = model.LogProductTypeFromString(msg.ProductType)
	CloseType, err := model.CloseTypeFromString(msg.CloseType)
	if err == nil {
		a.state.CloseType = &CloseType
	}
	if msg.ProductType == model.LogProductType_FullSqlLog.String() {
		a.state.LifeState.CurrentStepName = DeleteFullSqlFlowEnterStep
		return false
	}
	return true
}

func (a *AuditLifecycleActor) handleUpgradeInstanceFlowReq(ctx types.Context, msg *shared.UpgradeInstanceFlowRequest) bool {
	if a.state.LifeState.CurrentStepName != "" {
		log.Warn(ctx, "exist flow in running")
		return true
	}
	a.state.FlowType = UpgradeInstanceFlow
	a.state.LogProductType, _ = model.LogProductTypeFromString(msg.ProductType)
	a.state.DSType = model.DSType(msg.DSType)
	if msg.ProductType == model.LogProductType_FullSqlLog.String() {
		a.state.LifeState.CurrentStepName = UpgradeFullSqlFlowEnterStep
		return false
	}
	return true
}
