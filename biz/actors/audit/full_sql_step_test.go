package audit

import (
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"context"
	tls_sdk "github.com/volcengine/volc-sdk-golang/service/tls"
	"testing"

	local_dal "code.byted.org/infcs/dbw-mgr/biz/test/dal"
	mocks_def "code.byted.org/infcs/dbw-mgr/biz/test/service"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"

	. "github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"

	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/billing"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	service_config "code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/full_sql/zkconfig"
	"code.byted.org/infcs/dbw-mgr/biz/service/i18n"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/tenant"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/ds-lib/common/log"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/lib-mgr-common/volc/trade"
	"encoding/json"
	"errors"
	"github.com/stretchr/testify/suite"
	"strings"
)

type FullSqlStepTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *FullSqlStepTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}
func (suite *FullSqlStepTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}
func TestFullSqlStepSuite(t *testing.T) {
	suite.Run(t, new(FullSqlStepTestSuite))
}

func (suite *FullSqlStepTestSuite) TestNewFullSqlStep() {
	PatchConvey("", suite.T(), func() {
		ctx := context.TODO()
		ctx = context.WithValue(ctx, "biz-context", &fwctx.BizContext{TenantID: "2100000746"})

		local := &local_dal.LocalAudit{}
		local.Init(ctx, suite.ctrl)

		def := mocks_def.DefaultMock{}
		def.Init(suite.ctrl)
		So(ctx, ShouldNotBeNil)
	})
}

func TestConfigConsumerPipelineStep_getTlsKafkaEndpoint(t *testing.T) {
	type fields struct {
		BaseStep BaseStep
	}
	type args struct {
		tlsConsumerEndpoint string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			name: "",
			fields: fields{
				BaseStep: BaseStep{},
			},
			args: args{
				tlsConsumerEndpoint: "http://tls-cn-chongqing-sdv-inner.ivolces.com",
			},
			want: "tls-cn-chongqing-sdv-inner.ivolces.com:9093",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getTlsKafkaEndpoint(tt.args.tlsConsumerEndpoint); got != tt.want {
				t.Errorf("getTlsKafkaEndpoint() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCleanCloseIndexKey(t *testing.T) {
	type args struct {
		value  *[]tls_sdk.KeyValueInfo
		dsType model.DSType
		flag   model.CapabilitiesFlag
	}
	redisIndexReq := &tls_sdk.CreateIndexRequest{
		TopicID: "topic-xxx",
		KeyValue: &[]tls_sdk.KeyValueInfo{
			{
				Key: "time",
				Value: tls_sdk.Value{
					ValueType:      "long",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "latency",
				Value: tls_sdk.Value{
					ValueType:      "long",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "type",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "account",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "cmd",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "!@#%^&*()-_=\\\"', <>/?|;: \\n\\t\\r[]{}",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        false,
				},
			},
			{
				Key: "db",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "ip",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			// 预留字段
			//{
			//	Key: "__pod_name__",
			//	Value: tls_sdk.Value{
			//		ValueType:      "text",
			//		Delimiter:      "",
			//		CasSensitive:   false,
			//		IncludeChinese: false,
			//		SQLFlag:        true,
			//	},
			//},
			// tag
			{
				Key: "__tag__ResourceId__",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
			{
				Key: "__tag__component__",
				Value: tls_sdk.Value{
					ValueType:      "text",
					Delimiter:      "",
					CasSensitive:   false,
					IncludeChinese: false,
					SQLFlag:        true,
				},
			},
		},
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "",
			args: args{
				value:  redisIndexReq.KeyValue,
				dsType: model.DSType_Redis,
				flag:   model.CapabilitiesFlag_QueryDetailKeyword,
			},
			want: "cmd",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			KeyValue := CleanCloseIndexKey(tt.args.value, tt.args.dsType, tt.args.flag)
			for _, info := range *KeyValue {
				if info.Key == tt.want {
					t.Error("clean failed")
				}
			}
		})
	}
}

// Mock_FullSqlDAL_PrepareFullSqlInstanceStateStep mocks the dal.FullSqlDAL interface.
type Mock_FullSqlDAL_PrepareFullSqlInstanceStateStep struct {
	dal.FullSqlDAL
}

// Mock_ConfigProvider_PrepareFullSqlInstanceStateStep mocks the service_config.ConfigProvider interface.
type Mock_ConfigProvider_PrepareFullSqlInstanceStateStep struct {
	service_config.ConfigProvider
}

// Mock_DataSourceService_PrepareFullSqlInstanceStateStep mocks the datasource.DataSourceService interface.
type Mock_DataSourceService_PrepareFullSqlInstanceStateStep struct {
	datasource.DataSourceService
}

// Mock_Context_PrepareFullSqlInstanceStateStep mocks the types.Context interface.
type Mock_Context_PrepareFullSqlInstanceStateStep struct {
	types.Context
}

func Test_PrepareFullSqlInstanceStateStep_ProtectExec_BitsUTGen(t *testing.T) {
	PatchConvey("Test_PrepareFullSqlInstanceStateStep_ProtectExec", t, func() {
		// Data construction
		step := PrepareFullSqlInstanceStateStep{}
		mockCtx := &Mock_Context_PrepareFullSqlInstanceStateStep{}
		orderExtra := &billing.OrderExtra{
			TlsTtl:     90,
			CustomTags: map[string]string{"tag1": "val1"},
		}
		attributeBytes, _ := json.Marshal(orderExtra)

		actor := &AuditLifecycleActor{
			state: &AuditLifeState{
				InstanceId: "test-instance",
				OrderId:    "test-order",
				TenantId:   "**********",
				Message: &volctrade.TradeMessage{
					Attribute: string(attributeBytes),
				},
			},
			fullSqlInstDAL: &Mock_FullSqlDAL_PrepareFullSqlInstanceStateStep{},
			conf:           &Mock_ConfigProvider_PrepareFullSqlInstanceStateStep{},
			source:         &Mock_DataSourceService_PrepareFullSqlInstanceStateStep{},
		}

		mockAuditRecord := &dao.AuditTls{
			InstanceID:             "test-instance",
			TenantID:               "**********",
			Status:                 int32(model.AuditStatus(0)),
			FollowInstanceID:       "follow-instance",
			DbType:                 "MySQL",
			DeployType:             "InstanceFront",
			ProductType:            "FullSqlLog",
			StorageSqlTypes:        "select,update",
			Region:                 "cn-beijing",
			EnableFunctions:        "SQLAuditDetail,SQLInsight",
			SqlDesensitizationType: "None",
			RateSimpling:           100,
		}

		mockConfig := &config.Config{
			FullSqlInnerAccountId: "test_account_id",
		}

		PatchConvey("成功场景 - 所有依赖正常", func() {
			// 场景描述：
			// 当所有依赖都正常返回无异常情况下，验证目标函数能正确执行，并填充actor.state
			// 数据构造：
			// - actor.state 包含基本的 InstanceId, OrderId, TenantId 和 Message
			// - 依赖的 mock 对象都已初始化
			// 逻辑链路：
			// 1. Mock libutils.Unmarshal 成功解析订单信息
			// 2. Mock fullSqlInstDAL.GetByIDWithoutTenant 成功返回审计实例信息
			// 3. Mock model.*FromString 系列函数成功转换字符串为枚举
			// 4. Mock config provider 成功返回配置
			// 5. Mock datasource service 成功返回正在运行的实例详情
			// 6. 验证函数返回 nil 错误
			// 7. 验证 actor.state 的各个字段被正确填充

			// Mocks
			Mock(libutils.Unmarshal).To(func(data []byte, v interface{}) error {
				if oe, ok := v.(*billing.OrderExtra); ok {
					// Manually populate to ensure correctness, bypassing potential json.Unmarshal issues in test.
					oe.TlsTtl = 90
					oe.CustomTags = map[string]string{"tag1": "val1"}
				}
				return nil
			}).Build()
			Mock((*Mock_FullSqlDAL_PrepareFullSqlInstanceStateStep).GetByIDWithoutTenant).Return(mockAuditRecord, nil).Build()
			Mock(utils.IsByteCloud).Return(false).Build()
			Mock((*Mock_Context_PrepareFullSqlInstanceStateStep).WithValue).Return().Build()
			Mock(model.DSTypeFromString).Return(model.DSType_MySQL, nil).Build()
			Mock(model.LabelTypeFromString).Return(model.LabelType_InstanceFront, nil).Build()
			Mock(model.LogProductTypeFromString).Return(model.LogProductType_FullSqlLog, nil).Build()
			Mock(model.FullSqlFuncNameFromString).When(func(s string) bool { return s == "SQLAuditDetail" }).Return(model.FullSqlFuncName_SQLAuditDetail, nil).When(func(s string) bool { return s == "SQLInsight" }).Return(model.FullSqlFuncName_SQLInsight, nil).Build()
			Mock(model.SqlDesensitizationTypeFromString).Return(model.SqlDesensitizationType_None, nil).Build()
			Mock((*Mock_ConfigProvider_PrepareFullSqlInstanceStateStep).Get).Return(mockConfig).Build()
			Mock(i18n.GetSiteName).Return("VolcanoEngine").Build()
			Mock((*config.Config).GetTLSRegion).Return("cn-beijing", nil).Build()
			Mock((*config.Config).GetTlsRegionEndpoint).Return("https://tls-cn-beijing-inner.ivolces.com").Build()
			Mock((*Mock_DataSourceService_PrepareFullSqlInstanceStateStep).DescribeDBInstanceDetail).Return(&datasource.DescribeDBInstanceDetailResp{
				InstanceStatus: model.InstanceStatus_Running.String(),
			}, nil).Build()

			// 调用
			err := step.ProtectExec(mockCtx, actor)

			// 断言
			So(err, ShouldBeNil)
			So(actor.state.TlsTopicTTL, ShouldEqual, 90)
			So(actor.state.InitTags, ShouldResemble, map[string]string{"tag1": "val1"})
			So(actor.state.InstanceStatus, ShouldEqual, model.AuditStatus(0))
			So(actor.state.FollowInstanceId, ShouldEqual, "follow-instance")
			So(actor.state.DSType, ShouldEqual, model.DSType_MySQL)
			So(actor.state.EnableFunctions[model.FullSqlFuncName_SQLAuditDetail], ShouldBeTrue)
			So(actor.state.EnableFunctions[model.FullSqlFuncName_SQLInsight], ShouldBeTrue)
			So(actor.state.TlsRegion, ShouldEqual, "cn-beijing")
			So(actor.state.TlsEndpoint, ShouldEqual, "https://tls-cn-beijing-inner.ivolces.com")
			So(actor.state.SqlTemplateTls, ShouldNotBeNil)
			So(actor.state.SqlTemplateTls.TlsDataType, ShouldEqual, model.StatisticDataType_FullSqlTemplateAggr)
		})

		PatchConvey("成功场景 - 运维租户", func() {
			// 场景描述：
			// 当审计实例为运维租户("0"或"1")且环境不为ByteCloud时，验证 actor.state 的租户信息被正确更新
			// 数据构造：
			// - actor.state 的 TenantId 设置为与DB中不一致，但DB中为 "1"
			// 逻辑链路：
			// 1. Mock GetByIDWithoutTenant 返回 TenantID 为 "1" 的记录
			// 2. Mock IsByteCloud 返回 false
			// 3. 其他依赖正常 Mock
			// 4. 验证函数返回 nil 错误
			// 5. 验证 actor.state.TenantId 被更新为 "1"
			// 6. 验证 actor.state.IsOpsInstance 被设置为 true

			actor.state.TenantId = "user-tenant"
			mockAuditRecord.TenantID = "1"

			Mock(libutils.Unmarshal).To(func(data []byte, v interface{}) error {
				if oe, ok := v.(*billing.OrderExtra); ok {
					return json.Unmarshal(data, oe)
				}
				return errors.New("unmarshal target type mismatch")
			}).Build()
			Mock((*Mock_FullSqlDAL_PrepareFullSqlInstanceStateStep).GetByIDWithoutTenant).Return(mockAuditRecord, nil).Build()
			Mock(utils.IsByteCloud).Return(false).Build()
			Mock((*Mock_Context_PrepareFullSqlInstanceStateStep).WithValue).Return().Build()
			Mock(model.DSTypeFromString).Return(model.DSType_MySQL, nil).Build()
			Mock(model.LabelTypeFromString).Return(model.LabelType_InstanceFront, nil).Build()
			Mock(model.LogProductTypeFromString).Return(model.LogProductType_FullSqlLog, nil).Build()
			Mock(ConvEnableFuncNames).Return(map[model.FullSqlFuncName]bool{}, nil).Build()
			Mock(model.SqlDesensitizationTypeFromString).Return(model.SqlDesensitizationType_None, nil).Build()
			Mock((*Mock_ConfigProvider_PrepareFullSqlInstanceStateStep).Get).Return(mockConfig).Build()
			Mock((*config.Config).GetTLSRegion).Return("cn-beijing", nil).Build()
			Mock((*config.Config).GetTlsRegionEndpoint).Return("endpoint").Build()
			Mock((*Mock_DataSourceService_PrepareFullSqlInstanceStateStep).DescribeDBInstanceDetail).Return(&datasource.DescribeDBInstanceDetailResp{
				InstanceStatus: model.InstanceStatus_Running.String(),
			}, nil).Build()

			err := step.ProtectExec(mockCtx, actor)

			So(err, ShouldBeNil)
			So(actor.state.TenantId, ShouldEqual, "1")
			So(actor.state.IsOpsInstance, ShouldBeTrue)
		})

		PatchConvey("失败场景 - 依赖返回异常", func() {
			// Mock Unmarshal to succeed for all sub-tests
			Mock(libutils.Unmarshal).To(func(data []byte, v interface{}) error { return nil }).Build()
			Mock(libutils.Show).Return("").Build()
			Mock((*Mock_Context_PrepareFullSqlInstanceStateStep).WithValue).Return().Build()

			PatchConvey("GetByIDWithoutTenant 失败", func() {
				// 场景描述：当数据库查询审计实例失败时，函数应返回错误
				Mock((*Mock_FullSqlDAL_PrepareFullSqlInstanceStateStep).GetByIDWithoutTenant).Return(nil, errors.New("db error")).Build()

				err := step.ProtectExec(mockCtx, actor)
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "db error")
			})

			PatchConvey("租户ID不匹配", func() {
				// 场景描述：当 actor.state 中的租户ID与数据库中查出的不匹配时，函数应返回错误
				mockAuditRecord.TenantID = "another-tenant"
				Mock((*Mock_FullSqlDAL_PrepareFullSqlInstanceStateStep).GetByIDWithoutTenant).Return(mockAuditRecord, nil).Build()

				err := step.ProtectExec(mockCtx, actor)
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "tenantId not match")
			})

			PatchConvey("DSTypeFromString 失败", func() {
				// 场景描述：当从字符串转换DSType失败时，函数应返回错误
				Mock((*Mock_FullSqlDAL_PrepareFullSqlInstanceStateStep).GetByIDWithoutTenant).Return(mockAuditRecord, nil).Build()
				Mock(model.DSTypeFromString).Return(model.DSType(0), errors.New("invalid ds type")).Build()

				err := step.ProtectExec(mockCtx, actor)
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "invalid ds type")
			})

			PatchConvey("GetTLSRegion 失败", func() {
				// 场景描述：当获取TLS地域信息失败时，函数应返回错误
				Mock((*Mock_FullSqlDAL_PrepareFullSqlInstanceStateStep).GetByIDWithoutTenant).Return(mockAuditRecord, nil).Build()
				Mock(model.DSTypeFromString).Return(model.DSType_MySQL, nil).Build()
				Mock(model.LabelTypeFromString).Return(model.LabelType_InstanceFront, nil).Build()
				Mock(model.LogProductTypeFromString).Return(model.LogProductType_FullSqlLog, nil).Build()
				Mock(ConvEnableFuncNames).Return(nil, nil).Build()
				Mock(model.SqlDesensitizationTypeFromString).Return(model.SqlDesensitizationType_None, nil).Build()
				Mock((*Mock_ConfigProvider_PrepareFullSqlInstanceStateStep).Get).Return(mockConfig).Build()
				Mock(i18n.GetSiteName).Return("VolcanoEngine").Build()
				Mock((*config.Config).GetTLSRegion).Return("", errors.New("get tls region failed")).Build()

				err := step.ProtectExec(mockCtx, actor)
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "get tls region failed")
			})

			PatchConvey("DescribeDBInstanceDetail 失败", func() {
				// 场景描述：当调用数据源服务查询实例详情失败时，函数应返回错误
				Mock((*Mock_FullSqlDAL_PrepareFullSqlInstanceStateStep).GetByIDWithoutTenant).Return(mockAuditRecord, nil).Build()
				Mock(model.DSTypeFromString).Return(model.DSType_MySQL, nil).Build()
				Mock(model.LabelTypeFromString).Return(model.LabelType_InstanceFront, nil).Build()
				Mock(model.LogProductTypeFromString).Return(model.LogProductType_FullSqlLog, nil).Build()
				Mock(ConvEnableFuncNames).Return(nil, nil).Build()
				Mock(model.SqlDesensitizationTypeFromString).Return(model.SqlDesensitizationType_None, nil).Build()
				Mock((*Mock_ConfigProvider_PrepareFullSqlInstanceStateStep).Get).Return(mockConfig).Build()
				Mock((*config.Config).GetTLSRegion).Return("cn-beijing", nil).Build()
				Mock((*config.Config).GetTlsRegionEndpoint).Return("endpoint").Build()
				Mock((*Mock_DataSourceService_PrepareFullSqlInstanceStateStep).DescribeDBInstanceDetail).Return(nil, errors.New("describe detail failed")).Build()

				err := step.ProtectExec(mockCtx, actor)
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldContainSubstring, "describe detail failed")
			})

			PatchConvey("数据源实例状态非 Running", func() {
				// 场景描述：当数据源实例状态不是 Running 时，函数应返回错误 (根据当前实现，实际返回nil)
				Mock((*Mock_FullSqlDAL_PrepareFullSqlInstanceStateStep).GetByIDWithoutTenant).Return(mockAuditRecord, nil).Build()
				Mock(model.DSTypeFromString).Return(model.DSType_MySQL, nil).Build()
				Mock(model.LabelTypeFromString).Return(model.LabelType_InstanceFront, nil).Build()
				Mock(model.LogProductTypeFromString).Return(model.LogProductType_FullSqlLog, nil).Build()
				Mock(ConvEnableFuncNames).Return(nil, nil).Build()
				Mock(model.SqlDesensitizationTypeFromString).Return(model.SqlDesensitizationType_None, nil).Build()
				Mock((*Mock_ConfigProvider_PrepareFullSqlInstanceStateStep).Get).Return(mockConfig).Build()
				Mock((*config.Config).GetTLSRegion).Return("cn-beijing", nil).Build()
				Mock((*config.Config).GetTlsRegionEndpoint).Return("endpoint").Build()
				Mock((*Mock_DataSourceService_PrepareFullSqlInstanceStateStep).DescribeDBInstanceDetail).Return(&datasource.DescribeDBInstanceDetailResp{
					InstanceStatus: model.InstanceStatus_Creating.String(),
				}, nil).Build()

				err := step.ProtectExec(mockCtx, actor)
				// SUT有bug, 此处会返回nil, 测试用例需遵循SUT的实际行为
				So(err, ShouldBeNil)
			})
		})
	})
}

// 模拟 context 的 Value 方法，以便于 BizContext 的传递
func (m *Mock_Context_PrepareFullSqlInstanceStateStep) Value(key interface{}) interface{} {
	if key == "biz-context" {
		return &fwctx.BizContext{
			TenantID: "test-tenant",
			Extra:    make(map[string]string),
		}
	}
	return nil
}
func (m *Mock_Context_PrepareFullSqlInstanceStateStep) WithValue(key, val interface{}) {}

// Mock DataSourceService 接口
func (m *Mock_DataSourceService_PrepareFullSqlInstanceStateStep) DescribeDBInstanceDetail(ctx context.Context, req *datasource.DescribeDBInstanceDetailReq) (*datasource.DescribeDBInstanceDetailResp, error) {
	return nil, nil
}
func (m *Mock_DataSourceService_PrepareFullSqlInstanceStateStep) Type() shared.DataSourceType {
	return 0
}

// Mock ConfigProvider 接口
func (m *Mock_ConfigProvider_PrepareFullSqlInstanceStateStep) Get(ctx context.Context) *config.Config {
	return nil
}
func (m *Mock_ConfigProvider_PrepareFullSqlInstanceStateStep) Update(ctx context.Context, cnf *config.Config) error {
	return nil
}
func (m *Mock_ConfigProvider_PrepareFullSqlInstanceStateStep) AddUpdateHook(f func(*shared.ConfigUpdated)) {
}
func (m *Mock_ConfigProvider_PrepareFullSqlInstanceStateStep) Refresh(ctx context.Context) {}

// Mock FullSqlDAL 接口
func (m *Mock_FullSqlDAL_PrepareFullSqlInstanceStateStep) GetByIDWithoutTenant(ctx context.Context, instanceID string) (*dao.AuditTls, error) {
	return nil, nil
}

// MockFullSqlDAL is a mock for dal.FullSqlDAL
type MockFullSqlDAL struct {
	dal.FullSqlDAL
}

func (m *MockFullSqlDAL) GetByIDWithoutTenant(ctx context.Context, instanceID string) (*dao.AuditTls, error) {
	return nil, nil // This method will be mocked by mockey
}

// MockConfigProvider is a mock for service_config.ConfigProvider
type MockConfigProvider struct {
	service_config.ConfigProvider
}

func (m *MockConfigProvider) Get(ctx context.Context) *config.Config {
	return nil // This method will be mocked by mockey
}

// MockContext is a mock for types.Context
type MockContext struct {
	types.Context
}

func (m *MockContext) WithValue(key, val interface{}) {
	// This method will be mocked by mockey
}
func Test_PrepareDeleteFullSqlInstanceStateStep_ProtectExec_BitsUTGen(t *testing.T) {
	PatchConvey("Test_PrepareDeleteFullSqlInstanceStateStep_ProtectExec", t, func() {
		// 共享数据构造
		mockDal := &MockFullSqlDAL{}
		mockConfProvider := &MockConfigProvider{}
		mockCtx := &MockContext{}

		actor := &AuditLifecycleActor{
			state: &AuditLifeState{
				InstanceId: "test-instance-123",
				TenantId:   "test-tenant-456",
			},
			fullSqlInstDAL: mockDal,
			conf:           mockConfProvider,
		}
		p := PrepareDeleteFullSqlInstanceStateStep{}
		conf := &config.Config{FullSqlInnerAccountId: "inner-account-id"}

		PatchConvey("成功场景", func() {
			PatchConvey("普通租户场景", func() {
				// 场景描述：当所有依赖都正常返回，且租户为普通租户时，函数应正确执行并更新actor状态。
				// 数据构造：
				// - actor.state.TenantId 设置为普通租户ID "test-tenant-456"
				// - GetByIDWithoutTenant 返回的 audit.TenantID 与 actor.state.TenantId 匹配
				// 逻辑链路：
				// 1. Mock GetByIDWithoutTenant 成功返回匹配的租户信息
				// 2. Mock IsByteCloud 返回 false
				// 3. Mock DSTypeFromString 成功返回数据源类型
				// 4. Mock 配置服务(Get, GetTLSRegion, GetTlsRegionEndpoint) 和 i18n 服务(GetSiteName) 均成功返回
				// 5. 调用目标函数 ProtectExec
				// 6. 断言函数返回 nil，且 actor.state 中的各个字段被正确初始化

				// 数据构造
				auditFromDB := &dao.AuditTls{
					TenantID:         "test-tenant-456",
					Status:           1,
					FollowInstanceID: "follow-instance-789",
					DbType:           "MySQL",
					Region:           "cn-beijing",
					TlsId:            1001,
				}

				// Mock
				Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
				Mock((*MockFullSqlDAL).GetByIDWithoutTenant).Return(auditFromDB, nil).Build()
				Mock(utils.IsByteCloud).Return(false).Build()
				Mock((*MockContext).WithValue).To(func(_ *MockContext, key, val interface{}) {}).Build()
				Mock(model.DSTypeFromString).Return(model.DSType_MySQL, nil).Build()
				Mock((*MockConfigProvider).Get).Return(conf).Build()
				Mock(i18n.GetSiteName).Return("VolcanoEngine").Build()
				Mock((*config.Config).GetTLSRegion).Return("cn-beijing", nil).Build()
				Mock((*config.Config).GetTlsRegionEndpoint).Return("https://tls-cn-beijing.volces.com").Build()
				Mock(libutils.Show).Return("mocked state string").Build()

				// 调用
				err := p.ProtectExec(mockCtx, actor)

				// 断言
				So(err, ShouldBeNil)
				So(actor.state.InstanceStatus, ShouldEqual, model.AuditStatus(1))
				So(actor.state.FollowInstanceId, ShouldEqual, "follow-instance-789")
				So(actor.state.DSType, ShouldEqual, model.DSType_MySQL)
				So(actor.state.Region, ShouldEqual, "cn-beijing")
				So(actor.state.TlsId, ShouldEqual, 1001)
				So(actor.state.TlsRegion, ShouldEqual, "cn-beijing")
				So(actor.state.TlsEndpoint, ShouldEqual, "https://tls-cn-beijing.volces.com")
				So(actor.state.IsOpsInstance, ShouldBeFalse)
			})

			PatchConvey("运维租户场景", func() {
				// 场景描述：当租户为运维租户("0"或"1")时，actor状态应被正确更新为运维实例。
				// 数据构造：
				// - GetByIDWithoutTenant 返回的 audit.TenantID 为 "1"
				// 逻辑链路：
				// 1. Mock GetByIDWithoutTenant 成功返回运维租户信息
				// 2. Mock IsByteCloud 返回 false，触发运维实例状态更新
				// 3. 其他依赖项均 Mock 成功
				// 4. 调用目标函数 ProtectExec
				// 5. 断言函数返回 nil，且 actor.state.IsOpsInstance 为 true，actor.state.TenantId 被更新为 "1"

				// 数据构造
				auditFromDB := &dao.AuditTls{TenantID: "1", DbType: "MySQL"}

				// Mock
				Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
				Mock((*MockFullSqlDAL).GetByIDWithoutTenant).Return(auditFromDB, nil).Build()
				Mock(utils.IsByteCloud).Return(false).Build()
				Mock((*MockContext).WithValue).To(func(_ *MockContext, key, val interface{}) {}).Build()
				Mock(model.DSTypeFromString).Return(model.DSType_MySQL, nil).Build()
				Mock((*MockConfigProvider).Get).Return(conf).Build()
				Mock(i18n.GetSiteName).Return("VolcanoEngine").Build()
				Mock((*config.Config).GetTLSRegion).Return("cn-beijing", nil).Build()
				Mock((*config.Config).GetTlsRegionEndpoint).Return("https://tls-cn-beijing.volces.com").Build()
				Mock(libutils.Show).Return("mocked state string").Build()

				// 调用
				err := p.ProtectExec(mockCtx, actor)

				// 断言
				So(err, ShouldBeNil)
				So(actor.state.IsOpsInstance, ShouldBeTrue)
				So(actor.state.TenantId, ShouldEqual, "1")
			})
		})

		PatchConvey("失败场景", func() {
			PatchConvey("GetByIDWithoutTenant返回错误", func() {
				// 场景描述：当数据库查询失败时，函数应将错误向上传递。
				// 数据构造：无
				// 逻辑链路：
				// 1. Mock GetByIDWithoutTenant 返回一个非 nil 的 error
				// 2. Mock log.Error 记录错误
				// 3. 调用目标函数 ProtectExec
				// 4. 断言函数返回的 error 与 Mock 的 error 一致

				// Mock
				expectedErr := errors.New("db query failed")
				Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
				Mock((*MockFullSqlDAL).GetByIDWithoutTenant).Return(nil, expectedErr).Build()
				Mock(log.Error).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()

				// 调用
				err := p.ProtectExec(mockCtx, actor)

				// 断言
				So(err, ShouldEqual, expectedErr)
			})

			PatchConvey("租户ID不匹配", func() {
				// 场景描述：当从数据库查出的租户ID与actor状态中的租户ID不匹配，且不是运维租户时，应返回租户不匹配错误。
				// 数据构造：
				// - GetByIDWithoutTenant 返回的 audit.TenantID 与 actor.state.TenantId 不匹配
				// 逻辑链路：
				// 1. Mock GetByIDWithoutTenant 返回一个租户ID不匹配的 audit 对象
				// 2. Mock log.Error 记录错误
				// 3. 调用目标函数 ProtectExec
				// 4. 断言函数返回 "tenantId not match" 错误

				// 数据构造
				auditFromDB := &dao.AuditTls{TenantID: "another-tenant-789"}

				// Mock
				Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
				Mock((*MockFullSqlDAL).GetByIDWithoutTenant).Return(auditFromDB, nil).Build()
				Mock(log.Error).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()

				// 调用
				err := p.ProtectExec(mockCtx, actor)

				// 断言
				So(err, ShouldNotBeNil)
				So(err.Error(), ShouldEqual, "tenantId not match")
			})

			PatchConvey("DSTypeFromString失败", func() {
				// 场景描述：当从数据库中获取的DbType无法被解析为有效的DSType时，函数应返回解析错误。
				// 数据构造：
				// - GetByIDWithoutTenant 返回的 audit.DbType 是一个无效值
				// 逻辑链路：
				// 1. Mock GetByIDWithoutTenant 成功返回
				// 2. Mock DSTypeFromString 返回一个非 nil 的 error
				// 3. Mock log.Error 记录错误
				// 4. 调用目标函数 ProtectExec
				// 5. 断言函数返回的 error 与 Mock 的 error 一致

				// 数据构造
				auditFromDB := &dao.AuditTls{TenantID: "test-tenant-456", DbType: "InvalidDBType"}
				expectedErr := errors.New("invalid ds type")

				// Mock
				Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
				Mock((*MockFullSqlDAL).GetByIDWithoutTenant).Return(auditFromDB, nil).Build()
				Mock((*MockContext).WithValue).To(func(_ *MockContext, key, val interface{}) {}).Build()
				Mock(model.DSTypeFromString).Return(model.DSType(0), expectedErr).Build()
				Mock(log.Error).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()

				// 调用
				err := p.ProtectExec(mockCtx, actor)

				// 断言
				So(err, ShouldEqual, expectedErr)
			})

			PatchConvey("GetTLSRegion失败", func() {
				// 场景描述：当从配置中获取TLS Region失败时，函数应返回相应错误。
				// 数据构造：无
				// 逻辑链路：
				// 1. 前置依赖均 Mock 成功
				// 2. Mock GetTLSRegion 返回一个非 nil 的 error
				// 3. Mock log.Error 记录错误
				// 4. 调用目标函数 ProtectExec
				// 5. 断言函数返回的 error 与 Mock 的 error 一致

				// 数据构造
				auditFromDB := &dao.AuditTls{TenantID: "test-tenant-456", DbType: "MySQL"}
				expectedErr := errors.New("get tls region failed")

				// Mock
				Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
				Mock((*MockFullSqlDAL).GetByIDWithoutTenant).Return(auditFromDB, nil).Build()
				Mock((*MockContext).WithValue).To(func(_ *MockContext, key, val interface{}) {}).Build()
				Mock(model.DSTypeFromString).Return(model.DSType_MySQL, nil).Build()
				Mock((*MockConfigProvider).Get).Return(conf).Build()
				Mock(i18n.GetSiteName).Return("VolcanoEngine").Build()
				Mock((*config.Config).GetTLSRegion).Return("", expectedErr).Build()
				Mock(log.Error).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()

				// 调用
				err := p.ProtectExec(mockCtx, actor)

				// 断言
				So(err, ShouldEqual, expectedErr)
			})
		})
	})
}

func Test_RemoveCollectionFingerprintStep_PreCheck_BitsUTGen(t *testing.T) {
	var mockCtx types.Context // Ctx is not used in the function, so a nil value is acceptable.
	step := RemoveCollectionFingerprintStep{}

	PatchConvey("Test_RemoveCollectionFingerprintStep_PreCheck", t, func() {
		PatchConvey("失败场景 - InstanceId为空", func() {
			// 场景描述：
			// 当传入的 AuditLifecycleActor 的 state 中的 InstanceId 为空字符串时，PreCheck 应该返回一个错误。
			// 数据构造：
			// - ctx: 一个 types.Context 实例，此处为 nil 因为未使用
			// - a: 一个 AuditLifecycleActor 指针，其 state.InstanceId 为 ""
			// 逻辑链路：
			// 1. 调用 PreCheck 方法
			// 2. 检查返回的布尔值，应为 false
			// 3. 检查返回的 error，应不为 nil
			// 4. 检查错误信息应包含 "instance id can not be empty"

			// 数据构造
			actor := &AuditLifecycleActor{
				state: &AuditLifeState{
					InstanceId: "",
				},
			}

			// 调用
			skip, err := step.PreCheck(mockCtx, actor)

			// 断言
			So(skip, ShouldBeFalse)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldEqual, "instance id can not be empty")
		})

		PatchConvey("成功场景 - InstanceId不为空", func() {
			// 场景描述：
			// 当传入的 AuditLifecycleActor 的 state 中的 InstanceId 不为空时，PreCheck 应该成功执行并返回 nil 错误。
			// 数据构造：
			// - ctx: 一个 types.Context 实例，此处为 nil 因为未使用
			// - a: 一个 AuditLifecycleActor 指针，其 state.InstanceId 为一个非空字符串
			// 逻辑链路：
			// 1. 调用 PreCheck 方法
			// 2. 检查返回的布尔值，应为 false
			// 3. 检查返回的 error，应为 nil

			// 数据构造
			actor := &AuditLifecycleActor{
				state: &AuditLifeState{
					InstanceId: "test-instance-id",
				},
			}

			// 调用
			skip, err := step.PreCheck(mockCtx, actor)

			// 断言
			So(skip, ShouldBeFalse)
			So(err, ShouldBeNil)
		})
	})
}

func Test_formatTopicName_BitsUTGen(t *testing.T) {
	PatchConvey("Test_formatTopicName", t, func() {
		PatchConvey("非运维实例 (isOpsInstance=false)", func() {
			PatchConvey("场景: 常规名称，包含大写和点号，处理后长度小于64", func() {
				// 场景描述：
				// 当isOpsInstance为false时，验证函数能正确地将输入字符串转为小写，并将点号替换为下划线。
				// 数据构造：
				// - name: "My.Topic.Name" (包含大写和点号)
				// - isOpsInstance: false
				// 逻辑链路：
				// 1. 调用 formatTopicName
				// 2. 预期结果为 "my_topic_name"，不包含 "_ops" 后缀，且未被截断。

				// 数据构造
				name := "My.Topic.Name"
				isOpsInstance := false

				// Mock
				// 无需 mock

				// 调用
				result := formatTopicName(name, isOpsInstance)

				// 断言
				So(result, ShouldEqual, "my_topic_name")
			})

			PatchConvey("场景: 名称处理后长度超过64，应被截断保留最后64个字符", func() {
				// 场景描述：
				// 当isOpsInstance为false时，且处理后的名称长度超过64个字符，验证函数会截断字符串，只保留最后64个字符。
				// 数据构造：
				// - name: 一个处理后长度会超过64个字符的长字符串
				// - isOpsInstance: false
				// 逻辑链路：
				// 1. 调用 formatTopicName
				// 2. 预期结果为原始处理结果的最后64个字符。

				// 数据构造
				longName := "this.is.a.very.long.topic.name.that.will.definitely.exceed.the.sixty.four.character.limit"
				isOpsInstance := false
				expectedRaw := "this_is_a_very_long_topic_name_that_will_definitely_exceed_the_sixty_four_character_limit"
				expected := expectedRaw[len(expectedRaw)-64:]

				// Mock
				// 无需 mock

				// 调用
				result := formatTopicName(longName, isOpsInstance)

				// 断言
				So(len(result), ShouldEqual, 64)
				So(result, ShouldEqual, expected)
			})

			PatchConvey("场景: 名称处理后长度等于64，不应被截断", func() {
				// 场景描述：
				// 当isOpsInstance为false时，且处理后的名称长度正好为64个字符，验证函数返回完整的字符串。
				// 数据构造：
				// - name: 一个处理后长度为64的字符串
				// - isOpsInstance: false
				// 逻辑链路：
				// 1. 调用 formatTopicName
				// 2. 预期结果与处理后的字符串完全相同。

				// 数据构造
				name := strings.Repeat("a", 64)
				isOpsInstance := false

				// Mock
				// 无需 mock

				// 调用
				result := formatTopicName(name, isOpsInstance)

				// 断言
				So(result, ShouldEqual, name)
			})
		})

		PatchConvey("运维实例 (isOpsInstance=true)", func() {
			PatchConvey("场景: 常规名称，添加_ops后长度小于64", func() {
				// 场景描述：
				// 当isOpsInstance为true时，验证函数能正确地格式化名称并附加 "_ops" 后缀。
				// 数据构造：
				// - name: "My.Topic.Name"
				// - isOpsInstance: true
				// 逻辑链路：
				// 1. 调用 formatTopicName
				// 2. 预期结果为 "my_topic_name_ops"，且未被截断。

				// 数据构造
				name := "My.Topic.Name"
				isOpsInstance := true

				// Mock
				// 无需 mock

				// 调用
				result := formatTopicName(name, isOpsInstance)

				// 断言
				So(result, ShouldEqual, "my_topic_name_ops")
			})

			PatchConvey("场景: 名称添加_ops后长度超过64，应被截断", func() {
				// 场景描述：
				// 当isOpsInstance为true时，且处理并添加 "_ops" 后缀的名称长度超过64个字符，验证函数会截断字符串，只保留最后64个字符。
				// 数据构造：
				// - name: 一个处理后长度会超过64个字符的长字符串
				// - isOpsInstance: true
				// 逻辑链路：
				// 1. 调用 formatTopicName
				// 2. 预期结果为原始处理结果（含_ops）的最后64个字符。

				// 数据构造
				longName := "this.is.a.very.long.topic.name.that.will.definitely.exceed.the.sixty.four.character.limit"
				isOpsInstance := true
				expectedRaw := "this_is_a_very_long_topic_name_that_will_definitely_exceed_the_sixty_four_character_limit_ops"
				expected := expectedRaw[len(expectedRaw)-64:]

				// Mock
				// 无需 mock

				// 调用
				result := formatTopicName(longName, isOpsInstance)

				// 断言
				So(len(result), ShouldEqual, 64)
				So(result, ShouldEqual, expected)
			})

			PatchConvey("场景: 名称添加_ops后长度等于64，不应被截断", func() {
				// 场景描述：
				// 当isOpsInstance为true时，且处理并添加 "_ops" 后缀的名称长度正好为64个字符，验证函数返回完整的字符串。
				// 数据构造：
				// - name: 一个处理并加后缀后长度为64的字符串（原长60）
				// - isOpsInstance: true
				// 逻辑链路：
				// 1. 调用 formatTopicName
				// 2. 预期结果与处理后的字符串（含_ops）完全相同。

				// 数据构造
				name := strings.Repeat("a", 60)
				isOpsInstance := true
				expected := name + "_ops"

				// Mock
				// 无需 mock

				// 调用
				result := formatTopicName(name, isOpsInstance)

				// 断言
				So(result, ShouldEqual, expected)
			})
		})
	})
}

func Test_CancelConfigPipelineStep_PreCheck_BitsUTGen(t *testing.T) {
	PatchConvey("Test_CancelConfigPipelineStep_PreCheck", t, func() {
		PatchConvey("成功场景 - FollowInstanceId不为空", func() {
			// 场景描述：
			// 当 AuditLifecycleActor 的 state 中的 FollowInstanceId 字段不为空时，PreCheck 应返回 false 和 nil error。
			// 数据构造：
			// - a: 一个 AuditLifecycleActor 指针，其 state.FollowInstanceId 为一个非空字符串。
			// - ctx: 一个 types.Context，可以为 nil，因为函数内部未使用。
			// 逻辑链路：
			// 1. 创建一个 CancelConfigPipelineStep 实例。
			// 2. 创建一个 AuditLifecycleActor 实例，并设置其 state.FollowInstanceId 为 "some-instance-id"。
			// 3. 调用 PreCheck 方法。
			// 4. 断言返回的 bool 值为 false。
			// 5. 断言返回的 error 为 nil。

			// 数据构造
			step := CancelConfigPipelineStep{}
			actor := &AuditLifecycleActor{
				state: &AuditLifeState{
					FollowInstanceId: "some-instance-id",
				},
			}

			// 调用
			skip, err := step.PreCheck(nil, actor)

			// 断言
			So(err, ShouldBeNil)
			So(skip, ShouldBeFalse)
		})

		PatchConvey("失败场景 - FollowInstanceId为空", func() {
			// 场景描述：
			// 当 AuditLifecycleActor 的 state 中的 FollowInstanceId 字段为空时，PreCheck 应返回 false 和一个指定的错误。
			// 数据构造：
			// - a: 一个 AuditLifecycleActor 指针，其 state.FollowInstanceId 为空字符串。
			// - ctx: 一个 types.Context，可以为 nil，因为函数内部未使用。
			// 逻辑链路：
			// 1. 创建一个 CancelConfigPipelineStep 实例。
			// 2. 创建一个 AuditLifecycleActor 实例，并设置其 state.FollowInstanceId 为空字符串 ""。
			// 3. 调用 PreCheck 方法。
			// 4. 断言返回的 bool 值为 false。
			// 5. 断言返回的 error 不为 nil，并且错误信息包含 "follow instance id can not be empty"。

			// 数据构造
			step := CancelConfigPipelineStep{}
			actor := &AuditLifecycleActor{
				state: &AuditLifeState{
					FollowInstanceId: "",
				},
			}

			// 调用
			skip, err := step.PreCheck(nil, actor)

			// 断言
			So(skip, ShouldBeFalse)
			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "follow instance id can not be empty")
		})
	})
}

// Mock_C3ConfigProvider_ProtectExec is a mock for the c3.ConfigProvider interface.
type Mock_C3ConfigProvider_ProtectExec struct {
	c3.ConfigProvider
}

func (m *Mock_C3ConfigProvider_ProtectExec) GetNamespace(ctx context.Context, namespace string) *config.C3Config {
	// This method will be mocked in tests.
	return nil
}

// Mock_ConfigProvider_ProtectExec is a mock for the service_config.ConfigProvider interface.
type Mock_ConfigProvider_ProtectExec struct {
	service_config.ConfigProvider
}

func (m *Mock_ConfigProvider_ProtectExec) Get(ctx context.Context) *config.Config {
	// This method will be mocked in tests.
	return nil
}

// Mock_FullSqlConfigService_ProtectExec is a mock for the zkconfig.FullSqlConfigService interface.
type Mock_FullSqlConfigService_ProtectExec struct {
	zkconfig.FullSqlConfigService
}

func (m *Mock_FullSqlConfigService_ProtectExec) PipeConfigX(ctx context.Context, zkConf zkconfig.ZKConfig, input *zkconfig.TlsEtlConfig, output *zkconfig.TlsEtlConfig) error {
	// This method will be mocked in tests.
	return nil
}

// mockTypesContext is a mock for the types.Context interface.
type mockTypesContext struct {
	types.Context
}

func Test_ConfigConsumerPipelineStep_ProtectExec_BitsUTGen(t *testing.T) {
	// Common test data
	var ctx types.Context = &mockTypesContext{}
	step := ConfigConsumerPipelineStep{}

	mockC3ConfProvider := &Mock_C3ConfigProvider_ProtectExec{}
	mockConfProvider := &Mock_ConfigProvider_ProtectExec{}
	mockFullSqlConfigSvc := &Mock_FullSqlConfigService_ProtectExec{}

	baseActor := &AuditLifecycleActor{
		c3Conf:               mockC3ConfProvider,
		conf:                 mockConfProvider,
		fullSqlConfigService: mockFullSqlConfigSvc,
		state: &AuditLifeState{
			FollowInstanceId:    "test-instance",
			TenantId:            "test-tenant",
			TlsConsumerEndpoint: "kafka.test.com:9092",
			TlsConsumerProject:  "test-consumer-project",
			TlsConsumerTopic:    "test-consumer-topic",
			TlsProject:          "test-tls-project",
			TlsTopic:            "test-tls-topic",
			EnableFunctions: map[model.FullSqlFuncName]bool{
				model.FullSqlFuncName_SQLAuditDetail: true,
				model.FullSqlFuncName_SQLInsight:     true,
			},
			StorageSqlTypes: []string{"SELECT"},
		},
	}

	PatchConvey("Test_ConfigConsumerPipelineStep_ProtectExec", t, func() {
		// Mock common functions to avoid nil panics or unwanted side effects
		Mock(log.Info).Return().Build()
		Mock(log.Warn).Return().Build()
		Mock(libutils.Show).Return("mocked-json").Build()

		PatchConvey("成功场景 - MySQL 非运维实例", func() {
			// 场景描述：
			// 当实例类型为MySQL，且非运维实例时，验证函数能正确组装配置并成功调用下游服务。
			// 数据构造：
			// - a.state.DSType = model.DSType_MySQL
			// - a.state.IsOpsInstance = false
			// 逻辑链路：
			// 1. Mock c3Conf.GetNamespace 返回MySQL相关的AK/SK。
			// 2. Mock conf.Get 返回通用配置。
			// 3. Mock GetTLSRegion 和 GetTlsRegionEndpoint 成功返回。
			// 4. Mock IsSqlDesensitizationAccount 返回 false。
			// 5. Mock fullSqlConfigService.PipeConfigX 成功返回 nil。
			// 6. 验证函数返回 nil。
			actor := *baseActor
			actor.state.DSType = model.DSType_MySQL
			actor.state.IsOpsInstance = false

			Mock((*Mock_C3ConfigProvider_ProtectExec).GetNamespace).Return(&config.C3Config{
				Application: config.Application{
					RDSInnerAccountAK:   "mysql-ak",
					RDSInnerAccountSK:   "mysql-sk",
					TLSServiceAccessKey: "tls-ak",
					TLSServiceSecretKey: "tls-sk",
				},
			}).Build()
			Mock((*Mock_ConfigProvider_ProtectExec).Get).Return(&config.Config{
				FullSqlZkHosts: []string{"zk:2181"},
			}).Build()
			Mock(i18n.GetSiteName).Return("test-site").Build()
			Mock((*config.Config).GetTLSRegion).Return("test-region", nil).Build()
			Mock((*config.Config).GetTlsRegionEndpoint).Return("https://tls.test.com").Build()
			Mock(tenant.IsSqlDesensitizationAccount).Return(false).Build()
			Mock((*Mock_FullSqlConfigService_ProtectExec).PipeConfigX).Return(nil).Build()

			err := step.ProtectExec(ctx, &actor)

			So(err, ShouldBeNil)
		})

		PatchConvey("成功场景 - VeDBMySQL 运维实例", func() {
			// 场景描述：
			// 当实例类型为VeDBMySQL，且为运维实例时，验证黑名单被清空，并成功调用下游。
			// 数据构造：
			// - a.state.DSType = model.DSType_VeDBMySQL
			// - a.state.IsOpsInstance = true
			// 逻辑链路：
			// 1. Mock c3Conf.GetNamespace 返回VeDB相关的AK/SK。
			// 2. Mock conf.Get 返回通用配置。
			// 3. Mock IsSqlDesensitizationAccount 返回 true。
			// 4. Mock fullSqlConfigService.PipeConfigX 成功，并验证传入的黑名单为空。
			// 5. 验证函数返回 nil。
			actor := *baseActor
			actor.state.DSType = model.DSType_VeDBMySQL
			actor.state.IsOpsInstance = true
			actor.state.SqlDesensitizationType = model.SqlDesensitizationType_Storage

			Mock((*Mock_C3ConfigProvider_ProtectExec).GetNamespace).Return(&config.C3Config{
				Application: config.Application{
					VeDBInnerAccountAK:  "vedb-ak",
					VeDBInnerAccountSK:  "vedb-sk",
					TLSServiceAccessKey: "tls-ak",
					TLSServiceSecretKey: "tls-sk",
				},
			}).Build()
			Mock((*Mock_ConfigProvider_ProtectExec).Get).Return(&config.Config{
				FullSqlZkHosts: []string{"zk:2181"},
			}).Build()
			Mock(i18n.GetSiteName).Return("test-site").Build()
			Mock((*config.Config).GetTLSRegion).Return("test-region", nil).Build()
			Mock((*config.Config).GetTlsRegionEndpoint).Return("https://tls.test.com").Build()
			Mock(tenant.IsSqlDesensitizationAccount).Return(true).Build()
			Mock((*Mock_FullSqlConfigService_ProtectExec).PipeConfigX).To(func(ctx context.Context, zkConf zkconfig.ZKConfig, input *zkconfig.TlsEtlConfig, output *zkconfig.TlsEtlConfig) error {
				So(input.PipeMeta.DBBlackListFilter, ShouldBeEmpty)
				So(input.PipeMeta.UserBlackListFilter, ShouldBeEmpty)
				So(input.PipeMeta.SqlDesensitization, ShouldBeTrue)
				So(actor.state.TlsConsumerGroup, ShouldEqual, "dbw-full-sql-consumer-ops")
				return nil
			}).Build()

			err := step.ProtectExec(ctx, &actor)

			So(err, ShouldBeNil)
		})

		PatchConvey("成功场景 - MetaMySQL", func() {
			// 场景描述：
			// 当实例类型为MetaMySQL时，验证函数能正确处理并成功调用下游。
			// 数据构造：
			// - a.state.DSType = model.DSType_MetaMySQL
			// 逻辑链路：
			// 1. Mock 依赖项返回正常值。
			// 2. Mock fullSqlConfigService.PipeConfigX 成功返回。
			// 3. 验证函数返回 nil。
			actor := *baseActor
			actor.state.DSType = model.DSType_MetaMySQL
			actor.state.IsOpsInstance = false

			Mock((*Mock_C3ConfigProvider_ProtectExec).GetNamespace).Return(&config.C3Config{
				Application: config.Application{
					MetaRDSInnerAccountAK: "meta-ak",
					MetaRDSInnerAccountSK: "meta-sk",
					TLSServiceAccessKey:   "tls-ak",
					TLSServiceSecretKey:   "tls-sk",
				},
			}).Build()
			Mock((*Mock_ConfigProvider_ProtectExec).Get).Return(&config.Config{}).Build()
			Mock(i18n.GetSiteName).Return("test-site").Build()
			Mock((*config.Config).GetTLSRegion).Return("test-region", nil).Build()
			Mock((*config.Config).GetTlsRegionEndpoint).Return("https://tls.test.com").Build()
			Mock(tenant.IsSqlDesensitizationAccount).Return(false).Build()
			Mock((*Mock_FullSqlConfigService_ProtectExec).PipeConfigX).Return(nil).Build()

			err := step.ProtectExec(ctx, &actor)

			So(err, ShouldBeNil)
		})

		PatchConvey("失败场景 - 未知数据源类型", func() {
			// 场景描述：
			// 当a.state.DSType是一个未在switch-case中处理的类型时，函数应返回错误。
			// 数据构造：
			// - a.state.DSType = model.DSType(99) // 未知类型
			// 逻辑链路：
			// 1. 触发switch的default分支。
			// 2. 函数返回一个包含"unknown dstype"信息的错误。
			actor := *baseActor
			actor.state.DSType = model.DSType(99) // An unknown type

			Mock((*Mock_C3ConfigProvider_ProtectExec).GetNamespace).Return(&config.C3Config{}).Build()
			Mock((*Mock_ConfigProvider_ProtectExec).Get).Return(&config.Config{}).Build()
			Mock(i18n.GetSiteName).Return("test-site").Build()
			Mock((*config.Config).GetTLSRegion).Return("test-region", nil).Build()

			err := step.ProtectExec(ctx, &actor)

			So(err, ShouldNotBeNil)
			So(err.Error(), ShouldContainSubstring, "unknown dstype")
		})

		PatchConvey("失败场景 - ZK配置写入失败", func() {
			// 场景描述：
			// 当所有前置依赖都正常，但在最后调用fullSqlConfigService.PipeConfigX时返回错误。
			// 数据构造：
			// - a.state.DSType = model.DSType_MySQL
			// 逻辑链路：
			// 1. 所有依赖正常Mock。
			// 2. Mock fullSqlConfigService.PipeConfigX 返回一个错误。
			// 3. 验证函数返回的错误与Mock的错误一致。
			actor := *baseActor
			actor.state.DSType = model.DSType_MySQL
			actor.state.IsOpsInstance = false
			expectedErr := errors.New("zk pipe config error")

			Mock((*Mock_C3ConfigProvider_ProtectExec).GetNamespace).Return(&config.C3Config{
				Application: config.Application{
					RDSInnerAccountAK:   "mysql-ak",
					RDSInnerAccountSK:   "mysql-sk",
					TLSServiceAccessKey: "tls-ak",
					TLSServiceSecretKey: "tls-sk",
				},
			}).Build()
			Mock((*Mock_ConfigProvider_ProtectExec).Get).Return(&config.Config{}).Build()
			Mock(i18n.GetSiteName).Return("test-site").Build()
			Mock((*config.Config).GetTLSRegion).Return("test-region", nil).Build()
			Mock((*config.Config).GetTlsRegionEndpoint).Return("https://tls.test.com").Build()
			Mock(tenant.IsSqlDesensitizationAccount).Return(false).Build()
			Mock((*Mock_FullSqlConfigService_ProtectExec).PipeConfigX).Return(expectedErr).Build()

			err := step.ProtectExec(ctx, &actor)

			So(err, ShouldNotBeNil)
			So(err, ShouldEqual, expectedErr)
		})
	})
}
