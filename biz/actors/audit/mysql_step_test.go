package audit

import (
	"context"
	"encoding/json"
	"testing"

	ycnf "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls/tls_enhance"
	local_dal "code.byted.org/infcs/dbw-mgr/biz/test/dal"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	config2 "code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	mock_dal "code.byted.org/infcs/dbw-mgr/biz/test/mocks/dal"
	mocks_audit "code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/audit"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/bill"
	mocks_def "code.byted.org/infcs/dbw-mgr/biz/test/service"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	rdsModel_v2 "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2022-01-01/kitex_gen/model/v2"
	rdsModel "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/kitex_gen/model"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	volctrade "code.byted.org/infcs/lib-mgr-common/volc/trade"
	"code.byted.org/infcs/protoactor-go/actor"
	"code.byted.org/videoarch/cloud-volc_sdk_go/tradeapi"
	. "github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	. "github.com/smartystreets/goconvey/convey"

	"github.com/stretchr/testify/suite"
)

type MysqlStepTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *MysqlStepTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}
func (suite *MysqlStepTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}
func TestMysqlStepSuite(t *testing.T) {
	//suite.Run(t, new(MysqlStepTestSuite))
}

//func (suite *MysqlStepTestSuite) TestNewMysqlStep() {
//	PatchConvey("", suite.T(), func() {
//		ctx := context.TODO()
//		ctx = context.WithValue(ctx, "biz-context", &fwctx.BizContext{TenantID: "**********"})
//
//		local := &local_dal.LocalAudit{}
//		local.Init(ctx, suite.ctrl)
//
//		def := mocks_def.DefaultMock{}
//		def.Init(suite.ctrl)
//		So(ctx, ShouldNotBeNil)
//		_, cfg, c3Cnf, local, auditSvc, publishEventSvc := MockBillNeed(suite.ctrl)
//		mockCrossAuthService := MockCrossAuthService(suite.ctrl)
//		ds := MockDS(suite.ctrl)
//		tagSvc := MockTag(suite.ctrl)
//		proj := MockProject(suite.ctrl)
//		lcSvc := MockLogCollector(suite.ctrl)
//
//		Mock((*volctrade.VolcTradeClient).CallbackInstanceStatus).Return(nil).Build()
//		billSvc := bill.NewMockBillingService(suite.ctrl)
//		billSvc.EXPECT().GetInstance(gomock.Any(), gomock.Any()).Return(&tradeapi.Instance{BusinessStatus: int32(model.BusinessStatus_InstanceBusinessTerminating)}, nil).AnyTimes()
//		billSvc.EXPECT().GetMultiClient().Return(&volctrade.VolcTradeClient{}).AnyTimes()
//
//		cresp := &tls_enhance.CreateScheduleSqlTaskResponse{TaskId: "1"}
//		Mock(tls_enhance.CreateScheduleSqlTask).Return(cresp, nil).Build()
//		dresp := &tls_enhance.DeleteScheduleSqlTaskResponse{}
//		Mock(tls_enhance.DeleteScheduleSqlTask).Return(dresp, nil).Build()
//		diresp := &tls_sdk.DescribeIndexResponse{
//			CommonResponse: tls_sdk.CommonResponse{},
//			TopicID:        "1",
//			FullText:       nil,
//			KeyValue:       nil,
//			CreateTime:     "",
//			ModifyTime:     "",
//		}
//		Mock(tls_sdk.Client.DescribeIndex).Return(diresp, nil).Build()
//		ciresp := &tls_sdk.CreateIndexResponse{
//			CommonResponse: tls_sdk.CommonResponse{},
//			TopicID:        "1",
//		}
//		Mock(tls_sdk.Client.CreateIndex).Return(ciresp, nil).Build()
//		miresp := &tls_sdk.CommonResponse{}
//		Mock(tls_sdk.Client.ModifyIndex).Return(miresp, nil).Build()
//		dpresp := &tls_sdk.DescribeProjectResponse{
//			CommonResponse: tls_sdk.CommonResponse{},
//			ProjectInfo: tls_sdk.ProjectInfo{
//				ProjectID:       "1",
//				ProjectName:     "1",
//				Description:     "1",
//				CreateTimestamp: "",
//				TopicCount:      0,
//				InnerNetDomain:  "",
//			},
//		}
//		Mock(tls_sdk.Client.DescribeProject).Return(dpresp, nil).Build()
//		cpresp := &tls_sdk.CreateProjectResponse{
//			CommonResponse: tls_sdk.CommonResponse{},
//			ProjectID:      "1",
//		}
//		Mock(tls_sdk.Client.CreateProject).Return(cpresp, nil).Build()
//		ctresp := &tls_sdk.CreateTopicResponse{
//			CommonResponse: tls_sdk.CommonResponse{},
//			TopicID:        "1",
//		}
//		Mock(tls_sdk.Client.CreateTopic).Return(ctresp, nil).Build()
//		sqlStatisticStep := &SqlStatisticStep{}
//		a := &AuditLifecycleActor{
//			state: &AuditLifeState{
//				LifeState:        LifeState{},
//				TenantId:         "**********",
//				Region:           "cn-chongqing-sdv",
//				FollowInstanceId: "mysql-d39e3357cfd0",
//				TlsTopic:         "324ad023-29c9-4342-9e30-79d0c5cf82fa",
//				TlsProject:       "4591da5d-70bc-4831-ac8a-fb738c75508f",
//				TlsRegion:        "cn-guilin-boe",
//				TlsEndpoint:      "https://tls-cn-guilin-boe-inner.ivolces.com",
//				TlsTopicTTL:      1,
//				DSType:           model.DSType_MySQL,
//				MsgID:            "",
//				Message:          nil,
//				AzClusterMap:     map[string]string{"compute-a": "compute-a", "compute-b": "compute-b"},
//			},
//			statisticSqlTlsDAL:  local.StatisticSqlTlsDAL,
//			statisticSqlTaskDAL: local.StatisticSqlTaskDAL,
//
//			auditService:    auditSvc,
//			auditTlsDAL:     local.AuditDAL,
//			tlsDAL:          local.TlsDAL,
//			crossAuthSvc:    mockCrossAuthService,
//			conf:            cfg,
//			c3Conf:          c3Cnf,
//			publishEventSvc: publishEventSvc,
//			source:          ds,
//			tagSvc:          tagSvc,
//			projectSvc:      proj,
//			billSvc:         billSvc,
//			lcSvc:           lcSvc,
//		}
//
//		tctx := mocks.NewMockContext(suite.ctrl)
//		tctx.EXPECT().Value(gomock.Any()).AnyTimes()
//		tctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
//		tctx.EXPECT().Respond(gomock.Any()).AnyTimes()
//		tctx.EXPECT().Send(gomock.Any(), gomock.Any()).Return().AnyTimes()
//		tctx.EXPECT().Sender().Return(&actor.PID{}).AnyTimes()
//		sqlStatisticStep.ProtectExec(tctx, a)
//	})
//}

func (suite *MysqlStepTestSuite) TestNewMysqlStep1() {
	PatchConvey("", suite.T(), func() {
		ctx := context.TODO()
		ctx = context.WithValue(ctx, "biz-context", &fwctx.BizContext{TenantID: "**********"})

		local := &local_dal.LocalAudit{}
		local.Init(ctx, suite.ctrl)

		def := mocks_def.DefaultMock{}
		def.Init(suite.ctrl)
		So(ctx, ShouldNotBeNil)
		_, cfg, c3Cnf, local, auditSvc, publishEventSvc := MockBillNeed1(suite.ctrl)
		mockCrossAuthService := MockCrossAuthService(suite.ctrl)
		ds := MockDS(suite.ctrl)
		tagSvc := MockTag(suite.ctrl)
		proj := MockProject(suite.ctrl)
		lcSvc := MockLogCollector(suite.ctrl)

		Mock((*volctrade.VolcTradeClient).CallbackInstanceStatus).Return(nil).Build()
		billSvc := bill.NewMockBillingService(suite.ctrl)
		billSvc.EXPECT().GetInstance(gomock.Any(), gomock.Any()).Return(&tradeapi.Instance{BusinessStatus: int32(model.BusinessStatus_InstanceBusinessTerminating)}, nil).AnyTimes()
		billSvc.EXPECT().GetMultiClient().Return(&volctrade.VolcTradeClient{}).AnyTimes()

		sstls := mock_dal.NewMockStatisticSqlTlsDal(suite.ctrl)
		sstls.EXPECT().Create(gomock.Any(), gomock.Any()).Return(int64(1), nil).AnyTimes()
		sstls.EXPECT().Get(gomock.Any(), gomock.Any()).Return([]*dao.StatisticSqlTls{}, nil).AnyTimes()
		sstls.EXPECT().GetByID(gomock.Any(), gomock.Any()).Return(&dao.StatisticSqlTls{
			Id:       1,
			TenantId: "1",
			Region:   "1",
			DbType:   model.DSType_MySQL.String(),
			DataType: model.StatisticDataType_SqlExecCount.String(),
			TlsId:    1,
			Deleted:  0,
		}, nil).AnyTimes()
		sstls.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		sstls.EXPECT().Delete(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

		sstask := mock_dal.NewMockStatisticSqlTaskDal(suite.ctrl)
		ssts := []*dao.StatisticSqlTask{
			{
				Id:          1,
				InstanceId:  "1",
				StatisticId: 1,
				TenantId:    "1",
				Type:        model.ScheduleSqlTaskType_SqlExecL1.String(),
				DsType:      model.DSType_MySQL.String(),
				SqlTaskId:   "1",
				Version:     1,
				Deleted:     0,
			},
			{
				Id:          2,
				InstanceId:  "2",
				StatisticId: 2,
				TenantId:    "2",
				Type:        model.ScheduleSqlTaskType_SqlExecL2.String(),
				DsType:      model.DSType_MySQL.String(),
				SqlTaskId:   "1",
				Version:     1,
				Deleted:     0,
			},
		}
		sstask.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		sstask.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(ssts, nil).AnyTimes()
		sstask.EXPECT().GetByStatisticId(gomock.Any(), gomock.Any(), gomock.Any()).Return(ssts, nil).AnyTimes()
		sstask.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		sstask.EXPECT().Delete(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		sstask.EXPECT().GetByInstanceId(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(ssts, nil).AnyTimes()

		cresp := &tls_enhance.CreateScheduleSqlTaskResponse{TaskId: "1"}
		Mock(tls_enhance.CreateScheduleSqlTask).Return(cresp, nil).Build()
		dresp := &tls_enhance.DeleteScheduleSqlTaskResponse{}
		Mock(tls_enhance.DeleteScheduleSqlTask).Return(dresp, nil).Build()

		sqlStatisticStep := &SqlStatisticStep{}
		a := &AuditLifecycleActor{
			state: &AuditLifeState{
				LifeState:        LifeState{},
				TenantId:         "**********",
				Region:           "cn-chongqing-sdv",
				FollowInstanceId: "mysql-d39e3357cfd0",
				TlsTopic:         "324ad023-29c9-4342-9e30-79d0c5cf82fa",
				TlsProject:       "4591da5d-70bc-4831-ac8a-fb738c75508f",
				TlsRegion:        "cn-guilin-boe",
				TlsEndpoint:      "https://tls-cn-guilin-boe-inner.ivolces.com",
				TlsTopicTTL:      1,
				DSType:           model.DSType_MySQL,
				MsgID:            "",
				Message:          nil,
				AzClusterMap:     map[string]string{"compute-a": "compute-a", "compute-b": "compute-b"},
			},
			statisticSqlTlsDAL:  sstls,
			statisticSqlTaskDAL: sstask,

			auditService:    auditSvc,
			auditTlsDAL:     local.AuditDAL,
			tlsDAL:          local.TlsDAL,
			crossAuthSvc:    mockCrossAuthService,
			conf:            cfg,
			c3Conf:          c3Cnf,
			publishEventSvc: publishEventSvc,
			source:          ds,
			tagSvc:          tagSvc,
			projectSvc:      proj,
			billSvc:         billSvc,
			lcSvc:           lcSvc,
		}

		tctx := mocks.NewMockContext(suite.ctrl)
		tctx.EXPECT().Value(gomock.Any()).AnyTimes()
		tctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
		tctx.EXPECT().Respond(gomock.Any()).AnyTimes()
		tctx.EXPECT().Send(gomock.Any(), gomock.Any()).Return().AnyTimes()
		tctx.EXPECT().Sender().Return(&actor.PID{}).AnyTimes()
		Mock(SqlStatisticStep.GetTlsClient).Return(MockTlsClient(suite.ctrl)).Build()
		sqlStatisticStep.ProtectExec(tctx, a)
	})
}

func MockBillNeed1(ctrl *gomock.Controller) (
	context.Context,
	*config2.MockConfigProvider,
	*config2.MockC3ConfigProvider,
	*local_dal.LocalAudit,
	*mocks_audit.MockSqlAuditService,
	*mocks.MockPublishEventService,
) {
	ctx := context.TODO()
	ctx = context.WithValue(ctx, "biz-context", &fwctx.BizContext{TenantID: "**********"})
	cfg := config2.NewMockConfigProvider(ctrl)
	// boe-stable
	cfg.EXPECT().Get(gomock.Any()).Return(&ycnf.Config{
		VolcProfile:                "boe_stable",
		BillingAK:                  "MDgyYjM0NWRiYWY",
		BillingSK:                  "GMxZjk3MTczZTMxNGYzlhODg3NmRk",
		VolcServiceAccountAK:       "AKLTY2RmNGQ4NjhmZmJlNGI5YzhkZTM0ZjQyOWY0MTlmOTI",
		VolcServiceAccountSK:       "TURReU9UY3hOR05tTnpJME5HWmhZVGt4WVRZek5tUTVaRFprTm1RMk1EQQ==",
		BillingTopic:               "trade_instance",
		BillingCluster:             "rmq_test_new",
		BillingConsumerGroup:       "dbw_cn-nanjing-bbit",
		EnableBillingTenantIDList:  []string{"**********", "**********"},
		DisableBillingTenantIDList: []string{},
	}).AnyTimes()
	c3Cnf := config2.NewMockC3ConfigProvider(ctrl)
	c3Cnf.EXPECT().GetNamespace(gomock.Any(), gomock.Any()).Return(&ycnf.C3Config{
		Application: ycnf.Application{
			TOPServiceAccessKey: "AKLTODY2M2EzMzRlNTdhNGU3ZDhhZTlhNDgxYzA3MjkyODY",
			TOPServiceSecretKey: "WVdSak1UQTNNR1F6TURjMk5ETTJZVGt6TldJNU5XTXpZemxqT1RoallUSQ==",
			TLSServiceAccessKey: "AKLTMmMwNzM5ZWUxYjliNGUzY2FjNWZkMTg1YmZkZWQ0MTU",
			TLSServiceSecretKey: "TldWaVlUTXpNemszWlRJeE5ETTFNamhpTXpKaE56SmxPVE15TWpGbE5HSQ==",
		},
	}).AnyTimes()

	mgrProv := mocks.NewMockMgrProvider(ctrl)
	mgrClient := mocks.NewMockMgrClient(ctrl)
	mgrProv.EXPECT().Get().Return(mgrClient).AnyTimes()

	nodedetail := &rdsModel_v2.DescribeDBInstanceDetailResp{}
	json.Unmarshal([]byte("{\"BasicInfo\":{\"InstanceId\":\"mysql-0844ce4826e0\",\"InstanceName\":\"dyh_80_勿删\",\"InstanceStatus\":\"Running\",\"RegionId\":\"cn-nanjing-bbit\",\"ZoneId\":\"cn-nanjing-bbit-a\",\"DBEngine\":\"Mysql\",\"DBEngineVersion\":\"MySQL_8_0\",\"InstanceType\":\"HA\",\"VCPU\":2,\"Memory\":8,\"NodeSpec\":\"rds.mysql.2c8g\",\"NodeNumber\":\"3\",\"CreateTime\":\"2022-11-16 12:01:36\",\"UpdateTime\":\"\",\"StorageUse\":4.17,\"StorageSpace\":100,\"StorageType\":\"LocalSSD\",\"BackupUse\":0.76,\"VpcId\":\"vpc-8gvvy8uzp3i88k0tze9vkak9\",\"TimeZone\":\"UTC +08:00\",\"DataSyncMode\":\"SemiSync\",\"ProjectName\":\"\",\"InnerVersion\":\"\",\"IsLatestVersion\":false,\"LowerCaseTableNames\":\"1\",\"SubnetId\":\"subnet-h0alcmc3e96o3kyadhv0ggix\",\"ShardNumber\":0,\"StorageDataSize\":**********,\"StorageLogSize\":********,\"StorageBinLogSize\":********,\"StorageErrorLogSize\":859390,\"StorageAuditLogSize\":4096,\"StorageSlowLogSize\":4220526,\"BackupDataSize\":*********,\"BackupLogSize\":*********,\"BackupBinLogSize\":*********,\"BackupErrorLogSize\":868625,\"BackupAuditLogSize\":0,\"BackupSlowLogSize\":7292344,\"PrimaryDBAccount\":\"\",\"AllowListVersion\":\"v2\",\"ServerCollation\":\"\",\"MaintenanceWindow\":{\"MaintenanceTime\":\"18:00Z-22:00Z\",\"DayKind\":\"Week\",\"DayOfWeek\":[\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\",\"Sunday\"],\"DayOfMonth\":[]}},\"ConnectionInfo\":[{\"EndpointId\":\"mysql-0844ce4826e0-cluster\",\"EndpointType\":\"Cluster\",\"Description\":\"\",\"Address\":[{\"NetworkType\":\"Ingress\",\"Domain\":\"mysql0844ce4826e0.rds-boe.infcs.tob\",\"IPAddress\":\"\",\"Port\":\"25345\",\"SubnetId\":\"\",\"EipId\":\"\"},{\"NetworkType\":\"Private\",\"Domain\":\"mysql0844ce4826e0.rds-boe.ivolces.com\",\"IPAddress\":\"*************\",\"Port\":\"3306\",\"SubnetId\":\"subnet-h0alcmc3e96o3kyadhv0ggix\",\"EipId\":\"\"},{\"NetworkType\":\"Carma\",\"Domain\":\"mysql-0844ce4826e0-proxy-agent-hs.rds.svc.mix-panel-a.org\",\"IPAddress\":\"\",\"Port\":\"3679\",\"SubnetId\":\"\",\"EipId\":\"\"}],\"EnableReadWriteSplitting\":\"Disable\",\"EnableReadOnly\":\"Disable\",\"EndpointName\":\"\",\"ReadWriteMode\":\"ReadWrite\",\"ReadOnlyNodeWeight\":[{\"NodeType\":\"Primary\",\"NodeId\":\"\",\"Weight\":200},{\"NodeType\":\"ReadOnly\",\"NodeId\":\"mysql-0844ce4826e0-r9d3f\",\"Weight\":0}],\"AutoAddNewNodes\":\"Enable\",\"ReadOnlyNodeDistributionType\":\"Default\",\"ReadOnlyNodeMaxDelayTime\":30},{\"EndpointId\":\"mysql-0844ce4826e0-direct\",\"EndpointType\":\"Direct\",\"Description\":\"\",\"Address\":[{\"NetworkType\":\"Carma\",\"Domain\":\"mysql-0844ce4826e0-hs.rds.svc.mix-panel-a.org\",\"IPAddress\":\"\",\"Port\":\"3306\",\"SubnetId\":\"\",\"EipId\":\"\"}],\"EnableReadWriteSplitting\":\"Disable\",\"EnableReadOnly\":\"Disable\",\"EndpointName\":\"\",\"ReadWriteMode\":\"ReadWrite\",\"AutoAddNewNodes\":\"Disable\",\"ReadOnlyNodeDistributionType\":\"Default\"}],\"ChargeDetail\":{\"ChargeType\":\"PostPaid\",\"AutoRenew\":false,\"PeriodUnit\":\"Month\",\"Period\":1,\"Number\":1,\"ChargeStatus\":\"Normal\",\"ChargeStartTime\":\"\",\"ChargeEndTime\":\"\",\"OverdueTime\":\"\",\"OverdueReclaimTime\":\"\"},\"NodeDetailInfo\":[{\"InstanceId\":\"mysql-0844ce4826e0\",\"NodeId\":\"mysql-0844ce4826e0-0\",\"RegionId\":\"cn-nanjing-bbit\",\"ZoneId\":\"cn-nanjing-bbit-a\",\"NodeType\":\"Primary\",\"NodeStatus\":\"Running\",\"VCPU\":2,\"Memory\":8,\"NodeSpec\":\"rds.mysql.2c8g\",\"CreateTime\":\"2022-11-16 12:01:36\",\"UpdateTime\":\"\",\"ShardId\":\"\"},{\"InstanceId\":\"mysql-0844ce4826e0\",\"NodeId\":\"mysql-0844ce4826e0-1\",\"RegionId\":\"cn-nanjing-bbit\",\"ZoneId\":\"cn-nanjing-bbit-a\",\"NodeType\":\"Secondary\",\"NodeStatus\":\"Running\",\"VCPU\":2,\"Memory\":8,\"NodeSpec\":\"rds.mysql.2c8g\",\"CreateTime\":\"2022-11-16 12:01:36\",\"UpdateTime\":\"\",\"ShardId\":\"\"},{\"InstanceId\":\"mysql-0844ce4826e0\",\"NodeId\":\"mysql-0844ce4826e0-r9d3f\",\"RegionId\":\"cn-nanjing-bbit\",\"ZoneId\":\"cn-nanjing-bbit-a\",\"NodeType\":\"ReadOnly\",\"NodeStatus\":\"Running\",\"VCPU\":2,\"Memory\":8,\"NodeSpec\":\"rds.mysql.2c8g\",\"CreateTime\":\"2022-11-21 22:26:47\",\"UpdateTime\":\"2022-11-22 16:47:43\",\"ShardId\":\"\"}],\"ShardInfo\":null}"),
		nodedetail)
	mgrClient.EXPECT().Call(gomock.Any(), rdsModel_v2.Action_DescribeDBInstanceDetail.String(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(3, *nodedetail).Return(nil).AnyTimes()

	pods := &rdsModel.ListInstancePodsResp{}
	json.Unmarshal([]byte("{\"Total\":7,\"Datas\":[{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-m9c1a-ha-controller-0\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"200mC\",\"MemInfo\":\"200Mi\",\"DiskInfo\":\"\",\"RunningInfo\":\"Container:1/1\",\"LastestStartTime\":\"2022-11-23 10:31:40\",\"Resource\":\"mysql-bfb26ba0773e-m9c1a\",\"Containers\":[{\"Name\":\"ha-controller\",\"Port\":\"2338\",\"Image\":\"hub.byted.org/infcs/ha_controller:20221115.1718-epic_v1.1.10-1b9759\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"200Mi\"}],\"Component\":\"HA Controller\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-m9c1a-ha-controller-1\",\"NodeIP\":\"***********\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"200mC\",\"MemInfo\":\"200Mi\",\"DiskInfo\":\"\",\"RunningInfo\":\"Container:1/1\",\"LastestStartTime\":\"2022-11-23 10:31:40\",\"Resource\":\"mysql-bfb26ba0773e-m9c1a\",\"Containers\":[{\"Name\":\"ha-controller\",\"Port\":\"2338\",\"Image\":\"hub.byted.org/infcs/ha_controller:20221115.1718-epic_v1.1.10-1b9759\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"200Mi\"}],\"Component\":\"HA Controller\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-sbf4a-ha-controller-0\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"200mC\",\"MemInfo\":\"200Mi\",\"DiskInfo\":\"\",\"RunningInfo\":\"Container:1/1\",\"LastestStartTime\":\"2022-11-23 10:31:41\",\"Resource\":\"mysql-bfb26ba0773e-sbf4a\",\"Containers\":[{\"Name\":\"ha-controller\",\"Port\":\"2338\",\"Image\":\"hub.byted.org/infcs/ha_controller:20221115.1718-epic_v1.1.10-1b9759\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"200Mi\"}],\"Component\":\"HA Controller\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-m9c1a-0\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"1C 200mC\",\"MemInfo\":\"2458Mi500Mi\",\"DiskInfo\":\"20Gi\",\"RunningInfo\":\"Container:3/3\",\"LastestStartTime\":\"2022-11-23 10:31:26\",\"Resource\":\"mysql-bfb26ba0773e-m9c1a\",\"Containers\":[{\"Name\":\"mysql\",\"Port\":\"3306\",\"Image\":\"hub.byted.org/infcs/infcs_rds_mysql_server_5_7:1c16f3a2b1b86f0ab416221869608d41\",\"Status\":\"Ready\",\"Cpu\":\"1C\",\"Mem\":\"2458Mi\"},{\"Name\":\"backup-server\",\"Port\":\"8889\",\"Image\":\"hub.byted.org/infcs/rds_backup:20221109.2245-epic_v1.1.10-2c756b\",\"Status\":\"Ready\",\"Cpu\":\"\",\"Mem\":\"\"},{\"Name\":\"mysql-agent\",\"Port\":\"2335\",\"Image\":\"hub.byted.org/infcs/tob_rds_mysql_agent:20221116.1608-epic_v1.1.10-c1b154\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"500Mi\"}],\"Component\":\"MySQL\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-sbf4a-0\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"1C 200mC\",\"MemInfo\":\"2458Mi500Mi\",\"DiskInfo\":\"20Gi\",\"RunningInfo\":\"Container:3/3\",\"LastestStartTime\":\"2022-11-23 10:31:26\",\"Resource\":\"mysql-bfb26ba0773e-sbf4a\",\"Containers\":[{\"Name\":\"mysql\",\"Port\":\"3306\",\"Image\":\"hub.byted.org/infcs/infcs_rds_mysql_server_5_7:1c16f3a2b1b86f0ab416221869608d41\",\"Status\":\"Ready\",\"Cpu\":\"1C\",\"Mem\":\"2458Mi\"},{\"Name\":\"backup-server\",\"Port\":\"8889\",\"Image\":\"hub.byted.org/infcs/rds_backup:20221109.2245-epic_v1.1.10-2c756b\",\"Status\":\"Ready\",\"Cpu\":\"\",\"Mem\":\"\"},{\"Name\":\"mysql-agent\",\"Port\":\"2335\",\"Image\":\"hub.byted.org/infcs/tob_rds_mysql_agent:20221116.1608-epic_v1.1.10-c1b154\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"500Mi\"}],\"Component\":\"MySQL\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-m9c1a-proxy-agent-544dbc9ff7-fd4l6\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"500mC 200mC\",\"MemInfo\":\"2Gi500Mi\",\"DiskInfo\":\"\",\"RunningInfo\":\"Container:2/2\",\"LastestStartTime\":\"2022-11-23 10:31:48\",\"Resource\":\"mysql-bfb26ba0773e-m9c1a\",\"Containers\":[{\"Name\":\"proxy\",\"Port\":\"3679 23679 3680 23680 3681 23681 3682 23682 3683 23683 3684 23684 3685 23685 3686 23686 3687 23687 3688 23688 3689 23689 3690 23690 3691 23691 3692 23692 3693 23693 3694 23694 3695 23695\",\"Image\":\"hub.byted.org/bytendb2b/rds_proxy_sidecar:20221115.1403-epic_v1.1.10-e73d0b\",\"Status\":\"Ready\",\"Cpu\":\"500mC\",\"Mem\":\"2Gi\"},{\"Name\":\"proxy-agent\",\"Port\":\"2335\",\"Image\":\"hub.byted.org/bytendb2b/rds_proxy_agent_sidecar:07383ec561c92137e9b26bc326b5af06\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"500Mi\"}],\"Component\":\"Proxy\"},{\"Region\":\"cn-nanjing-bbit\",\"Zone\":\"cn-nanjing-bbit-a\",\"KubeCluster\":\"mix-panel-a\",\"NodePool\":\"mix-panel-a-default\",\"Name\":\"mysql-bfb26ba0773e-sbf4a-proxy-agent-bb476c4fb-4mblj\",\"NodeIP\":\"************\",\"PodIP\":\"*************\",\"PodStatus\":\"Running\",\"CpuInfo\":\"500mC 200mC\",\"MemInfo\":\"2Gi500Mi\",\"DiskInfo\":\"\",\"RunningInfo\":\"Container:2/2\",\"LastestStartTime\":\"2022-11-23 10:31:49\",\"Resource\":\"mysql-bfb26ba0773e-sbf4a\",\"Containers\":[{\"Name\":\"proxy\",\"Port\":\"3679 23679 3680 23680 3681 23681 3682 23682 3683 23683 3684 23684 3685 23685 3686 23686 3687 23687 3688 23688 3689 23689 3690 23690 3691 23691 3692 23692 3693 23693 3694 23694 3695 23695\",\"Image\":\"hub.byted.org/bytendb2b/rds_proxy_sidecar:20221115.1403-epic_v1.1.10-e73d0b\",\"Status\":\"Ready\",\"Cpu\":\"500mC\",\"Mem\":\"2Gi\"},{\"Name\":\"proxy-agent\",\"Port\":\"2335\",\"Image\":\"hub.byted.org/bytendb2b/rds_proxy_agent_sidecar:07383ec561c92137e9b26bc326b5af06\",\"Status\":\"Ready\",\"Cpu\":\"200mC\",\"Mem\":\"500Mi\"}],\"Component\":\"Proxy\"}]}"),
		pods)
	mgrClient.EXPECT().Call(gomock.Any(), rdsModel.Action_ListInstancePods.String(), gomock.Any(), gomock.Any(), gomock.Any()).SetArg(3, *pods).Return(nil).AnyTimes()

	local := &local_dal.LocalAudit{}
	local.Init(ctx, ctrl)

	auditSvc := mocks_audit.NewMockSqlAuditService(ctrl)
	auditSvc.EXPECT().TerminateSqlAuditOrderCallback(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	// auditSvc1 模拟创建失败的场景
	auditSvc.EXPECT().TerminateSqlAuditOrderCallback(gomock.Any(), gomock.Any(), gomock.Any()).Return(consts.ErrorOf(model.ErrorCode_InternalError)).AnyTimes()
	tlsClient := MockTlsClient(ctrl)

	auditSvc.EXPECT().GetTenantTlsClient(gomock.Any(), gomock.Any()).Return(tlsClient, nil).AnyTimes()
	auditSvc.EXPECT().DeleteAuditResource(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	auditSvc.EXPECT().GetTlsClient(gomock.Any(), gomock.Any()).Return(tlsClient, nil).AnyTimes()

	publishEventSvc := mocks.NewMockPublishEventService(ctrl)
	publishEventSvc.EXPECT().PublishEvent(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	publishEventSvc.EXPECT().UpdateEventResult(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	return ctx, cfg, c3Cnf, local, auditSvc, publishEventSvc
}

func TestGetStatisticQuery(t *testing.T) {
	type args struct {
		dsType         model.DSType
		dataType       model.StatisticDataType
		sqlTaskType    model.ScheduleSqlTaskType
		sqlTaskVersion int64
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "",
			args: args{
				dsType:         model.DSType_MySQL,
				dataType:       model.StatisticDataType_SqlDetail,
				sqlTaskType:    0,
				sqlTaskVersion: 1,
			},
			want: "",
		},
		{
			name: "",
			args: args{
				dsType:         model.DSType_MySQL,
				dataType:       model.StatisticDataType_SqlExecCount,
				sqlTaskType:    model.ScheduleSqlTaskType_SqlExecL1,
				sqlTaskVersion: 1,
			},
			want: "* | select '' as instance_id, 'L1' as type, count(*) as count where event_duration <= 1000000 and mysql_sql_template_md5 != ''",
		},
		{
			name: "",
			args: args{
				dsType:         model.DSType_MySQL,
				dataType:       model.StatisticDataType_SqlExecCount,
				sqlTaskType:    model.ScheduleSqlTaskType_SqlExecTotolCount,
				sqlTaskVersion: 1,
			},
			want: "* | select '' as instance_id, 'ALL' as type, COUNT(*) as count where mysql_sql_template_md5 != ''",
		},
		{
			name: "",
			args: args{
				dsType:         model.DSType_MySQL,
				dataType:       model.StatisticDataType_SqlTemplateAggregate,
				sqlTaskType:    model.ScheduleSqlTaskType_SqlTemplateAggregate,
				sqlTaskVersion: 1,
			},
			want: "* | select '' as instance_id,   mysql_sql_template_md5 as sql_fingerprint,   mysql_sql_template as sql_template,   mysql_context_db as context_db,   method,   COUNT(*) as count,     SUM(event_duration) as event_duration_sum,   SUM(mysql_affected_rows) as affected_rows_sum,  SUM(mysql_num_rows) as num_rows_sum,   SUM(server_bytes) as server_bytes_sum  WHERE mysql_sql_template_md5 != ''  group by mysql_sql_template_md5, mysql_sql_template, mysql_context_db, method",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetStatisticQuery("", tt.args.dsType, tt.args.dataType, tt.args.sqlTaskType, tt.args.sqlTaskVersion); got != tt.want {
				t.Errorf("GetStatisticQuery() = %v, want %v", got, tt.want)
			}
		})
	}
}
