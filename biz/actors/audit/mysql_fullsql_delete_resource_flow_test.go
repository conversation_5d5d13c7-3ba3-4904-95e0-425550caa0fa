package audit

import (
	"encoding/json"
	"reflect"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/com"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/location"
	"code.byted.org/infcs/dbw-mgr/biz/service/audit"
	"code.byted.org/infcs/dbw-mgr/biz/service/billing"
	crossauth "code.byted.org/infcs/dbw-mgr/biz/service/cross_service_authorization"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/full_sql"
	"code.byted.org/infcs/dbw-mgr/biz/service/full_sql/zkconfig"
	"code.byted.org/infcs/dbw-mgr/biz/service/project"
	"code.byted.org/infcs/dbw-mgr/biz/service/tag"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls/index"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
)

// Mock-definierte Strukturen für Schnittstellenabhängigkeiten

type MockLocation struct{ location.Location }
type MockDataSourceService struct{ datasource.DataSourceService }
type MockCrossAuthService1 struct {
	crossauth.CrossServiceAuthorizationService
}
type MockAuditTlsDAL struct{ dal.AuditTlsDAL }

type MockTagService struct{ tag.TagService }
type MockProjectService struct{ project.ProjectService }
type MockSqlAuditService struct{ audit.SqlAuditService }
type MockGenTlsService struct{ index.GenTlsService }
type MockTlsDAL struct{ dal.TlsDAL }
type MockLogCollectorInterface struct{ audit.LogCollectorInterface }
type MockBillingService struct{ billing.BillingService }
type MockPublishEventService struct{ com.PublishEventService }
type MockStatisticSqlTlsDal struct{ dal.StatisticSqlTlsDal }
type MockStatisticSqlTaskDal struct{ dal.StatisticSqlTaskDal }
type MockFullSqlService struct{ full_sql.FullSqlService }
type MockFullSqlConfigService struct{ zkconfig.FullSqlConfigService }
type MockInstanceExtraTlsDAL struct{ dal.InstanceExtraTlsDAL }
type MockInstanceExtraNodeDAL struct{ dal.InstanceExtraNodeDAL }

func TestNewFullSqlDeleteResourceLifecycleActor_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("TestNewFullSqlDeleteResourceLifecycleActor", t, func() {
		// Datenkonstruktion für Eingabeparameter
		in := NewAuditCreateLifecycleActorIn{
			Conf:                 &MockConfigProvider{},
			C3Conf:               &MockC3ConfigProvider{},
			Loc:                  &MockLocation{},
			DataSource:           &MockDataSourceService{},
			CrossAuthSvc:         &MockCrossAuthService1{},
			AuditTlsDAL:          &MockAuditTlsDAL{},
			FullSqlInstDAL:       &MockFullSqlDAL{},
			TagSvc:               &MockTagService{},
			ProjectSvc:           &MockProjectService{},
			AuditService:         &MockSqlAuditService{},
			GenTlsSvc:            &MockGenTlsService{},
			TlsDAL:               &MockTlsDAL{},
			LCSvc:                &MockLogCollectorInterface{},
			BillSvc:              &MockBillingService{},
			PublishEventSvc:      &MockPublishEventService{},
			StatisticSqlTlsDAL:   &MockStatisticSqlTlsDal{},
			StatisticSqlTaskDAL:  &MockStatisticSqlTaskDal{},
			FullSqlSvc:           &MockFullSqlService{},
			FullSqlConfigService: &MockFullSqlConfigService{},
			InstanceExtraTlsDAL:  &MockInstanceExtraTlsDAL{},
			InstanceExtraNodeDAL: &MockInstanceExtraNodeDAL{},
		}

		mockey.PatchConvey("Erfolgsszenario: Korrekte Erstellung des Actor-Producers ohne initialen Zustand", func() {
			// Szenariobeschreibung:
			// Überprüfen, ob die Funktion einen VirtualPersistenceProducer korrekt erstellt, wenn alle Abhängigkeiten bereitgestellt werden.
			// Datenkonstruktion:
			// - `in` ist mit allen erforderlichen Mock-Abhängigkeiten gefüllt.
			// Logische Kette:
			// 1. Rufen Sie NewFullSqlDeleteResourceLifecycleActor mit der konstruierten `in` auf.
			// 2. Überprüfen Sie, ob der zurückgegebene Producer nicht nil ist und das Kind-Feld korrekt ist.
			// 3. Rufen Sie die Producer-Funktion auf, um einen Actor zu erstellen.
			// 4. Überprüfen Sie, ob der erstellte Actor vom richtigen Typ ist (*AuditLifecycleActor).
			// 5. Überprüfen Sie, ob die Felder des Actors (Abhängigkeiten) korrekt aus dem Eingabeparameter `in` initialisiert wurden.
			// 6. Überprüfen Sie, ob die idempotentSteps korrekt auf deleteFullSqlResourceSteps gesetzt sind.
			// 7. Überprüfen Sie, ob der Zustand des Actors leer ist, wenn nil-Zustandsbytes bereitgestellt werden.

			// Aufruf
			producer := NewFullSqlDeleteResourceLifecycleActor(in)

			// Behauptung
			convey.So(producer, convey.ShouldNotBeNil)
			convey.So(producer.Kind, convey.ShouldEqual, consts.MysqlFullSqlDeleteResourceActorKind)
			convey.So(producer.Producer, convey.ShouldNotBeNil)

			// Erstellen Sie einen Actor mit dem Producer
			actor := producer.Producer.Spawn("kind", "name", nil)

			// Behauptung für den erstellten Actor
			convey.So(actor, convey.ShouldNotBeNil)
			lifecycleActor, ok := actor.(*AuditLifecycleActor)
			convey.So(ok, convey.ShouldBeTrue)

			// Überprüfen Sie, ob alle Felder korrekt initialisiert wurden
			convey.So(lifecycleActor.source, convey.ShouldEqual, in.DataSource)
			convey.So(lifecycleActor.crossAuthSvc, convey.ShouldEqual, in.CrossAuthSvc)
			convey.So(lifecycleActor.conf, convey.ShouldEqual, in.Conf)
			convey.So(lifecycleActor.c3Conf, convey.ShouldEqual, in.C3Conf)
			convey.So(lifecycleActor.loc, convey.ShouldEqual, in.Loc)
			convey.So(lifecycleActor.tagSvc, convey.ShouldEqual, in.TagSvc)
			convey.So(lifecycleActor.projectSvc, convey.ShouldEqual, in.ProjectSvc)
			convey.So(lifecycleActor.tlsDAL, convey.ShouldEqual, in.TlsDAL)
			convey.So(lifecycleActor.lcSvc, convey.ShouldEqual, in.LCSvc)
			convey.So(lifecycleActor.billSvc, convey.ShouldEqual, in.BillSvc)
			convey.So(lifecycleActor.publishEventSvc, convey.ShouldEqual, in.PublishEventSvc)
			convey.So(lifecycleActor.fullSqlInstDAL, convey.ShouldEqual, in.FullSqlInstDAL)
			convey.So(lifecycleActor.fullSqlSvc, convey.ShouldEqual, in.FullSqlSvc)
			convey.So(lifecycleActor.fullSqlConfigService, convey.ShouldEqual, in.FullSqlConfigService)
			convey.So(lifecycleActor.instanceExtraTlsDAL, convey.ShouldEqual, in.InstanceExtraTlsDAL)
			convey.So(reflect.DeepEqual(lifecycleActor.idempotentSteps, deleteFullSqlResourceSteps), convey.ShouldBeTrue)
			convey.So(lifecycleActor.state, convey.ShouldResemble, &AuditLifeState{})
		})

		mockey.PatchConvey("Erfolgsszenario: Korrekte Erstellung des Actors mit initialem Zustand", func() {
			// Szenariobeschreibung:
			// Überprüfen, ob der erstellte Actor seinen Zustand korrekt aus den bereitgestellten Bytes deserialisiert.
			// Datenkonstruktion:
			// - `in` ist mit allen erforderlichen Mock-Abhängigkeiten gefüllt.
			// - Ein `AuditLifeState`-Objekt wird erstellt und in JSON serialisiert.
			// Logische Kette:
			// 1. Rufen Sie NewFullSqlDeleteResourceLifecycleActor auf.
			// 2. Rufen Sie die Producer-Funktion mit den serialisierten Zustandsbytes auf.
			// 3. Überprüfen Sie, ob der Zustand des resultierenden Actors mit dem ursprünglichen Zustandsobjekt übereinstimmt.

			// Datenkonstruktion für den Zustand
			initialState := &AuditLifeState{
				InstanceId:     "test-instance-123",
				InstanceStatus: model.AuditStatus_Deleting,
				TenantId:       "test-tenant-abc",
			}
			stateBytes, err := json.Marshal(initialState)
			convey.So(err, convey.ShouldBeNil)

			// Aufruf
			producer := NewFullSqlDeleteResourceLifecycleActor(in)
			actor := producer.Producer.Spawn("kind", "name", stateBytes)

			// Behauptung
			convey.So(actor, convey.ShouldNotBeNil)
			lifecycleActor, ok := actor.(*AuditLifecycleActor)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(lifecycleActor.state, convey.ShouldResemble, initialState)
		})
	})
}
