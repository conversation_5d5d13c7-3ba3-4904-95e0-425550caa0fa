package abnormal_detection

import (
	"fmt"
	"testing"
	"time"

	config2 "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/dslibmocks"
	"code.byted.org/infcs/ds-lib/common/log"

	//"code.byted.org/infcs/ds-lib/framework/metrics"
	"code.byted.org/infcs/protoactor-go/actor"
	"github.com/Shopify/sarama"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
)

func mockDetectionConsumerActor() *DetectionConsumerActor {
	return &DetectionConsumerActor{
		state:          &DetectionConsumerActorState{},
		cnf:            &config.MockConfigProvider{},
		repo:           &repository.AbnormalDetectionRepoImpl{},
		actorClient:    &dslibmocks.MockActorClient{},
		c3ConfProvider: &config.MockC3ConfigProvider{},
		idSvc:          &mocks.MockService{},
		ds:             &mocks.MockDataSourceService{},
		consumerState:  &ConsumerState{},
	}
}

func TestOnStart(t *testing.T) {
	mock1 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer mock2.UnPatch()

	consumerActor := mockDetectionConsumerActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	consumerActor.OnStart(&mocks.MockContext{}, &actor.Started{})
}

func TestInit(t *testing.T) {
	consumerActor := mockDetectionConsumerActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	mock1 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer mock2.UnPatch()

	mock3 := mockey.Mock((*DetectionConsumerActor).initConsumer).Return(nil).Build()
	consumerActor.init(&mocks.MockContext{})
	mock3.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock((*DetectionConsumerActor).initConsumer).Return(fmt.Errorf("test")).Build()
	defer mock4.UnPatch()
	consumerActor.init(&mocks.MockContext{})
}
func TestInitConsumer(t *testing.T) {
	consumerActor := mockDetectionConsumerActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*config.MockC3ConfigProvider).GetNamespace).Return(&config2.C3Config{
		Application: config2.Application{},
	}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock(sarama.NewConsumerGroup).Return(nil, fmt.Errorf("test")).Build()
	mock3 := mockey.Mock((*config.MockConfigProvider).Get).Return(&config2.Config{}).Build()
	defer mock3.UnPatch()
	err := consumerActor.initConsumer(&mocks.MockContext{})
	assert.NotNil(t, err)
	mock2.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock4 := mockey.Mock(sarama.NewConsumerGroup).Return(nil, nil).Build()
	defer mock4.UnPatch()
	err = consumerActor.initConsumer(&mocks.MockContext{})
	assert.Nil(t, err)
}

func TestReInitConsumer(t *testing.T) {
	consumerActor := mockDetectionConsumerActor()
	mock1 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer mock2.UnPatch()

	consumerActor.reInitConsumer(&mocks.MockContext{})
}

func TestNewDetectionConsumerActor(t *testing.T) {

	in := NewDetectionConsumerActorIn{
		Cnf:            &config.MockConfigProvider{},
		Repo:           &repository.AbnormalDetectionRepoImpl{},
		ActorClient:    &dslibmocks.MockActorClient{},
		C3ConfProvider: &config.MockC3ConfigProvider{},
		IdSvc:          &mocks.MockService{},
		Ds:             &mocks.MockDataSourceService{},
	}
	_ = NewDetectionConsumerActor(in)
}

func TestConsumeEvent(t *testing.T) {
	consumerActor := mockDetectionConsumerActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	consumerActor.ConsumeEvent(&mocks.MockContext{})
	mock1 := mockey.Mock((*config.MockConfigProvider).Get).Return(&config2.Config{}).Build()
	defer mock1.UnPatch()
	consumerActor.consumerState.consumerGroup = &MockConsumerGroup{}
	mock2 := mockey.Mock((*DetectionConsumerActor).initConsumerGroupHandler).Return(nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*MockConsumerGroup).Consume).Return(fmt.Errorf("test")).Build()
	defer mock3.UnPatch()
	mock4 := mockey.Mock((*DetectionConsumerActor).reInitConsumer).Return().Build()
	defer mock4.UnPatch()
	consumerActor.ConsumeEvent(&mocks.MockContext{})

}
