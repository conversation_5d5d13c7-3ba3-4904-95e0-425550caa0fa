package abnormal_detection

import (
	"fmt"
	"testing"
	"time"

	config2 "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/metric_data"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/dslibmocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"github.com/volcengine/volc-sdk-golang/service/tls"
)

func mockDetectionProducerActor() *DetectionProducerActor {
	return &DetectionProducerActor{
		state:          &DetectionProducerActorState{},
		cnf:            &config.MockConfigProvider{},
		repo:           &repository.AbnormalDetectionRepoImpl{},
		actorClient:    &dslibmocks.MockActorClient{},
		c3ConfProvider: &config.MockC3ConfigProvider{},
		loca:           &mocks.MockLocation{},
		idSvc:          &mocks.MockService{},
		ds:             &mocks.MockDataSourceService{},
		metricItem:     mockRootMetricDataItem(),
	}
}

func mockRootMetricDataItem() metric_data.RootMetricDataItem {
	items := make(map[string]metric_data.MetricDataItem)
	items[model.MetricDataItem_CpuUsage.String()] = &metric_data.CpuUsage{}
	return metric_data.RootMetricDataItem{
		Items: items,
	}
}

func mockDetectionConfig() *entity.DbwAbnormalDetectionConfig {
	return &entity.DbwAbnormalDetectionConfig{
		ConfigId:     1,
		InstanceId:   "instanceId",
		InstanceType: "MySQL",
		RegionId:     "test",
		TenantId:     "1",
		Enable:       1,
	}
}

func mockDetectionMetricInfos() []*DetectionMetricInfo {
	return []*DetectionMetricInfo{{
		MetricName:  model.MetricDataItem_CpuUsage.String(),
		ServerId:    "instanceId",
		MetricValue: 1,
		Timestamp:   12345,
	}}
}

func TestDoWhenIdle(t *testing.T) {
	actor := mockDetectionProducerActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	mockConfig := []*entity.DbwAbnormalDetectionConfig{mockDetectionConfig()}

	mock1 := mockey.Mock((*repository.AbnormalDetectionRepoImpl).GetEnableDetectionConfigs).Return(mockConfig, fmt.Errorf("error")).Build()
	actor.doWhenIdle(&mocks.MockContext{})
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*repository.AbnormalDetectionRepoImpl).GetEnableDetectionConfigs).Return(mockConfig, fmt.Errorf("error")).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*DetectionProducerActor).detection).Return().Build()
	defer mock3.UnPatch()

	actor.doWhenIdle(&mocks.MockContext{})
}

func TestDetection(t *testing.T) {
	actor := mockDetectionProducerActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	currentTime := time.Now().Unix()

	mock1 := mockey.Mock((*DetectionProducerActor).collectData).Return(nil, fmt.Errorf("test")).Build()
	actor.detection(&mocks.MockContext{}, mockDetectionConfig(), currentTime)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*DetectionProducerActor).collectData).Return(nil, nil).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*DetectionProducerActor).ProducerMetricData).Return().Build()
	defer mock3.UnPatch()
	actor.detection(&mocks.MockContext{}, mockDetectionConfig(), currentTime)
}

func TestCollectData(t *testing.T) {
	actor := mockDetectionProducerActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	currentTime := time.Now().Unix()

	mock1 := mockey.Mock((*DetectionProducerActor).getItemMetricInfo).Return(nil, fmt.Errorf("test")).Build()
	req, err := actor.formatReq(&mocks.MockContext{}, mockDetectionConfig(), currentTime)
	if err != nil {
		assert.Fail(t, "")
	}
	_, err = actor.collectData(&mocks.MockContext{}, req, currentTime)
	assert.NotNil(t, err)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*DetectionProducerActor).getItemMetricInfo).Return(&DetectionMetricInfo{Timestamp: 1, MetricValue: 1}, nil).Build()
	defer mock2.UnPatch()

	res, err := actor.collectData(&mocks.MockContext{}, req, currentTime)
	assert.Nil(t, err)
	assert.Equal(t, len(res), 1)
	assert.Equal(t, res[0].ServerId, "instanceId")
	assert.Equal(t, res[0].MetricValue, float64(1))
}

func TestGetItemMetricInfo(t *testing.T) {
	actor := mockDetectionProducerActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	currentTime := time.Now().Unix()

	req, err := actor.formatReq(&mocks.MockContext{}, mockDetectionConfig(), currentTime)
	if err != nil {
		assert.Fail(t, "")
	}
	mock1 := mockey.Mock((*metric_data.CpuUsage).Avg).Return(float64(1), fmt.Errorf("test")).Build()
	_, err = actor.getItemMetricInfo(&mocks.MockContext{}, actor.metricItem.Items[model.MetricDataItem_CpuUsage.String()], req)
	assert.NotNil(t, err)
	mock1.UnPatch()

	time.Sleep(100 * time.Millisecond)
	mock2 := mockey.Mock((*metric_data.CpuUsage).Avg).Return(float64(1), nil).Build()
	defer mock2.UnPatch()

	res, err := actor.getItemMetricInfo(&mocks.MockContext{}, actor.metricItem.Items[model.MetricDataItem_CpuUsage.String()], req)
	assert.Nil(t, err)
	assert.Equal(t, res.MetricName, model.MetricDataItem_CpuUsage.String())
	assert.Equal(t, res.MetricValue, float64(1))
}

func TestProducerMetricData(t *testing.T) {
	actor := mockDetectionProducerActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*DetectionProducerActor).ProducerToTls).Return().Build()
	defer mock1.UnPatch()
	actor.ProducerMetricData(&mocks.MockContext{}, []*DetectionMetricInfo{{MetricValue: 1}}, mockDetectionConfig())
	mock2 := mockey.Mock((*DetectionProducerActor).isInstanceExist).Return(false).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*repository.AbnormalDetectionRepoImpl).DeleteDetectionConfig).Return(nil).Build()
	defer mock3.UnPatch()
	actor.ProducerMetricData(&mocks.MockContext{}, []*DetectionMetricInfo{}, mockDetectionConfig())
}

func TestProducerToTls(t *testing.T) {
	actor := mockDetectionProducerActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*config.MockC3ConfigProvider).GetNamespace).Return(&config2.C3Config{
		Application: config2.Application{},
	}).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*config.MockConfigProvider).Get).Return(&config2.Config{}).Build()
	defer mock2.UnPatch()
	mock3 := mockey.Mock((*tls.LsClient).PutLogs).Return(nil, fmt.Errorf("test")).Build()
	defer mock3.UnPatch()
	actor.ProducerToTls(&mocks.MockContext{}, mockDetectionMetricInfos(), 123, "instanceId")
}

func TestIsInstanceExist(t *testing.T) {
	actor := mockDetectionProducerActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	resp := &datasource.ListInstanceResp{}
	mock1 := mockey.Mock((*mocks.MockDataSourceService).ListInstance).Return(resp, fmt.Errorf("error")).Build()
	res := actor.isInstanceExist(&mocks.MockContext{}, mockDetectionConfig())
	assert.True(t, res)
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*mocks.MockDataSourceService).ListInstance).Return(resp, nil).Build()
	defer mock2.UnPatch()
	res = actor.isInstanceExist(&mocks.MockContext{}, mockDetectionConfig())
	assert.False(t, res)
	resp.Total = 1
	res = actor.isInstanceExist(&mocks.MockContext{}, mockDetectionConfig())
	assert.True(t, res)
}

func TestDsTypeStringToDatasourceType(t *testing.T) {
	actor := mockDetectionProducerActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	_, err := actor.dsTypeStringToDatasourceType("MY")
	assert.NotNil(t, err)
	_, err = actor.dsTypeStringToDatasourceType("MySQL")
	assert.Nil(t, err)
}
