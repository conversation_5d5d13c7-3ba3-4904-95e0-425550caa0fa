package command

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"go.uber.org/dig"
	"sync"
	"time"
)

type NewCommandSetResultBufferActorIn struct {
	dig.In
	CmdRepo repository.CommandRepo
	Config  config.ConfigProvider
}

func NewCommandSetResultBufferActor(p NewCommandSetResultBufferActorIn) types.VirtualProducer {
	return types.VirtualProducer{
		Kind: consts.CommandSetResultBufferActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			return &CommandSetResultBufferActor{
				cnf:     p.Config,
				cmdRepo: p.CmdRepo,
			}
		}),
	}
}

type CommandSetResultBufferActor struct {
	cnf     config.ConfigProvider
	cmdRepo repository.CommandRepo
	buffer  *shared.ResultObjects
	mu      sync.RWMutex
	state   *bufferState
}

type bufferState struct {
	createdAt    time.Time
	hasResponded bool
}

const (
	TimeIntervalSeconds int64 = 10
	MaxAliveDuration          = 5 * time.Minute
)

func (a *CommandSetResultBufferActor) Process(ctx types.Context) {
	defer ctx.SetReceiveTimeout(time.Duration(TimeIntervalSeconds) * time.Second)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		a.onStart(ctx)
	case *actor.Stopped:
		log.Info(ctx, "actor stopped for commandset: %s", ctx.GetName())
		ctx.Stop(ctx.Self())
	case *actor.ReceiveTimeout:
		a.doWhenIdle(ctx)
	case *shared.ResultObjects:
		log.Info(ctx, "Receive *shared.ResultObjects message.")
		a.BufferResult(ctx, msg)
	case *shared.FetchBufferedResults:
		a.GetResults(ctx, msg)
	default:
		log.Warn(ctx, "CommandSetResultBufferActor received unhandled message type: %T", msg)
	}
}

func (a *CommandSetResultBufferActor) GetCommandSetID(ctx types.Context) string {
	return ctx.GetName()
}

func (a *CommandSetResultBufferActor) onStart(ctx types.Context) {
	log.Info(ctx, "CommandSetResultBufferActor started for commandset: %s", ctx.GetName())
	a.state = &bufferState{
		createdAt:    time.Now(),
		hasResponded: false,
	}
	_, err := a.cmdRepo.GetCommandSet(ctx, a.GetCommandSetID(ctx))
	if err != nil {
		log.Info(ctx, "command set %s not found yet, will wait. err=%v", a.GetCommandSetID(ctx), err)
		return
	}
}

func (a *CommandSetResultBufferActor) BufferResult(ctx types.Context, resultObjs *shared.ResultObjects) {
	if resultObjs == nil {
		log.Warn(ctx, "CommandSetResultBufferActor: shared.ResultObjects is nil, ignore buffer it.")
		return
	}
	resultObj := resultObjs.Results
	if len(resultObj) == 0 {
		log.Warn(ctx, "CommandSetResultBufferActor: No content in shared.ResultObject, ignore buffer it.")
		return
	}
	if a.state == nil {
		log.Warn(ctx, "unexpected nil bufferState, reinitializing")
		a.state = &bufferState{
			createdAt:    time.Now(),
			hasResponded: false,
		}
	}
	a.state.createdAt = time.Now()
	a.state.hasResponded = false

	log.Info(ctx, "get result objects, buffer to memory, obj length is %d", len(resultObj))
	a.mu.Lock()
	a.buffer = resultObjs
	a.mu.Unlock()

	ctx.Respond(&shared.OK{})
}

func (a *CommandSetResultBufferActor) GetResults(ctx types.Context, msg *shared.FetchBufferedResults) {
	if a.state == nil {
		log.Warn(ctx, "buffer state not initialized")
		ctx.Respond(&shared.ErrNoBuffer{
			ErrorMessage: "buffer state not initialized",
		})
		return
	}

	if a.state.hasResponded {
		log.Info(ctx, "buffer results have already been returned once, skipping redundant response")
		ctx.Respond(&shared.ErrNoBuffer{
			ErrorMessage: "CommandSet Results have been retrieved. To fetch the data, please rerun DataExecCommandSetAsync",
		})
		return
	}

	if a.buffer != nil {
		ctx.Respond(a.buffer)
		a.mu.Lock()
		a.buffer = nil
		a.mu.Unlock()
		a.state.hasResponded = true
		// ctx.Send(ctx.Self(), &actor.Stopped{})
		return
	}

	ctx.Respond(&shared.ErrNoBuffer{
		ErrorMessage: "No results found now, pls wait commands been executed or command set results have been retrieved or expired, you cannot fetch it again.",
	})
}

func (a *CommandSetResultBufferActor) doWhenIdle(ctx types.Context) {
	if a.state != nil && time.Since(a.state.createdAt) > MaxAliveDuration {
		log.Info(ctx, "idle timeout over %v, shutting down.", MaxAliveDuration)
		a.state.hasResponded = true
		a.mu.Lock()
		a.buffer = nil
		a.mu.Unlock()
		ctx.Send(ctx.Self(), &actor.Stopped{})
	}
}
