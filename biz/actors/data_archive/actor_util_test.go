package data_archive

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/dslibmocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/repository"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/sqltask"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"fmt"
	"testing"
	"time"
)

func TestGetTimeCase(t *testing.T) {
	nowTime := time.Now().Unix()
	fmt.Println(nowTime)
	offsetTime := int64(86400)
	timeFormat := "yyyy-mm-dd hh:MM:ss"

	res := getTimeCase(time.Now().Unix(), 86400, "yyyy-mm-dd hh:MM:ss", "create_time")
	fmt.Println(res)
	res = getTimeCase(time.Now().Unix(), 0, "yyyy-mm-dd hh:MM:ss", "create_time")
	fmt.Println(res)

	datetime := time.Unix(nowTime-offsetTime, 0)
	fmt.Println(datetime.Unix())
	fmt.Println(getTimeLayout(timeFormat))
}

func TestFormatDeleteSqlWhereCase(t *testing.T) {
	archiveConfig := &entity.ArchiveConfig{
		BizTimes: []*model.TimeInfo{{TimeColumn: "create_time", TimeFormat: "yyyy-mm-dd hh:MM:ss", OffsetTime: -86400}},
	}
	res := FormatDeleteSqlWhereCase(**********, archiveConfig)
	fmt.Println(res)
}

func mockDataArchiveActor() *DataArchiveActor {
	return &DataArchiveActor{
		state:          &DataArchiveActorState{ArchiveTask: &entity.ArchiveTask{}, ArchiveConfig: &entity.ArchiveConfig{}},
		cnf:            &config.MockConfigProvider{},
		idgenSvc:       &mocks.MockService{},
		ds:             &mocks.MockDataSourceService{},
		loca:           &mocks.MockLocation{},
		c3ConfProvider: &config.MockC3ConfigProvider{},
		sqlTaskService: &sqltask.MockSqlTaskService{},
		archiveRepo:    &repository.MockDataArchiveRepo{},
		workflowDal:    &mocks.MockWorkflowDAL{},
		actorClient:    &dslibmocks.MockActorClient{},
	}
}

func mockDataArchiveConfigActor() *ArchiveConfigActor {
	return &ArchiveConfigActor{
		state:       &ArchiveConfigActorState{ArchiveConfig: &entity.ArchiveConfig{}},
		cnf:         &config.MockConfigProvider{},
		idSvc:       &mocks.MockService{},
		ds:          &mocks.MockDataSourceService{},
		archiveRepo: &repository.MockDataArchiveRepo{},
		workflowDal: &mocks.MockWorkflowDAL{},
		actorClient: &dslibmocks.MockActorClient{},
	}
}
