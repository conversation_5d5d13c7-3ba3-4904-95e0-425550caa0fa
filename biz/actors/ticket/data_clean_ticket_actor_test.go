package ticket

import (
	"context"
	"errors"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"go.uber.org/dig"
	"time"
)

// Mock_WorkflowDAL_processExecuteTicketAction mocks the dal.WorkflowDAL interface.
type Mock_WorkflowDAL_processExecuteTicketAction struct {
	dal.WorkflowDAL
}

// DescribeByTicketID is a mock implementation.
func (m *Mock_WorkflowDAL_processExecuteTicketAction) DescribeByTicketID(ctx context.Context, ticketId int64) (*dao.Ticket, error) {
	// This method will be mocked by mockey.
	return nil, nil
}

// Mock_Context_processExecuteTicketAction mocks the types.Context interface.
type Mock_Context_processExecuteTicketAction struct {
	types.Context
}

// GetName is a mock implementation.
func (m *Mock_Context_processExecuteTicketAction) GetName() string {
	// This method will be mocked by mockey.
	return ""
}

// Self is a mock implementation.
func (m *Mock_Context_processExecuteTicketAction) Self() *actor.PID {
	// This method will be mocked by mockey.
	return nil
}

// Send is a mock implementation.
func (m *Mock_Context_processExecuteTicketAction) Send(pid *actor.PID, message interface{}) {
	// This method will be mocked by mockey.
}

func Test_DataCleanTicketActor_processExecuteTicketAction_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("Test_DataCleanTicketActor_processExecuteTicketAction", t, func() {
		// Common setup
		mockWorkflowDal := &Mock_WorkflowDAL_processExecuteTicketAction{}
		mockCtx := &Mock_Context_processExecuteTicketAction{}
		e := &DataCleanTicketActor{
			workflowDal: mockWorkflowDal,
		}

		mockey.Mock(log.Info).Return().Build()
		mockey.Mock(log.Warn).Return().Build()

		mockey.PatchConvey("成功场景 - actor重启后重新执行任务", func() {
			// 场景描述：
			// 当actor重启并且state中的CurrentAction为ReceiveCommand时，函数应该能从数据库中获取工单信息，并向自身发送ExecTicket消息以重新触发执行流程。
			// 数据构造：
			// - actor.state.CurrentAction = model.ExecDataCleanTicketAction_ReceiveCommand
			// - actor.state.Ds 不为 nil
			// - mock context.GetName() 返回一个有效的工单ID字符串
			// - mock workflowDal.DescribeByTicketID 返回一个有效的工单实体
			// 逻辑链路：
			// 1. 函数进入 ReceiveCommand case。
			// 2. 检查 e.state.Ds 不为 nil，条件通过。
			// 3. 调用 ctx.GetName() 获取工单ID。
			// 4. 调用 e.workflowDal.DescribeByTicketID 获取工单详情。
			// 5. 调用 ctx.Self() 获取自身PID。
			// 6. 调用 ctx.Send() 发送 ExecTicket 消息。

			// 数据构造
			e.state = &DataCleanTicketState{
				CurrentAction: model.ExecDataCleanTicketAction_ReceiveCommand,
				Ds:            &shared.DataSource{Address: "test-addr"},
				TenantId:      "test-tenant",
				UserId:        "test-user",
			}
			ticketIDStr := "12345"
			ticketID := int64(12345)
			selfPID := actor.NewPID("local", "self-actor")
			expectedTicket := &dao.Ticket{
				TicketType:          1,
				ExecuteType:         2,
				ExecutableStartTime: 1672531200, // 2023-01-01 00:00:00
				ExecutableEndTime:   1672617600, // 2023-01-02 00:00:00
			}
			var sentMessage interface{}

			// Mock
			mockey.Mock((*Mock_Context_processExecuteTicketAction).GetName).Return(ticketIDStr).Build()
			mockey.Mock((*Mock_WorkflowDAL_processExecuteTicketAction).DescribeByTicketID).
				When(func(_ context.Context, id int64) bool {
					return id == ticketID
				}).Return(expectedTicket, nil).Build()
			mockey.Mock((*Mock_Context_processExecuteTicketAction).Self).Return(selfPID).Build()
			mockey.Mock((*Mock_Context_processExecuteTicketAction).Send).To(func(_ *actor.PID, msg interface{}) {
				sentMessage = msg
			}).Build()

			// 调用
			e.processExecuteTicketAction(mockCtx)

			// 断言
			convey.So(sentMessage, convey.ShouldNotBeNil)
			execTicketMsg, ok := sentMessage.(*shared.ExecTicket)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(execTicketMsg.Source, convey.ShouldEqual, e.state.Ds)
			convey.So(execTicketMsg.TenantID, convey.ShouldEqual, e.state.TenantId)
			convey.So(execTicketMsg.UserID, convey.ShouldEqual, e.state.UserId)
			convey.So(execTicketMsg.TicketType, convey.ShouldEqual, int32(expectedTicket.TicketType))
			convey.So(execTicketMsg.ExecuteType, convey.ShouldEqual, int32(expectedTicket.ExecuteType))
			convey.So(execTicketMsg.ExecutableStartTime, convey.ShouldEqual, int32(expectedTicket.ExecutableStartTime))
			convey.So(execTicketMsg.ExecutableEndTime, convey.ShouldEqual, int32(expectedTicket.ExecutableEndTime))
		})

		mockey.PatchConvey("失败场景 - 数据库查询工单失败", func() {
			// 场景描述：
			// 在重新执行任务时，如果通过workflowDal从数据库获取工单信息失败，函数应该向自身发送一个SuicideMessage，终止actor。
			// 数据构造：
			// - actor.state.CurrentAction = model.ExecDataCleanTicketAction_ReceiveCommand
			// - actor.state.Ds 不为 nil
			// - mock workflowDal.DescribeByTicketID 返回一个error
			// 逻辑链路：
			// 1. 函数进入 ReceiveCommand case。
			// 2. 检查 e.state.Ds 不为 nil，条件通过。
			// 3. 调用 e.workflowDal.DescribeByTicketID，返回错误。
			// 4. 函数记录警告日志。
			// 5. 调用 ctx.Self() 获取自身PID。
			// 6. 调用 ctx.Send() 发送 SuicideMessage 消息。

			// 数据构造
			e.state = &DataCleanTicketState{
				CurrentAction: model.ExecDataCleanTicketAction_ReceiveCommand,
				Ds:            &shared.DataSource{},
			}
			dbErr := errors.New("database connection failed")
			selfPID := actor.NewPID("local", "self-actor")
			var sentMessage interface{}

			// Mock
			mockey.Mock((*Mock_Context_processExecuteTicketAction).GetName).Return("12345").Build()
			mockey.Mock((*Mock_WorkflowDAL_processExecuteTicketAction).DescribeByTicketID).Return(nil, dbErr).Build()
			mockey.Mock((*Mock_Context_processExecuteTicketAction).Self).Return(selfPID).Build()
			mockey.Mock((*Mock_Context_processExecuteTicketAction).Send).To(func(_ *actor.PID, msg interface{}) {
				sentMessage = msg
			}).Build()

			// 调用
			e.processExecuteTicketAction(mockCtx)

			// 断言
			convey.So(sentMessage, convey.ShouldNotBeNil)
			suicideMsg, ok := sentMessage.(*proto.SuicideMessage)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(suicideMsg, convey.ShouldResemble, &proto.SuicideMessage{})
		})

		mockey.PatchConvey("边界场景 - state.Ds为nil", func() {
			// 场景描述：
			// 如果actor重启后，state不完整（例如Ds字段为nil），则函数应该直接退出，不执行任何操作。
			// 数据构造：
			// - actor.state.CurrentAction = model.ExecDataCleanTicketAction_ReceiveCommand
			// - actor.state.Ds 为 nil
			// 逻辑链路：
			// 1. 函数进入 ReceiveCommand case。
			// 2. 检查 e.state != nil && e.state.Ds != nil，条件不通过。
			// 3. 函数直接返回，不执行后续逻辑。

			// 数据构造
			e.state = &DataCleanTicketState{
				CurrentAction: model.ExecDataCleanTicketAction_ReceiveCommand,
				Ds:            nil, // Ds is nil
			}

			// Mock
			sendMock := mockey.Mock((*Mock_Context_processExecuteTicketAction).Send).Return().Build()

			// 调用
			e.processExecuteTicketAction(mockCtx)

			// 断言
			convey.So(sendMock.Times(), convey.ShouldEqual, 0)
		})

		mockey.PatchConvey("边界场景 - CurrentAction不是ReceiveCommand", func() {
			// 场景描述：
			// 如果actor的CurrentAction不是ReceiveCommand，则switch语句没有匹配的case，函数不执行任何操作。
			// 数据构造：
			// - actor.state.CurrentAction 设置为非ReceiveCommand的值
			// 逻辑链路：
			// 1. 函数进入switch语句。
			// 2. 没有匹配的case，函数直接结束。

			// 数据构造
			e.state = &DataCleanTicketState{
				CurrentAction: model.ExecDataCleanTicketAction_Started, // Not ReceiveCommand
				Ds:            &shared.DataSource{},
			}

			// Mock
			sendMock := mockey.Mock((*Mock_Context_processExecuteTicketAction).Send).Return().Build()

			// 调用
			e.processExecuteTicketAction(mockCtx)

			// 断言
			convey.So(sendMock.Times(), convey.ShouldEqual, 0)
		})
	})
}

// Mock_Context_DataCleanTicketActor_BuildCtx is a mock structure for the types.Context interface,
// specifically for testing the BuildCtx method of DataCleanTicketActor.
type Mock_Context_DataCleanTicketActor_BuildCtx struct {
	types.Context
}

// GetName is a mock implementation of the GetName method.
func (m *Mock_Context_DataCleanTicketActor_BuildCtx) GetName() string {
	// This is a placeholder, the actual behavior is controlled by mockey.
	return ""
}

// WithValue is a mock implementation of the WithValue method.
func (m *Mock_Context_DataCleanTicketActor_BuildCtx) WithValue(key, val interface{}) {
	// This is a placeholder, the actual behavior is controlled by mockey.
}
func Test_DataCleanTicketActor_BuildCtx_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("Test_DataCleanTicketActor_BuildCtx", t, func() {
		// 场景描述：测试当state中包含所有ID(LogID, TenantId, UserId)时，能否正确构建BizContext
		// 数据构造：
		// - DataCleanTicketActor的state字段被初始化
		// - state.LogID, state.TenantId, state.UserId 均有值
		// 逻辑链路：
		// 1. Mock types.Context的WithValue方法，以捕获并验证传入的BizContext
		// 2. 调用目标函数 BuildCtx
		// 3. 在WithValue的mock实现中，断言BizContext的字段值与state中的值一致
		mockey.PatchConvey("success: all ids are present in state", func() {
			actor1 := &DataCleanTicketActor{
				state: &DataCleanTicketState{
					LogID:    "state-log-id",
					TenantId: "state-tenant-id",
					UserId:   "state-user-id",
				},
			}
			mockCtx := &Mock_Context_DataCleanTicketActor_BuildCtx{}

			mockey.Mock((*Mock_Context_DataCleanTicketActor_BuildCtx).WithValue).To(func(ctx *Mock_Context_DataCleanTicketActor_BuildCtx, key, val interface{}) {
				convey.So(key, convey.ShouldEqual, "biz-context")
				bizCtx, ok := val.(*fwctx.BizContext)
				convey.So(ok, convey.ShouldBeTrue)
				convey.So(bizCtx.LogID, convey.ShouldEqual, "state-log-id")
				convey.So(bizCtx.TenantID, convey.ShouldEqual, "state-tenant-id")
				convey.So(bizCtx.UserID, convey.ShouldEqual, "state-user-id")
			}).Build()

			actor1.BuildCtx(mockCtx)
		})

		// 场景描述：测试当state中缺少LogID时，能否通过调用ctx.GetName()来生成LogID
		// 数据构造：
		// - DataCleanTicketActor的state字段被初始化
		// - state.LogID 为空字符串
		// - state.TenantId, state.UserId 有值
		// 逻辑链路：
		// 1. Mock types.Context的GetName方法，使其返回一个预设的名称
		// 2. Mock types.Context的WithValue方法，以捕获并验证传入的BizContext
		// 3. 调用目标函数 BuildCtx
		// 4. 在WithValue的mock实现中，断言BizContext.LogID是根据GetName的返回值生成的
		mockey.PatchConvey("success: LogID is missing in state, should use ctx.GetName()", func() {
			actor1 := &DataCleanTicketActor{
				state: &DataCleanTicketState{
					LogID:    "", // LogID is empty
					TenantId: "state-tenant-id",
					UserId:   "state-user-id",
				},
			}
			mockCtx := &Mock_Context_DataCleanTicketActor_BuildCtx{}

			mockey.Mock((*Mock_Context_DataCleanTicketActor_BuildCtx).GetName).Return("test-ctx-name").Build()
			mockey.Mock((*Mock_Context_DataCleanTicketActor_BuildCtx).WithValue).To(func(ctx *Mock_Context_DataCleanTicketActor_BuildCtx, key, val interface{}) {
				convey.So(key, convey.ShouldEqual, "biz-context")
				bizCtx, ok := val.(*fwctx.BizContext)
				convey.So(ok, convey.ShouldBeTrue)
				convey.So(bizCtx.LogID, convey.ShouldEqual, "ticket-test-ctx-name")
				convey.So(bizCtx.TenantID, convey.ShouldEqual, "state-tenant-id")
				convey.So(bizCtx.UserID, convey.ShouldEqual, "state-user-id")
			}).Build()

			actor1.BuildCtx(mockCtx)
		})

		// 场景描述：测试当state中所有ID都为空时，能否正确处理并生成默认值
		// 数据构造：
		// - DataCleanTicketActor的state字段被初始化
		// - state.LogID, state.TenantId, state.UserId 均为空字符串
		// 逻辑链路：
		// 1. Mock types.Context的GetName方法
		// 2. Mock types.Context的WithValue方法
		// 3. 调用目标函数 BuildCtx
		// 4. 在WithValue的mock实现中，断言LogID已生成，而TenantID和UserID为空字符串
		mockey.PatchConvey("success: all ids are missing in state", func() {
			actor1 := &DataCleanTicketActor{
				state: &DataCleanTicketState{
					LogID:    "",
					TenantId: "",
					UserId:   "",
				},
			}
			mockCtx := &Mock_Context_DataCleanTicketActor_BuildCtx{}

			mockey.Mock((*Mock_Context_DataCleanTicketActor_BuildCtx).GetName).Return("another-ctx-name").Build()
			mockey.Mock((*Mock_Context_DataCleanTicketActor_BuildCtx).WithValue).To(func(ctx *Mock_Context_DataCleanTicketActor_BuildCtx, key, val interface{}) {
				convey.So(key, convey.ShouldEqual, "biz-context")
				bizCtx, ok := val.(*fwctx.BizContext)
				convey.So(ok, convey.ShouldBeTrue)
				convey.So(bizCtx.LogID, convey.ShouldEqual, "ticket-another-ctx-name")
				convey.So(bizCtx.TenantID, convey.ShouldBeEmpty)
				convey.So(bizCtx.UserID, convey.ShouldBeEmpty)
			}).Build()

			actor1.BuildCtx(mockCtx)
		})
	})
}

// Mock_ConfigProvider_NewDataCleanTicketActor is a mock for config.ConfigProvider
type Mock_ConfigProvider_NewDataCleanTicketActor struct {
	config.ConfigProvider
}

// Mock_WorkflowDAL_NewDataCleanTicketActor is a mock for dal.WorkflowDAL
type Mock_WorkflowDAL_NewDataCleanTicketActor struct {
	dal.WorkflowDAL
}

// Mock_DataSourceService_NewDataCleanTicketActor is a mock for datasource.DataSourceService
type Mock_DataSourceService_NewDataCleanTicketActor struct {
	datasource.DataSourceService
}

// Mock_TicketCommonService_NewDataCleanTicketActor is a mock for dbw_ticket.TicketCommonService
type Mock_TicketCommonService_NewDataCleanTicketActor struct {
	dbw_ticket.TicketCommonService
}

func TestNewDataCleanTicketActor_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("TestNewDataCleanTicketActor", t, func() {
		// 数据构造
		mockConf := &Mock_ConfigProvider_NewDataCleanTicketActor{}
		mockWorkflowDal := &Mock_WorkflowDAL_NewDataCleanTicketActor{}
		mockDs := &Mock_DataSourceService_NewDataCleanTicketActor{}
		mockTicketCommonService := &Mock_TicketCommonService_NewDataCleanTicketActor{}

		p := DataCleanTicketActorIn{
			In:                  dig.In{},
			Conf:                mockConf,
			WorkflowDal:         mockWorkflowDal,
			Ds:                  mockDs,
			TicketCommonService: mockTicketCommonService,
		}

		mockey.PatchConvey("成功场景: 成功创建并初始化Actor", func() {
			// 场景描述：
			// 调用NewDataCleanTicketActor函数，验证返回的VirtualPersistenceProducer结构体字段正确。
			// 接着执行其Producer方法，并传入一个有效的state JSON，验证返回的Actor实例及其内部字段被正确初始化。
			// 数据构造：
			// - p: 一个有效的 DataCleanTicketActorIn 实例，包含所有依赖的 mock 对象。
			// - stateBytes: 一个有效的 JSON 字符串，可以被成功反序列化为 DataCleanTicketState。
			// 逻辑链路：
			// 1. 调用 NewDataCleanTicketActor(p) 获取 producer。
			// 2. 验证 producer.Kind 的值是否正确。
			// 3. 验证 producer.Producer 不为 nil。
			// 4. 调用 producer.Producer.Spawn() 方法。
			// 5. 验证返回的 actor 是 *DataCleanTicketActor 类型。
			// 6. 验证 actor 内部的 state 字段被成功反序列化，且内容正确。
			// 7. 验证 actor 内部的依赖字段与输入参数 p 中的字段是同一个实例。

			// 数据构造
			state := DataCleanTicketState{SessionId: "test-session-id"}
			stateBytes, err := json.Marshal(state)
			convey.So(err, convey.ShouldBeNil)

			// 调用
			producer := NewDataCleanTicketActor(p)

			// 断言
			convey.So(producer.Kind, convey.ShouldEqual, consts.DbwDataCleanActorKind)
			convey.So(producer.Producer, convey.ShouldNotBeNil)

			// 调用 producer 的 Spawn 方法
			actorInstance := producer.Producer.Spawn("test-kind", "test-name", stateBytes)

			// 断言 actor 实例
			convey.So(actorInstance, convey.ShouldNotBeNil)
			dataCleanActor, ok := actorInstance.(*DataCleanTicketActor)
			convey.So(ok, convey.ShouldBeTrue)

			convey.So(dataCleanActor.state, convey.ShouldNotBeNil)
			convey.So(dataCleanActor.state.SessionId, convey.ShouldEqual, "test-session-id")
			convey.So(dataCleanActor.cnf, convey.ShouldEqual, mockConf)
			convey.So(dataCleanActor.workflowDal, convey.ShouldEqual, mockWorkflowDal)
			convey.So(dataCleanActor.ds, convey.ShouldEqual, mockDs)
			convey.So(dataCleanActor.ticketCommonService, convey.ShouldEqual, mockTicketCommonService)
		})

		mockey.PatchConvey("失败场景: state反序列化失败导致state为nil", func() {
			// 场景描述：
			// 当 Producer.Spawn 方法接收到一个无效的 state JSON 字节切片时，
			// 内部调用 newDataCleanTicketState 会因为 json.Unmarshal 失败而返回 nil。
			// 验证在这种情况下，创建的 Actor 实例的 state 字段为 nil。
			// 数据构造：
			// - p: 一个有效的 DataCleanTicketActorIn 实例。
			// - invalidStateBytes: 一个无效的 JSON 字符串。
			// 逻辑链路：
			// 1. 调用 NewDataCleanTicketActor(p) 获取 producer。
			// 2. 准备一个无效的 JSON 字节切片。
			// 3. 调用 producer.Producer.Spawn() 方法。
			// 4. 验证返回的 actor 是 *DataCleanTicketActor 类型。
			// 5. 验证 actor 内部的 state 字段为 nil。

			// 数据构造
			invalidStateBytes := []byte("this is not a valid json")

			// 调用
			producer := NewDataCleanTicketActor(p)
			actorInstance := producer.Producer.Spawn("test-kind", "test-name", invalidStateBytes)

			// 断言
			convey.So(actorInstance, convey.ShouldNotBeNil)
			dataCleanActor, ok := actorInstance.(*DataCleanTicketActor)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(dataCleanActor.state, convey.ShouldBeNil)
		})
	})
}

// MockContextForOnStart is a mock implementation of the types.Context interface for testing OnStart.
// It captures sent messages for assertion and provides necessary mock implementations,
// avoiding the "function too short to patch" error by not requiring its own methods to be mocked.
type MockContextForOnStart struct {
	types.Context
	sentMessages []struct {
		pid *actor.PID
		msg interface{}
	}
}

func (m *MockContextForOnStart) SetReceiveTimeout(d time.Duration) {}
func (m *MockContextForOnStart) GetName() string                   { return "test-actor-name" }
func (m *MockContextForOnStart) Self() *actor.PID                  { return actor.NewPID("local", "self") }
func (m *MockContextForOnStart) Send(pid *actor.PID, message interface{}) {
	m.sentMessages = append(m.sentMessages, struct {
		pid *actor.PID
		msg interface{}
	}{pid: pid, msg: message})
}
func (m *MockContextForOnStart) WithValue(key, val interface{})    {}
func (m *MockContextForOnStart) Value(key interface{}) interface{} { return fwctx.NewBizContext() }
func (m *MockContextForOnStart) Done() <-chan struct{}             { return nil }
func (m *MockContextForOnStart) Deadline() (time.Time, bool)       { return time.Time{}, false }
func (m *MockContextForOnStart) Err() error                        { return nil }
func Test_DataCleanTicketActor_OnStart_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("Test_DataCleanTicketActor_OnStart", t, func() {
		// Mock通用依赖
		mockey.Mock(log.Info).Return().Build()
		mockey.Mock(fwctx.GetTenantID).Return("test-tenant-id").Build()

		mockey.PatchConvey("成功场景: CurrentAction为ExecuteFinished, 发送SuicideMessage", func() {
			// 场景描述：
			// 当actor重启时，如果其状态已经是“执行完成”，则应发送一个自杀消息并终止。
			// 数据构造：
			// - actorInstance.state.CurrentAction 设置为 model.ExecDataCleanTicketAction_ExecuteFinished
			// 逻辑链路：
			// 1. 调用 ctx.SetReceiveTimeout (由mock context处理)
			// 2. 调用 log.Info 记录启动日志
			// 3. if 条件判断为 true (CurrentAction is ExecuteFinished)
			// 4. 调用 ctx.Send 发送 SuicideMessage (由mock context捕获)
			// 5. 函数返回，不调用 processExecuteTicketAction

			// 数据构造
			actorInstance := &DataCleanTicketActor{
				state: &DataCleanTicketState{
					CurrentAction: model.ExecDataCleanTicketAction_ExecuteFinished,
				},
			}
			mockCtx := &MockContextForOnStart{}

			// Mock: 无需额外mock，mock context已处理所有交互

			// 调用
			actorInstance.OnStart(mockCtx)

			// 断言
			convey.So(len(mockCtx.sentMessages), convey.ShouldEqual, 1)
			convey.So(mockCtx.sentMessages[0].msg, convey.ShouldHaveSameTypeAs, &proto.SuicideMessage{})
			convey.So(mockCtx.sentMessages[0].pid, convey.ShouldResemble, mockCtx.Self())
		})

		mockey.PatchConvey("成功场景: CurrentAction为ExecuteFailed, 发送SuicideMessage", func() {
			// 场景描述：
			// 当actor重启时，如果其状态已经是“执行失败”，则应发送一个自杀消息并终止。
			// 数据构造：
			// - actorInstance.state.CurrentAction 设置为 model.ExecDataCleanTicketAction_ExecuteFailed
			// 逻辑链路：
			// 1. 调用 ctx.SetReceiveTimeout (由mock context处理)
			// 2. 调用 log.Info 记录启动日志
			// 3. if 条件判断为 true (CurrentAction is ExecuteFailed)
			// 4. 调用 ctx.Send 发送 SuicideMessage (由mock context捕获)
			// 5. 函数返回，不调用 processExecuteTicketAction

			// 数据构造
			actorInstance := &DataCleanTicketActor{
				state: &DataCleanTicketState{
					CurrentAction: model.ExecDataCleanTicketAction_ExecuteFailed,
				},
			}
			mockCtx := &MockContextForOnStart{}

			// Mock: 无需额外mock

			// 调用
			actorInstance.OnStart(mockCtx)

			// 断言
			convey.So(len(mockCtx.sentMessages), convey.ShouldEqual, 1)
			convey.So(mockCtx.sentMessages[0].msg, convey.ShouldHaveSameTypeAs, &proto.SuicideMessage{})
			convey.So(mockCtx.sentMessages[0].pid, convey.ShouldResemble, mockCtx.Self())
		})

		mockey.PatchConvey("成功场景: CurrentAction为中间状态, 调用processExecuteTicketAction", func() {
			// 场景描述：
			// 当actor重启时，如果其状态为中间状态（非完成或失败），则应继续执行工单流程。
			// 数据构造：
			// - actorInstance.state.CurrentAction 设置为 model.ExecDataCleanTicketAction_ReceiveCommand
			// 逻辑链路：
			// 1. 调用 ctx.SetReceiveTimeout (由mock context处理)
			// 2. 调用 log.Info 记录启动日志
			// 3. if 条件判断为 false
			// 4. 调用 e.processExecuteTicketAction 继续执行流程

			// 数据构造
			actorInstance := &DataCleanTicketActor{
				state: &DataCleanTicketState{
					CurrentAction: model.ExecDataCleanTicketAction_ReceiveCommand,
				},
			}
			mockCtx := &MockContextForOnStart{}

			// Mock
			mockey.Mock((*DataCleanTicketActor).processExecuteTicketAction).To(func(_ *DataCleanTicketActor, _ types.Context) {}).Build()

			// 调用
			actorInstance.OnStart(mockCtx)

			// 断言
			convey.So(len(mockCtx.sentMessages), convey.ShouldEqual, 0)
		})
	})
}

// Mock_Context_UpdateTicketRepo 是用于mock `types.Context` 接口的结构体
type Mock_Context_UpdateTicketRepo struct {
	types.Context
}

// GetName 是被mock的方法
func (m *Mock_Context_UpdateTicketRepo) GetName() string {
	// mockey会提供实际的返回值
	return ""
}

// Mock_WorkflowDAL_UpdateTicketRepo 是用于mock `dal.WorkflowDAL` 接口的结构体
type Mock_WorkflowDAL_UpdateTicketRepo struct {
	dal.WorkflowDAL
}

// UpdateWorkStatus 是被mock的方法
func (m *Mock_WorkflowDAL_UpdateTicketRepo) UpdateWorkStatus(ctx context.Context, ticket *dao.Ticket) error {
	// mockey会提供实际的返回值
	return nil
}
func Test_DataCleanTicketActor_UpdateTicketRepo_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("Test_DataCleanTicketActor_UpdateTicketRepo", t, func() {
		// 通用数据构造
		mockWorkflowDAL := &Mock_WorkflowDAL_UpdateTicketRepo{}
		actor1 := &DataCleanTicketActor{
			workflowDal: mockWorkflowDAL,
		}
		mockCtx := &Mock_Context_UpdateTicketRepo{}
		ticket := &dao.Ticket{
			TicketStatus: 1,
		}
		ticketIDStr := "12345"

		mockey.PatchConvey("成功场景 - 更新工单状态成功", func() {
			// 场景描述：
			// 当依赖的 `workflowDal.UpdateWorkStatus` 方法成功执行并返回 nil 时，
			// 验证 `UpdateTicketRepo` 方法也返回 nil，并且工单的 TicketId 被正确设置。
			// 数据构造：
			// - ctx.GetName() 返回一个可被解析为 int64 的字符串 "12345"。
			// - ticket 对象初始状态正常。
			// - DataCleanTicketActor 实例，其 workflowDal 字段被 mock。
			// 逻辑链路：
			// 1. Mock log.Info 以忽略日志输出。
			// 2. Mock ctx.GetName() 返回 "12345"。
			// 3. Mock workflowDal.UpdateWorkStatus 成功，返回 nil。
			// 4. 调用目标函数 `UpdateTicketRepo`。
			// 5. 断言函数返回的 error 为 nil。
			// 6. 断言 ticket.TicketId 被正确更新为 12345。

			// Mock
			mockey.Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
			mockey.Mock((*Mock_Context_UpdateTicketRepo).GetName).Return(ticketIDStr).Build()
			mockey.Mock((*Mock_WorkflowDAL_UpdateTicketRepo).UpdateWorkStatus).Return(nil).Build()

			// 调用
			err := actor1.UpdateTicketRepo(mockCtx, ticket)

			// 断言
			convey.So(err, convey.ShouldBeNil)
			convey.So(ticket.TicketId, convey.ShouldEqual, 12345)
		})

		mockey.PatchConvey("失败场景 - 更新工单状态时数据库返回错误", func() {
			// 场景描述：
			// 当依赖的 `workflowDal.UpdateWorkStatus` 方法执行失败并返回 error 时，
			// 验证 `UpdateTicketRepo` 方法将该 error 向上层透传。
			// 数据构造：
			// - ctx.GetName() 返回一个可被解析为 int64 的字符串 "12345"。
			// - ticket 对象初始状态正常。
			// - DataCleanTicketActor 实例，其 workflowDal 字段被 mock。
			// 逻辑链路：
			// 1. Mock log.Info 以忽略日志输出。
			// 2. Mock ctx.GetName() 返回 "12345"。
			// 3. Mock workflowDal.UpdateWorkStatus 失败，返回一个指定的 error。
			// 4. Mock log.Warn 以忽略警告日志输出。
			// 5. 调用目标函数 `UpdateTicketRepo`。
			// 6. 断言函数返回的 error 不为 nil，且与 mock 的 error 一致。

			// 数据构造
			dbErr := errors.New("database update failed")

			// Mock
			mockey.Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
			mockey.Mock((*Mock_Context_UpdateTicketRepo).GetName).Return(ticketIDStr).Build()
			mockey.Mock((*Mock_WorkflowDAL_UpdateTicketRepo).UpdateWorkStatus).Return(dbErr).Build()
			mockey.Mock(log.Warn).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()

			// 调用
			err := actor1.UpdateTicketRepo(mockCtx, ticket)

			// 断言
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "database update failed")
		})
	})
}

// MockContextForProtectUserCall is a mock implementation of types.Context for testing purposes.
// As the context is only passed to a mocked logger, we don't need to implement any of its methods.
type MockContextForProtectUserCall struct {
	types.Context
}

func Test_DataCleanTicketActor_protectUserCall_BitsUTGen(t *testing.T) {
	// actor is the receiver for the method under test.
	// Its internal state is not used in protectUserCall, so an empty instance is sufficient.
	actor1 := &DataCleanTicketActor{}
	// mockCtx is a mock context passed to the function.
	mockCtx := &MockContextForProtectUserCall{}

	mockey.PatchConvey("Test_DataCleanTicketActor_protectUserCall", t, func() {
		mockey.PatchConvey("成功场景 - 传入的函数正常执行，不发生panic", func() {
			// 场景描述：
			// 验证当传入的函数`fn`正常执行且不发生panic时，`protectUserCall`能够正确调用`fn`，并且不触发panic恢复逻辑。
			// 数据构造：
			// - e: 一个*DataCleanTicketActor实例。
			// - ctx: 一个mock的types.Context。
			// - fn: 一个不会panic的函数，该函数会修改一个外部变量以验证其是否被调用。
			// 逻辑链路：
			// 1. 定义一个布尔变量`called`并初始化为false。
			// 2. 创建一个函数`fn`，其功能是将`called`置为true。
			// 3. 调用`actor.protectUserCall`方法，并传入mock上下文和`fn`。
			// 4. 断言`called`为true，以确认`fn`被成功执行。

			// 数据构造
			var called bool
			fn := func() {
				called = true
			}

			// 调用
			actor1.protectUserCall(mockCtx, fn)

			// 断言
			convey.So(called, convey.ShouldBeTrue)
		})

		mockey.PatchConvey("失败场景 - 传入的函数发生panic", func() {
			// 场景描述：
			// 验证当传入的函数`fn`执行时发生panic，`protectUserCall`能够成功捕获此panic，记录警告日志，并防止程序崩溃。
			// 数据构造：
			// - e: 一个*DataCleanTicketActor实例。
			// - ctx: 一个mock的types.Context。
			// - fn: 一个会立即引发panic的函数。
			// 逻辑链路：
			// 1. Mock `log.Warn`函数，使其不执行任何操作，仅用于拦截对日志系统的调用。
			// 2. 创建一个会引发panic的函数`fn`。
			// 3. 调用`actor.protectUserCall`方法。
			// 4. 使用`convey.ShouldNotPanic`断言该调用不会将panic传播到外部，证明recover机制生效。

			// 数据构造
			fn := func() {
				panic("test panic")
			}

			// Mock
			mockey.Mock(log.Warn).Return().Build()

			// 调用和断言
			convey.So(func() {
				actor1.protectUserCall(mockCtx, fn)
			}, convey.ShouldNotPanic)
		})
	})
}
