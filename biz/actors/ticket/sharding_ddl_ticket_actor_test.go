package ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/parser"
	sqltask_svc "code.byted.org/infcs/dbw-mgr/biz/service/sqltask"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"code.byted.org/infcs/protoactor-go/cluster"
	"code.byted.org/lang/gg/gptr"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/robfig/cron/v3"
	"github.com/smartystreets/goconvey/convey"
	"strings"
	"testing"
	"time"
)

// Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask is a mock for the types.Context interface.
type Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask struct {
	types.Context
}

func (m *Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask) ClientOf(kind string) cli.KindClient {
	return nil
}
func (m *Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask) Send(pid *actor.PID, message interface{}) {
}
func (m *Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask) Self() *actor.PID { return nil }
func (m *Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask) GetName() string  { return "" }

// Mock_KindClient_ShardingDDLTicketActor_DescribeOnlineDDLTask is a mock for the cli.KindClient interface.
type Mock_KindClient_ShardingDDLTicketActor_DescribeOnlineDDLTask struct {
	cli.KindClient
}

func (m *Mock_KindClient_ShardingDDLTicketActor_DescribeOnlineDDLTask) Call(ctx context.Context, name string, msg interface{}, callopts ...*cluster.GrainCallOptions) (interface{}, error) {
	return nil, nil
}

// Mock_WorkflowDAL_ShardingDDLTicketActor_DescribeOnlineDDLTask is a mock for the dal.WorkflowDAL interface.
type Mock_WorkflowDAL_ShardingDDLTicketActor_DescribeOnlineDDLTask struct {
	dal.WorkflowDAL
}

func (m *Mock_WorkflowDAL_ShardingDDLTicketActor_DescribeOnlineDDLTask) UpdateWorkStatusAndProgress(ctx context.Context, ticketId int64, status int32, desc string, progress int32) error {
	return nil
}
func (m *Mock_WorkflowDAL_ShardingDDLTicketActor_DescribeOnlineDDLTask) UpdateWorkStatus(ctx context.Context, ticket *dao.Ticket) error {
	return nil
}

func TestShardingDDLTicketActor_DescribeOnlineDDLTask_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("TestShardingDDLTicketActor_DescribeOnlineDDLTask", t, func() {
		// General Mocks
		mockey.Mock(log.Info).Return().Build()
		mockey.Mock(log.Error).Return().Build()
		mockey.Mock(log.Warn).Return().Build()

		now := time.Now()
		mockey.Mock(time.Now).Return(now).Build()

		// Common variables
		var (
			mockCtx         *Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask
			mockKindClient  *Mock_KindClient_ShardingDDLTicketActor_DescribeOnlineDDLTask
			mockWorkflowDal *Mock_WorkflowDAL_ShardingDDLTicketActor_DescribeOnlineDDLTask
			actorInstance   *ShardingDDLTicketActor
			testTicket      *dao.Ticket
		)

		// Setup function for each test case
		setup := func() {
			mockCtx = &Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask{}
			mockKindClient = &Mock_KindClient_ShardingDDLTicketActor_DescribeOnlineDDLTask{}
			mockWorkflowDal = &Mock_WorkflowDAL_ShardingDDLTicketActor_DescribeOnlineDDLTask{}
			actorInstance = &ShardingDDLTicketActor{
				state: &ShardingDDLTicketState{
					TenantId: "test-tenant",
					UserId:   "test-user",
				},
				workflowDal: mockWorkflowDal,
			}
			testTicket = &dao.Ticket{
				TicketId:   123,
				TenantId:   "test-tenant",
				InstanceId: "test-instance",
				TaskId:     "test-task",
			}
		}

		mockey.PatchConvey("场景：定时DDL任务首次执行，在执行窗口内，有结束时间", func() {
			// 场景描述：当一个定时DDL任务首次被检查，并且当前时间在预设的执行窗口内时，系统应发送执行消息。
			// 数据构造：
			// - ticket.ExecuteType: Cron
			// - state.IsDDLTaskCreated: false
			// - ticket.ExecutableEndTime > 0 且在当前时间之后
			// - ticket.ExecutableStartTime 在当前时间之前
			// 逻辑链路：
			// 1. 函数判断为未创建的定时任务。
			// 2. 函数判断当前时间在 [StartTime, EndTime) 区间内。
			// 3. 调用 `sendExecTicketMsg` 方法发送执行消息。
			// 4. 函数提前返回。
			setup()
			actorInstance.state.IsDDLTaskCreated = false
			testTicket.ExecuteType = int8(model.ExecuteType_Cron)
			testTicket.ExecutableStartTime = now.Unix() - 100
			testTicket.ExecutableEndTime = now.Unix() + 100

			mockey.Mock((*ShardingDDLTicketActor).sendExecTicketMsg).Return().Build()

			actorInstance.DescribeOnlineDDLTask(mockCtx, testTicket)
		})

		mockey.PatchConvey("场景：定时DDL任务首次执行，无结束时间", func() {
			// 场景描述：当一个定时DDL任务首次被检查，且没有设置结束时间，只要当前时间晚于开始时间，就应发送执行消息。
			// 数据构造：
			// - ticket.ExecuteType: Cron
			// - state.IsDDLTaskCreated: false
			// - ticket.ExecutableEndTime: 0
			// - ticket.ExecutableStartTime 在当前时间之前
			// 逻辑链路：
			// 1. 函数判断为未创建的定时任务。
			// 2. 函数判断无结束时间，且当前时间 > StartTime。
			// 3. 调用 `sendExecTicketMsg` 方法。
			// 4. 函数提前返回。
			setup()
			actorInstance.state.IsDDLTaskCreated = false
			testTicket.ExecuteType = int8(model.ExecuteType_Cron)
			testTicket.ExecutableStartTime = now.Unix() - 100
			testTicket.ExecutableEndTime = 0

			mockey.Mock((*ShardingDDLTicketActor).sendExecTicketMsg).Return().Build()

			actorInstance.DescribeOnlineDDLTask(mockCtx, testTicket)
		})

		mockey.PatchConvey("场景：任务执行中，未到截止时间", func() {
			// 场景描述：当一个普通DDL任务或已创建的定时任务正在执行中，并且尚未到达截止时间，系统应更新工单状态和进度。
			// 数据构造：
			// - ticket.ExecuteType: Auto (非首次Cron)
			// - ticket.ExecutableEndTime 在当前时间之后
			// - SqlTaskActor返回 "Running" 状态
			// 逻辑链路：
			// 1. 函数调用SqlTaskActor获取任务状态。
			// 2. SqlTaskActor返回 "Running" 状态，进入 `IsSqlTaskInProgress` 分支。
			// 3. 调用 `workflowDal.UpdateWorkStatusAndProgress` 更新工单状态为 "TicketExecute"。
			// 4. 函数判断未到截止时间，直接返回。
			setup()
			actorInstance.state.IsDDLTaskCreated = true
			testTicket.ExecutableEndTime = now.Unix() + 100
			statusResp := &shared.GetDDLTaskStatusResp{SqlTaskStatus: model.SqlTaskStatus_Running.String(), Progress: 50}

			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).ClientOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_ShardingDDLTicketActor_DescribeOnlineDDLTask).Call).Return(statusResp, nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_ShardingDDLTicketActor_DescribeOnlineDDLTask).UpdateWorkStatusAndProgress).Return(nil).Build()

			actorInstance.DescribeOnlineDDLTask(mockCtx, testTicket)
		})

		mockey.PatchConvey("场景：任务执行中，已到截止时间", func() {
			// 场景描述：当一个正在执行的任务到达了设定的截止时间，系统应停止任务，更新工单状态为错误，并使Actor自杀。
			// 数据构造：
			// - ticket.ExecutableEndTime 在当前时间之前
			// - SqlTaskActor返回 "Running" 状态
			// 逻辑链路：
			// 1. SqlTaskActor返回 "Running" 状态。
			// 2. 函数判断已到达截止时间。
			// 3. 调用 `doWhenReachEndTime` 停止任务。
			// 4. 调用 `updateActorState` 更新Actor状态。
			// 5. 调用 `workflowDal.UpdateWorkStatusAndProgress` 更新工单状态为 "TicketError"。
			// 6. 发送自杀消息。
			setup()
			testTicket.ExecutableEndTime = now.Unix() - 100
			statusResp := &shared.GetDDLTaskStatusResp{SqlTaskStatus: model.SqlTaskStatus_Running.String(), Progress: 50}
			var sentMsg interface{}

			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).ClientOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_ShardingDDLTicketActor_DescribeOnlineDDLTask).Call).Return(statusResp, nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_ShardingDDLTicketActor_DescribeOnlineDDLTask).UpdateWorkStatusAndProgress).Return(nil).Build()
			mockey.Mock((*ShardingDDLTicketActor).doWhenReachEndTime).Return(nil).Build()
			mockey.Mock((*ShardingDDLTicketActor).updateActorState).Return().Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).Self).Return(&actor.PID{}).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).Send).To(func(pid *actor.PID, msg interface{}) {
				sentMsg = msg
			}).Build()

			actorInstance.DescribeOnlineDDLTask(mockCtx, testTicket)

			convey.So(sentMsg, convey.ShouldNotBeNil)
			convey.So(sentMsg, convey.ShouldHaveSameTypeAs, &proto.SuicideMessage{})
		})

		mockey.PatchConvey("场景：任务执行成功", func() {
			// 场景描述：当SqlTaskActor返回任务成功时，系统应更新工单状态为完成，并使Actor自杀。
			// 数据构造：
			// - SqlTaskActor返回 "Success" 状态
			// 逻辑链路：
			// 1. SqlTaskActor返回 "Success" 状态。
			// 2. 调用 `workflowDal.UpdateWorkStatusAndProgress` 更新工单状态为 "TicketFinished"。
			// 3. 调用 `updateActorState` 更新Actor状态。
			// 4. 发送自杀消息。
			setup()
			statusResp := &shared.GetDDLTaskStatusResp{SqlTaskStatus: model.SqlTaskStatus_Success.String(), Progress: 100}
			var sentMsg interface{}

			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).ClientOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_ShardingDDLTicketActor_DescribeOnlineDDLTask).Call).Return(statusResp, nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_ShardingDDLTicketActor_DescribeOnlineDDLTask).UpdateWorkStatusAndProgress).Return(nil).Build()
			mockey.Mock((*ShardingDDLTicketActor).updateActorState).Return().Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).Self).Return(&actor.PID{}).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).Send).To(func(pid *actor.PID, msg interface{}) {
				sentMsg = msg
			}).Build()

			actorInstance.DescribeOnlineDDLTask(mockCtx, testTicket)

			convey.So(sentMsg, convey.ShouldNotBeNil)
			convey.So(sentMsg, convey.ShouldHaveSameTypeAs, &proto.SuicideMessage{})
		})

		mockey.PatchConvey("场景：任务被停止", func() {
			// 场景描述：当SqlTaskActor返回任务被停止时，系统应更新工单状态为错误，并使Actor自杀。
			// 数据构造：
			// - SqlTaskActor返回 "Stop" 状态
			// 逻辑链路：
			// 1. SqlTaskActor返回 "Stop" 状态。
			// 2. 调用 `workflowDal.UpdateWorkStatusAndProgress` 更新工单状态为 "TicketError"。
			// 3. 调用 `updateActorState` 更新Actor状态。
			// 4. 发送自杀消息。
			setup()
			statusResp := &shared.GetDDLTaskStatusResp{SqlTaskStatus: model.SqlTaskStatus_Stop.String()}
			var sentMsg interface{}

			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).ClientOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_ShardingDDLTicketActor_DescribeOnlineDDLTask).Call).Return(statusResp, nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_ShardingDDLTicketActor_DescribeOnlineDDLTask).UpdateWorkStatusAndProgress).Return(nil).Build()
			mockey.Mock((*ShardingDDLTicketActor).updateActorState).Return().Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).Self).Return(&actor.PID{}).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).Send).To(func(pid *actor.PID, msg interface{}) {
				sentMsg = msg
			}).Build()

			actorInstance.DescribeOnlineDDLTask(mockCtx, testTicket)

			convey.So(sentMsg, convey.ShouldNotBeNil)
			convey.So(sentMsg, convey.ShouldHaveSameTypeAs, &proto.SuicideMessage{})
		})

		mockey.PatchConvey("场景：任务状态异常(Failed)", func() {
			// 场景描述：当SqlTaskActor返回一个未明确处理的异常状态（如Failed）时，系统应更新工单为错误状态，并使Actor自杀。
			// 数据构造：
			// - SqlTaskActor返回 "Failed" 状态
			// 逻辑链路：
			// 1. SqlTaskActor返回 "Failed" 状态，进入 `else` 分支。
			// 2. 调用 `UpdateTicketRepo` 更新工单状态为 "TicketError"。
			// 3. 发送自杀消息。
			setup()
			statusResp := &shared.GetDDLTaskStatusResp{SqlTaskStatus: model.SqlTaskStatus_Failed.String(), Result: "some error"}
			var sentMsg interface{}
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).GetName).Return("123").Build()
			mockey.Mock((*Mock_WorkflowDAL_ShardingDDLTicketActor_DescribeOnlineDDLTask).UpdateWorkStatus).Return(nil).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).ClientOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_ShardingDDLTicketActor_DescribeOnlineDDLTask).Call).Return(statusResp, nil).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).Self).Return(&actor.PID{}).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).Send).To(func(pid *actor.PID, msg interface{}) {
				sentMsg = msg
			}).Build()

			actorInstance.DescribeOnlineDDLTask(mockCtx, testTicket)

			convey.So(sentMsg, convey.ShouldNotBeNil)
			convey.So(sentMsg, convey.ShouldHaveSameTypeAs, &proto.SuicideMessage{})
		})

		mockey.PatchConvey("场景：获取任务状态返回业务错误", func() {
			// 场景描述：当调用SqlTaskActor获取状态时，返回一个业务错误对象（GetDDLTaskStatusErr），系统应将错误信息更新到工单，并使Actor自杀。
			// 数据构造：
			// - SqlTaskActor返回 `*shared.GetDDLTaskStatusErr`
			// 逻辑链路：
			// 1. SqlTaskActor返回业务错误。
			// 2. 进入 `case *shared.GetDDLTaskStatusErr` 分支。
			// 3. 调用 `UpdateTicketRepo` 将错误消息写入工单描述。
			// 4. 发送自杀消息。
			setup()
			statusErr := &shared.GetDDLTaskStatusErr{Message: "business error"}
			var sentMsg interface{}
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).GetName).Return("123").Build()
			mockey.Mock((*Mock_WorkflowDAL_ShardingDDLTicketActor_DescribeOnlineDDLTask).UpdateWorkStatus).Return(nil).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).ClientOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_ShardingDDLTicketActor_DescribeOnlineDDLTask).Call).Return(statusErr, nil).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).Self).Return(&actor.PID{}).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).Send).To(func(pid *actor.PID, msg interface{}) {
				sentMsg = msg
			}).Build()

			actorInstance.DescribeOnlineDDLTask(mockCtx, testTicket)

			convey.So(sentMsg, convey.ShouldNotBeNil)
			convey.So(sentMsg, convey.ShouldHaveSameTypeAs, &proto.SuicideMessage{})
		})

		mockey.PatchConvey("场景：获取任务状态调用失败", func() {
			// 场景描述：当调用SqlTaskActor时发生RPC或网络错误，系统应记录错误日志并直接返回，不做任何状态更新。
			// 数据构造：
			// - SqlTaskActor调用返回 `error`
			// 逻辑链路：
			// 1. `ctx.ClientOf.Call` 返回一个非空的 `error`。
			// 2. 函数捕获错误，调用 `log.Error` 记录。
			// 3. 函数提前返回。
			setup()
			callErr := errors.New("call failed")
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_DescribeOnlineDDLTask).ClientOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_ShardingDDLTicketActor_DescribeOnlineDDLTask).Call).Return(nil, callErr).Build()

			actorInstance.DescribeOnlineDDLTask(mockCtx, testTicket)
		})
	})
}

// Mock_Context_CreateOnlineDDLTask is a mock for the types.Context interface.
type Mock_Context_CreateOnlineDDLTask struct {
	types.Context
}

func (m *Mock_Context_CreateOnlineDDLTask) GetName() string { return "12345" }
func (m *Mock_Context_CreateOnlineDDLTask) ClientOf(kind string) cli.KindClient {
	return &Mock_KindClient_CreateOnlineDDLTask{}
}
func (m *Mock_Context_CreateOnlineDDLTask) Deadline() (deadline time.Time, ok bool) {
	return time.Time{}, false
}
func (m *Mock_Context_CreateOnlineDDLTask) Done() <-chan struct{} { return nil }
func (m *Mock_Context_CreateOnlineDDLTask) Err() error            { return nil }
func (m *Mock_Context_CreateOnlineDDLTask) Value(key interface{}) interface{} {
	return context.Background().Value(key)
}
func (m *Mock_Context_CreateOnlineDDLTask) SaveState()                     {}
func (m *Mock_Context_CreateOnlineDDLTask) WithValue(key, val interface{}) {}
func (m *Mock_Context_CreateOnlineDDLTask) Publish(msg interface{})        {}
func (m *Mock_Context_CreateOnlineDDLTask) GetKind() string {
	return consts.ShardingFreeLockDDLActorKind
}

// Mock_WorkflowDAL_CreateOnlineDDLTask is a mock for the dal.WorkflowDAL interface.
type Mock_WorkflowDAL_CreateOnlineDDLTask struct {
	dal.WorkflowDAL
}

func (m *Mock_WorkflowDAL_CreateOnlineDDLTask) UpdateWorkStatus(ctx context.Context, ticket *dao.Ticket) error {
	return nil
}
func (m *Mock_WorkflowDAL_CreateOnlineDDLTask) DescribeByTicketID(ctx context.Context, ticketId int64) (*dao.Ticket, error) {
	return &dao.Ticket{}, nil
}
func (m *Mock_WorkflowDAL_CreateOnlineDDLTask) Save(ctx context.Context, ticket *dao.Ticket) error {
	return nil
}

// Mock_TicketService_CreateOnlineDDLTask is a mock for the workflow.TicketService interface.
type Mock_TicketService_CreateOnlineDDLTask struct {
	workflow.TicketService
}

func (m *Mock_TicketService_CreateOnlineDDLTask) IsInstanceRunning(ctx context.Context, instanceId string, instanceType shared.DataSourceType) bool {
	return true
}

// Mock_SqlTaskService_CreateOnlineDDLTask is a mock for the sqltask_svc.SqlTaskService interface.
type Mock_SqlTaskService_CreateOnlineDDLTask struct {
	sqltask_svc.SqlTaskService
}

func (m *Mock_SqlTaskService_CreateOnlineDDLTask) CreateOnlineDDLSqlTask(ctx context.Context, req *model.CreateSqlTaskReq) (*string, error) {
	return gptr.Of("task-123"), nil
}

// Mock_KindClient_CreateOnlineDDLTask is a mock for the cli.KindClient interface.
type Mock_KindClient_CreateOnlineDDLTask struct {
	cli.KindClient
}

func (m *Mock_KindClient_CreateOnlineDDLTask) Send(ctx context.Context, actorName string, msg interface{}, callopts ...*cluster.GrainCallOptions) error {
	return nil
}

// Mock_StandardError_CreateOnlineDDLTask is a mock for the consts.StandardError interface.
type Mock_StandardError_CreateOnlineDDLTask struct {
	consts.StandardError
}

func (m *Mock_StandardError_CreateOnlineDDLTask) Error() string { return "mock standard error" }
func TestShardingDDLTicketActor_CreateOnlineDDLTask_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("TestShardingDDLTicketActor_CreateOnlineDDLTask", t, func() {
		mockCtx := &Mock_Context_CreateOnlineDDLTask{}
		mockWorkflowDal := &Mock_WorkflowDAL_CreateOnlineDDLTask{}
		mockTicketService := &Mock_TicketService_CreateOnlineDDLTask{}
		mockSqlTaskSvc := &Mock_SqlTaskService_CreateOnlineDDLTask{}
		mockControlledCron := cron.New()

		actor1 := &ShardingDDLTicketActor{
			state:         &ShardingDDLTicketState{},
			workflowDal:   mockWorkflowDal,
			ticketService: mockTicketService,
			sqlTaskSvc:    mockSqlTaskSvc,
			cron:          cron.New(), // Initial cron, will be replaced if generateStopCronTask is called
		}

		mockey.PatchConvey("成功场景 - 无定时截止任务", func() {
			// 场景描述：
			// 当所有依赖都正常返回，且工单没有设置 ExecutableEndTime 时，函数应成功执行，不创建定时任务。
			// 数据构造：
			// - dao.Ticket 的 ExecutableEndTime 为 0。
			// - InstanceType 为合法的 "MySQL"。
			// 逻辑链路：
			// 1. Mock UpdateTicketRepo 成功。
			// 2. Mock v.workflowDal.DescribeByTicketID 成功，返回一个无 ExecutableEndTime 的 ticket。
			// 3. Mock v.ticketService.IsInstanceRunning 返回 true。
			// 4. Mock v.sqlTaskSvc.CreateOnlineDDLSqlTask 成功，返回有效的 task ID。
			// 5. Mock v.workflowDal.Save 成功。
			// 6. 验证函数返回 nil。
			// 7. 验证 actor 状态被更新为 ExecuteCommand。

			// 数据构造
			ticket := &dao.Ticket{
				TicketId:          12345,
				InstanceType:      "MySQL",
				InstanceId:        "inst-1",
				DbName:            "db1",
				SqlText:           "CREATE TABLE t1 (...)",
				Description:       "test ddl",
				ExecutableEndTime: 0,
			}

			// Mock
			mockey.Mock((*ShardingDDLTicketActor).UpdateTicketRepo).Return(nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_CreateOnlineDDLTask).DescribeByTicketID).Return(ticket, nil).Build()
			mockey.Mock((*Mock_TicketService_CreateOnlineDDLTask).IsInstanceRunning).Return(true).Build()
			mockey.Mock((*Mock_SqlTaskService_CreateOnlineDDLTask).CreateOnlineDDLSqlTask).Return(gptr.Of("task-id-123"), nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_CreateOnlineDDLTask).Save).Return(nil).Build()

			// 调用
			err := actor1.CreateOnlineDDLTask(mockCtx)

			// 断言
			convey.So(err, convey.ShouldBeNil)
			convey.So(actor1.state.CurrentAction, convey.ShouldEqual, model.ExecShardingDDLTicketAction_ExecuteCommand)
		})

		mockey.PatchConvey("成功场景 - 有定时截止任务", func() {
			// 场景描述：
			// 当所有依赖都正常返回，且工单设置了 ExecutableEndTime 时，函数应成功执行，并创建定时截止任务。
			// 数据构造：
			// - dao.Ticket 的 ExecutableEndTime 为一个未来的时间戳。
			// 逻辑链路：
			// 1. Mock 依赖项均成功。
			// 2. Mock cron.New 返回一个可控的 cron 实例。
			// 3. Mock cron 实例的 Start 和 AddFunc 方法，以验证定时任务逻辑被触发。
			// 4. 验证函数返回 nil。

			// 数据构造
			ticket := &dao.Ticket{
				TicketId:          12345,
				InstanceType:      "MySQL",
				InstanceId:        "inst-1",
				DbName:            "db1",
				SqlText:           "CREATE TABLE t1 (...)",
				Description:       "test ddl",
				ExecutableEndTime: time.Now().Add(1 * time.Hour).Unix(),
			}

			// Mock
			mockey.Mock((*ShardingDDLTicketActor).UpdateTicketRepo).Return(nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_CreateOnlineDDLTask).DescribeByTicketID).Return(ticket, nil).Build()
			mockey.Mock((*Mock_TicketService_CreateOnlineDDLTask).IsInstanceRunning).Return(true).Build()
			mockey.Mock((*Mock_SqlTaskService_CreateOnlineDDLTask).CreateOnlineDDLSqlTask).Return(gptr.Of("task-id-123"), nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_CreateOnlineDDLTask).Save).Return(nil).Build()
			mockey.Mock(cron.New).Return(mockControlledCron).Build()
			mockey.Mock((*cron.Cron).Start).Return().Build()
			mockey.Mock((*cron.Cron).AddFunc).Return(cron.EntryID(1), nil).Build()

			// 调用
			err := actor1.CreateOnlineDDLTask(mockCtx)

			// 断言
			convey.So(err, convey.ShouldBeNil)
		})

		mockey.PatchConvey("失败场景 - 获取工单信息失败", func() {
			// 场景描述：
			// 当 v.workflowDal.DescribeByTicketID 返回错误时，函数应立即返回错误。
			// 逻辑链路：
			// 1. Mock v.workflowDal.DescribeByTicketID 返回一个非 nil 的 error。
			// 2. 验证函数返回的 error 与 mock 的 error 一致。

			// Mock
			mockey.Mock((*ShardingDDLTicketActor).UpdateTicketRepo).Return(nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_CreateOnlineDDLTask).DescribeByTicketID).Return(nil, errors.New("db error")).Build()

			// 调用
			err := actor1.CreateOnlineDDLTask(mockCtx)

			// 断言
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "db error")
		})

		mockey.PatchConvey("失败场景 - 实例类型未知", func() {
			// 场景描述：
			// 当 ticket.InstanceType 是一个无法识别的类型时，model.DSTypeFromString 会返回错误，函数应终止并返回错误。
			// 数据构造：
			// - ticket.InstanceType 设置为 "UnknownDB"。
			// 逻辑链路：
			// 1. Mock v.workflowDal.DescribeByTicketID 返回一个带有未知 InstanceType 的 ticket。
			// 2. 验证函数返回的 error 包含 "not a valid DSType string"。

			// 数据构造
			ticket := &dao.Ticket{TicketId: 12345, InstanceType: "UnknownDB"}

			// Mock
			mockey.Mock((*ShardingDDLTicketActor).UpdateTicketRepo).Return(nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_CreateOnlineDDLTask).DescribeByTicketID).Return(ticket, nil).Build()

			// 调用
			err := actor1.CreateOnlineDDLTask(mockCtx)

			// 断言
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "not a valid DSType string")
		})

		mockey.PatchConvey("失败场景 - 实例未运行", func() {
			// 场景描述：
			// 当 v.ticketService.IsInstanceRunning 返回 false 时，函数应返回 CheckInstanceError。
			// 逻辑链路：
			// 1. Mock v.ticketService.IsInstanceRunning 返回 false。
			// 2. Mock consts.ErrorOf 返回一个自定义的 error。
			// 3. 验证函数返回的 error 与 mock 的 error 一致。

			// 数据构造
			ticket := &dao.Ticket{TicketId: 12345, InstanceType: "MySQL", InstanceId: "inst-1"}

			// Mock
			mockey.Mock((*ShardingDDLTicketActor).UpdateTicketRepo).Return(nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_CreateOnlineDDLTask).DescribeByTicketID).Return(ticket, nil).Build()
			mockey.Mock((*Mock_TicketService_CreateOnlineDDLTask).IsInstanceRunning).Return(false).Build()
			mockey.Mock(consts.ErrorOf).Return(&Mock_StandardError_CreateOnlineDDLTask{}).Build()

			// 调用
			err := actor1.CreateOnlineDDLTask(mockCtx)

			// 断言
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "mock standard error")
		})

		mockey.PatchConvey("失败场景 - 创建 Online DDL 任务失败", func() {
			// 场景描述：
			// 当 v.sqlTaskSvc.CreateOnlineDDLSqlTask 返回错误时，函数应返回该错误。
			// 逻辑链路：
			// 1. Mock v.sqlTaskSvc.CreateOnlineDDLSqlTask 返回一个非 nil 的 error。
			// 2. 验证函数返回的 error 与 mock 的 error 一致。

			// 数据构造
			ticket := &dao.Ticket{TicketId: 12345, InstanceType: "MySQL", InstanceId: "inst-1"}

			// Mock
			mockey.Mock((*ShardingDDLTicketActor).UpdateTicketRepo).Return(nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_CreateOnlineDDLTask).DescribeByTicketID).Return(ticket, nil).Build()
			mockey.Mock((*Mock_TicketService_CreateOnlineDDLTask).IsInstanceRunning).Return(true).Build()
			mockey.Mock((*Mock_SqlTaskService_CreateOnlineDDLTask).CreateOnlineDDLSqlTask).Return(nil, errors.New("create task failed")).Build()

			// 调用
			err := actor1.CreateOnlineDDLTask(mockCtx)

			// 断言
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "create task failed")
		})

		mockey.PatchConvey("失败场景 - 创建 Online DDL 任务返回空 ID", func() {
			// 场景描述：
			// 当 v.sqlTaskSvc.CreateOnlineDDLSqlTask 返回一个空的 TaskId 时，函数应返回一个表示 TaskId 为空的错误。
			// 逻辑链路：
			// 1. Mock v.sqlTaskSvc.CreateOnlineDDLSqlTask 返回一个空的 string 指针。
			// 2. 验证函数返回的 error 包含 "Task id is empty"。

			// 数据构造
			ticket := &dao.Ticket{TicketId: 12345, InstanceType: "MySQL", InstanceId: "inst-1"}

			// Mock
			mockey.Mock((*ShardingDDLTicketActor).UpdateTicketRepo).Return(nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_CreateOnlineDDLTask).DescribeByTicketID).Return(ticket, nil).Build()
			mockey.Mock((*Mock_TicketService_CreateOnlineDDLTask).IsInstanceRunning).Return(true).Build()
			mockey.Mock((*Mock_SqlTaskService_CreateOnlineDDLTask).CreateOnlineDDLSqlTask).Return(gptr.Of(""), nil).Build()

			// 调用
			err := actor1.CreateOnlineDDLTask(mockCtx)

			// 断言
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "Online DDL Task id is empty")
		})

		mockey.PatchConvey("失败场景 - 保存工单信息失败", func() {
			// 场景描述：
			// 在所有前置步骤成功后，如果最后保存工单信息（v.workflowDal.Save）失败，函数应返回错误。
			// 逻辑链路：
			// 1. Mock 其他依赖项均成功。
			// 2. Mock v.workflowDal.Save 返回一个非 nil 的 error。
			// 3. 验证函数返回的 error 与 mock 的 error 一致。

			// 数据构造
			ticket := &dao.Ticket{TicketId: 12345, InstanceType: "MySQL"}

			// Mock
			mockey.Mock((*ShardingDDLTicketActor).UpdateTicketRepo).Return(nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_CreateOnlineDDLTask).DescribeByTicketID).Return(ticket, nil).Build()
			mockey.Mock((*Mock_TicketService_CreateOnlineDDLTask).IsInstanceRunning).Return(true).Build()
			mockey.Mock((*Mock_SqlTaskService_CreateOnlineDDLTask).CreateOnlineDDLSqlTask).Return(gptr.Of("task-id-123"), nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_CreateOnlineDDLTask).Save).Return(errors.New("save failed")).Build()

			// 调用
			err := actor1.CreateOnlineDDLTask(mockCtx)

			// 断言
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "save failed")
		})

		mockey.PatchConvey("失败场景 - 创建 DDL 任务返回空指针 ID", func() {
			// 场景描述：
			// 当 CreateOnlineDDLSqlTask 返回的 sqlTaskId 指针为 nil 时，程序应该正确处理并返回错误。
			// 逻辑链路：
			// 1. Mock CreateOnlineDDLSqlTask 返回 (nil, nil)。
			// 2. 验证函数返回 "Task id is empty" 相关的错误。
			// 数据构造
			ticket := &dao.Ticket{TicketId: 12345, InstanceType: "MySQL"}

			// Mock
			mockey.Mock((*ShardingDDLTicketActor).UpdateTicketRepo).Return(nil).Build()
			mockey.Mock((*Mock_WorkflowDAL_CreateOnlineDDLTask).DescribeByTicketID).Return(ticket, nil).Build()
			mockey.Mock((*Mock_TicketService_CreateOnlineDDLTask).IsInstanceRunning).Return(true).Build()
			mockey.Mock((*Mock_SqlTaskService_CreateOnlineDDLTask).CreateOnlineDDLSqlTask).Return(nil, nil).Build()

			// 调用
			err := actor1.CreateOnlineDDLTask(mockCtx)

			// 断言
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldEqual, fmt.Sprintf("ticket %d Online DDL Task id is empty", ticket.TicketId))
		})
	})
}

// Mock_Context_ShardingDDLTicketActor_StopTicket mocks the types.Context interface
type Mock_Context_ShardingDDLTicketActor_StopTicket struct {
	types.Context
}

func (m *Mock_Context_ShardingDDLTicketActor_StopTicket) ClientOf(kind string) cli.KindClient {
	return nil
}
func (m *Mock_Context_ShardingDDLTicketActor_StopTicket) Respond(response interface{})             {}
func (m *Mock_Context_ShardingDDLTicketActor_StopTicket) Self() *actor.PID                         { return nil }
func (m *Mock_Context_ShardingDDLTicketActor_StopTicket) Send(pid *actor.PID, message interface{}) {}

// Mock_KindClient_ShardingDDLTicketActor_StopTicket mocks the cli.KindClient interface
type Mock_KindClient_ShardingDDLTicketActor_StopTicket struct {
	cli.KindClient
}

func (m *Mock_KindClient_ShardingDDLTicketActor_StopTicket) Call(ctx context.Context, name string, msg interface{}, callopts ...*cluster.GrainCallOptions) (interface{}, error) {
	return nil, nil
}
func Test_ShardingDDLTicketActor_StopTicket_BitsUTGen(t *testing.T) {
	// The actor itself is stateless for this method, so a simple instance is fine.
	actorInstance := &ShardingDDLTicketActor{}

	mockey.PatchConvey("Test_ShardingDDLTicketActor_StopTicket", t, func() {
		// Common data setup
		msg := &shared.StopTicket{
			TicketId:   12345,
			TaskId:     "task-id-67890",
			TenantId:   "tenant-id-abcde",
			InstanceId: "instance-id-fghij",
		}
		mockCtx := &Mock_Context_ShardingDDLTicketActor_StopTicket{}
		mockKindClient := &Mock_KindClient_ShardingDDLTicketActor_StopTicket{}

		mockey.Mock(log.Info).Return().Build()

		mockey.PatchConvey("成功场景: 成功停止 ticket 并收到有效响应", func() {
			// 场景描述：
			// 当向 SqlTaskActor 发送停止请求成功，并收到有效的 StopDDLTaskResp 时，
			// 函数应正确地将响应回复给调用者，并发送一个自杀消息。
			// 数据构造：
			// - msg: 包含有效的 TicketId, TaskId, TenantId, InstanceId
			// - ctx: Mock Context
			// - stopDDLResp: 成功的 StopDDLTaskResp 响应，状态为 "SUCCESS"
			// 逻辑链路：
			// 1. Mock ctx.ClientOf(...) 返回一个 mock KindClient。
			// 2. Mock kindClient.Call(...) 成功返回一个 *shared.StopDDLTaskResp。
			// 3. Mock ctx.Respond(...) 以捕获并验证响应内容。
			// 4. Mock ctx.Self() 返回一个有效的 PID。
			// 5. Mock ctx.Send(...) 以验证是否发送了 SuicideMessage。
			// 6. 调用 StopTicket。
			// 7. 断言 Respond 和 Send 被正确调用，且 Respond 的内容符合预期。

			// 数据构造
			stopDDLResp := &shared.StopDDLTaskResp{
				ErrMessage: "",
				Status:     "SUCCESS",
			}
			var capturedResp *shared.StopTicketResp
			var capturedMsg interface{}

			// Mock
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_StopTicket).ClientOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_ShardingDDLTicketActor_StopTicket).Call).Return(stopDDLResp, nil).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_StopTicket).Respond).To(func(resp interface{}) {
				capturedResp, _ = resp.(*shared.StopTicketResp)
			}).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_StopTicket).Self).Return(actor.NewPID("local", "self")).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_StopTicket).Send).To(func(_ *actor.PID, msg interface{}) {
				capturedMsg = msg
			}).Build()

			// 调用
			actorInstance.StopTicket(mockCtx, msg)

			// 断言
			convey.So(capturedResp, convey.ShouldNotBeNil)
			convey.So(capturedResp.TicketId, convey.ShouldEqual, utils.Int64ToStr(msg.TicketId))
			convey.So(capturedResp.Status, convey.ShouldEqual, stopDDLResp.Status)
			convey.So(capturedResp.ErrMessage, convey.ShouldEqual, stopDDLResp.ErrMessage)
			convey.So(capturedMsg, convey.ShouldHaveSameTypeAs, &proto.SuicideMessage{})
		})

		mockey.PatchConvey("失败场景: 调用 SqlTaskActor 返回错误", func() {
			// 场景描述：
			// 当向 SqlTaskActor 发送请求时，Call 方法返回一个非 nil 的 error。
			// 函数应记录错误日志并直接返回，不进行后续的 Respond 或 Send 操作。
			// 数据构造：
			// - msg: 包含有效的 TicketId, TaskId, TenantId, InstanceId
			// - ctx: Mock Context
			// - callErr: 一个非 nil 的 error
			// 逻辑链路：
			// 1. Mock ctx.ClientOf(...) 返回一个 mock KindClient。
			// 2. Mock kindClient.Call(...) 返回一个错误。
			// 3. Mock log.Error 以验证错误被记录。
			// 4. 调用 StopTicket。
			// 5. 断言 log.Error 被调用，且 Respond 和 Send 未被调用。

			// 数据构造
			callErr := errors.New("actor call failed")
			respondCalled := false
			sendCalled := false

			// Mock
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_StopTicket).ClientOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_ShardingDDLTicketActor_StopTicket).Call).Return(nil, callErr).Build()
			mockey.Mock(log.Error).Return().Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_StopTicket).Respond).To(func(resp interface{}) {
				respondCalled = true
			}).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_StopTicket).Send).To(func(_ *actor.PID, msg interface{}) {
				sendCalled = true
			}).Build()

			// 调用
			actorInstance.StopTicket(mockCtx, msg)

			// 断言
			convey.So(respondCalled, convey.ShouldBeFalse)
			convey.So(sendCalled, convey.ShouldBeFalse)
		})

		mockey.PatchConvey("失败场景: SqlTaskActor 返回未知的响应类型", func() {
			// 场景描述：
			// 当向 SqlTaskActor 发送请求成功，但返回的响应类型不是预期的 *shared.StopDDLTaskResp。
			// 函数应回复一个空的 StopTicketResp 并发送自杀消息。
			// 数据构造：
			// - msg: 包含有效的 TicketId, TaskId, TenantId, InstanceId
			// - ctx: Mock Context
			// - unexpectedResp: 一个非 *shared.StopDDLTaskResp 类型的数据，例如一个字符串。
			// 逻辑链路：
			// 1. Mock ctx.ClientOf(...) 返回一个 mock KindClient。
			// 2. Mock kindClient.Call(...) 成功返回一个未知类型的响应。
			// 3. Mock ctx.Respond(...) 捕获响应并验证其为空。
			// 4. Mock ctx.Self() 和 ctx.Send(...) 验证自杀消息被发送。
			// 5. 调用 StopTicket。
			// 6. 断言 Respond 的内容为空，并且 Send 被调用。

			// 数据构造
			unexpectedResp := "this is not a valid response"
			var capturedResp *shared.StopTicketResp
			var capturedMsg interface{}

			// Mock
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_StopTicket).ClientOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_ShardingDDLTicketActor_StopTicket).Call).Return(unexpectedResp, nil).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_StopTicket).Respond).To(func(resp interface{}) {
				capturedResp, _ = resp.(*shared.StopTicketResp)
			}).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_StopTicket).Self).Return(actor.NewPID("local", "self")).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_StopTicket).Send).To(func(_ *actor.PID, msg interface{}) {
				capturedMsg = msg
			}).Build()

			// 调用
			actorInstance.StopTicket(mockCtx, msg)

			// 断言
			convey.So(capturedResp, convey.ShouldNotBeNil)
			convey.So(*capturedResp, convey.ShouldResemble, shared.StopTicketResp{})
			convey.So(capturedMsg, convey.ShouldHaveSameTypeAs, &proto.SuicideMessage{})
		})
	})
}

// MockContextForProcess is a mock implementation for the types.Context interface.
type MockContextForProcess struct {
	types.Context
	theMessage interface{}
}

// Message returns the message set in the mock context.
func (m *MockContextForProcess) Message() interface{} {
	return m.theMessage
}
func TestShardingDDLTicketActor_Process_BitsUTGen(t *testing.T) {
	// actorInstance is the target instance for testing.
	actorInstance := &ShardingDDLTicketActor{}

	mockey.PatchConvey("Test ShardingDDLTicketActor.Process", t, func() {
		// Mock common logging functions.
		mockey.Mock(log.Info).Return().Build()

		mockey.PatchConvey("场景：收到 *actor.Started 消息", func() {
			// 场景描述：
			// 当 actor 启动时，会收到一个 *actor.Started 消息。
			// Process 方法应该捕获这个消息，并调用 OnStart 方法来执行启动逻辑。
			// 数据构造：
			// - 创建一个 MockContextForProcess 实例，并将其消息设置为 &actor.Started{}。
			// 逻辑链路：
			// 1. Mock 内部方法 OnStart，以验证它是否被正确分发调用。
			// 2. 调用 actorInstance.Process 方法。
			// 3. 断言 OnStart 方法被调用了一次。

			// 数据构造
			mockCtx := &MockContextForProcess{theMessage: &actor.Started{}}

			// Mock
			mockOnStart := mockey.Mock((*ShardingDDLTicketActor).OnStart).Return().Build()

			// 调用
			actorInstance.Process(mockCtx)

			// 断言
			convey.So(mockOnStart.Times(), convey.ShouldEqual, 1)
		})

		mockey.PatchConvey("场景：收到 *shared.ExecShardingFreeLockDDLTicket 消息", func() {
			// 场景描述：
			// 当 actor 收到执行分片 DDL 的请求时，会收到一个 *shared.ExecShardingFreeLockDDLTicket 消息。
			// Process 方法应调用 ExecShardingDDLTicket 方法来处理该请求。
			// 数据构造：
			// - 创建一个 MockContextForProcess 实例，并将其消息设置为 &shared.ExecShardingFreeLockDDLTicket{}。
			// 逻辑链路：
			// 1. Mock 内部方法 ExecShardingDDLTicket，以验证其是否被正确分发调用。
			// 2. 调用 actorInstance.Process 方法。
			// 3. 断言 ExecShardingDDLTicket 被调用了一次。

			// 数据构造
			msg := &shared.ExecShardingFreeLockDDLTicket{}
			mockCtx := &MockContextForProcess{theMessage: msg}

			// Mock
			mockExecShardingDDLTicket := mockey.Mock((*ShardingDDLTicketActor).ExecShardingDDLTicket).Return().Build()

			// 调用
			actorInstance.Process(mockCtx)

			// 断言
			convey.So(mockExecShardingDDLTicket.Times(), convey.ShouldEqual, 1)
		})

		mockey.PatchConvey("场景：收到 *shared.StopTicket 消息", func() {
			// 场景描述：
			// 当需要停止一个正在执行的工单时，actor 会收到 *shared.StopTicket 消息。
			// Process 方法应调用 StopTicket 方法来处理停止请求。
			// 数据构造：
			// - 创建一个 MockContextForProcess 实例，并将其消息设置为 &shared.StopTicket{}。
			// 逻辑链路：
			// 1. Mock 内部方法 StopTicket，以验证其是否被正确分发调用。
			// 2. 调用 actorInstance.Process 方法。
			// 3. 断言 StopTicket 被调用了一次。

			// 数据构造
			msg := &shared.StopTicket{}
			mockCtx := &MockContextForProcess{theMessage: msg}

			// Mock
			mockStopTicket := mockey.Mock((*ShardingDDLTicketActor).StopTicket).Return().Build()

			// 调用
			actorInstance.Process(mockCtx)

			// 断言
			convey.So(mockStopTicket.Times(), convey.ShouldEqual, 1)
		})

		mockey.PatchConvey("场景：收到 *actor.ReceiveTimeout 消息", func() {
			// 场景描述：
			// actor 在等待时，可能会收到 *actor.ReceiveTimeout 消息以轮询任务状态。
			// Process 方法应调用 GetShardingDDLTicketStatus 获取状态，并重置超时。
			// 数据构造：
			// - 创建一个 MockContextForProcess 实例，并将其消息设置为 &actor.ReceiveTimeout{}。
			// 逻辑链路：
			// 1. Mock 内部方法 GetShardingDDLTicketStatus 以验证其调用。
			// 2. Mock context 的 SetReceiveTimeout 方法以验证超时是否被重置。
			// 3. 调用 actorInstance.Process 方法。
			// 4. 断言 GetShardingDDLTicketStatus 和 SetReceiveTimeout 都被调用。

			// 数据构造
			mockCtx := &MockContextForProcess{theMessage: &actor.ReceiveTimeout{}}

			// Mock
			mockGetStatus := mockey.Mock((*ShardingDDLTicketActor).GetShardingDDLTicketStatus).Return().Build()
			mockSetTimeout := mockey.Mock((*MockContextForProcess).SetReceiveTimeout).Return().Build()

			// 调用
			actorInstance.Process(mockCtx)

			// 断言
			convey.So(mockGetStatus.Times(), convey.ShouldEqual, 1)
			convey.So(mockSetTimeout.Times(), convey.ShouldEqual, 1)
		})

		mockey.PatchConvey("场景：收到 *actor.Stopped 消息", func() {
			// 场景描述：
			// 当 actor 被外部停止时，它会收到一个 *actor.Stopped 消息。
			// Process 方法应记录日志，并向自己发送一个自杀消息以完成清理。
			// 数据构造：
			// - 创建一个 MockContextForProcess 实例，并将其消息设置为 &actor.Stopped{}。
			// 逻辑链路：
			// 1. Mock context 的 GetName, Self, 和 Send 方法。
			// 2. 调用 actorInstance.Process 方法。
			// 3. 断言 Send 方法被调用，并且发送的消息是 *proto.SuicideMessage 类型。

			// 数据构造
			mockCtx := &MockContextForProcess{theMessage: &actor.Stopped{}}
			var sentMessage interface{} // 用于捕获发送的消息

			// Mock
			mockey.Mock((*MockContextForProcess).GetName).Return("test-actor").Build()
			mockey.Mock((*MockContextForProcess).Self).Return(&actor.PID{Address: "local", Id: "test"}).Build()
			mockSend := mockey.Mock((*MockContextForProcess).Send).To(func(_ *MockContextForProcess, _ *actor.PID, message interface{}) {
				sentMessage = message
			}).Build()

			// 调用
			actorInstance.Process(mockCtx)

			// 断言
			convey.So(mockSend.Times(), convey.ShouldEqual, 1)
			convey.So(sentMessage, convey.ShouldHaveSameTypeAs, &proto.SuicideMessage{})
		})

		mockey.PatchConvey("场景：收到未知消息", func() {
			// 场景描述：
			// 当 actor 收到一个未在 switch case 中定义的消息类型时，Process 方法应不执行任何特定的业务逻辑，直接返回。
			// 数据构造：
			// - 创建一个 MockContextForProcess 实例，并将其消息设置为一个空的匿名结构体。
			// 逻辑链路：
			// 1. 调用 actorInstance.Process 方法。
			// 2. 由于没有匹配的 case，函数应该直接返回，不会触发 panic 或错误。

			// 数据构造
			mockCtx := &MockContextForProcess{theMessage: struct{}{}}

			// 调用
			actorInstance.Process(mockCtx)

			// 断言 - 验证程序正常执行，没有 panic
			convey.So(true, convey.ShouldBeTrue)
		})
	})
}

// MockConfigProvider_NewShardingDDLTicketActor is a mock for the config.ConfigProvider interface.
type MockConfigProvider_NewShardingDDLTicketActor struct {
	config.ConfigProvider
}

// MockWorkflowDAL_NewShardingDDLTicketActor is a mock for the dal.WorkflowDAL interface.
type MockWorkflowDAL_NewShardingDDLTicketActor struct {
	dal.WorkflowDAL
}

// MockIdgenService_NewShardingDDLTicketActor is a mock for the idgen.Service interface.
type MockIdgenService_NewShardingDDLTicketActor struct {
	idgen.Service
}

// MockCommandParser_NewShardingDDLTicketActor is a mock for the parser.CommandParser interface.
type MockCommandParser_NewShardingDDLTicketActor struct {
	parser.CommandParser
}

// MockCommandRepo_NewShardingDDLTicketActor is a mock for the repository.CommandRepo interface.
type MockCommandRepo_NewShardingDDLTicketActor struct {
	repository.CommandRepo
}

// MockTicketService_NewShardingDDLTicketActor is a mock for the workflow.TicketService interface.
type MockTicketService_NewShardingDDLTicketActor struct {
	workflow.TicketService
}

// MockSqlTaskService_NewShardingDDLTicketActor is a mock for the sqltask_svc.SqlTaskService interface.
type MockSqlTaskService_NewShardingDDLTicketActor struct {
	sqltask_svc.SqlTaskService
}

func TestNewShardingDDLTicketActor_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("TestNewShardingDDLTicketActor", t, func() {
		mockey.PatchConvey("成功场景: 正常创建 ShardingDDLTicketActor 的 VirtualPersistenceProducer", func() {
			// 场景描述：
			// 测试 NewShardingDDLTicketActor 函数能否成功创建一个 types.VirtualPersistenceProducer 实例，
			// 并且该实例内部的 Producer 函数能够正确生成一个 ShardingDDLTicketActor 实例。
			// 数据构造：
			// - 构造一个 ShardingDDLTicketActorIn 实例，其中包含所有依赖接口的 mock 实现。
			// - 构造一个 ShardingDDLTicketState 实例并将其序列化为 JSON 字节数组。
			// 逻辑链路：
			// 1. 调用 NewShardingDDLTicketActor 函数，并传入构造的参数。
			// 2. 验证返回的 VirtualPersistenceProducer 实例不为 nil，且其 Kind 字段值正确。
			// 3. 调用 Producer 的 Spawn 方法，传入构造的 state 数据。
			// 4. 验证 Spawn 方法返回的 actor 实例不为 nil，且类型为 *ShardingDDLTicketActor。
			// 5. 验证 actor 实例中的 state 字段被正确反序列化，并且所有依赖项都已正确注入。

			// 数据构造
			mockConf := &MockConfigProvider_NewShardingDDLTicketActor{}
			mockWorkflowDal := &MockWorkflowDAL_NewShardingDDLTicketActor{}
			mockIdgenSvc := &MockIdgenService_NewShardingDDLTicketActor{}
			mockSp := &MockCommandParser_NewShardingDDLTicketActor{}
			mockCmdRepo := &MockCommandRepo_NewShardingDDLTicketActor{}
			mockTicketService := &MockTicketService_NewShardingDDLTicketActor{}
			mockSqlTaskSvc := &MockSqlTaskService_NewShardingDDLTicketActor{}

			p := ShardingDDLTicketActorIn{
				Conf:          mockConf,
				WorkflowDal:   mockWorkflowDal,
				IdgenSvc:      mockIdgenSvc,
				Sp:            mockSp,
				CmdRepo:       mockCmdRepo,
				TicketService: mockTicketService,
				SqlTaskSvc:    mockSqlTaskSvc,
			}

			stateToMarshal := &ShardingDDLTicketState{
				SessionId:    "test_session_id",
				ConnectionId: "test_connection_id",
				Cs:           &entity.CommandSet{},
				Ds:           &shared.DataSource{},
				LogID:        "test_log_id",
				TenantId:     "test_tenant_id",
				UserId:       "test_user_id",
				TicketType:   1,
			}
			stateBytes, err := json.Marshal(stateToMarshal)
			convey.So(err, convey.ShouldBeNil)

			// 调用
			vpp := NewShardingDDLTicketActor(p)

			// 断言 VirtualPersistenceProducer
			convey.So(vpp, convey.ShouldNotBeNil)
			convey.So(vpp.Kind, convey.ShouldEqual, consts.ShardingFreeLockDDLActorKind)
			convey.So(vpp.Producer, convey.ShouldNotBeNil)

			// 调用 Producer
			actor1 := vpp.Producer.Spawn("test_kind", "test_name", stateBytes)

			// 断言 actor
			convey.So(actor1, convey.ShouldNotBeNil)
			convey.So(actor1, convey.ShouldHaveSameTypeAs, &ShardingDDLTicketActor{})

			shardingActor := actor1.(*ShardingDDLTicketActor)
			convey.So(shardingActor, convey.ShouldNotBeNil)
			convey.So(shardingActor.state, convey.ShouldResemble, stateToMarshal)
			convey.So(shardingActor.cnf, convey.ShouldEqual, mockConf)
			convey.So(shardingActor.workflowDal, convey.ShouldEqual, mockWorkflowDal)
			convey.So(shardingActor.idgenSvc, convey.ShouldEqual, mockIdgenSvc)
			convey.So(shardingActor.sp, convey.ShouldEqual, mockSp)
			convey.So(shardingActor.cmdRepo, convey.ShouldEqual, mockCmdRepo)
			convey.So(shardingActor.ticketService, convey.ShouldEqual, mockTicketService)
			convey.So(shardingActor.sqlTaskSvc, convey.ShouldEqual, mockSqlTaskSvc)
			convey.So(shardingActor.cron, convey.ShouldNotBeNil)
		})

		mockey.PatchConvey("场景: Producer 接收到无效 state 数据", func() {
			// 场景描述：
			// 测试当 Producer 函数接收到无法解析的 state 数据时，创建的 ShardingDDLTicketActor 实例的 state 字段应为 nil。
			// 数据构造：
			// - 构造一个 ShardingDDLTicketActorIn 实例，其中包含所有依赖接口的 mock 实现。
			// - 使用一个非法的 JSON 字符串作为 state 数据。
			// 逻辑链路：
			// 1. 调用 NewShardingDDLTicketActor 函数。
			// 2. 调用返回的 Producer 的 Spawn 方法，传入非法的 state 数据。
			// 3. 验证返回的 actor 实例不为 nil。
			// 4. 验证 actor 实例的 state 字段为 nil，但其他依赖项仍然被正确注入。

			// 数据构造
			p := ShardingDDLTicketActorIn{
				Conf:          &MockConfigProvider_NewShardingDDLTicketActor{},
				WorkflowDal:   &MockWorkflowDAL_NewShardingDDLTicketActor{},
				IdgenSvc:      &MockIdgenService_NewShardingDDLTicketActor{},
				Sp:            &MockCommandParser_NewShardingDDLTicketActor{},
				CmdRepo:       &MockCommandRepo_NewShardingDDLTicketActor{},
				TicketService: &MockTicketService_NewShardingDDLTicketActor{},
				SqlTaskSvc:    &MockSqlTaskService_NewShardingDDLTicketActor{},
			}
			invalidStateBytes := []byte("this is not valid json")

			// 调用
			vpp := NewShardingDDLTicketActor(p)
			actor1 := vpp.Producer.Spawn("test_kind", "test_name", invalidStateBytes)

			// 断言
			convey.So(actor1, convey.ShouldNotBeNil)
			shardingActor, ok := actor1.(*ShardingDDLTicketActor)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(shardingActor.state, convey.ShouldBeNil)
			convey.So(shardingActor.cnf, convey.ShouldEqual, p.Conf)
			convey.So(shardingActor.cron, convey.ShouldNotBeNil)
		})
	})
}

// Mock_Context_doWhenReachExecEndTime is a mock implementation of the types.Context interface for testing.
type Mock_Context_doWhenReachExecEndTime struct {
	types.Context
}

// GetName is a mock implementation.
func (m *Mock_Context_doWhenReachExecEndTime) GetName() string {
	// Controlled by mockey.
	return ""
}

// Respond is a mock implementation.
func (m *Mock_Context_doWhenReachExecEndTime) Respond(response interface{}) {
	// Controlled by mockey.
}

// Self is a mock implementation.
func (m *Mock_Context_doWhenReachExecEndTime) Self() *actor.PID {
	// Controlled by mockey.
	return nil
}

// Send is a mock implementation.
func (m *Mock_Context_doWhenReachExecEndTime) Send(pid *actor.PID, message interface{}) {
	// Controlled by mockey.
}
func Test_ShardingDDLTicketActor_doWhenReachExecEndTime_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("Test ShardingDDLTicketActor.doWhenReachExecEndTime", t, func() {
		mockey.PatchConvey("正常执行场景-超出执行时间窗口", func() {
			// 场景描述：
			// 当工单执行时间超出允许的执行时间窗口时，调用 doWhenReachExecEndTime 方法。
			// 数据构造：
			// - actor 实例，包含一个非空的 state
			// - mock 的 context，用于模拟 actor 上下文行为
			// 逻辑链路：
			// 1. Mock log.Info, 验证日志被正确调用。
			// 2. Mock ctx.GetName, 返回一个预设的工单ID。
			// 3. Mock v.UpdateTicketRepo, 验证工单状态被更新为 TicketError。
			// 4. Mock ctx.Respond, 验证向请求方返回执行失败的消息。
			// 5. 调用目标函数，验证 actor 状态被更新为 ExecuteFailed。
			// 6. Mock ctx.Self 和 ctx.Send, 验证 actor 向自身发送了 SuicideMessage。
			// 7. 调用目标函数，验证整个流程按预期执行。

			// 数据构造
			const ticketID = "ticket-123"
			selfPID := actor.NewPID("local", "self-id")

			v := &ShardingDDLTicketActor{
				state: &ShardingDDLTicketState{},
			}
			ctx := &Mock_Context_doWhenReachExecEndTime{}

			// Mock
			mockey.Mock(log.Info).Return().Build() // Also covers the log call inside updateActorState
			mockey.Mock((*Mock_Context_doWhenReachExecEndTime).GetName).Return(ticketID).Build()

			mockey.Mock((*ShardingDDLTicketActor).UpdateTicketRepo).To(func(_ *ShardingDDLTicketActor, _ types.Context, ticket *dao.Ticket) error {
				convey.So(ticket.TicketStatus, convey.ShouldEqual, int8(model.TicketStatus_TicketError))
				convey.So(ticket.Description, convey.ShouldEqual, "The execution time exceeds the allowable execution time window and will not be executed.")
				return nil
			}).Build()

			mockey.Mock((*Mock_Context_doWhenReachExecEndTime).Respond).To(func(_ *Mock_Context_doWhenReachExecEndTime, response interface{}) {
				msg, ok := response.(*shared.TicketExecuted)
				convey.So(ok, convey.ShouldBeTrue)
				convey.So(msg.TicketId, convey.ShouldEqual, ticketID)
				convey.So(msg.Code, convey.ShouldEqual, shared.ExecutedFail)
				convey.So(msg.Message, convey.ShouldEqual, "The execution time exceeds the allowable execution time window and will not be executed.")
			}).Build()

			mockey.Mock((*Mock_Context_doWhenReachExecEndTime).Self).Return(selfPID).Build()

			mockey.Mock((*Mock_Context_doWhenReachExecEndTime).Send).To(func(_ *Mock_Context_doWhenReachExecEndTime, pid *actor.PID, message interface{}) {
				_, ok := message.(*proto.SuicideMessage)
				convey.So(ok, convey.ShouldBeTrue)
				convey.So(pid, convey.ShouldEqual, selfPID)
			}).Build()

			// 调用
			v.doWhenReachExecEndTime(ctx)

			// 断言
			// 断言私有方法 updateActorState 的副作用
			convey.So(v.state.CurrentAction, convey.ShouldEqual, model.ExecShardingDDLTicketAction_ExecuteFailed)
		})
	})
}

// Mock_Context_GetShardingDDLTicketStatus is a mock for the types.Context interface.
type Mock_Context_GetShardingDDLTicketStatus struct {
	types.Context
}

func (m *Mock_Context_GetShardingDDLTicketStatus) WithValue(key, val interface{}) {}
func (m *Mock_Context_GetShardingDDLTicketStatus) GetName() string {
	return "12345"
}

// Mock_WorkflowDAL_GetShardingDDLTicketStatus is a mock for the dal.WorkflowDAL interface.
type Mock_WorkflowDAL_GetShardingDDLTicketStatus struct {
	dal.WorkflowDAL
}

func (m *Mock_WorkflowDAL_GetShardingDDLTicketStatus) DescribeByTicketID(ctx context.Context, ticketId int64) (*dao.Ticket, error) {
	return &dao.Ticket{}, nil
}
func Test_ShardingDDLTicketActor_GetShardingDDLTicketStatus_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("Test_ShardingDDLTicketActor_GetShardingDDLTicketStatus", t, func() {
		// 构造共享的 actor 和 ctx
		actor1 := &ShardingDDLTicketActor{
			state: &ShardingDDLTicketState{
				TenantId: "test_tenant",
				UserId:   "test_user",
			},
			workflowDal: &Mock_WorkflowDAL_GetShardingDDLTicketStatus{},
		}
		mockCtx := &Mock_Context_GetShardingDDLTicketStatus{}

		mockey.PatchConvey("成功场景 - 获取工单成功并调用DescribeOnlineDDLTask", func() {
			// 场景描述：
			// 验证当从数据库成功获取到工单信息后，函数会继续调用 DescribeOnlineDDLTask 方法处理后续逻辑。
			// 数据构造：
			// - actor.state 包含租户ID和用户ID
			// - workflowDal.DescribeByTicketID 返回一个有效的 dao.Ticket 对象和 nil error
			// 逻辑链路：
			// 1. Mock ctx.WithValue, ctx.GetName, fwctx.GetTenantID, fwctx.GetUserID, log.Info, utils.Show
			// 2. Mock workflowDal.DescribeByTicketID 成功返回一个 ticket 对象
			// 3. Mock ShardingDDLTicketActor.DescribeOnlineDDLTask 被调用，表明主流程继续执行
			// 4. 调用 GetShardingDDLTicketStatus
			// 5. 断言无错误发生

			// Mock
			mockey.Mock((*Mock_Context_GetShardingDDLTicketStatus).WithValue).Return().Build()
			mockey.Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
			mockey.Mock(fwctx.GetTenantID).Return("test_tenant").Build()
			mockey.Mock(fwctx.GetUserID).Return("test_user").Build()
			mockey.Mock((*Mock_Context_GetShardingDDLTicketStatus).GetName).Return("12345").Build()
			mockey.Mock(utils.Show).Return("ticket_info").Build()

			ticket := &dao.Ticket{TicketId: 12345}
			mockey.Mock((*Mock_WorkflowDAL_GetShardingDDLTicketStatus).DescribeByTicketID).Return(ticket, nil).Build()

			mockey.Mock((*ShardingDDLTicketActor).DescribeOnlineDDLTask).To(func(v *ShardingDDLTicketActor, ctx types.Context, ticket *dao.Ticket) {}).Build()

			// 调用
			actor1.GetShardingDDLTicketStatus(mockCtx)

			// 断言
			// 验证 DescribeOnlineDDLTask 被调用即可，无需额外断言
		})

		mockey.PatchConvey("失败场景 - 获取工单失败", func() {
			// 场景描述：
			// 验证当从数据库获取工单信息失败时，函数会记录警告日志并提前返回，不会执行后续的 DescribeOnlineDDLTask 逻辑。
			// 数据构造：
			// - actor.state 包含租户ID和用户ID
			// - workflowDal.DescribeByTicketID 返回 nil 和一个 error
			// 逻辑链路：
			// 1. Mock ctx.WithValue, ctx.GetName, fwctx.GetTenantID, fwctx.GetUserID, log.Info
			// 2. Mock workflowDal.DescribeByTicketID 返回一个错误
			// 3. Mock log.Warn 被调用，以确认错误被记录
			// 4. 调用 GetShardingDDLTicketStatus
			// 5. 验证函数提前返回，DescribeOnlineDDLTask 未被调用

			// Mock
			mockey.Mock((*Mock_Context_GetShardingDDLTicketStatus).WithValue).Return().Build()
			mockey.Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
			mockey.Mock(fwctx.GetTenantID).Return("test_tenant").Build()
			mockey.Mock(fwctx.GetUserID).Return("test_user").Build()
			mockey.Mock((*Mock_Context_GetShardingDDLTicketStatus).GetName).Return("12345").Build()

			dbErr := errors.New("database error")
			mockey.Mock((*Mock_WorkflowDAL_GetShardingDDLTicketStatus).DescribeByTicketID).Return(nil, dbErr).Build()
			mockey.Mock(log.Warn).To(func(ctx context.Context, format string, args ...interface{}) {
				convey.So(format, convey.ShouldEqual, "ticket: get ticket from ticketId err:%s")
				convey.So(args[0], convey.ShouldEqual, "database error")
			}).Build()

			// 调用
			actor1.GetShardingDDLTicketStatus(mockCtx)

			// 断言
			// 验证 log.Warn 被调用即可，无需额外断言
		})
	})
}

// Mock_Context_ShardingDDLTicketActor_generateStopCronTask is a mock for the types.Context interface.
type Mock_Context_ShardingDDLTicketActor_generateStopCronTask struct {
	types.Context
}

// ClientOf mocks the ClientOf method of the types.Context interface.
func (m *Mock_Context_ShardingDDLTicketActor_generateStopCronTask) ClientOf(kind string) cli.KindClient {
	// This method body is a placeholder; the actual behavior is defined by mockey.
	return nil
}

// Mock_KindClient_ShardingDDLTicketActor_generateStopCronTask is a mock for the cli.KindClient interface.
type Mock_KindClient_ShardingDDLTicketActor_generateStopCronTask struct {
	cli.KindClient
}

// Send mocks the Send method of the cli.KindClient interface.
func (m *Mock_KindClient_ShardingDDLTicketActor_generateStopCronTask) Send(ctx context.Context, actorName string, msg interface{}, callopts ...*cluster.GrainCallOptions) error {
	// This method body is a placeholder; the actual behavior is defined by mockey.
	return nil
}
func Test_ShardingDDLTicketActor_generateStopCronTask_BitsUTGen(t *testing.T) {
	// Common test data
	mockActor := &ShardingDDLTicketActor{}
	mockTicket := &dao.Ticket{
		TenantId:          "test-tenant",
		InstanceId:        "test-instance",
		TaskId:            "test-task",
		ExecutableEndTime: time.Now().Add(1 * time.Minute).Unix(),
	}
	mockKindClient := &Mock_KindClient_ShardingDDLTicketActor_generateStopCronTask{}
	mockCtx := &Mock_Context_ShardingDDLTicketActor_generateStopCronTask{}

	mockey.PatchConvey("Test ShardingDDLTicketActor.generateStopCronTask", t, func() {
		// Mock the cron instance creation and lifecycle
		mockCron := &cron.Cron{}
		mockey.Mock(cron.New).Return(mockCron).Build()
		mockey.Mock((*cron.Cron).Start).Return().Build()

		mockey.PatchConvey("Success scenario", func() {
			// 场景描述：
			// 当所有依赖都正常返回时，验证定时任务被正确设置，并且定时任务执行时能成功发送actor消息。
			// 数据构造：
			// - ticket: 包含租户、实例、任务ID和未来的执行结束时间。
			// - actor: ShardingDDLTicketActor实例。
			// - ctx: Mock的actor context。
			// 逻辑链路：
			// 1. Mock cron.New 返回一个可控的 cron 实例。
			// 2. Mock cron.Start 正常执行。
			// 3. Mock cron.AddFunc 捕获被添加的函数，并返回成功。
			// 4. Mock actor context 的 ClientOf 和 Send 方法，使其成功返回。
			// 5. Mock log.Info 以避免日志输出。
			// 6. 调用目标函数 generateStopCronTask。
			// 7. 执行被捕获的 cron 函数。
			// 8. 断言整个过程没有panic。
			var capturedFunc func()
			mockey.Mock((*cron.Cron).AddFunc).To(func(c *cron.Cron, spec string, cmd func()) (cron.EntryID, error) {
				capturedFunc = cmd
				return 1, nil
			}).Build()
			mockey.Mock(log.Info).Return().Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_generateStopCronTask).ClientOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_ShardingDDLTicketActor_generateStopCronTask).Send).Return(nil).Build()

			// 调用
			mockActor.generateStopCronTask(mockCtx, mockTicket)

			// 断言并执行捕获的函数
			convey.So(capturedFunc, convey.ShouldNotBeNil)
			if capturedFunc != nil {
				capturedFunc()
			}
		})

		mockey.PatchConvey("Failure scenario – cron.AddFunc returns error", func() {
			// 场景描述：
			// 当 cron.AddFunc 添加任务失败时，函数应能优雅处理错误并直接返回。
			// 数据构造：
			// - ticket, actor, ctx: 使用通用测试数据。
			// 逻辑链路：
			// 1. Mock cron.New 和 cron.Start 正常执行。
			// 2. Mock cron.AddFunc 返回一个错误。
			// 3. Mock log.Info 以避免日志输出。
			// 4. 调用目标函数 generateStopCronTask。
			// 5. 断言函数能正常返回，没有panic。
			mockey.Mock((*cron.Cron).AddFunc).Return(cron.EntryID(0), errors.New("add func failed")).Build()
			mockey.Mock(log.Info).Return().Build()

			// 调用
			mockActor.generateStopCronTask(mockCtx, mockTicket)

			// 断言
			convey.So(true, convey.ShouldBeTrue) // The main check is that no panic occurred
		})

		mockey.PatchConvey("Failure scenario – send actor message returns error", func() {
			// 场景描述：
			// 当定时任务被触发，但发送actor消息失败时，函数应能记录警告日志并优雅处理。
			// 数据构造：
			// - ticket, actor, ctx: 使用通用测试数据。
			// 逻辑链路：
			// 1. Mock cron.New, cron.Start, cron.AddFunc 均成功，并捕获定时任务函数。
			// 2. Mock actor context 的 ClientOf 方法成功，但 Send 方法返回错误。
			// 3. Mock log.Info 和 log.Warn 以避免日志输出。
			// 4. 调用目标函数 generateStopCronTask。
			// 5. 执行被捕获的 cron 函数。
			// 6. 断言函数能正常返回，没有panic。
			var capturedFunc func()
			mockey.Mock((*cron.Cron).AddFunc).To(func(c *cron.Cron, spec string, cmd func()) (cron.EntryID, error) {
				capturedFunc = cmd
				return 1, nil
			}).Build()
			mockey.Mock(log.Info).Return().Build()
			mockey.Mock(log.Warn).Return().Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_generateStopCronTask).ClientOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_ShardingDDLTicketActor_generateStopCronTask).Send).Return(errors.New("send failed")).Build()

			// 调用
			mockActor.generateStopCronTask(mockCtx, mockTicket)

			// 断言并执行捕获的函数
			convey.So(capturedFunc, convey.ShouldNotBeNil)
			if capturedFunc != nil {
				capturedFunc()
			}
			convey.So(true, convey.ShouldBeTrue) // The main check is that no panic occurred
		})
	})
}

// MockContext_sendExecTicketMsg is a mock implementation of the types.Context interface
// for testing the sendExecTicketMsg method.
type MockContext_sendExecTicketMsg struct {
	types.Context
}

// Self mocks the Self method of the context.
func (m *MockContext_sendExecTicketMsg) Self() *actor.PID {
	return nil // This will be mocked by mockey
}

// Send mocks the Send method of the context.
func (m *MockContext_sendExecTicketMsg) Send(pid *actor.PID, message interface{}) {
	// This will be mocked by mockey
}
func TestShardingDDLTicketActor_sendExecTicketMsg_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("TestShardingDDLTicketActor_sendExecTicketMsg", t, func() {
		mockey.PatchConvey("成功场景 - 正常发送执行消息", func() {
			// 场景描述：
			// 验证当调用 sendExecTicketMsg 方法时，能够正确地记录日志并向自身发送一个构造正确的 ExecVeDBDDLTicket 消息。
			// 数据构造：
			// - ShardingDDLTicketActor: 包含 state，其中有 TenantId, UserId, 和 DataSource。
			// - dao.Ticket: 包含 TicketId 和其他执行相关的属性。
			// - types.Context: 使用 mock 实现，用于捕获 Send 方法的调用。
			// 逻辑链路：
			// 1. Mock log.Info 函数，验证其被调用。
			// 2. Mock ctx.Self() 方法，返回一个预设的 PID。
			// 3. Mock ctx.Send() 方法，捕获发送的消息，并断言消息内容是否符合预期。
			// 4. 调用目标函数 sendExecTicketMsg。
			// 5. 在 Send 的 mock 实现中完成所有断言。

			// 数据构造
			actorState := &ShardingDDLTicketState{
				TenantId: "test-tenant-id",
				UserId:   "test-user-id",
				Ds: &shared.DataSource{
					Address: "localhost:3306",
					Db:      "testdb",
				},
			}
			v := &ShardingDDLTicketActor{
				state: actorState,
			}
			ticket := &dao.Ticket{
				TicketId:            1001,
				TicketType:          1,
				ExecuteType:         2,
				ExecutableStartTime: 1625097600,
				ExecutableEndTime:   1625184000,
			}
			mockCtx := &MockContext_sendExecTicketMsg{}
			selfPID := actor.NewPID("local", "test-actor")

			// Mock
			mockey.Mock(log.Info).Return().Build()
			mockey.Mock((*MockContext_sendExecTicketMsg).Self).Return(selfPID).Build()
			mockey.Mock((*MockContext_sendExecTicketMsg).Send).To(func(pid *actor.PID, message interface{}) {
				convey.So(pid, convey.ShouldEqual, selfPID)
				msg, ok := message.(*shared.ExecVeDBDDLTicket)
				convey.So(ok, convey.ShouldBeTrue)
				convey.So(msg.TenantID, convey.ShouldEqual, actorState.TenantId)
				convey.So(msg.UserID, convey.ShouldEqual, actorState.UserId)
				convey.So(msg.Source, convey.ShouldEqual, actorState.Ds)
				convey.So(msg.TicketType, convey.ShouldEqual, int32(ticket.TicketType))
				convey.So(msg.ExecuteType, convey.ShouldEqual, int32(ticket.ExecuteType))
				convey.So(msg.ExecutableStartTime, convey.ShouldEqual, int32(ticket.ExecutableStartTime))
				convey.So(msg.ExecutableEndTime, convey.ShouldEqual, int32(ticket.ExecutableEndTime))
			}).Build()

			// 调用
			v.sendExecTicketMsg(mockCtx, ticket)

			// 断言（已在 Send 的 Mock 中完成）
		})
	})
}

// Mock_Context_ShardingDDLTicketActor_UpdateTicketRepo is a mock structure for the types.Context interface.
type Mock_Context_ShardingDDLTicketActor_UpdateTicketRepo struct {
	types.Context
}

// GetName is a mock implementation of the GetName method.
func (m *Mock_Context_ShardingDDLTicketActor_UpdateTicketRepo) GetName() string {
	// This is a placeholder, the actual return value will be provided by mockey.
	return ""
}

// Mock_WorkflowDAL_ShardingDDLTicketActor_UpdateTicketRepo is a mock structure for the dal.WorkflowDAL interface.
type Mock_WorkflowDAL_ShardingDDLTicketActor_UpdateTicketRepo struct {
	dal.WorkflowDAL
}

// UpdateWorkStatus is a mock implementation of the UpdateWorkStatus method.
func (m *Mock_WorkflowDAL_ShardingDDLTicketActor_UpdateTicketRepo) UpdateWorkStatus(ctx context.Context, ticket *dao.Ticket) error {
	// This is a placeholder, the actual return value will be provided by mockey.
	return nil
}
func Test_ShardingDDLTicketActor_UpdateTicketRepo_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("Test_ShardingDDLTicketActor_UpdateTicketRepo", t, func() {
		mockey.PatchConvey("成功场景 - 成功更新工单仓库状态", func() {
			// 场景描述：
			// 当所有依赖都正常返回无异常情况下，验证目标函数可以成功更新工单状态。
			// 数据构造：
			// - actor: ShardingDDLTicketActor 实例，包含一个 mock 的 workflowDal。
			// - ctx: 一个 mock 的 types.Context，GetName 方法将返回一个有效的工单 ID 字符串。
			// - ticket: 一个 dao.Ticket 实例，用于更新。
			// 逻辑链路：
			// 1. Mock ctx.GetName() 返回工单ID "12345"。
			// 2. Mock workflowDal.UpdateWorkStatus() 成功，返回 nil。
			// 3. Mock log.Info() 以避免打印日志。
			// 4. 调用 UpdateTicketRepo 方法。
			// 5. 断言函数返回的 error 为 nil。
			// 6. 断言 ticket.TicketId 被正确更新为 12345。

			// 数据构造
			mockDal := &Mock_WorkflowDAL_ShardingDDLTicketActor_UpdateTicketRepo{}
			actor1 := &ShardingDDLTicketActor{
				workflowDal: mockDal,
			}
			mockCtx := &Mock_Context_ShardingDDLTicketActor_UpdateTicketRepo{}
			ticket := &dao.Ticket{
				TicketStatus: 1,
			}

			// Mock
			mockey.Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_UpdateTicketRepo).GetName).Return("12345").Build()
			mockey.Mock((*Mock_WorkflowDAL_ShardingDDLTicketActor_UpdateTicketRepo).UpdateWorkStatus).Return(nil).Build()

			// 调用
			err := actor1.UpdateTicketRepo(mockCtx, ticket)

			// 断言
			convey.So(err, convey.ShouldBeNil)
			convey.So(ticket.TicketId, convey.ShouldEqual, 12345)
		})

		mockey.PatchConvey("失败场景 - 更新工单状态时数据库返回错误", func() {
			// 场景描述：
			// 当依赖的 workflowDal.UpdateWorkStatus 方法返回错误时，验证目标函数会将错误向上传递。
			// 数据构造：
			// - actor: ShardingDDLTicketActor 实例，包含一个 mock 的 workflowDal。
			// - ctx: 一个 mock 的 types.Context。
			// - ticket: 一个 dao.Ticket 实例。
			// - dbErr: 一个自定义的 error，用于模拟数据库错误。
			// 逻辑链路：
			// 1. Mock ctx.GetName() 返回工单ID "12345"。
			// 2. Mock workflowDal.UpdateWorkStatus() 失败，返回一个预设的 error。
			// 3. Mock log.Info() 和 log.Warn() 以避免打印日志。
			// 4. 调用 UpdateTicketRepo 方法。
			// 5. 断言函数返回的 error 不为 nil，且与预设的 error 相同。

			// 数据构造
			mockDal := &Mock_WorkflowDAL_ShardingDDLTicketActor_UpdateTicketRepo{}
			actor1 := &ShardingDDLTicketActor{
				workflowDal: mockDal,
			}
			mockCtx := &Mock_Context_ShardingDDLTicketActor_UpdateTicketRepo{}
			ticket := &dao.Ticket{
				TicketStatus: 2,
			}
			dbErr := errors.New("database update failed")

			// Mock
			mockey.Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
			mockey.Mock(log.Warn).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_UpdateTicketRepo).GetName).Return("54321").Build()
			mockey.Mock((*Mock_WorkflowDAL_ShardingDDLTicketActor_UpdateTicketRepo).UpdateWorkStatus).Return(dbErr).Build()

			// 调用
			err := actor1.UpdateTicketRepo(mockCtx, ticket)

			// 断言
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err, convey.ShouldEqual, dbErr)
			convey.So(ticket.TicketId, convey.ShouldEqual, 54321) // TicketId is updated before the error occurs
		})
	})
}

// Mock_Context_ShardingDDLTicketActor_doWhenReachEndTime mocks the types.Context interface
type Mock_Context_ShardingDDLTicketActor_doWhenReachEndTime struct {
	types.Context
}

// ClientOf is the method we need to mock
func (m *Mock_Context_ShardingDDLTicketActor_doWhenReachEndTime) ClientOf(kind string) cli.KindClient {
	// This implementation will be replaced by mockey
	return nil
}

// Mock_KindClient_ShardingDDLTicketActor_doWhenReachEndTime mocks the cli.KindClient interface
type Mock_KindClient_ShardingDDLTicketActor_doWhenReachEndTime struct {
	cli.KindClient
}

// Call is the method we need to mock. Its signature must match the interface.
func (m *Mock_KindClient_ShardingDDLTicketActor_doWhenReachEndTime) Call(ctx context.Context, name string, msg interface{}, callopts ...*cluster.GrainCallOptions) (interface{}, error) {
	// This implementation will be replaced by mockey
	return nil, nil
}
func Test_ShardingDDLTicketActor_doWhenReachEndTime_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("Test_ShardingDDLTicketActor_doWhenReachEndTime", t, func() {
		// 数据构造
		actor1 := &ShardingDDLTicketActor{
			state: &ShardingDDLTicketState{},
		}
		mockCtx := &Mock_Context_ShardingDDLTicketActor_doWhenReachEndTime{}
		mockKindClient := &Mock_KindClient_ShardingDDLTicketActor_doWhenReachEndTime{}

		mockey.PatchConvey("成功场景-定时任务到期且DDL任务已创建", func() {
			// 场景描述：
			// 当工单类型为定时执行(Cron)且DDL任务已创建时，函数应调用ActorClient停止SQL任务。
			// 数据构造：
			// - ticket.ExecuteType = model.ExecuteType_Cron
			// - actor.state.IsDDLTaskCreated = true
			// - ticket.TaskId 和 ticket.TenantId 设置为非空字符串
			// 逻辑链路：
			// 1. 检查工单执行类型和DDL任务创建状态，条件满足。
			// 2. Mock ctx.ClientOf() 返回一个mock的KindClient。
			// 3. Mock KindClient.Call() 方法成功返回 (nil, nil)。
			// 4. 调用目标函数 doWhenReachEndTime。
			// 5. 断言函数返回值为 nil。

			// 数据构造
			actor1.state.IsDDLTaskCreated = true
			ticket := &dao.Ticket{
				ExecuteType: int8(model.ExecuteType_Cron),
				TaskId:      "test-task-id",
				TenantId:    "test-tenant-id",
				InstanceId:  "test-instance-id",
			}

			// Mock
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_doWhenReachEndTime).ClientOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_ShardingDDLTicketActor_doWhenReachEndTime).Call).Return(nil, nil).Build()

			// 调用
			err := actor1.doWhenReachEndTime(mockCtx, ticket)

			// 断言
			convey.So(err, convey.ShouldBeNil)
		})

		mockey.PatchConvey("失败场景-调用Actor停止任务失败", func() {
			// 场景描述：
			// 当工单类型为定时执行且DDL任务已创建时，尝试停止SQL任务，但Actor调用失败。
			// 数据构造：
			// - ticket.ExecuteType = model.ExecuteType_Cron
			// - actor.state.IsDDLTaskCreated = true
			// 逻辑链路：
			// 1. 检查工单执行类型和DDL任务创建状态，条件满足。
			// 2. Mock ctx.ClientOf() 返回一个mock的KindClient。
			// 3. Mock KindClient.Call() 方法返回一个错误。
			// 4. Mock log.Error() 以避免在测试中打印错误日志。
			// 5. 调用目标函数 doWhenReachEndTime。
			// 6. 断言函数返回值为mock的错误。

			// 数据构造
			actor1.state.IsDDLTaskCreated = true
			ticket := &dao.Ticket{
				ExecuteType: int8(model.ExecuteType_Cron),
				TaskId:      "test-task-id",
				TenantId:    "test-tenant-id",
				InstanceId:  "test-instance-id",
			}
			expectedErr := errors.New("actor call failed")

			// Mock
			mockey.Mock((*Mock_Context_ShardingDDLTicketActor_doWhenReachEndTime).ClientOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_ShardingDDLTicketActor_doWhenReachEndTime).Call).Return(nil, expectedErr).Build()
			mockey.Mock(log.Error).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()

			// 调用
			err := actor1.doWhenReachEndTime(mockCtx, ticket)

			// 断言
			convey.So(err, convey.ShouldNotBeNil)
			convey.So(err.Error(), convey.ShouldContainSubstring, "actor call failed")
		})

		mockey.PatchConvey("成功场景-非定时任务直接返回", func() {
			// 场景描述：
			// 当工单的执行类型不是定时执行(Cron)时，函数应直接返回nil，不执行任何操作。
			// 数据构造：
			// - ticket.ExecuteType != model.ExecuteType_Cron (e.g., model.ExecuteType_Manual)
			// - actor.state.IsDDLTaskCreated = true
			// 逻辑链路：
			// 1. 检查工单执行类型，条件不满足。
			// 2. 函数直接返回 nil。
			// 3. 验证没有Actor调用发生。

			// 数据构造
			actor1.state.IsDDLTaskCreated = true
			ticket := &dao.Ticket{
				ExecuteType: int8(model.ExecuteType_Manual),
			}

			// 调用
			err := actor1.doWhenReachEndTime(mockCtx, ticket)

			// 断言
			convey.So(err, convey.ShouldBeNil)
		})

		mockey.PatchConvey("成功场景-DDL任务未创建直接返回", func() {
			// 场景描述：
			// 当工单类型为定时执行，但DDL任务尚未创建时，函数应直接返回nil，不执行任何操作。
			// 数据构造：
			// - ticket.ExecuteType = model.ExecuteType_Cron
			// - actor.state.IsDDLTaskCreated = false
			// 逻辑链路：
			// 1. 检查DDL任务创建状态，条件不满足。
			// 2. 函数直接返回 nil。
			// 3. 验证没有Actor调用发生。

			// 数据构造
			actor1.state.IsDDLTaskCreated = false
			ticket := &dao.Ticket{
				ExecuteType: int8(model.ExecuteType_Cron),
			}

			// 调用
			err := actor1.doWhenReachEndTime(mockCtx, ticket)

			// 断言
			convey.So(err, convey.ShouldBeNil)
		})
	})
}

// Mock_Context_sendStopVeDBDDLTaskToSQLTaskActor is a mock for the types.Context interface,
// used in the test for sendStopVeDBDDLTaskToSQLTaskActor.
type Mock_Context_sendStopVeDBDDLTaskToSQLTaskActor struct {
	types.Context
}

// Mock_KindClient_sendStopVeDBDDLTaskToSQLTaskActor is a mock for the cli.KindClient interface,
// used in the test for sendStopVeDBDDLTaskToSQLTaskActor.
type Mock_KindClient_sendStopVeDBDDLTaskToSQLTaskActor struct {
	cli.KindClient
}

func Test_ShardingDDLTicketActor_sendStopVeDBDDLTaskToSQLTaskActor_BitsUTGen(t *testing.T) {
	// The target function is a method of ShardingDDLTicketActor.
	// We can create a zero-value instance since the method doesn't use any of its fields.
	actor1 := &ShardingDDLTicketActor{}

	mockey.PatchConvey("Test_ShardingDDLTicketActor_sendStopVeDBDDLTaskToSQLTaskActor", t, func() {
		// Data setup common to all scenarios
		ticket := &dao.Ticket{
			TenantId:   "test_tenant",
			InstanceId: "test_instance",
			TaskId:     "test_task",
		}
		mockCtx := &Mock_Context_sendStopVeDBDDLTaskToSQLTaskActor{}
		mockKindClient := &Mock_KindClient_sendStopVeDBDDLTaskToSQLTaskActor{}

		mockey.PatchConvey("成功场景: 成功发送停止任务消息", func() {
			// 场景描述：
			// 当 actor system client 成功发送 StopDDLTaskReq 消息时，函数应正常执行，不记录警告日志。
			// 数据构造：
			// - ticket: 一个包含 TenantId, InstanceId, TaskId 的有效 ticket 对象。
			// - ctx: 一个 mock 的 types.Context。
			// - kindClient: 一个 mock 的 cli.KindClient。
			// 逻辑链路：
			// 1. Mock log.Info 函数，以避免在测试期间产生实际的日志输出。
			// 2. Mock ctx.ClientOf 方法，使其返回预设的 mock KindClient。
			// 3. Mock KindClient.Send 方法，使其在接收到正确的参数时返回 nil (表示成功)。
			// 4. 调用目标函数 sendStopVeDBDDLTaskToSQLTaskActor。
			// 5. 验证函数能够顺利执行完毕，没有发生 panic。

			// Mock
			mockey.Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()

			mockey.Mock((*Mock_Context_sendStopVeDBDDLTaskToSQLTaskActor).ClientOf).Return(mockKindClient).Build()

			expectedActorName := strings.Join([]string{ticket.TenantId, ticket.InstanceId, ticket.TaskId}, consts.ActorSplitSymbol)
			mockey.Mock((*Mock_KindClient_sendStopVeDBDDLTaskToSQLTaskActor).Send).
				When(func(ctx context.Context, actorName string, msg interface{}, callopts ...*cluster.GrainCallOptions) bool {
					req, ok := msg.(*shared.StopDDLTaskReq)
					return ok &&
						actorName == expectedActorName &&
						req.SqlTaskId == ticket.TaskId &&
						req.TenantId == ticket.TenantId
				}).
				Return(nil).Build()

			// 调用
			actor1.sendStopVeDBDDLTaskToSQLTaskActor(mockCtx, ticket)

			// 断言
			// 成功执行无 panic 即可认为通过。
			convey.So(true, convey.ShouldBeTrue)
		})

		mockey.PatchConvey("失败场景: 发送停止任务消息失败", func() {
			// 场景描述：
			// 当 actor system client 发送 StopDDLTaskReq 消息失败时，函数应记录一条警告日志并正常返回。
			// 数据构造：
			// - ticket: 一个包含 TenantId, InstanceId, TaskId 的有效 ticket 对象。
			// - ctx: 一个 mock 的 types.Context。
			// - kindClient: 一个 mock 的 cli.KindClient。
			// 逻辑链路：
			// 1. Mock log.Info 和 log.Warn 函数，以避免真实日志输出。
			// 2. Mock ctx.ClientOf 方法，使其返回 mock KindClient。
			// 3. Mock KindClient.Send 方法，使其返回一个 error，模拟发送失败。
			// 4. 调用目标函数 sendStopVeDBDDLTaskToSQLTaskActor。
			// 5. 验证函数能够顺利执行完毕，没有发生 panic。

			// Mock
			mockey.Mock(log.Info).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
			mockey.Mock(log.Warn).To(func(ctx context.Context, format string, args ...interface{}) {}).Build()
			mockey.Mock((*Mock_Context_sendStopVeDBDDLTaskToSQLTaskActor).ClientOf).Return(mockKindClient).Build()
			mockey.Mock((*Mock_KindClient_sendStopVeDBDDLTaskToSQLTaskActor).Send).Return(errors.New("send failed")).Build()

			// 调用
			actor1.sendStopVeDBDDLTaskToSQLTaskActor(mockCtx, ticket)

			// 断言
			// 成功执行无 panic 即可认为通过。
			convey.So(true, convey.ShouldBeTrue)
		})
	})
}

// Mock_Context_initState 是为 initState 方法测试伪造的 types.Context 接口实现
type Mock_Context_initState struct {
	types.Context
}

// GetName 是伪造的接口方法，将在测试中被 mockey 拦截
func (m *Mock_Context_initState) GetName() string {
	return ""
}

// WithValue 是伪造的接口方法，将在测试中被 mockey 拦截
func (m *Mock_Context_initState) WithValue(key, val interface{}) {
}
func Test_ShardingDDLTicketActor_initState_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("Test_ShardingDDLTicketActor_initState", t, func() {
		mockey.PatchConvey("成功场景: 正确初始化 actor 状态", func() {
			// 场景描述：
			// 测试在给定消息和上下文的情况下，initState 函数是否能正确地初始化 ShardingDDLTicketActor 的 state 字段，
			// 并通过上下文设置业务信息。
			// 数据构造：
			// - v: 一个 ShardingDDLTicketActor 实例，其 state 字段已初始化。
			// - msg: 一个 ExecShardingFreeLockDDLTicket 消息，包含 TenantID, UserID 和 Source 数据源信息。
			// - ctx: 一个 mock 的 types.Context 实例。
			// 逻辑链路：
			// 1. Mock ctx.GetName() 方法，使其返回一个预设的 session ID。
			// 2. Mock ctx.WithValue() 方法，用于捕获设置的业务上下文并进行验证。
			// 3. 调用 initState 方法。
			// 4. 断言 actor 的 state 字段 (TenantId, Ds, SessionId) 被正确设置。
			// 5. 断言 ctx.WithValue() 方法被以正确的参数调用。

			// 数据构造
			actor1 := &ShardingDDLTicketActor{
				state: &ShardingDDLTicketState{},
			}
			msg := &shared.ExecShardingFreeLockDDLTicket{
				TenantID: "test-tenant-id",
				UserID:   "test-user-id",
				Source: &shared.DataSource{
					Address: "localhost:3306",
					Db:      "test_db",
				},
			}
			mockCtx := &Mock_Context_initState{}
			expectedSessionID := "test-session-id"

			var capturedKey interface{}
			var capturedVal interface{}

			// Mock
			mockey.Mock((*Mock_Context_initState).GetName).Return(expectedSessionID).Build()
			mockey.Mock((*Mock_Context_initState).WithValue).To(func(key, val interface{}) {
				capturedKey = key
				capturedVal = val
			}).Build()

			// 调用
			actor1.initState(mockCtx, msg)

			// 断言
			convey.So(actor1.state.TenantId, convey.ShouldEqual, msg.TenantID)
			convey.So(actor1.state.Ds, convey.ShouldEqual, msg.Source)
			convey.So(actor1.state.SessionId, convey.ShouldEqual, expectedSessionID)

			convey.So(capturedKey, convey.ShouldEqual, "biz-context")
			bizCtx, ok := capturedVal.(*fwctx.BizContext)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(bizCtx, convey.ShouldNotBeNil)
			convey.So(bizCtx.TenantID, convey.ShouldEqual, msg.TenantID)
			convey.So(bizCtx.UserID, convey.ShouldEqual, msg.UserID)
		})
	})
}

// Mock_Context_ShardingDDLTicketActor_OnStart mocks the types.Context interface.
// This is a stateful mock to record method calls for assertions.
type Mock_Context_ShardingDDLTicketActor_OnStart struct {
	types.Context
	// For asserting Send calls
	sendCalled bool
	sentPid    *actor.PID
	sentMsg    interface{}
	// For asserting WithValue calls
	withValueCalled bool
	withValueKey    interface{}
	withValueVal    interface{}
	// For asserting SetReceiveTimeout calls
	timeoutSet   bool
	timeoutValue time.Duration
}

func (m *Mock_Context_ShardingDDLTicketActor_OnStart) GetName() string {
	return "12345"
}
func (m *Mock_Context_ShardingDDLTicketActor_OnStart) WithValue(key, val interface{}) {
	m.withValueCalled = true
	m.withValueKey = key
	m.withValueVal = val
}
func (m *Mock_Context_ShardingDDLTicketActor_OnStart) Send(pid *actor.PID, message interface{}) {
	m.sendCalled = true
	m.sentPid = pid
	m.sentMsg = message
}
func (m *Mock_Context_ShardingDDLTicketActor_OnStart) Self() *actor.PID {
	return &actor.PID{Id: "self"}
}
func (m *Mock_Context_ShardingDDLTicketActor_OnStart) SetReceiveTimeout(d time.Duration) {
	m.timeoutSet = true
	m.timeoutValue = d
}

// Mock_WorkflowDAL_ShardingDDLTicketActor_OnStart mocks the dal.WorkflowDAL interface.
type Mock_WorkflowDAL_ShardingDDLTicketActor_OnStart struct {
	dal.WorkflowDAL
}

func (m *Mock_WorkflowDAL_ShardingDDLTicketActor_OnStart) DescribeByTicketID(ctx context.Context, ticketId int64) (*dao.Ticket, error) {
	// This method is mocked using mockey in the test cases.
	return nil, nil
}
func Test_ShardingDDLTicketActor_OnStart_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("Test ShardingDDLTicketActor.OnStart", t, func() {
		// Mock global/package-level functions
		mockey.Mock(log.Info).Return().Build()
		mockey.Mock(log.Warn).Return().Build()
		mockey.Mock(fwctx.GetTenantID).Return("test-tenant").Build()

		mockey.PatchConvey("场景: state为nil, 成功初始化", func() {
			// 场景描述：
			// 当actor启动时其内部state为nil，OnStart会负责初始化state。
			// 数据构造：
			// - ShardingDDLTicketActor 的 state 字段为 nil。
			// 逻辑链路：
			// 1. 调用 OnStart。
			// 2. 函数内部检查到 state 为 nil，会创建一个新的 ShardingDDLTicketState。
			// 3. repairTicketState 会以默认的 `Started` 状态执行，直接返回。
			// 4. 断言 actor 的 state 字段不再是 nil。

			// 数据构造
			actorInstance := &ShardingDDLTicketActor{
				state: nil, // state is nil
			}
			mockCtx := &Mock_Context_ShardingDDLTicketActor_OnStart{}

			// 调用
			actorInstance.OnStart(mockCtx)

			// 断言
			convey.So(actorInstance.state, convey.ShouldNotBeNil)
			convey.So(actorInstance.state.CurrentAction, convey.ShouldEqual, model.ExecShardingDDLTicketAction_Started)
		})

		mockey.PatchConvey("场景: CurrentAction为Started, 无需修复", func() {
			// 场景描述：
			// 当actor状态为 `Started` 时，表示是正常启动或已处理，`repairTicketState` 将直接返回，不执行任何修复操作。
			// 数据构造：
			// - ShardingDDLTicketActor 的 state.CurrentAction 为 ExecShardingDDLTicketAction_Started。
			// 逻辑链路：
			// 1. 调用 OnStart。
			// 2. repairTicketState 检查到状态为 `Started` 并直接返回。
			// 3. 验证没有额外的修复逻辑被触发（例如，没有调用 Send 或 SetReceiveTimeout）。

			// 数据构造
			actorInstance := &ShardingDDLTicketActor{
				state: &ShardingDDLTicketState{
					CurrentAction: model.ExecShardingDDLTicketAction_Started,
				},
			}
			mockCtx := &Mock_Context_ShardingDDLTicketActor_OnStart{}

			// 调用
			actorInstance.OnStart(mockCtx)

			// 断言
			convey.So(mockCtx.sendCalled, convey.ShouldBeFalse)
		})

		mockey.PatchConvey("场景: CurrentAction为ReceiveCommand, 修复成功", func() {
			// 场景描述：
			// actor在 `ReceiveCommand` 状态下重启，需要重新触发执行工单的逻辑。
			// 数据构造：
			// - state.CurrentAction 为 ExecShardingDDLTicketAction_ReceiveCommand。
			// - state.Ds, state.TenantId, state.UserId 均有值。
			// - Mock workflowDal.DescribeByTicketID 返回成功的工单信息。
			// 逻辑链路：
			// 1. OnStart 调用 repairTicketState。
			// 2. 识别到状态为 `ReceiveCommand`，进入修复逻辑。
			// 3. 调用 workflowDal.DescribeByTicketID 获取工单详情。
			// 4. 成功获取后，向自身发送 ExecShardingFreeLockDDLTicket 消息以重新执行。
			// 5. 验证 context.WithValue 和 context.Send 被正确调用。

			// 数据构造
			mockDal := &Mock_WorkflowDAL_ShardingDDLTicketActor_OnStart{}
			actorInstance := &ShardingDDLTicketActor{
				state: &ShardingDDLTicketState{
					CurrentAction: model.ExecShardingDDLTicketAction_ReceiveCommand,
					Ds:            &shared.DataSource{},
					TenantId:      "test-tenant-id",
					UserId:        "test-user-id",
				},
				workflowDal: mockDal,
			}
			mockTicket := &dao.Ticket{TicketType: 1, ExecuteType: 1, ExecutableStartTime: 100, ExecutableEndTime: 200}
			mockCtx := &Mock_Context_ShardingDDLTicketActor_OnStart{}

			// Mock
			mockey.Mock((*Mock_WorkflowDAL_ShardingDDLTicketActor_OnStart).DescribeByTicketID).Return(mockTicket, nil).Build()

			// 调用
			actorInstance.OnStart(mockCtx)

			// 断言
			convey.So(mockCtx.withValueCalled, convey.ShouldBeTrue)
			convey.So(mockCtx.sendCalled, convey.ShouldBeTrue)
			convey.So(mockCtx.sentMsg, convey.ShouldHaveSameTypeAs, &shared.ExecShardingFreeLockDDLTicket{})
			execMsg := mockCtx.sentMsg.(*shared.ExecShardingFreeLockDDLTicket)
			convey.So(execMsg.TenantID, convey.ShouldEqual, actorInstance.state.TenantId)
			convey.So(execMsg.UserID, convey.ShouldEqual, actorInstance.state.UserId)
		})

		mockey.PatchConvey("场景: CurrentAction为ReceiveCommand, 获取工单失败", func() {
			// 场景描述：
			// actor在 `ReceiveCommand` 状态下重启，但从数据库获取工单信息失败。
			// 数据构造：
			// - state.CurrentAction 为 ExecShardingDDLTicketAction_ReceiveCommand。
			// - Mock workflowDal.DescribeByTicketID 返回 error。
			// 逻辑链路：
			// 1. repairTicketState 尝试获取工单信息。
			// 2. workflowDal.DescribeByTicketID 返回错误。
			// 3. 记录警告日志并中止修复流程。
			// 4. 验证没有向自身发送消息。

			// 数据构造
			mockDal := &Mock_WorkflowDAL_ShardingDDLTicketActor_OnStart{}
			actorInstance := &ShardingDDLTicketActor{
				state: &ShardingDDLTicketState{
					CurrentAction: model.ExecShardingDDLTicketAction_ReceiveCommand,
					Ds:            &shared.DataSource{},
					TenantId:      "test-tenant-id",
					UserId:        "test-user-id",
				},
				workflowDal: mockDal,
			}
			mockCtx := &Mock_Context_ShardingDDLTicketActor_OnStart{}

			// Mock
			mockey.Mock((*Mock_WorkflowDAL_ShardingDDLTicketActor_OnStart).DescribeByTicketID).Return(nil, errors.New("db error")).Build()

			// 调用
			actorInstance.OnStart(mockCtx)

			// 断言
			convey.So(mockCtx.sendCalled, convey.ShouldBeFalse)
		})

		mockey.PatchConvey("场景: CurrentAction为ExecuteCommand, 设置超时", func() {
			// 场景描述：
			// actor在 `ExecuteCommand` 状态下重启，需要重新设置接收消息的超时。
			// 数据构造：
			// - state.CurrentAction 为 ExecShardingDDLTicketAction_ExecuteCommand。
			// 逻辑链路：
			// 1. repairTicketState 识别到状态为 `ExecuteCommand`。
			// 2. 调用 context.SetReceiveTimeout 设置超时。
			// 3. 验证 context.SetReceiveTimeout 被调用。

			// 数据构造
			actorInstance := &ShardingDDLTicketActor{
				state: &ShardingDDLTicketState{
					CurrentAction: model.ExecShardingDDLTicketAction_ExecuteCommand,
				},
			}
			mockCtx := &Mock_Context_ShardingDDLTicketActor_OnStart{}

			// 调用
			actorInstance.OnStart(mockCtx)

			// 断言
			convey.So(mockCtx.timeoutSet, convey.ShouldBeTrue)
			convey.So(mockCtx.timeoutValue, convey.ShouldEqual, ShardingDDLTicketTimeout)
		})

		mockey.PatchConvey("场景: CurrentAction为ExecuteFailed, 发送自杀消息", func() {
			// 场景描述：
			// actor重启时发现工单已处于失败状态，应主动停止。
			// 数据构造：
			// - state.CurrentAction 为 ExecShardingDDLTicketAction_ExecuteFailed。
			// 逻辑链路：
			// 1. repairTicketState 识别到状态为 `ExecuteFailed`。
			// 2. 向自身发送 SuicideMessage 以停止actor。
			// 3. 验证 context.Send 被调用且消息类型为 SuicideMessage。

			// 数据构造
			actorInstance := &ShardingDDLTicketActor{
				state: &ShardingDDLTicketState{
					CurrentAction: model.ExecShardingDDLTicketAction_ExecuteFailed,
				},
			}
			mockCtx := &Mock_Context_ShardingDDLTicketActor_OnStart{}

			// 调用
			actorInstance.OnStart(mockCtx)

			// 断言
			convey.So(mockCtx.sendCalled, convey.ShouldBeTrue)
			convey.So(mockCtx.sentMsg, convey.ShouldHaveSameTypeAs, &proto.SuicideMessage{})
		})

		mockey.PatchConvey("场景: CurrentAction为ExecuteFinished, 发送自杀消息", func() {
			// 场景描述：
			// actor重启时发现工单已处于完成状态，应主动停止。
			// 数据构造：
			// - state.CurrentAction 为 ExecShardingDDLTicketAction_ExecuteFinished。
			// 逻辑链路：
			// 1. repairTicketState 识别到状态为 `ExecuteFinished`。
			// 2. 向自身发送 SuicideMessage 以停止actor。
			// 3. 验证 context.Send 被调用且消息类型为 SuicideMessage。

			// 数据构造
			actorInstance := &ShardingDDLTicketActor{
				state: &ShardingDDLTicketState{
					CurrentAction: model.ExecShardingDDLTicketAction_ExecuteFinished,
				},
			}
			mockCtx := &Mock_Context_ShardingDDLTicketActor_OnStart{}

			// 调用
			actorInstance.OnStart(mockCtx)

			// 断言
			convey.So(mockCtx.sendCalled, convey.ShouldBeTrue)
			convey.So(mockCtx.sentMsg, convey.ShouldHaveSameTypeAs, &proto.SuicideMessage{})
		})
	})
}

func Test_newShardingDDLTicketState_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("Test_newShardingDDLTicketState", t, func() {
		mockey.PatchConvey("成功场景 - 输入为有效的JSON字节", func() {
			// 场景描述：
			// 当输入一个可以被正确反序列化为ShardingDDLTicketState结构体的JSON字节数组时，函数应返回一个被正确填充的结构体指针。
			// 数据构造：
			// - 构造一个完整的ShardingDDLTicketState实例。
			// - 使用json.Marshal将其序列化为字节数组。
			// 逻辑链路：
			// 1. 构造一个ShardingDDLTicketState实例`expectedState`。
			// 2. 将`expectedState`序列化为JSON字节数组`bytes`。
			// 3. 调用newShardingDDLTicketState(bytes)。
			// 4. 断言返回的`*ShardingDDLTicketState`指针不为nil。
			// 5. 断言返回的结构体内容与原始的`expectedState`深度相等。

			// 数据构造
			expectedState := &ShardingDDLTicketState{
				SessionId:        "test-session-id",
				ConnectionId:     "test-connection-id",
				Cs:               &entity.CommandSet{},
				Ds:               &shared.DataSource{},
				CurrentAction:    model.ExecShardingDDLTicketAction(1),
				LogID:            "test-log-id",
				TenantId:         "test-tenant-id",
				UserId:           "test-user-id",
				TicketType:       1,
				IsDDLTaskCreated: true,
			}
			bytes, err := json.Marshal(expectedState)
			convey.So(err, convey.ShouldBeNil)

			// 调用
			result := newShardingDDLTicketState(bytes)

			// 断言
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(result, convey.ShouldResemble, expectedState)
		})

		mockey.PatchConvey("失败场景 - 输入为无效的JSON字节", func() {
			// 场景描述：
			// 当输入的字节数组是无效的JSON格式时，json.Unmarshal会失败，函数应返回nil。
			// 数据构造：
			// - 一个无效的JSON字符串，如 "invalid-json"。
			// 逻辑链路：
			// 1. 构造一个无效的JSON字节数组。
			// 2. 调用newShardingDDLTicketState。
			// 3. 断言返回值为nil。

			// 数据构造
			invalidBytes := []byte("invalid-json")

			// 调用
			result := newShardingDDLTicketState(invalidBytes)

			// 断言
			convey.So(result, convey.ShouldBeNil)
		})

		mockey.PatchConvey("失败场景 - 输入为nil", func() {
			// 场景描述：
			// 当输入的字节数组为nil时，json.Unmarshal会返回错误，函数应返回nil。
			// 数据构造：
			// - 输入参数为nil。
			// 逻辑链路：
			// 1. 调用newShardingDDLTicketState并传入nil。
			// 2. 断言返回值为nil。

			// 数据构造
			// 无需构造

			// 调用
			result := newShardingDDLTicketState(nil)

			// 断言
			convey.So(result, convey.ShouldBeNil)
		})

		mockey.PatchConvey("成功场景 - 输入为空JSON对象", func() {
			// 场景描述：
			// 当输入的字节数组是合法的空JSON对象"{}"时，函数应返回一个所有字段都为零值的ShardingDDLTicketState结构体指针。
			// 数据构造：
			// - 输入参数为`[]byte("{}")`。
			// 逻辑链路：
			// 1. 调用newShardingDDLTicketState并传入`[]byte("{}")`。
			// 2. 断言返回值不为nil。
			// 3. 断言返回的结构体等于一个零值ShardingDDLTicketState。

			// 数据构造
			emptyJSON := []byte("{}")
			expectedState := &ShardingDDLTicketState{}

			// 调用
			result := newShardingDDLTicketState(emptyJSON)

			// 断言
			convey.So(result, convey.ShouldNotBeNil)
			convey.So(result, convey.ShouldResemble, expectedState)
		})
	})
}

func Test_ShardingDDLTicketActor_protectUserCall_BitsUTGen(t *testing.T) {
	// 构造一个 ShardingDDLTicketActor 实例。
	// 由于 protectUserCall 方法不访问 ShardingDDLTicketActor 的任何字段，
	// 因此可以使用一个空的实例。
	actor1 := &ShardingDDLTicketActor{}

	mockey.PatchConvey("TestShardingDDLTicketActor_protectUserCall", t, func() {
		// 构造一个 types.Context。由于该上下文仅传递给被 mock 的 log.Warn，
		// 且 mock 实现中不使用它，因此可以使用 nil。
		var ctx types.Context

		mockey.PatchConvey("正常场景 - fn函数不发生panic", func() {
			// 场景描述：
			// 测试当传入的函数fn正常执行不发生panic时，protectUserCall的行为。
			// 数据构造：
			// - ShardingDDLTicketActor实例
			// - 一个不panic的函数fn
			// - 一个标记fn是否被调用的布尔变量
			// - 一个标记log.Warn是否被调用的布尔变量
			// 逻辑链路：
			// 1. 构造一个正常执行的函数fn，该函数会设置fnCalled为true。
			// 2. Mock log.Warn，使其在被调用时设置logWarnCalled为true。
			// 3. 调用actor.protectUserCall。
			// 4. 断言fn被调用（fnCalled为true）。
			// 5. 断言log.Warn未被调用（logWarnCalled为false）。

			// 数据构造
			fnCalled := false
			fn := func() {
				fnCalled = true
			}
			logWarnCalled := false

			// Mock
			mockey.Mock(log.Warn).To(func(_ context.Context, _ string, _ ...interface{}) {
				logWarnCalled = true
			}).Build()

			// 调用
			actor1.protectUserCall(ctx, fn)

			// 断言
			convey.So(fnCalled, convey.ShouldBeTrue)
			convey.So(logWarnCalled, convey.ShouldBeFalse)
		})

		mockey.PatchConvey("异常场景 - fn函数发生panic", func() {
			// 场景描述：
			// 测试当传入的函数fn执行时发生panic，protectUserCall是否能正确捕获异常并记录日志。
			// 数据构造：
			// - ShardingDDLTicketActor实例
			// - 一个会panic的函数fn
			// - 一个标记log.Warn是否被调用的布尔变量
			// 逻辑链路：
			// 1. 构造一个会触发panic的函数fn。
			// 2. Mock log.Warn，使其在被调用时设置logWarnCalled为true。
			// 3. 调用actor.protectUserCall，此调用不应向外抛出panic。
			// 4. 断言log.Warn被调用（logWarnCalled为true）。

			// 数据构造
			logWarnCalled := false
			fn := func() {
				panic(errors.New("test panic"))
			}

			// Mock
			mockey.Mock(log.Warn).To(func(_ context.Context, _ string, _ ...interface{}) {
				logWarnCalled = true
			}).Build()

			// 调用
			actor1.protectUserCall(ctx, fn)

			// 断言
			convey.So(logWarnCalled, convey.ShouldBeTrue)
		})
	})
}
