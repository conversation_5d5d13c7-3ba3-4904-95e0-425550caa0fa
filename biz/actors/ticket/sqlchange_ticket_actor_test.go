package ticket

import (
	bizConfig "code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/service/mock_dbw_ticket"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/context"
	"errors"
	"fmt"
	"github.com/bytedance/mockey"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	"code.byted.org/infcs/protoactor-go/actor"
	"github.com/golang/mock/gomock"
	"github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"go.uber.org/dig"
)

type ExecTicketActorSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *ExecTicketActorSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *ExecTicketActorSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestExecTicketActorSuite(t *testing.T) {
	suite.Run(t, new(ExecTicketActorSuite))
}

func (suite *ExecTicketActorSuite) TestGetState() {
	ExecTicketActor := ExecTicketActor{}
	ret := ExecTicketActor.GetState()
	suite.NotEmpty(ret)
}

func (suite *ExecTicketActorSuite) TestNewExecTicketActor() {
	ret := NewExecTicketActor(ExecTicketActorIn{
		In: dig.In{},
	})
	suite.NotEmpty(ret)
}

func (suite *ExecTicketActorSuite) TestProcessStarted() {
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Started{}).Times(1)
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().GetName().AnyTimes()
	//dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	ExecTicketActor := NewExecTicketActor(ExecTicketActorIn{
		In: dig.In{},
	}).Producer.Spawn(consts.ExecTicketActorKind, consts.SingletonActorName, []byte("\"log_id\":1,\"tenant_id\":1,\"user_id\":1"))
	ExecTicketActor.GetState()
	ExecTicketActor.Process(Ctx)
}

func (suite *ExecTicketActorSuite) TestProcessStopping() {
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Stopping{}).Times(1)
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().GetName().AnyTimes()

	ExecTicketActor := ExecTicketActor{}
	ExecTicketActor.Process(Ctx)
}

func (suite *ExecTicketActorSuite) TestProcessStopped() {
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Stopped{}).Times(1)
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().GetName().AnyTimes()
	Ctx.EXPECT().Self().AnyTimes()
	Ctx.EXPECT().Send(gomock.Any(), gomock.Any()).AnyTimes()

	//dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	ExecTicketActor := NewExecTicketActor(ExecTicketActorIn{
		In: dig.In{},
	}).Producer.Spawn(consts.ExecTicketActorKind, consts.SingletonActorName, []byte{})
	ExecTicketActor.Process(Ctx)
}

func (suite *ExecTicketActorSuite) TestProcessRestarting() {
	Ctx := mocks.NewMockContext(suite.ctrl)

	Ctx.EXPECT().Value(gomock.Any()).AnyTimes()
	Ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().SetReceiveTimeout(gomock.Any()).Return().AnyTimes()
	Ctx.EXPECT().Message().Return(&actor.Restarting{}).Times(1)
	Ctx.EXPECT().Respond(gomock.Any()).AnyTimes()
	Ctx.EXPECT().GetName().Return("123").AnyTimes()

	//dbProvider := mocks.NewMockDBProvider(suite.ctrl)
	ExecTicketActor := NewExecTicketActor(ExecTicketActorIn{
		In: dig.In{},
	}).Producer.Spawn(consts.ExecTicketActorKind, consts.SingletonActorName, []byte{})
	ExecTicketActor.Process(Ctx)
}

// mock一个actor
func mockTicketActor() *ExecTicketActor {
	return &ExecTicketActor{
		state:               &ExecTicketState{TenantId: "1", IsTaskCreated: false},
		cnf:                 &config.MockConfigProvider{},
		workflowDal:         &mocks.MockWorkflowDAL{},
		ticketCommonService: &mock_dbw_ticket.MockTicketCommonService{},
		ds:                  &mocks.MockDataSourceService{},
	}
}

func TestExecTicket(t *testing.T) {
	execActor := mockTicketActor()
	ctx := &mocks.MockContext{}
	// mock一个日志
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock(context.GetTenantID).Return("1").Build()
	defer baseMock3.UnPatch()
	baseMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock4.UnPatch()
	baseMock5 := mockey.Mock((*config.MockConfigProvider).Get).Return(&bizConfig.Config{TicketSessionTimeout: 1}).Build()
	defer baseMock5.UnPatch()
	baseMock6 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock6.UnPatch()
	baseMock7 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock7.UnPatch()
	baseMock8 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock8.UnPatch()
	baseMock10 := mockey.Mock((*mocks.MockContext).SetReceiveTimeout).Return().Build()
	defer baseMock10.UnPatch()
	baseMock11 := mockey.Mock((*ExecTicketActor).UpdateTicketRepo).Return(nil).Build()
	defer baseMock11.UnPatch()
	baseMock12 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatus).Return(nil).Build()
	defer baseMock12.UnPatch()
	baseMock121 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{}, nil).Build()
	defer baseMock121.UnPatch()
	baseMock1211 := mockey.Mock((*mocks.MockDataSourceService).GetDatasourceAddress).Return(nil).Build()
	defer baseMock1211.UnPatch()
	execActor.ExecTicket(ctx, &shared.ExecTicket{
		TenantID:            "1",
		UserID:              "1",
		Source:              &shared.DataSource{},
		TicketType:          0,
		ExecuteType:         0,
		ExecutableStartTime: 0,
		ExecutableEndTime:   100,
	})

	execActor.ExecTicket(ctx, &shared.ExecTicket{
		TenantID:            "1",
		UserID:              "1",
		Source:              &shared.DataSource{},
		TicketType:          1,
		ExecuteType:         0,
		ExecutableStartTime: 0,
		ExecutableEndTime:   100,
	})

	execActor.ExecTicket(ctx, &shared.ExecTicket{
		TenantID:            "1",
		UserID:              "1",
		Source:              &shared.DataSource{},
		TicketType:          1,
		ExecuteType:         2,
		ExecutableStartTime: 0,
		ExecutableEndTime:   100,
	})

	execActor.ExecTicket(ctx, &shared.ExecTicket{
		TenantID:            "1",
		UserID:              "1",
		Source:              &shared.DataSource{},
		TicketType:          1,
		ExecuteType:         0,
		ExecutableStartTime: 0,
		ExecutableEndTime:   100,
	})

	execActor.ExecTicket(ctx, &shared.ExecTicket{
		TenantID:            "1",
		UserID:              "1",
		Source:              &shared.DataSource{},
		TicketType:          1,
		ExecuteType:         int32(model.ExecuteType_Cron),
		ExecutableStartTime: 0,
		ExecutableEndTime:   2147483647,
	})

	execActor.ExecTicket(ctx, &shared.ExecTicket{
		TenantID:            "1",
		UserID:              "1",
		Source:              &shared.DataSource{},
		TicketType:          int32(model.TicketType_FreeLockStructChange),
		ExecuteType:         int32(model.ExecuteType_Cron),
		ExecutableStartTime: 2147483647,
		ExecutableEndTime:   0,
	})

	execActor.ExecTicket(ctx, &shared.ExecTicket{
		TenantID:            "1",
		UserID:              "1",
		Source:              &shared.DataSource{},
		TicketType:          1,
		ExecuteType:         0,
		ExecutableStartTime: 0,
		ExecutableEndTime:   0,
	})
}

func TestGetCommandSetResult(t *testing.T) {
	execActor := mockTicketActor()
	ctx := &mocks.MockContext{}
	// mock一个日志
	baseMock0 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock0.UnPatch()
	baseMock5 := mockey.Mock((*config.MockConfigProvider).Get).Return(&bizConfig.Config{TicketSessionTimeout: 1}).Build()
	defer baseMock5.UnPatch()
	baseMock6 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock6.UnPatch()
	baseMock7 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock7.UnPatch()
	baseMock8 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock8.UnPatch()
	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{TicketType: int8(model.TicketType_NormalSqlChange)}, nil).Build()
	execActor.GetCommandSetResult(ctx)
	baseMock2.UnPatch()

	baseMock4 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{TicketType: int8(model.TicketType_FreeLockStructChange)}, nil).Build()
	execActor.GetCommandSetResult(ctx)
	baseMock4.UnPatch()

	baseMock20 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(nil, errors.New("error")).Build()
	execActor.GetCommandSetResult(ctx)
	baseMock20.UnPatch()
}

func TestExecSqlChangeTicket(t *testing.T) {
	execActor := mockTicketActor()
	ctx := &mocks.MockContext{}

	baseMock1 := mockey.Mock(log.Log).Return().Build()
	defer baseMock1.UnPatch()
	baseMock2 := mockey.Mock((*mocks.MockContext).WithValue).Return().Build()
	defer baseMock2.UnPatch()
	baseMock3 := mockey.Mock(context.GetTenantID).Return("1").Build()
	defer baseMock3.UnPatch()
	baseMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("1").Build()
	defer baseMock4.UnPatch()
	baseMock5 := mockey.Mock((*config.MockConfigProvider).Get).Return(&bizConfig.Config{TicketSessionTimeout: 1}).Build()
	defer baseMock5.UnPatch()
	baseMock6 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer baseMock6.UnPatch()
	baseMock7 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer baseMock7.UnPatch()
	baseMock8 := mockey.Mock((*mocks.MockContext).Respond).Return().Build()
	defer baseMock8.UnPatch()

	baseMock20 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeByTicketID).Return(&dao.Ticket{
		TicketId: 1,
	}, errors.New("error")).Build()
	baseMock11 := mockey.Mock((*ExecTicketActor).UpdateTicketRepo).Return(nil).Build()
	defer baseMock11.UnPatch()
	defer baseMock20.UnPatch()
	baseMock21 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqlsByDBType).Return([]*dbw_ticket.SqlInfo{
		{
			Sql:        "select 1",
			TableNames: []string{"a"},
			DbNames:    []string{"b"},
		},
	}).Build()
	baseMock24 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatusAndProgress).Return(nil).Build()
	defer baseMock24.UnPatch()

	baseMock22 := mockey.Mock((*mocks.MockDataSourceService).ExecuteSql).Return(fmt.Errorf("")).Build()
	execActor.execSqlChangeTicket(ctx, &shared.ExecTicket{
		ExecutableStartTime: 0,
		ExecutableEndTime:   0,
		ExecuteType:         int32(model.ExecuteType_Auto),
		Source: &shared.DataSource{
			Type: shared.MySQL,
		},
	})
	baseMock22.UnPatch()

	baseMock23 := mockey.Mock((*mocks.MockDataSourceService).ExecuteSql).Return(nil).Build()
	execActor.execSqlChangeTicket(ctx, &shared.ExecTicket{
		ExecutableStartTime: 0,
		ExecutableEndTime:   0,
		ExecuteType:         int32(model.ExecuteType_Auto),
		Source: &shared.DataSource{
			Type: shared.MySQL,
		},
	})
	defer baseMock23.UnPatch()
	baseMock21.UnPatch()

	baseMock211 := mockey.Mock((*mock_dbw_ticket.MockTicketCommonService).SplitSqlsByDBType).Return([]*dbw_ticket.SqlInfo{}).Build()
	execActor.execSqlChangeTicket(ctx, &shared.ExecTicket{
		ExecutableStartTime: 0,
		ExecutableEndTime:   0,
		ExecuteType:         int32(model.ExecuteType_Auto),
		Source: &shared.DataSource{
			Type: shared.MySQL,
		},
	})
	baseMock211.UnPatch()

}

// MockContextForStopTicket is a mock struct used as a target for mocking the types.Context interface.
// It does not need to fully implement the interface; mockey patches its methods at the type level.
type MockContextForStopTicket struct {
	types.Context
}

func (m *MockContextForStopTicket) Respond(response interface{})             {}
func (m *MockContextForStopTicket) GetName() string                          { return "" }
func (m *MockContextForStopTicket) Self() *actor.PID                         { return nil }
func (m *MockContextForStopTicket) Send(pid *actor.PID, message interface{}) {}
func Test_ExecTicketActor_StopTicket_BitsUTGen(t *testing.T) {
	mockey.PatchConvey("Test_ExecTicketActor_StopTicket", t, func() {
		// Common test data
		msg := &shared.StopTicket{TicketId: 12345}
		mockCtx := &MockContextForStopTicket{}
		actorInstance := &ExecTicketActor{}

		mockey.PatchConvey("成功场景 - state为nil", func() {
			// 场景描述：
			// 当 actor 的 state 为 nil 时, 函数应该立即响应成功并提前返回, 不执行后续的停止逻辑。
			// 数据构造：
			// - ExecTicketActor 实例的 state 字段为 nil。
			// - 构造一个 mock 的 types.Context。
			// 逻辑链路：
			// 1. Mock ctx.Respond() 被调用以发送成功响应。
			// 2. Mock ctx.GetName() 返回工单ID。
			// 3. 调用 StopTicket 方法。
			// 4. 断言函数返回 nil，并且 actor 的其他更新方法（如 updateActorAction, UpdateTicketRepo）不会被调用。

			// 数据构造
			actorInstance.state = nil

			// Mock
			mockey.Mock((*MockContextForStopTicket).Respond).Return().Build()
			mockey.Mock((*MockContextForStopTicket).GetName).Return("12345").Build()

			// 调用
			err := actorInstance.StopTicket(mockCtx, msg)

			// 断言
			convey.So(err, convey.ShouldBeNil)
		})

		mockey.PatchConvey("成功场景 - state不为nil", func() {
			// 场景描述：
			// 当 actor 的 state 不为 nil 时, 函数应执行完整的停止流程, 包括更新 actor 状态、更新工单仓库状态以及发送自杀消息。
			// 数据构造：
			// - ExecTicketActor 实例的 state 字段被初始化。
			// - 构造一个 mock 的 types.Context。
			// 逻辑链路：
			// 1. Mock 所有依赖的 context 方法 (Respond, GetName, Self, Send)。
			// 2. Mock 私有方法 e.updateActorAction() 以确认其被调用。
			// 3. Mock 公有方法 e.UpdateTicketRepo() 正常返回。
			// 4. 调用 StopTicket 方法。
			// 5. 断言函数返回 nil，表示所有操作都已触发（即使是异步的）。

			// 数据构造
			actorInstance.state = &ExecTicketState{}

			// Mock
			mockey.Mock((*MockContextForStopTicket).Respond).Return().Build()
			mockey.Mock((*MockContextForStopTicket).GetName).Return("12345").Build()
			mockey.Mock((*ExecTicketActor).updateActorAction).To(func(_ *ExecTicketActor, _ types.Context, action model.ExecuteTicketAction) {
				convey.So(action, convey.ShouldEqual, model.ExecuteTicketAction_ExecuteFailed)
			}).Build()
			mockey.Mock((*ExecTicketActor).UpdateTicketRepo).Return(nil).Build()
			mockey.Mock((*MockContextForStopTicket).Self).Return(actor.NewPID("local", "self")).Build()
			mockey.Mock((*MockContextForStopTicket).Send).To(func(_ *MockContextForStopTicket, _ *actor.PID, msg interface{}) {
				_, ok := msg.(*proto.SuicideMessage)
				convey.So(ok, convey.ShouldBeTrue)
			}).Build()

			// 调用
			err := actorInstance.StopTicket(mockCtx, msg)

			// 断言
			convey.So(err, convey.ShouldBeNil)
		})

		mockey.PatchConvey("失败场景 - UpdateTicketRepo返回错误", func() {
			// 场景描述：
			// 即使在更新工单数据库状态时发生错误, StopTicket 函数也应该捕获该错误并继续执行后续流程（如发送自杀消息），并最终返回 nil。
			// 数据构造：
			// - ExecTicketActor 实例的 state 字段被初始化。
			// - 构造一个 mock 的 types.Context。
			// 逻辑链路：
			// 1. Mock 所有依赖的 context 方法。
			// 2. Mock 私有方法 e.updateActorAction()。
			// 3. Mock 公有方法 e.UpdateTicketRepo() 返回一个错误。
			// 4. 调用 StopTicket 方法。
			// 5. 断言函数仍然返回 nil，表明错误被内部处理。

			// 数据构造
			actorInstance.state = &ExecTicketState{}
			dbError := errors.New("database connection failed")

			// Mock
			mockey.Mock((*MockContextForStopTicket).Respond).Return().Build()
			mockey.Mock((*MockContextForStopTicket).GetName).Return("12345").Build()
			mockey.Mock((*ExecTicketActor).updateActorAction).Return().Build()
			mockey.Mock((*ExecTicketActor).UpdateTicketRepo).Return(dbError).Build()
			mockey.Mock((*MockContextForStopTicket).Self).Return(actor.NewPID("local", "self")).Build()
			mockey.Mock((*MockContextForStopTicket).Send).Return().Build()

			// 调用
			err := actorInstance.StopTicket(mockCtx, msg)

			// 断言
			convey.So(err, convey.ShouldBeNil)
		})
	})
}
