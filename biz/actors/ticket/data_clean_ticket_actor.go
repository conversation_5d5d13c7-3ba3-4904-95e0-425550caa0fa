package ticket

import (
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/dbw_ticket"
	"code.byted.org/infcs/dbw-mgr/biz/service/parser"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"fmt"
	"go.uber.org/dig"
	"runtime/debug"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
)

const DataCleanTicketTimeout = 10 * time.Second

type DataCleanTicketActorIn struct {
	dig.In
	Conf                config.ConfigProvider
	WorkflowDal         dal.WorkflowDAL
	Sp                  parser.CommandParser
	Ds                  datasource.DataSourceService
	TicketCommonService dbw_ticket.TicketCommonService
}

// NewDataCleanTicketActor 执行清表工单的actor
func NewDataCleanTicketActor(p DataCleanTicketActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.DbwDataCleanActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			DataCleanActor := &DataCleanTicketActor{
				state:               newDataCleanTicketState(state),
				cnf:                 p.Conf,
				workflowDal:         p.WorkflowDal,
				ds:                  p.Ds,
				ticketCommonService: p.TicketCommonService,
			}
			return DataCleanActor
		}),
	}
}

func newDataCleanTicketState(bytes []byte) *DataCleanTicketState {
	ts := &DataCleanTicketState{}
	err := json.Unmarshal(bytes, ts)
	if err != nil {
		return nil
	}
	return ts
}

type DataCleanTicketState struct {
	SessionId     string                          `json:"session_id"`
	ConnectionId  string                          `json:"connection_id"`
	Cs            *entity.CommandSet              `json:"command_set"`
	Ds            *shared.DataSource              `json:"datasource"`
	CurrentAction model.ExecDataCleanTicketAction `json:"current_action"`
	LogID         string                          `json:"log_id"`
	TenantId      string                          `json:"tenant_id"`
	UserId        string                          `json:"user_id"`
	TicketType    int32                           `json:"ticket_type"`
	IsTaskCreated bool                            `json:"is_task_created"`
}

type DataCleanTicketActor struct {
	state               *DataCleanTicketState
	cnf                 config.ConfigProvider
	workflowDal         dal.WorkflowDAL
	ticketCommonService dbw_ticket.TicketCommonService
	ds                  datasource.DataSourceService
}

func (e *DataCleanTicketActor) GetState() []byte {
	state, _ := json.Marshal(e.state)
	return state
}

func (e *DataCleanTicketActor) Process(ctx types.Context) {
	if e.state == nil {
		e.state = new(DataCleanTicketState)
	}
	e.BuildCtx(ctx)

	switch msg := ctx.Message().(type) {
	case *actor.Started:
		// 启动时候做的事情
		e.protectUserCall(ctx, func() {
			e.OnStart(ctx)
		})
	case *shared.ExecDataCleanTicket:
		e.protectUserCall(ctx, func() {
			e.ExecDataCleanTicket(ctx, msg)
		})
	case *shared.StopTicket:
		e.protectUserCall(ctx, func() {
			e.StopTicket(ctx, msg)
		})
	case *actor.ReceiveTimeout:
		e.protectUserCall(ctx, func() {
			e.GetTicketResult(ctx) // 每10s调用一下获取结果的命令
			ctx.SetReceiveTimeout(DataCleanTicketTimeout)
		})
	case *actor.Stopped:
		log.Info(ctx, "DataCleanTicketActor %s stop", ctx.GetName())
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}
}

func (e *DataCleanTicketActor) BuildCtx(ctx types.Context) {
	var logID, tenantID, userID string
	if e.state.LogID != "" {
		logID = e.state.LogID
	} else {
		logID = "ticket-" + ctx.GetName()
	}
	if e.state.TenantId != "" {
		tenantID = e.state.TenantId
	}
	if e.state.UserId != "" {
		userID = e.state.UserId
	}
	ctx.WithValue("biz-context", &fwctx.BizContext{
		TenantID: tenantID,
		UserID:   userID,
		LogID:    logID,
	})
}

func (e *DataCleanTicketActor) OnStart(ctx types.Context) {
	ctx.SetReceiveTimeout(DataCleanTicketTimeout)
	log.Info(ctx, "DataCleanTicketActor %s start,CurrentAction is %s,tenant is %s", ctx.GetName(), e.state.CurrentAction.String(), fwctx.GetTenantID(ctx))
	// 如果是挂了重启,这个时候需要做修复的动作,仅修复状态不为"执行完成"或者"执行失败"的
	if e.state.CurrentAction == model.ExecDataCleanTicketAction_ExecuteFinished ||
		e.state.CurrentAction == model.ExecDataCleanTicketAction_ExecuteFailed {
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	e.processExecuteTicketAction(ctx)
	return
}

func (e *DataCleanTicketActor) ExecDataCleanTicket(ctx types.Context, msg *shared.ExecDataCleanTicket) {
	ctx.WithValue("biz-context", &fwctx.BizContext{
		TenantID: msg.TenantID,
		UserID:   msg.UserID,
	})
	log.Info(ctx, "ticket %v: actor receive command,datasource is %v,tenant is %s,msg tenant id is %s", ctx.GetName(), msg.Source, fwctx.GetTenantID(ctx), msg.TenantID)
	e.state.TenantId = msg.TenantID
	if msg.Source.Address == "" {
		e.ds.GetDatasourceAddress(ctx, msg.Source)
	}
	e.state.Ds = msg.Source
	e.updateExecTicketActorState(ctx, model.ExecDataCleanTicketAction_ReceiveCommand)
	e.execDataCleanTicket(ctx, msg) // SQL变更
}

func (e *DataCleanTicketActor) execDataCleanTicket(ctx types.Context, msg *shared.ExecDataCleanTicket) {
	// 1. 普通变更工单审批过后如果已经到达了截止时间,则直接提示工单超过截止时间，关闭即可
	if msg.ExecutableEndTime != 0 && time.Now().Unix() > int64(msg.ExecutableEndTime) {
		log.Info(ctx, "ticket: %s out of execute time window", ctx.GetName())
		e.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError),
			Description: "The execution time exceeds the allowable execution time window and ticket will not be executed"})
		ctx.Respond(&shared.TicketExecuted{
			TicketId: ctx.GetName(),
			Code:     shared.ExecutedFail,
			Message:  fmt.Sprintf("The execution time exceeds the allowable execution time window and ticket will not be executed"),
		})
		e.updateExecTicketActorState(ctx, model.ExecDataCleanTicketAction_ExecuteFailed)
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	// 2. 定时DML工单如果未到执行时间,计算一下和起始时间的时间差,并设置为ctx的ReceiveTimeOut
	if msg.ExecuteType == int32(model.ExecuteType_Cron) && time.Now().Unix() < int64(msg.ExecutableStartTime) {
		timeDelta := int64(msg.ExecutableStartTime) - time.Now().Unix()
		if timeDelta < 0 {
			timeDelta = 0
		}
		log.Info(ctx, "ticket: %v timeDelta is %v s", ctx.GetName(), timeDelta)
		ctx.SetReceiveTimeout(time.Duration(timeDelta+1) * time.Second)
		ctx.Respond(&shared.TicketExecuted{
			TicketId: ctx.GetName(),
			Code:     shared.ExecutedStart,
			Message:  "Data Clean Ticket Create Successfully",
		})
		return
	}
	// 3、这里调用方法,直接执行即可
	ticket, err := e.workflowDal.DescribeByTicketID(ctx, utils.MustStrToInt64(ctx.GetName()))
	if err != nil {
		log.Info(ctx, "ticket: get ticket %s info err:%s", ctx.GetName(), err.Error())
		err = e.UpdateTicketRepo(ctx, &dao.Ticket{TicketId: utils.MustStrToInt64(ctx.GetName()), TicketStatus: int8(model.TicketStatus_TicketError)})
		if err != nil {
			log.Info(ctx, "ticket: update ticket %s execute err:%s", ctx.GetName(), err.Error())
		}
	}
	if ticket == nil || (ticket != nil && ticket.TicketId == 0) {
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	ctx.Respond(&shared.TicketExecuted{
		TicketId: ctx.GetName(),
		Code:     shared.ExecutedStart,
		Message:  "ticket task initiation successfully",
	})
	// 标记为工单已经开始执行
	e.state.IsTaskCreated = true
	res := e.ticketCommonService.SplitSqlsByDBType(ctx, shared.DataSourceType(shared.DataSourceType_value[ticket.InstanceType]), ticket.SqlText)
	if res == nil || len(res) == 0 {
		e.UpdateTicketRepo(ctx, &dao.Ticket{TicketId: utils.MustStrToInt64(ctx.GetName()), TicketStatus: int8(model.TicketStatus_TicketError), Description: "ticket sql text is empty"})
		log.Warn(ctx, "ticket %v: execute sql fail %v", ctx.GetName(), err)
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	log.Info(ctx, "ticket %v: execute sql split %v,datasource is %v", ctx.GetName(), utils.Show(res), msg.Source)
	for idx, sqlInfo := range res {
		progress := (idx + 1) * 100 / len(res)
		if progress > 100 {
			progress = 100
		}
		if err = e.ds.ExecuteSql(ctx, &datasource.ExecuteReq{
			Source:     msg.Source,
			Type:       msg.Source.Type,
			ExecuteSql: sqlInfo.Sql,
		}); err != nil {
			log.Warn(ctx, "ticket %v: execute sql %v fail %v", ctx.GetName(), sqlInfo.Sql, err)
			e.workflowDal.UpdateWorkStatusAndProgress(ctx, ticket.TicketId, int32(model.TicketStatus_TicketError),
				fmt.Sprintf("execute sql %v fail %v", sqlInfo.Sql, err.Error()), int32(progress))
			ctx.Send(ctx.Self(), &proto.SuicideMessage{})
			return
		}
		e.workflowDal.UpdateWorkStatusAndProgress(ctx, ticket.TicketId, int32(model.TicketStatus_TicketExecute), "ticket is executing", int32(progress))
	}
	e.UpdateTicketRepo(ctx, &dao.Ticket{TicketId: utils.MustStrToInt64(ctx.GetName()), TicketStatus: int8(model.TicketStatus_TicketFinished)})
	log.Info(ctx, "ticket %v execute success", ctx.GetName())
	ctx.Send(ctx.Self(), &proto.SuicideMessage{})
}

// UpdateTicketRepo 更新任务状态
func (e *DataCleanTicketActor) UpdateTicketRepo(ctx types.Context, ticket *dao.Ticket) error {
	log.Info(ctx, "update ticket %v status to %v", ctx.GetName(), ticket.TicketStatus)
	ticket.TicketId = utils.MustStrToInt64(ctx.GetName())
	err := e.workflowDal.UpdateWorkStatus(ctx, ticket)
	if err != nil {
		log.Warn(ctx, "update ticket status to %v error", ticket.TicketStatus)
		return err
	}
	return nil
}

func (e *DataCleanTicketActor) updateExecTicketActorState(ctx types.Context, currentAction model.ExecDataCleanTicketAction) {
	log.Info(ctx, "update ExecTicketActor action %s as action %s", e.state.CurrentAction.String(), currentAction.String())
	e.state.CurrentAction = currentAction
}

func (e *DataCleanTicketActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call ExecTicketActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func (e *DataCleanTicketActor) processExecuteTicketAction(ctx types.Context) {
	switch e.state.CurrentAction {
	case model.ExecDataCleanTicketAction_ReceiveCommand:
		log.Info(ctx, "actor restart, try to exec ticket again")
		if e.state != nil && e.state.Ds != nil { // 说明是之前执行了一半,断掉了
			log.Info(ctx, "actor restart, send exec ticket message to exec ticket actor ")
			ticket, err := e.workflowDal.DescribeByTicketID(ctx, utils.MustStrToInt64(ctx.GetName()))
			if err != nil {
				log.Warn(ctx, "actor restart find ticket error:%s", err.Error())
				ctx.Send(ctx.Self(), &proto.SuicideMessage{})
				return
			}
			ctx.Send(ctx.Self(), &shared.ExecTicket{
				Source:              e.state.Ds,
				TenantID:            e.state.TenantId,
				UserID:              e.state.UserId,
				TicketType:          int32(ticket.TicketType),
				ExecuteType:         int32(ticket.ExecuteType),
				ExecutableStartTime: int32(ticket.ExecutableStartTime),
				ExecutableEndTime:   int32(ticket.ExecutableEndTime),
			})
		}
	}

}

func (e *DataCleanTicketActor) GetTicketResult(ctx types.Context) {
	ticket, err := e.workflowDal.DescribeByTicketID(ctx, utils.MustStrToInt64(ctx.GetName()))
	if err != nil {
		log.Warn(ctx, "ticket: get ticket from ticketId err:%s", err.Error())
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	log.Info(ctx, "ExecActor Receive TimeOut, ticket is %s", utils.Show(ticket))
	// 判断一下当前时间是否超过了结束时间,如果超过了,就停止执行,并且更新数据库
	if ticket.ExecutableEndTime > 0 && time.Now().Unix() > ticket.ExecutableEndTime {
		log.Info(ctx, "ticket:%s reach endtime,begin to stop execute ticket", ctx.GetName())
		if err = e.StopTicket(ctx, &shared.StopTicket{}); err != nil {
			log.Warn(ctx, "ticket: stop ticket %s execute err:%s", ctx.GetName(), err.Error())
		}
		e.UpdateTicketRepo(ctx, &dao.Ticket{TicketStatus: int8(model.TicketStatus_TicketError), Description: "execute timeout"})
		e.updateExecTicketActorState(ctx, model.ExecDataCleanTicketAction_ExecuteFailed)
		return
	}
	// 1、如果还未开始执行
	if !e.state.IsTaskCreated {
		log.Info(ctx, "IsDMLTaskCreated is %v", e.state.IsTaskCreated)
		e.execDataCleanTicket(ctx, &shared.ExecDataCleanTicket{
			TenantID:            e.state.TenantId,
			UserID:              e.state.UserId,
			Source:              e.state.Ds,
			TicketType:          int32(ticket.TicketType),
			ExecuteType:         int32(ticket.ExecuteType),
			ExecutableStartTime: int32(ticket.ExecutableStartTime),
			ExecutableEndTime:   int32(ticket.ExecutableEndTime),
		})
	}
	return
}

func (e *DataCleanTicketActor) StopTicket(ctx types.Context, msg *shared.StopTicket) error {
	// 这块重新写
	return nil
}
