package sharding_free_lock_dml

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	"code.byted.org/infcs/protoactor-go/actor"
	"encoding/json"
	"github.com/volcengine/volc-sdk-golang/service/tls"
	"go.uber.org/dig"
	"runtime/debug"
	"strconv"
)

type ShardingFreeLockDMLActorIn struct {
	dig.In
	WorkflowDal    dal.WorkflowDAL
	IdgenSvc       idgen.Service
	TicketService  workflow.TicketService
	Ds             datasource.DataSourceService
	ActorClient    cli.ActorClient
	Conf           config.ConfigProvider
	SqlTask        dal.SqlTask
	C3ConfProvider c3.ConfigProvider
}

// NewShardingFreeLockDMLActor 执行工单的actor
func NewShardingFreeLockDMLActor(p ShardingFreeLockDMLActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.ShardingFreeLockDMLActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			return &ShardingFreeLockDMLActor{
				state:          newShardingFreeLockDMLActorState(state),
				workflowDal:    p.WorkflowDal,
				idgenSvc:       p.IdgenSvc,
				ticketService:  p.TicketService,
				ds:             p.Ds,
				actorClient:    p.ActorClient,
				conf:           p.Conf,
				sqlTask:        p.SqlTask,
				c3ConfProvider: p.C3ConfProvider,
			}
		}),
	}
}

type ShardingFreeLockDMLActor struct {
	state          *ShardingFreeLockDMLActorState
	workflowDal    dal.WorkflowDAL
	idgenSvc       idgen.Service
	ticketService  workflow.TicketService
	ds             datasource.DataSourceService
	actorClient    cli.ActorClient
	conf           config.ConfigProvider
	sqlTask        dal.SqlTask
	tlsClient      tls.Client
	c3ConfProvider c3.ConfigProvider
}

func (f *ShardingFreeLockDMLActor) Process(ctx types.Context) {
	ctx.SetReceiveTimeout(ActorReceiveTimeout)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		// 启动时候做的事情
		f.protectUserCall(ctx, func() {
			f.OnStart(ctx)
		})
	case *shared.ExecShardingFreeLockDMLTicket:
		f.protectUserCall(ctx, func() {
			f.SplitShardingFreeLockDMLTicket(ctx, msg)
		})
	case *shared.CreateShardingSubTicket:
		f.protectUserCall(ctx, func() {
			f.CreateShardingSubTickets(ctx)
		})
	case *shared.StopTicket:
		f.protectUserCall(ctx, func() {
			f.StopTicket(ctx)
		})
	case *actor.ReceiveTimeout:
		f.protectUserCall(ctx, func() {
			f.CheckShardingDmlTicket(ctx)
		})
	case *actor.Stopped:
		log.Info(ctx, "ShardingFreeLockDMLActor %s stop", ctx.GetName())
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
	}
}

type ShardingFreeLockDMLActorState struct {
	Ticket          *dao.Ticket
	InstanceBatches []*InstanceBatch
	RunningInfo     *RunningInfo
	DataSource      *shared.DataSource `json:"DataSource,omitempty"`
	Status          int
	DbBatchNum      int
	ExecErrorNum    int
	TotalSubTask    int
	SuccessTask     int
	ErrorMsg        string
	User            string
	Password        string
	Address         string
	SqlText         string
	TableName       string
}

func (f *ShardingFreeLockDMLActor) protectUserCall(ctx types.Context, fn func()) {
	defer func() {
		if r := recover(); r != nil {
			log.Warn(ctx, " user call FreeLockDMLActor panic %v %s", r, string(debug.Stack()))
		}
	}()
	fn()
}

func (f *ShardingFreeLockDMLActor) GetState() []byte {
	state, _ := json.Marshal(f.state)
	return state
}

func newShardingFreeLockDMLActorState(bytes []byte) *ShardingFreeLockDMLActorState {
	ts := &ShardingFreeLockDMLActorState{}
	err := json.Unmarshal(bytes, ts)
	if err != nil {
		return &ShardingFreeLockDMLActorState{}
	}
	return ts
}

// OnStart FreeLockDMLActor启动
func (f *ShardingFreeLockDMLActor) OnStart(ctx types.Context) {
	if f.state == nil {
		f.state = new(ShardingFreeLockDMLActorState)
	}
	ticketId := ctx.GetName()
	shardingTicket, err := f.workflowDal.DescribeTicketWithoutTenant(ctx, utils.MustStrToInt64(ticketId))
	if err != nil || shardingTicket == nil {
		log.Warn(ctx, "close self, ticket:%s is not exists, or query db error:%s", ticketId, err.Error())
		ctx.Send(ctx.Self(), &proto.SuicideMessage{})
		return
	}
	f.state.Ticket = shardingTicket
	if f.state.RunningInfo == nil {
		f.state.RunningInfo = &RunningInfo{ShardStatus: RunningStatus_Running}
	}
	f.initTlsClient(ctx)
	log.Info(ctx, "ShardingFreeLockDMLActor %s start", ctx.GetName())
}

func (f *ShardingFreeLockDMLActor) StopTicket(ctx types.Context) {
	f.state.Status = Error
	for _, batch := range f.state.InstanceBatches {
		f.StopOneBatchTicket(ctx, batch)
	}
	f.FinishedTicket(ctx)
}

func (f *ShardingFreeLockDMLActor) StopOneBatchTicket(ctx types.Context, instanceBatch *InstanceBatch) {
	for i := instanceBatch.BatchIdx; i < instanceBatch.BatchIdx+f.state.DbBatchNum && i < instanceBatch.Total; i++ {
		if i >= len(instanceBatch.TicketIds) {
			continue
		}
		f.StopSubFreeLockTicket(ctx, instanceBatch.TicketIds[i])
	}
	instanceBatch.Status = ShardBatchError
	instanceBatch.ErrorMsg = instanceBatch.ErrorMsg + "User Stop Ticket;"
}

func (f *ShardingFreeLockDMLActor) StopSubFreeLockTicket(ctx types.Context, ticketId int64) {
	if ticketId == int64(0) {
		return
	}
	err := f.actorClient.KindOf(consts.FreeLockDMLActorKind).
		Send(ctx, conv.Int64ToStr(ticketId), &shared.StopTicket{
			TicketId:   ticketId,
			TaskId:     strconv.FormatInt(ticketId, 10),
			TenantId:   f.state.Ticket.TenantId,
			InstanceId: f.state.Ticket.InstanceId,
		})
	if err != nil {
		log.Warn(ctx, "StopTicket:%d error: %s", ticketId, err.Error())
	}
}

func (f *ShardingFreeLockDMLActor) UpdateRunningInfo(ctx types.Context) {
	process := 0
	if f.state.TotalSubTask != 0 {
		process = f.state.SuccessTask * 100 / f.state.TotalSubTask
	}
	err := f.workflowDal.UpdateWorkStatusAndProgress(ctx, f.state.Ticket.TicketId, int32(model.TicketStatus_TicketExecute), "", int32(process))
	if err != nil {
		log.Warn(ctx, "ticketId:%d, UpdateWorkStatusAndProgress error:%s", f.state.Ticket.TicketId, err.Error())
	}

	f.updateSqlTaskRunningInfo(ctx, "", process, model.SqlTaskStatus_Running.String())
}

func (f *ShardingFreeLockDMLActor) updateSqlTaskRunningInfo(ctx types.Context, result string, process int, status string) {
	runningInfoBytes, err := json.Marshal(f.state.RunningInfo)
	if err != nil {
		log.Warn(ctx, "Error marshaling RunningInfo to JSON: %v", err)
	}
	runningInfoJson := string(runningInfoBytes)
	err = f.sqlTask.UpdateTaskRunningInfo(ctx, runningInfoJson, f.state.Ticket.TicketId, result, process, status)
	if err != nil {
		log.Warn(ctx, "UpdateTaskRunningInfo: %s", err.Error())
	}
}
