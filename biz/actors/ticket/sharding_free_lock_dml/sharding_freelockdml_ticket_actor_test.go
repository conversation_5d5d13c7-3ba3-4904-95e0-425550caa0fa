package sharding_free_lock_dml

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	dal_mock "code.byted.org/infcs/dbw-mgr/biz/test/mocks/dal"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/dslibmocks"
	"code.byted.org/infcs/ds-lib/common/log"
	"fmt"
	"github.com/bytedance/mockey"
	"testing"
	"time"
)

func mockShardingFreeLockDMLActor() *ShardingFreeLockDMLActor {
	return &ShardingFreeLockDMLActor{
		state:          &ShardingFreeLockDMLActorState{},
		workflowDal:    &mocks.MockWorkflowDAL{},
		idgenSvc:       &mocks.MockService{},
		actorClient:    &dslibmocks.MockActorClient{},
		ds:             &mocks.MockDataSourceService{},
		conf:           &config.MockConfigProvider{},
		ticketService:  &mocks.MockTicketService{},
		sqlTask:        &dal_mock.MockSqlTask{},
		c3ConfProvider: &config.MockC3ConfigProvider{},
	}
}

func TestNewShardingFreeLockDMLActor(t *testing.T) {
	NewShardingFreeLockDMLActor(ShardingFreeLockDMLActorIn{})
}

func TestGetState(t *testing.T) {
	actor := mockShardingFreeLockDMLActor()
	actor.GetState()
}

func TestOnStart(t *testing.T) {
	actor := mockShardingFreeLockDMLActor()
	actor.state = nil

	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()
	basicMock4 := mockey.Mock((*mocks.MockContext).GetName).Return("111").Build()
	defer basicMock4.UnPatch()
	basicMock5 := mockey.Mock((*ShardingFreeLockDMLActor).initTlsClient).Return().Build()
	defer basicMock5.UnPatch()

	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeTicketWithoutTenant).Return(&dao.Ticket{}, fmt.Errorf("test")).Build()
	actor.OnStart(&mocks.MockContext{})
	mock1.UnPatch()
	time.Sleep(100 * time.Millisecond)

	mock2 := mockey.Mock((*mocks.MockWorkflowDAL).DescribeTicketWithoutTenant).Return(&dao.Ticket{}, nil).Build()
	defer mock2.UnPatch()
	actor.OnStart(&mocks.MockContext{})
}

func TestStopTicket(t *testing.T) {
	actor := mockShardingFreeLockDMLActor()
	actor.state.InstanceBatches = []*InstanceBatch{{}}

	mock1 := mockey.Mock((*ShardingFreeLockDMLActor).StopOneBatchTicket).Return().Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*ShardingFreeLockDMLActor).FinishedTicket).Return().Build()
	defer mock2.UnPatch()

	actor.StopTicket(&mocks.MockContext{})
}

func TestStopOneBatchTicket(t *testing.T) {
	actor := mockShardingFreeLockDMLActor()

	mock1 := mockey.Mock((*ShardingFreeLockDMLActor).StopSubFreeLockTicket).Return().Build()
	defer mock1.UnPatch()

	actor.StopOneBatchTicket(&mocks.MockContext{}, &InstanceBatch{TicketIds: []int64{1}, Total: 1})
}

func TestUpdateRunningInfo(t *testing.T) {
	actor := mockShardingFreeLockDMLActor()

	actor.state.TotalSubTask = 1
	actor.state.RunningInfo = &RunningInfo{}
	actor.state.Ticket = &dao.Ticket{}

	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	basicMock2 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer basicMock2.UnPatch()
	basicMock3 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer basicMock3.UnPatch()

	mock1 := mockey.Mock((*mocks.MockWorkflowDAL).UpdateWorkStatusAndProgress).Return(fmt.Errorf("test")).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*dal_mock.MockSqlTask).UpdateTaskRunningInfo).Return(fmt.Errorf("test")).Build()
	defer mock2.UnPatch()

	actor.UpdateRunningInfo(&mocks.MockContext{})
}

func TestNewShardingFreeLockDMLActorState(t *testing.T) {
	newShardingFreeLockDMLActorState([]byte{})
}
