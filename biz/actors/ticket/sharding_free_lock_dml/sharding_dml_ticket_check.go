package sharding_free_lock_dml

import (
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types/proto"
	"fmt"
	"time"
)

func (f *ShardingFreeLockDMLActor) CheckShardingDmlTicket(ctx types.Context) {
	switch f.state.Status {
	case ShardingDmlUndo, ShardingDmlSplitSql:
		f.StartRecover(ctx)
	case CreateSubTicket:
		f.CheckSubExecStatus(ctx)
	case Error, Finished:
		f.FinishedTicket(ctx)
	}
}

func (f *ShardingFreeLockDMLActor) StartRecover(ctx types.Context) {
	ctx.Send(ctx.Self(), &shared.ExecShardingFreeLockDMLTicket{
		Source:    f.state.DataSource,
		SqlText:   f.state.SqlText,
		TableName: f.state.TableName,
	})
}

func (f *ShardingFreeLockDMLActor) CheckSubExecStatus(ctx types.Context) {
	for idx, batch := range f.state.InstanceBatches {
		if err := f.CheckOneBatchTicket(ctx, batch, f.state.RunningInfo.ShardRunningInfo[idx]); err != nil {
			return
		}
	}
	f.CheckAllBatchStatus(ctx)
}

func (f *ShardingFreeLockDMLActor) CheckAllBatchStatus(ctx types.Context) {
	isAllFinished := true
	hasError := false
	for _, batch := range f.state.InstanceBatches {
		if batch.Status == ShardBatchError || batch.Status == ShardBatchPartialFailure {
			hasError = true
		}
		if batch.Status != ShardBatchError && batch.Status != ShardBatchFinished {
			isAllFinished = false
		}
	}
	if !isAllFinished {
		// 如果没有全部做完则什么也不需要做
		return
	}
	if hasError {
		f.state.Status = Error
		if f.state.ErrorMsg == "" {
			f.state.ErrorMsg = f.formatErrorMsg()
		}
	} else {
		f.state.Status = Finished
	}
	f.FinishedTicket(ctx)
}

func (f *ShardingFreeLockDMLActor) formatErrorMsg() string {
	if f.state.RunningInfo == nil {
		return ""
	}
	errMsg := ""
	for _, shardRunningInfo := range f.state.RunningInfo.ShardRunningInfo {
		for _, process := range shardRunningInfo.ShardProgress {
			if process.Status == SubShardStatusToString(SubTaskError) {
				errMsg += process.ErrMsg + ";"
			}
		}
	}
	return interceptStr1k(errMsg)
}

func (f *ShardingFreeLockDMLActor) CheckOneBatchTicket(ctx types.Context, instanceBatch *InstanceBatch, shardRunningInfo *ShardRunningInfo) error {
	if instanceBatch.Status == ShardBatchError || instanceBatch.Status == ShardBatchFinished {
		// 对于这个instance上已经做完的就不再进行处理，直接返回
		return nil
	}
	batchExecuteInfo := &BatchExecuteInfo{}
	for i := instanceBatch.BatchIdx; i < instanceBatch.BatchIdx+f.state.DbBatchNum && i < instanceBatch.Total; i++ {
		batchExecuteInfo.BatchTotal++
		if i >= len(instanceBatch.TicketIds) {
			batchExecuteInfo.ErrorNum++
			continue
		}
		status, errMsg, err := f.GetSubTicketStatus(ctx, instanceBatch.TicketIds[i], shardRunningInfo.ShardProgress[i])
		if err != nil {
			log.Warn(ctx, "GetSubTicketStatus error:%s", err.Error())
			return err
		}
		switch status {
		case ShardingDmlUndo:
			batchExecuteInfo.UndoNum++
			continue
		case SubTaskRunning:
			batchExecuteInfo.ExecutingNum++
			continue
		case SubTaskSuccess:
			batchExecuteInfo.SuccessNum++
		case SubTaskError:
			batchExecuteInfo.ErrorNum++
			instanceBatch.Status = ShardBatchPartialFailure
			newMessage := instanceBatch.ErrorMsg + fmt.Sprintf(" sql:%s execute error:%s; ", instanceBatch.Sqls[i], errMsg)
			instanceBatch.ErrorMsg = newMessage
		}
	}
	f.UpdateRunningInfo(ctx)
	f.CheckOneBatchStatus(ctx, instanceBatch, batchExecuteInfo, shardRunningInfo)
	return nil
}

func (f *ShardingFreeLockDMLActor) CheckOneBatchStatus(ctx types.Context, instanceBatch *InstanceBatch, batchExecuteInfo *BatchExecuteInfo, shardRunningInfo *ShardRunningInfo) {
	// 一共有4种情况，分别是这个批次有失败的，但是都做完了，这个批次没有失败的，都做完了，这个批次还没有做完，还有一种是都没有做都
	if batchExecuteInfo.ExecutingNum != 0 {
		// 没做完，就等下一次检查的时候继续检查，直到做完为止
		return
	}
	// 做完的情况
	if batchExecuteInfo.ErrorNum == 0 {
		f.state.SuccessTask = f.state.SuccessTask + batchExecuteInfo.SuccessNum
		// 全成功了
		f.writeFinishedTlsLog(ctx, instanceBatch, shardRunningInfo)
		f.DoNextBatch(ctx, instanceBatch, batchExecuteInfo, shardRunningInfo)
	} else {
		f.state.SuccessTask = f.state.SuccessTask + batchExecuteInfo.SuccessNum
		// 有失败的，就直接转为失败，这个实例后续不再进行变更
		f.writeFinishedTlsLog(ctx, instanceBatch, shardRunningInfo)
		instanceBatch.Status = ShardBatchError
	}
}

func (f *ShardingFreeLockDMLActor) writeFinishedTlsLog(ctx types.Context, instanceBatch *InstanceBatch, shardRunningInfo *ShardRunningInfo) {
	// 记录日志
	for i := instanceBatch.BatchIdx; i < instanceBatch.BatchIdx+f.state.DbBatchNum && i < instanceBatch.Total; i++ {

		shardProgress := shardRunningInfo.ShardProgress[i]
		if shardProgress.Status == SubShardStatusToString(SubTaskSuccess) {
			f.ProducerToTls(ctx, "finished execute sub task, execute sql:  "+shardProgress.ExecSql)
		} else if shardProgress.Status == SubShardStatusToString(SubTaskError) {
			f.ProducerToTls(ctx, fmt.Sprintf("execute sub task error, execute sql: %s, errorMsg: %s  ", shardProgress.ExecSql, shardProgress.ErrMsg))
		}
	}
}

func (f *ShardingFreeLockDMLActor) DoNextBatch(ctx types.Context, instanceBatch *InstanceBatch, batchExecuteInfo *BatchExecuteInfo, shardRunningInfo *ShardRunningInfo) {
	// 减去还没有做的，因为我们程序设计原则，如果当前是个undo，那么后面一定都是undo
	nextIdx := instanceBatch.BatchIdx + f.state.DbBatchNum - batchExecuteInfo.UndoNum
	instanceBatch.BatchIdx = nextIdx
	if instanceBatch.BatchIdx >= instanceBatch.Total {
		// 全部都做完了
		instanceBatch.Status = ShardBatchFinished
		return
	}
	// 接着往下走
	f.CreateOneBatchSubTicket(ctx, instanceBatch, shardRunningInfo)
}

func (f *ShardingFreeLockDMLActor) GetSubTicketStatus(ctx types.Context, ticketId int64, process *ShardProgress) (status int, errMsg string, err error) {
	if ticketId == int64(0) {
		return SubTaskUndo, "", nil
	}
	subTicket, err := f.workflowDal.DescribeTicketWithoutTenant(ctx, ticketId)
	if err != nil {
		log.Warn(ctx, "DescribeTicketWithoutTenant error:%s", err.Error())
		return 0, "", err
	}
	process.Progress = subTicket.Progress
	switch subTicket.TicketStatus {
	case int8(model.TicketStatus_TicketError), int8(model.TicketStatus_TicketTermination):
		process.ErrMsg = subTicket.Description
		process.Status = SubShardStatusToString(SubTaskError)
		return SubTaskError, subTicket.Description, nil
	case int8(model.TicketStatus_TicketFinished):
		process.Status = SubShardStatusToString(SubTaskSuccess)
		return SubTaskSuccess, "", nil
	}
	return SubTaskRunning, "", nil
}

func (f *ShardingFreeLockDMLActor) FinishedTicket(ctx types.Context) {
	f.state.Ticket.TicketStatus = int8(model.TicketStatus_TicketFinished)
	sqlTaskStatus := model.SqlTaskStatus_Success.String()
	if f.state.Status == Error {
		sqlTaskStatus = model.SqlTaskStatus_Failed.String()
		f.state.Ticket.TicketStatus = int8(model.TicketStatus_TicketError)
	}
	process := f.state.TotalSubTask
	if process != 0 {
		process = f.state.SuccessTask * 100 / f.state.TotalSubTask
	}
	f.state.Ticket.Progress = int32(process)
	f.state.Ticket.Description = f.state.ErrorMsg
	_ = f.workflowDal.UpdateWorkStatusAndProgress(ctx, f.state.Ticket.TicketId, int32(f.state.Ticket.TicketStatus), "", int32(process))
	f.updateSqlTaskRunningInfo(ctx, f.state.Ticket.Description, process, sqlTaskStatus)
	err := f.workflowDal.UpdateWorkStatus(ctx, f.state.Ticket)
	if err != nil {
		log.Warn(ctx, "update ticket status error:%s", err.Error())
	}
	f.ProducerToTls(ctx, "end all task")
	ctx.Send(ctx.Self(), &proto.SuicideMessage{})
}

// TODO 待做，暂时不做这个
func (f *ShardingFreeLockDMLActor) CheckEndExecuteTimeLegal() bool {
	if f.state.Ticket.TicketType != int8(model.ExecuteType_Cron) {
		// 如果不是定时的，时间一定是合法的
		return true
	}
	nowTime := time.Now().Unix()
	if f.state.Ticket.ExecutableEndTime != 0 && nowTime > f.state.Ticket.ExecutableEndTime {
		// 如果配置了结束时间&超过了结束时间，当前执行时间不合法
		return true
	}
	return true
}
