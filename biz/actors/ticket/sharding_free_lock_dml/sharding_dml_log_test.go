package sharding_free_lock_dml

import (
	config2 "code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"testing"

	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"github.com/bytedance/mockey"
)

func TestFormatTlsLogs(t *testing.T) {
	actor := mockShardingFreeLockDMLActor()
	ctx := &mocks.MockContext{}

	baseMock1 := mockey.Mock((*config2.MockC3ConfigProvider).GetNamespace).Return(&config.C3Config{Application: config.Application{
		TicketLogTopic: "1",
	}}).Build()
	defer baseMock1.UnPatch()
	actor.formatTlsLogs(ctx, "a", "100", "1", "1", "1")
}

func TestGetHash(t *testing.T) {
	actor := mockShardingFreeLockDMLActor()
	actor.getHash("1")
}
