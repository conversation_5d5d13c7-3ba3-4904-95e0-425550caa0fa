package actors

import (
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"testing"
)

type LogAnalysisActorTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *LogAnalysisActorTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *LogAnalysisActorTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestLogAnalysisActorTestSuite(t *testing.T) {
	suite.Run(t, new(LogAnalysisActorTestSuite))
}

func (suite *LogAnalysisActorTestSuite) generateActor() types.IActor {
	configProvider := config.NewMockConfigProvider(suite.ctrl)
	c3ConfigProvider := config.NewMockC3ConfigProvider(suite.ctrl)

	in := NewLogAnalysisActorIn{
		Conf:   configProvider,
		C3Conf: c3ConfigProvider,
	}

	actor := NewLogAnalysisActor(in)

	return actor.Producer.Spawn()
}
