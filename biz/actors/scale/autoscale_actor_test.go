package scale

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/config"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks/dslibmocks"
	repository2 "code.byted.org/infcs/dbw-mgr/biz/test/repository"
	"code.byted.org/infcs/ds-lib/common/log"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"go.uber.org/dig"
	"testing"
)

func TestNewAutoScaleActor(t *testing.T) {
	NewAutoScaleActor(AutoScaleActorIn{
		In:   dig.In{},
		Conf: &config.MockConfigProvider{},
	})
}

func mockAutoScaleActor() *AutoScaleActor {
	return &AutoScaleActor{
		state:          &AutoScaleActorState{},
		cnf:            &config.MockConfigProvider{},
		repo:           &repository2.MockAutoScaleRepo{},
		actorClient:    &dslibmocks.MockActorClient{},
		c3ConfProvider: &config.MockC3ConfigProvider{},
		loca:           &mocks.MockLocation{},
		idgenSvc:       &mocks.MockService{},
		ds:             &mocks.MockDataSourceService{},
	}
}

func TestStartScale(t *testing.T) {
	actor := mockAutoScaleActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	actor.state.EventState = shared.EventScaling
	actor.StartScale(&mocks.MockContext{}, &shared.CreateAutoScaleEvent{})
}

func TestRecoverState(t *testing.T) {
	actor := mockAutoScaleActor()
	actor.state.RuleId = 0
	recoverState([]byte{})
	recoverState([]byte{1})
}

func TestOnStart(t *testing.T) {
	actor := mockAutoScaleActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock1 := mockey.Mock((*mocks.MockContext).GetName).Return("123").Build()
	defer mock1.UnPatch()

	actor.OnStart(&mocks.MockContext{})
	log.Info(context.Background(), "%d", actor.state.RuleId)
	mock2 := mockey.Mock((*AutoScaleActor).finishEvent).Return().Build()
	defer mock2.UnPatch()
	actor.state.EventState = shared.EventError
	actor.OnStart(&mocks.MockContext{})
	actor.state.EventState = shared.EventFinished
	actor.OnStart(&mocks.MockContext{})
}

func TestGetState(t *testing.T) {
	actor := mockAutoScaleActor()
	actor.GetState()
}

func TestGetRuleByRuleId(t *testing.T) {
	actor := mockAutoScaleActor()
	actor.GetState()

	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock3 := mockey.Mock((*repository2.MockAutoScaleRepo).GetScaleRuleById).Return(nil, fmt.Errorf("err")).Build()
	actor.getRuleByRuleId(&mocks.MockContext{}, 1)
	mock3.UnPatch()

	mock4 := mockey.Mock((*repository2.MockAutoScaleRepo).GetScaleRuleById).Return(&entity.AutoScaleRule{
		RuleId:            0,
		InstanceId:        "",
		InstanceType:      "",
		ScaleTarget:       "",
		TenantId:          "",
		RegionId:          "",
		ScalingType:       0,
		ScalingThreshold:  0,
		Enable:            0,
		ObservationWindow: 0,
		CloudAlarmId:      "",
		CreateTime:        0,
		UpdateTime:        0,
		UpdateUser:        "",
		Deleted:           0,
		ScalingLimit:      "",
	}, nil).Build()
	defer mock4.UnPatch()
	actor.getRuleByRuleId(&mocks.MockContext{}, 1)
	actor.getRuleByRuleId(&mocks.MockContext{}, 0)
}

func TestIsScaleLimited(t *testing.T) {
	actor := mockAutoScaleActor()
	actor.GetState()

	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock3 := mockey.Mock((*repository2.MockAutoScaleRepo).GetLatestFinishedEventByRuleId).Return(nil, fmt.Errorf("err")).Build()
	actor.isScaleLimited(&mocks.MockContext{}, 1)
	mock3.UnPatch()

	mock4 := mockey.Mock((*repository2.MockAutoScaleRepo).GetLatestFinishedEventByRuleId).Return(&entity.AutoScaleEvent{
		EventId:           0,
		RuleId:            0,
		InstanceId:        "",
		TenantId:          "",
		ScalingType:       0,
		CreateTime:        0,
		StartTime:         0,
		EndTime:           0,
		BeforeValue:       0,
		TargetUtilization: "",
		AfterValue:        0,
		Status:            0,
		Memo:              "",
		Deleted:           0,
		BeforeMetric:      "",
		AfterMetric:       "",
	}, nil).Build()
	defer mock4.UnPatch()
	actor.isScaleLimited(&mocks.MockContext{}, 1)
}

func TestDoScale(t *testing.T) {
	actor := mockAutoScaleActor()
	actor.GetState()

	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	mock3 := mockey.Mock((*repository2.MockAutoScaleRepo).UpdateAutoScaleErrorEvent).Return(nil).Build()
	defer mock3.UnPatch()

	mock4 := mockey.Mock((*repository2.MockAutoScaleRepo).UpdateScalingEvent).Return(nil).Build()
	defer mock4.UnPatch()

	mock1 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
	defer mock2.UnPatch()

	mock0 := mockey.Mock((*AutoScaleActor).ModifyInstanceSpec).Return(fmt.Errorf("err")).Build()

	actor.DoScale(&mocks.MockContext{}, &entity.AutoScaleRule{
		ScaleTarget: "Spec",
	}, &ScaleTarget{})
	mock0.UnPatch()
}

//func TestWatchResult(t *testing.T) {
//	actor := mockAutoScaleActor()
//	actor.GetState()
//
//	mock2 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(&datasource.DescribeDBInstanceDetailResp{}, fmt.Errorf("err")).Build()
//
//	actor.watchResult(&mocks.MockContext{}, &entity.AutoScaleRule{
//		RuleId:            0,
//		InstanceId:        "",
//		InstanceType:      "",
//		ScaleTarget:       "",
//		TenantId:          "",
//		RegionId:          "",
//		ScalingType:       0,
//		ScalingThreshold:  0,
//		Enable:            0,
//		ObservationWindow: 0,
//		CloudAlarmId:      "",
//		CreateTime:        0,
//		UpdateTime:        0,
//		UpdateUser:        "",
//		Deleted:           0,
//		ScalingLimit:      "",
//	}, "")
//	mock2.UnPatch()
//
//	mock3 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(&datasource.DescribeDBInstanceDetailResp{
//		InstanceId:         "",
//		InstanceName:       "",
//		InstanceStatus:     "",
//		RegionId:           "",
//		ZoneId:             "",
//		DBEngine:           "",
//		DBEngineVersion:    "",
//		InstanceType:       "",
//		VCPU:               0,
//		Memory:             0,
//		StorageSpace:       0,
//		ProjectName:        "",
//		VPCID:              "",
//		NodeInfos:          nil,
//		ConnectionsInfos:   nil,
//		Endpoints:          nil,
//		DBEngineSubVersion: "",
//		NodeSpec:           "",
//		NoAuthMode:         nil,
//		AllowListInfo:      datasource.AllowListInfo{},
//		SpecFamily:         "",
//		ChargeType:         "",
//		NodeNumber:         0,
//	}, nil).Build()
//
//	actor.watchResult(&mocks.MockContext{}, &entity.AutoScaleRule{
//		RuleId:            0,
//		InstanceId:        "",
//		InstanceType:      "",
//		ScaleTarget:       "",
//		TenantId:          "",
//		RegionId:          "",
//		ScalingType:       0,
//		ScalingThreshold:  0,
//		Enable:            0,
//		ObservationWindow: 0,
//		CloudAlarmId:      "",
//		CreateTime:        0,
//		UpdateTime:        0,
//		UpdateUser:        "",
//		Deleted:           0,
//		ScalingLimit:      "",
//	}, "0C0G")
//	mock3.UnPatch()
//}

//func TestRetryScale(t *testing.T) {
//	actor := mockAutoScaleActor()
//	actor.GetState()
//
//	logMock := mockey.Mock(log.Log).Return().Build()
//	defer logMock.UnPatch()
//
//	mock1 := mockey.Mock((*mocks.MockContext).Send).Return().Build()
//	defer mock1.UnPatch()
//	mock2 := mockey.Mock((*mocks.MockContext).Self).Return(nil).Build()
//	defer mock2.UnPatch()
//
//	mock3 := mockey.Mock((*repository2.MockAutoScaleRepo).UpdateAutoScaleEndEvent).Return(nil).Build()
//	defer mock3.UnPatch()
//
//	mock4 := mockey.Mock((*repository2.MockAutoScaleRepo).UpdateScaleErrorEvent).Return(nil).Build()
//	defer mock4.UnPatch()
//
//	actor.state.RetryCount = 1
//	actor.retryScale(&mocks.MockContext{})
//
//	actor.state.RetryCount = 100
//	actor.retryScale(&mocks.MockContext{})
//}

func TestGetEventId(t *testing.T) {
	actor := mockAutoScaleActor()
	actor.GetState()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()

	actor.state.EventId = 1
	mock1 := mockey.Mock((*mocks.MockService).NextID).Return(1, "").Build()
	actor.getEventId(&mocks.MockContext{})
	mock1.UnPatch()

	actor.state.EventId = 0
	mock2 := mockey.Mock((*mocks.MockService).NextID).Return(1, fmt.Errorf("error")).Build()
	actor.getEventId(&mocks.MockContext{})
	mock2.UnPatch()

	mock3 := mockey.Mock((*mocks.MockService).NextID).Return(1, nil).Build()
	actor.getEventId(&mocks.MockContext{})
	mock3.UnPatch()
}

func TestCreateScaleEvent(t *testing.T) {
	actor := mockAutoScaleActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	mock1 := mockey.Mock((*AutoScaleActor).getEventId).Return(1, nil).Build()
	defer mock1.UnPatch()
	mock2 := mockey.Mock((*repository2.MockAutoScaleRepo).CreateScaleEvent).Return(nil).Build()
	defer mock2.UnPatch()
	actor.createScaleEvent(&mocks.MockContext{}, &entity.AutoScaleRule{
		RuleId:            0,
		InstanceId:        "",
		InstanceType:      shared.VeDBMySQL.String(),
		ScaleTarget:       "Spec",
		TenantId:          "",
		RegionId:          "",
		ScalingType:       0,
		ScalingThreshold:  0,
		Enable:            0,
		ObservationWindow: 0,
		CloudAlarmId:      "",
		CreateTime:        0,
		UpdateTime:        0,
		UpdateUser:        "",
		Deleted:           0,
		ScalingLimit:      "",
	}, &shared.CreateAutoScaleEvent{
		BizContext: "",
		RuleId:     0,
		Metrics: []*shared.AutoScaleMetric{
			{
				MetricName:  "MaxCpuUtil",
				MetricValue: 0,
			},
		},
		TenantId: "",
	})

}

func TestGetTargetUtilizationFromRuleAndMSG(t *testing.T) {
	actor := mockAutoScaleActor()
	actor.GetTargetUtilizationFromRuleAndMSG(&entity.AutoScaleRule{
		RuleId:            0,
		InstanceId:        "",
		InstanceType:      shared.VeDBMySQL.String(),
		ScaleTarget:       "Spec",
		TenantId:          "",
		RegionId:          "",
		ScalingType:       0,
		ScalingThreshold:  0,
		Enable:            0,
		ObservationWindow: 0,
		CloudAlarmId:      "",
		CreateTime:        0,
		UpdateTime:        0,
		UpdateUser:        "",
		Deleted:           0,
		ScalingLimit:      "",
	}, &shared.CreateAutoScaleEvent{
		BizContext: "",
		RuleId:     0,
		Metrics: []*shared.AutoScaleMetric{
			{
				MetricName:  "MaxCpuUtil",
				MetricValue: 0,
			},
		},
		TenantId: "",
	})
}

func TestWatchResult(t *testing.T) {
	actor := mockAutoScaleActor()
	logMock := mockey.Mock(log.Log).Return().Build()
	defer logMock.UnPatch()
	mock1 := mockey.Mock((*mocks.MockDataSourceService).DescribeDBInstanceDetail).Return(&datasource.DescribeDBInstanceDetailResp{
		VCPU:   4,
		Memory: 16,
	}, nil).Build()

	actor.watchResult(&mocks.MockContext{}, &entity.AutoScaleRule{}, "4C16G")
	mock1.UnPatch()
}
