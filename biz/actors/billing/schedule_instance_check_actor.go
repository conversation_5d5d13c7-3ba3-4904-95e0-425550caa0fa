package billing

import (
	"code.byted.org/infcs/dbw-mgr/biz/dal/dao"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/ds_utils"
	"code.byted.org/infcs/dbw-mgr/biz/service/full_sql"
	"code.byted.org/infcs/protoactor-go/actor"
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"

	audit_svc "code.byted.org/infcs/dbw-mgr/biz/service/audit"

	"github.com/google/uuid"

	"code.byted.org/infcs/dbw-mgr/biz/com"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/dal"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/service/billing"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/log"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"go.uber.org/dig"
)

type NewScheduleInstanceCheckActorIn struct {
	dig.In
	BillSvc         billing.BillingService
	AuditTlsDAL     dal.ObInstDAL
	AuditSvc        audit_svc.SqlAuditService
	PublishEventSvc com.PublishEventService
	Source          datasource.DataSourceService
	FullSqlService  full_sql.FullSqlService
}

type ScheduleInstanceCheckActor struct {
	state map[string]string

	billSvc         billing.BillingService
	auditTlsDAL     dal.ObInstDAL
	auditSvc        audit_svc.SqlAuditService
	publishEventSvc com.PublishEventService
	source          datasource.DataSourceService
	fullSqlService  full_sql.FullSqlService
}

const (
	CheckDeletedInstanceTimeout = 10 * time.Minute
)

func NewScheduleInstanceCheckActor(i NewScheduleInstanceCheckActorIn) types.VirtualPersistenceProducer {
	return types.VirtualPersistenceProducer{
		Kind: consts.ScheduleInstanceCheckActorKind,
		Producer: types.PersistActorProducerFunc(func(kind, name string, state []byte) types.IPersistActor {
			sicActor := &ScheduleInstanceCheckActor{
				billSvc:         i.BillSvc,
				auditTlsDAL:     i.AuditTlsDAL,
				auditSvc:        i.AuditSvc,
				publishEventSvc: i.PublishEventSvc,
				source:          i.Source,
				fullSqlService:  i.FullSqlService,
			}

			err := json.Unmarshal(state, &sicActor.state)
			if err != nil {
				log.Warn(context.Background(), "%s", err)
			}
			return sicActor
		}),
	}
}

func (b *ScheduleInstanceCheckActor) Process(ctx types.Context) {
	sysCtx := context.WithValue(context.Background(), "biz-context", &fwctx.BizContext{
		LogID: "ScheduleInstanceCheck-" + uuid.New().String(),
	})
	ctx = types.BuildMyContext(sysCtx, ctx, nil)
	switch msg := ctx.Message().(type) {
	case *actor.Started:
		log.Info(ctx, "started ScheduleInstanceCheckActor")
		ctx.SetReceiveTimeout(CheckDeletedInstanceTimeout)
	case *shared.CheckParentInstance:
		b.CheckOneInstance(ctx, msg.FollowInstanceID)
		ctx.SetReceiveTimeout(CheckDeletedInstanceTimeout)
	case *actor.ReceiveTimeout:
		log.Info(ctx, "timeout msg: %s", msg)
		b.checkReportTask(ctx)
		ctx.SetReceiveTimeout(CheckDeletedInstanceTimeout)
	case *actor.Stopping:
		context.Background()
		log.Info(ctx, "Billing schedule is stop")
	}
}

func (b *ScheduleInstanceCheckActor) GetState() []byte {
	state, _ := json.Marshal(b.state)
	return state
}

func (b *ScheduleInstanceCheckActor) checkReportTask(ctx context.Context) {
	// 巡检主实例被删除的情况
	err := b.checkDeletedMasterRDS(ctx)
	if err != nil {
		log.Warn(ctx, "%s", err)
	}
}

func (b *ScheduleInstanceCheckActor) checkDeletedMasterRDS(ctx context.Context) error {
	log.Info(ctx, "check deleted rds instance for terminate audit instance")
	auditInstanceList, err := b.auditTlsDAL.GetAll(ctx, model.LogProductType_AuditLog.String())
	if err != nil {
		log.Warn(ctx, "[checkDeletedMasterRDS] %s", err)
		return err
	}
	b.checkInstances(ctx, auditInstanceList)

	FullSqlInstances, err := b.auditTlsDAL.GetAll(ctx, model.LogProductType_FullSqlLog.String())
	if err != nil {
		log.Warn(ctx, "[checkDeletedMasterRDS] %s", err)
		return err
	}
	b.checkInstances(ctx, FullSqlInstances)
	return err
}

func (b *ScheduleInstanceCheckActor) checkInstances(ctx context.Context, auditInstanceList []*dao.AuditTls) {
	region := utils.GetRegion()
	for _, auditInstance := range auditInstanceList {
		if auditInstance.DbType == model.DSType_MetaMySQL.String() {
			log.Info(ctx, "check instance status, pass MetaMySQL, instanceId:%s, followInstanceId:%s", auditInstance.InstanceID, auditInstance.FollowInstanceID)
			continue
		}
		log.Info(ctx, "check instance status, instanceId:%s, followInstanceId:%s", auditInstance.InstanceID, auditInstance.FollowInstanceID)
		if auditInstance.Region != region {
			log.Error(ctx, "[checkDeletedMasterRDS] region not match %s %s %s", region, auditInstance.Region, libutils.Show(auditInstance))
			continue
		}
		ctx = utils.Su(ctx, auditInstance.TenantID)
		inst, err := b.billSvc.GetInstance(ctx, auditInstance.FollowInstanceID)
		log.Info(ctx, "%s", libutils.Show(inst))
		if err != nil {
			log.Warn(ctx, "get bill instance err")
			continue
		}
		if inst != nil {
			if inst.Region != region {
				log.Error(ctx, "[checkDeletedMasterRDS] region not match %s %s", region, inst.Region, libutils.Show(auditInstance))
				continue
			}
		}
		instanceType, err := model.DSTypeFromString(auditInstance.DbType)
		if err != nil {
			log.Error(ctx, "unknown instanceType: %s", auditInstance.DbType)
			continue
		}

		if inst == nil || inst.Status == 2 || inst.Status == 3 || inst.Status == 5 || inst.Status == 7 {
			err, isInstanceDelete := ds_utils.IsFollowInstanceDelete(ctx, b.source, auditInstance.FollowInstanceID, instanceType)
			if err != nil {
				continue
			}
			if isInstanceDelete {
				log.Info(ctx, "[checkDeletedMasterRDS] DescribeDBInstanceDetail %s", err)
				audit, err := b.auditTlsDAL.GetByIDWithoutTenant(ctx, auditInstance.InstanceID)
				if err != nil {
					log.Warn(ctx, "[checkDeletedMasterRDS] GetByFollowInstanceID %s", err)
					continue
				}
				DSType, err := model.DSTypeFromString(audit.DbType)
				if err != nil {
					log.Warn(ctx, "[checkDeletedMasterRDS] DSTypeFromString %s", err)
					continue
				}
				closeType, err := model.CloseTypeFromString(audit.DefaultCloseType)
				if err != nil {
					log.Warn(ctx, "[checkDeletedMasterRDS] CloseTypeFromString %s", err)
					continue
				}
				logProductType, err := model.LogProductTypeFromString(auditInstance.ProductType)
				if err != nil {
					log.Warn(ctx, "[checkDeletedMasterRDS] logProductType %s", err)
					continue
				}
				auditEntity := &entity.AuditInstance{
					AuditID:       audit.InstanceID,
					TenantID:      audit.TenantID,
					RDSInstanceID: audit.FollowInstanceID,
					Region:        audit.Region,
					DSType:        DSType,

					DefaultCloseType: closeType,
					LogProductType:   logProductType,
				}
				log.Warn(ctx, "[checkDeletedMasterRDS] due to rds instance deleted. delete audit instance . entity:%s", libutils.Show(auditEntity))
				err = b.publishEventSvc.PublishEvent(ctx, &entity.Event{
					InstanceID: audit.InstanceID,
					Type:       model.EventType_DeleteAuditInstanceByCheck,
					Result:     0,
					TenantID:   audit.TenantID,
				})
				if err != nil {
					log.Warn(ctx, "%s", err)
				}
				auditInst, err := b.billSvc.GetInstance(ctx, auditInstance.InstanceID)
				if err != nil {
					log.Warn(ctx, "[checkDeletedMasterRDS] Get audit instance err: %s", err)
					continue
				}
				if logProductType == model.LogProductType_AuditLog {
					if auditInst == nil || auditInst.Status == 2 || auditInst.Status == 3 || auditInst.Status == 5 || auditInst.Status == 7 {
						log.Info(ctx, "[checkDeletedMasterRDS] direct delete instance %s", libutils.Show(auditInstance))
						err := b.auditSvc.TerminateSqlAuditOrderCallback(ctx, &model.DeleteSqlAuditReq{
							RegionId:         audit.Region,
							FollowInstanceID: audit.FollowInstanceID,
							DSType:           DSType,
							CloseType:        closeType,
						}, "")
						if err != nil {
							log.Warn(ctx, "%s", err)
						}
						continue
					}
					if auditInst.BusinessStatus == 0 || auditInst.BusinessStatus == 1 {
						log.Info(ctx, "[checkDeletedMasterRDS] order delete instance %s", libutils.Show(auditInstance))
						_, err = b.auditSvc.OrderCancelAudit(ctx, auditEntity)
						if err != nil {
							log.Warn(ctx, "[checkDeletedMasterRDS] CancelInstance err: %s", err)
						}
						continue
					}
					if auditInst.BusinessStatus > 1 {
						log.Error(ctx, "instance bill is in progress. instance %s, bill %s", libutils.Show(auditInstance), libutils.Show(auditInst))
					} else {
						log.Error(ctx, "[checkDeletedMasterRDS] unknown delete. instance %s, bill %s", libutils.Show(auditInstance), libutils.Show(auditInst))
					}
				} else if logProductType == model.LogProductType_FullSqlLog {
					if auditInst == nil || auditInst.Status == 2 || auditInst.Status == 3 || auditInst.Status == 5 || auditInst.Status == 7 {
						// 应该不存在这种情况
						if auditInst != nil {
							log.Warn(ctx, "Instance status not consistent, BILL instance status:%s, DBW instance status:%s",
								billing.ConvertBillingInstanceStatusToStr(billing.BillingInstanceStatus(auditInst.Status)),
								model.AuditStatus(auditInstance.Status).String())
						}
						//err := b.fullSqlService.DeleteFullSqlResource(ctx, auditEntity.RDSInstanceID, auditEntity.TenantID, auditEntity.DefaultCloseType, auditEntity.DSType)
						_, err := b.fullSqlService.DeleteFullsqlResourceFlow(ctx, &model.DeleteFullsqlResourceReq{
							InstanceId: auditEntity.RDSInstanceID,
							CloseType:  &auditEntity.DefaultCloseType,
						})
						if err != nil {
							log.Warn(ctx, "%s", err)
						}
						continue
					}
					if auditInst.BusinessStatus == 0 || auditInst.BusinessStatus == 1 {
						log.Info(ctx, "[checkDeletedMasterRDS] order delete instance %s", libutils.Show(auditInstance))
						_, err = b.fullSqlService.CreateCancelOrder(ctx, auditEntity)
						if err != nil {
							log.Warn(ctx, "[checkDeletedMasterRDS] CancelInstance err: %s", err)
						}
						continue
					}
					if auditInst.BusinessStatus > 1 {
						log.Error(ctx, "instance bill is in progress. instance %s, bill %s", libutils.Show(auditInstance), libutils.Show(auditInst))
					} else {
						log.Error(ctx, "[checkDeletedMasterRDS] unknown delete. instance %s, bill %s", libutils.Show(auditInstance), libutils.Show(auditInst))
					}
				}
			} else {
				log.Warn(ctx, "[checkDeletedMasterRDS] query instance. instance %s, err:%s", libutils.Show(auditInstance), err)
			}
		} else {
			if inst != nil {
				log.Info(ctx, "[checkDeletedMasterRDS] check normal instance id : %s, status: %s, rds bill_instance_status: %s, rds bill_business_status: %s",
					auditInstance.InstanceID,
					model.AuditStatus(auditInstance.Status).String(),
					billing.ConvertBillingInstanceStatusToStr(billing.BillingInstanceStatus(inst.Status)),
					billing.ConvertBusinessStatusToStr(billing.BusinessStatus(inst.BusinessStatus)))
			} else {
				log.Info(ctx, "[checkDeletedMasterRDS] check normal instance id : %s, status: %s",
					auditInstance.InstanceID,
					model.AuditStatus(auditInstance.Status).String())
			}
		}
	}
}

func (b *ScheduleInstanceCheckActor) CheckOneInstance(ctx types.Context, followInstanceID string) {
	log.Info(ctx, "CheckOneInstance, check deleted rds instance, followInstanceID:%s", followInstanceID)
	auditInstance, err := b.auditTlsDAL.GetByFollowInstanceIDWithoutTenant(ctx, followInstanceID, model.LogProductType_AuditLog.String())
	if err == nil {
		b.checkInstances(ctx, []*dao.AuditTls{auditInstance})
	} else if err != gorm.ErrRecordNotFound {
		log.Warn(ctx, "GetByFollowInstanceID query error:%s", err)
	}
	auditInstance, err = b.auditTlsDAL.GetByFollowInstanceIDWithoutTenant(ctx, followInstanceID, model.LogProductType_FullSqlLog.String())
	if err == nil {
		b.checkInstances(ctx, []*dao.AuditTls{auditInstance})
	} else if err != gorm.ErrRecordNotFound {
		log.Warn(ctx, "GetByFollowInstanceID query error:%s", err)
	}

}
