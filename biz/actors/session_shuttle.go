package actors

import (
	"code.byted.org/infcs/dbw-mgr/biz/config"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/shuttle"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

func (a *SessionActor) closeTunnel(ctx types.Context, ds *shared.DataSource) error {
	if !isEnableShuttle(ds, a.cnf.Get(ctx)) {
		return nil
	}

	if err := a.unregisterMySelf(ctx, ds); err != nil {
		log.Warn(ctx, "Unregister ShuttleMgr failed, continue delete shuttle server")
	}

	hostsIp := []string{a.state.MyIP}

	req := &shuttle.CloseTunnelReq{
		ID:      a.GetSessionID(ctx),
		VpcID:   ds.GetVpcID(),
		HostsIp: hostsIp,
	}

	_, err := a.ShuttleSvc.CloseTunnel(ctx, req)
	if err != nil {
		log.Warn(ctx, "redisImpl: close tunnel failed: %s", err.Error())
		return err
	}
	return nil
}

func (a *SessionActor) onPingActor(ctx types.Context, msg *shuttle.PingActor) {
	vpc := ""
	if a.state.SessionCreated && a.state.DataSource != nil {
		vpc = a.state.DataSource.VpcID
	}
	ctx.ClientOf(consts.ShuttleMgrActorKind).Send(ctx, consts.SingletonActorName, &shared.PingActorResp{
		Kind:  consts.SessionActorKind,
		Name:  a.GetSessionID(ctx),
		VpcId: vpc,
	})
}

func (a *SessionActor) registerMySelf(ctx types.Context, ds *shared.DataSource) error {
	// TODO shuttle use config
	if !isEnableShuttle(ds, a.cnf.Get(ctx)) || ds.VpcID == "" {
		return nil
	}
	vpcID := ds.VpcID
	// FIXME: use Call?
	err := ctx.ClientOf(consts.ShuttleMgrActorKind).
		Send(ctx, consts.SingletonActorName, &shared.RegisterActor{
			VpcId: vpcID,
			Ip:    a.state.MyIP,
			Kind:  consts.SessionActorKind,
			Name:  a.GetSessionID(ctx),
		})
	log.Info(ctx, "Register Session Actor %s with Host IP %s err: %v", a.GetSessionID(ctx), a.state.MyIP, err)
	return err
}

// unregisterMySelf Pod重启导致session重启之后，shuttle的client ip发生变化，这里清理shuttlemgr中的信息
func (a *SessionActor) unregisterMySelf(ctx types.Context, ds *shared.DataSource) error {
	if !isEnableShuttle(ds, a.cnf.Get(ctx)) {
		return nil
	}
	vpcID := ds.VpcID
	err := ctx.ClientOf(consts.ShuttleMgrActorKind).
		Send(ctx, consts.SingletonActorName, &shared.UnregisterActor{
			VpcId: vpcID,
			Ip:    a.state.MyIP,
			Kind:  consts.SessionActorKind,
			Name:  a.GetSessionID(ctx),
		})
	log.Info(ctx, "UnRegister Session Actor %s with Host IP %s err: %v", a.GetSessionID(ctx), a.state.MyIP, err)
	return err
}

func isEnableShuttle(ds *shared.DataSource, conf *config.Config) bool {
	// mongo和redis都是通过shuttle连接的，直接判断
	if ds != nil && (ds.Type == shared.Redis || ds.Type == shared.Postgres || ds.Type == shared.MSSQL) {
		return true
	}
	// mysql有可能直连，有可能通过shuttle，所以要判断vpcID
	if ds != nil && ds.Type == shared.Mongo {
		return !conf.DisableMongoShuttle
	}
	if ds != nil && ds.Type == shared.MySQL && ds.VpcID != "" {
		return true
	}
	return false
}
