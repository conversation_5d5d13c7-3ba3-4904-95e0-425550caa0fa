package auto_create_index

import (
	"github.com/kr/pretty"
	"testing"
	"time"
)

func TestFmtTime(t *testing.T) {
	pretty.Print(GetDateString(time.Now().Add(-24 * time.Hour)))
}

func TestTrigger(t *testing.T) {
	var (
		a uint64 = 107374182400
		b int64 = 14612078433280
	)
	f := uint64(a) - uint64(b)
	println(float64(f))

}

func TestGetLas(t *testing.T) {
	wednesday := GetLastWednesday()
	pretty.Println(wednesday.Unix())
}