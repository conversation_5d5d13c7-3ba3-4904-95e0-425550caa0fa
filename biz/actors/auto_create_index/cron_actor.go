package auto_create_index

import (
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/service/approval_flow"
	"code.byted.org/infcs/dbw-mgr/biz/service/auto_create_index_event"
	"code.byted.org/infcs/dbw-mgr/biz/service/common"
	"code.byted.org/infcs/dbw-mgr/biz/service/message"
	"code.byted.org/infcs/dbw-mgr/biz/service/usermgmt"
	"code.byted.org/infcs/dbw-mgr/biz/service/workflow"
	dbw_utils "code.byted.org/infcs/dbw-mgr/biz/utils"
	parser "code.byted.org/infcs/ds-sql-parser"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-lark/lark"
	"github.com/kr/pretty"
	"regexp"
	"strconv"
	"strings"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/service/config"
	"code.byted.org/infcs/dbw-mgr/biz/service/space_analysis"
	"code.byted.org/infcs/dbw-mgr/biz/service/tls"
	"github.com/apache/thrift/lib/go/thrift"
	"k8s.io/utils/strings/slices"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/conv"
	"code.byted.org/infcs/dbw-mgr/biz/repository"
	"code.byted.org/infcs/dbw-mgr/biz/service/c3"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource"
	"code.byted.org/infcs/dbw-mgr/biz/service/idgen"
	"code.byted.org/infcs/dbw-mgr/biz/service/slow_advice"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	rdsModel_v2_new "code.byted.org/infcs/dbw-mgr/gen/rds-mgr/2022-01-01/kitex_gen/model/v2"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/cli"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
	"code.byted.org/infcs/protoactor-go/actor"
	"github.com/robfig/cron/v3"
	"go.uber.org/dig"
)

const (
	BackgroundStr = "DBW 检测到有索引未创建，自动创建索引"
)

type NewCronActorIn struct {
	dig.In
	Repo                    repository.InspectionRepo
	IdSvc                   idgen.Service
	ActorClient             cli.ActorClient
	Ds                      datasource.DataSourceService
	Cnf                     config.ConfigProvider
	C3                      c3.ConfigProvider
	LarkService             message.LarkService
	WorkflowService         workflow.TicketWorkflowService
	ConfigService           slow_advice.SlowAdviceConfigService
	SlowAdviceResService    slow_advice.SlowAdviceResService
	EventService            auto_create_index_event.AutoCreateIndexEventService
	SqlSpaceAnalysisService space_analysis.SqlSpaceAnalysisService
	TicketService           workflow.TicketService
	ApprovalFlowService     approval_flow.ApprovalFlowService
	UserMgmtSvc             usermgmt.UserService
}

func NewCronActor(p NewCronActorIn) types.SingletonProducer {
	return types.SingletonProducer{
		Name: consts.AutoCreateIndexActorKind,
		Producer: types.ActorProducerFunc(func() types.IActor {
			newCronActor := &CronActor{
				repo:                    p.Repo,
				state:                   &cronActorState{},
				cronMap:                 make(map[string]int),
				cron:                    cron.New(),
				idSvc:                   p.IdSvc,
				actorClient:             p.ActorClient,
				ds:                      p.Ds,
				cnf:                     p.Cnf,
				c3:                      p.C3,
				configService:           p.ConfigService,
				larkService:             p.LarkService,
				workflowService:         p.WorkflowService,
				slowAdviceResService:    p.SlowAdviceResService,
				sqlSpaceAnalysisService: p.SqlSpaceAnalysisService,
				eventService:            p.EventService,
				ticketService:           p.TicketService,
				approvalFlowService:     p.ApprovalFlowService,
				userMgmtSvc:             p.UserMgmtSvc,
			}
			return newCronActor
		}),
	}
}

type CronActor struct {
	repo                    repository.InspectionRepo
	state                   *cronActorState
	cronMap                 map[string]int
	cron                    *cron.Cron
	idSvc                   idgen.Service
	actorClient             cli.ActorClient
	cnf                     config.ConfigProvider
	c3                      c3.ConfigProvider
	ds                      datasource.DataSourceService
	larkService             message.LarkService
	workflowService         workflow.TicketWorkflowService
	configService           slow_advice.SlowAdviceConfigService
	slowAdviceResService    slow_advice.SlowAdviceResService
	eventService            auto_create_index_event.AutoCreateIndexEventService
	sqlSpaceAnalysisService space_analysis.SqlSpaceAnalysisService
	ticketService           workflow.TicketService
	approvalFlowService     approval_flow.ApprovalFlowService
	userMgmtSvc             usermgmt.UserService
}

type cronActorState struct{}

const (
	timeIntervalSeconds uint64 = 60 * 10 // 10分钟
)

func (c *CronActor) GetState() []byte {
	state, _ := json.Marshal(c.state)
	return state
}

func (self *CronActor) Process(ctx types.Context) {
	//defer ctx.SetReceiveTimeout(time.Duration(timeIntervalSeconds) * time.Second)
	log.Debug(ctx, "msg is %#v", ctx.Message())
	//switch msg := ctx.Message().(type) {
	switch ctx.Message().(type) {
	case *actor.Started:
		self.initCronTask(ctx)
	}
}

// GetLastWednesday 获取上一个星期三的日期
func GetLastWednesday() time.Time {
	now := time.Now()
	// 计算当前是星期几
	weekday := now.Weekday()
	// 计算距离上一个星期三的天数
	daysToSubtract := (weekday - time.Wednesday + 7) % 7
	if daysToSubtract == 0 {
		// 如果今天是星期三，返回上一个星期三
		daysToSubtract = 7
	}
	// 减去相应的天数
	return now.AddDate(0, 0, -int(daysToSubtract))
}

// initCronTask 启动定时任务
func (self *CronActor) initCronTask(ctx types.Context) {
	log.Info(ctx, "CronActor initCronTask start!")
	// todo 如果上次任务没执行完继续执行
	self.cron = cron.New(cron.WithChain(
		cron.SkipIfStillRunning(cron.DefaultLogger),
	))
	self.cron.Start()
	_, err := self.cron.AddFunc("50 10 * * *", func() {
		// 推最近周三的建议
		self.Trigger(ctx, GetLastWednesday())
	})
	log.Info(ctx, "SqlReviewActor initCronTask finished!")
	if err != nil {
		log.Error(ctx, "SqlReviewActor initCronTask error, err is: %v", err)
	}
}

func GetDateString(ts time.Time) string {
	return ts.Format("20060102")
}

type TriggerAgg struct {
	ScanAllCnt             int
	RecordedCnt            int
	NotRunningCnt          int
	RpcFailedCnt           int
	NotEnableAutoCreateCnt int
	NoIndexAdviceCnt       int
	SpeedUpFilterCnt       int
	ExeFilterCnt           int
	ExclusivityFilterCnt   int
	SpaceFilterCnt         int
	SpaceNotTwoCnt         int
	SpaceTooHigh           int
	LeftSpaceTooSmall      int
	ManyIndexCnt           int // 需要多个索引的暂时过滤掉，如果过多再来考虑实现
	PromoteCnt             int // 触达总数
	ErrList                []string
	SafeCntlCnt            int
	PreCheckFilterCnt      int // 安全检查过滤
}

func (c *CronActor) Trigger(ctx types.Context, SQLDate time.Time) {
	agg := &TriggerAgg{}
	start := time.Now()
	types := []shared.DataSourceType{shared.ByteRDS}
	if !dbw_utils.IsByteCloud() {
		types = []shared.DataSourceType{shared.MySQL, shared.VeDBMySQL}
	}
	limit := 10
	// 配置开关
	if c.c3.GetNamespace(ctx, consts.C3ApplicationNamespace).PromoteLimitCntPerDay != 0 {
		limit = c.c3.GetNamespace(ctx, consts.C3ApplicationNamespace).PromoteLimitCntPerDay
	}

	regions := []string{"cn"}
	//regions = c.c3.GetNamespace(ctx, consts.C3ApplicationNamespace).AutoCreateIndexRegionIds
LOOP:
	for _, regionId := range regions {
		for _, sourceType := range types {
			resp, err := c.ds.ListInstance(
				ctx, &datasource.ListInstanceReq{
					Type:       sourceType,
					RegionId:   regionId,
					PageNumber: 1,
					PageSize:   10,
				},
			)
			if err != nil {
				log.Error(ctx, "inspection: rds api err %s", err.Error())
				continue
			}
			total := resp.Total
			for i := 0; i <= int(total/10); i++ {
				time.Sleep(1 * time.Second)
				req := &datasource.ListInstanceReq{
					Type:       sourceType,
					RegionId:   regionId,
					PageNumber: int32(i + 1),
					PageSize:   10,
				}
				instances, err := c.ds.ListInstance(ctx, req)
				if err != nil {
					log.Error(ctx, "inspection: rds api err %s", err.Error())
					continue
				}
				for _, info := range instances.InstanceList {
					agg.ScanAllCnt++

					c.TriggerOne(ctx, agg, info, SQLDate, regionId)
					// over time 每天17点以后不再推送
					if time.Since(start) > 7*time.Hour {
						break LOOP
					}
					// limit
					if agg.PromoteCnt > limit {
						break LOOP
					}
				}
			}
		}
	}
}

func (c *CronActor) TriggerOne(ctx types.Context, agg *TriggerAgg, info *model.InstanceInfo, SQLDate time.Time, regionId string) {
	// gen task for one
	SQLDateStr := GetDateString(SQLDate)
	log.Info(ctx, "TriggerOne instanceId: %s SQLDate %s", *info.InstanceId, SQLDateStr)
	var (
		oneErr            error
		recorded          bool
		running           bool
		enableCreateIndex bool
		ddls              []*model.Advice
		benefitSum        float64
		spaceInfo         string
		spacePass         bool
		Res               *IndexAdviceRes
		status            = model.AutoCreateIndexEventStatus_EventUndo
	)

	defer func() {
		log.Info(ctx, "instanceId: %s agg %s", *info.InstanceId, pretty.Sprint(agg))

		if oneErr != nil {
			log.Warn(ctx, "TriggerOne failed %v", oneErr)
			agg.RpcFailedCnt++
			agg.ErrList = append(agg.ErrList, oneErr.Error())
		}
		if Res != nil && oneErr == nil {
			// record
			log.Info(ctx, "reach one res %s", pretty.Sprint(Res))
			if Res.TicketId != "" {
				status = model.AutoCreateIndexEventStatus_EventFinished
			}
			event := &entity.AutoCreateIndexEvent{
				InstanceID:         *info.InstanceId,
				TenantID:           info.GetAccountId(),
				Status:             status.String(),
				PlanTime:           Res.PlanExecTimeStamp.Unix(),
				PlanTimeTyp:        Res.PlanTimeType.String(),
				SQLDate:            Res.SQLDate,
				TicketPlatFormType: Res.TicketPlatFormType.String(),
			}
			if len(Res.advices) > 0 {
				event.Ddl = Res.advices[0].Advice
			}
			event.TicketID = Res.TicketId
			c.eventService.Create(ctx, event)
		}
	}()

	if recorded, oneErr = c.recorded(ctx, agg, info, SQLDateStr); oneErr != nil || recorded {
		return
	}

	date, _ := strconv.Atoi(SQLDateStr)
	Res = &IndexAdviceRes{
		info:       info,
		advices:    ddls,
		BenefitSum: benefitSum,
		DBWLink:    c.getCloudLink(info),
		AutoLink:   c.getCloudOpsLink(info),
		SpaceInfo:  spaceInfo,
		SQLDate:    int64(date),
		TenantId:   info.GetAccountId(),
	}
	if running, oneErr = c.instanceRunning(ctx, agg, info); oneErr != nil || !running {
		return
	}
	if ddls, benefitSum, oneErr = c.getIndexAdvice(ctx, agg, info, Res, SQLDate); oneErr != nil || len(ddls) <= 0 {
		return
	}
	if enableCreateIndex, oneErr = c.EnableAutoCreateIndex(ctx, agg, info, Res); oneErr != nil {
		return
	}
	if spacePass, spaceInfo, oneErr = c.checkSpacePass(ctx, agg, regionId, info, ddls[0]); oneErr != nil {
		return
	}
	if spacePass && enableCreateIndex {
		if oneErr = c.CreateIndex(ctx, agg, Res); oneErr != nil {
			return
		}
	}
	// 触达用户，推荐开启 or 提示审批
	if oneErr = c.Promote(ctx, agg, info, Res); oneErr != nil {
		return
	}
}

func (c *CronActor) recorded(ctx types.Context, agg *TriggerAgg, info *model.InstanceInfo, SQLDateStr string) (recorded bool, err error) {
	defer func() {
		if recorded {
			log.Info(ctx, "recorded %s", *info.InstanceId)
			agg.RecordedCnt++
		}
	}()
	SQLDate, _ := strconv.Atoi(SQLDateStr)
	var eventResp *model.DescribeAutoCreateIndexEventsResp
	eventResp, err = c.eventService.ListAutoCreateIndexEvent(
		ctx, repository.QueryCreateIndexEventParam{
			InstanceType:  thrift.StringPtr(info.InstanceType.String()),
			InstanceId:    info.InstanceId,
			OnlyEffective: false,
		}, 1, 1)
	if err != nil {
		return true, err
	}
	if eventResp == nil || eventResp.Total == 0 || *eventResp.DiskAutoScaleEvents[0].SQLDate <= int64(SQLDate) {
		//if eventResp == nil || eventResp.Total == 0 || *eventResp.DiskAutoScaleEvents[0].SQLDate == int64(SQLDate) {
		return false, nil
	}
	return true, nil
}

func (c *CronActor) instanceRunning(ctx types.Context, agg *TriggerAgg, info *model.InstanceInfo) (running bool, err error) {
	defer func() {
		if !running {
			log.Info(ctx, "instance not Running %s", *info.InstanceId)
			agg.NotRunningCnt++
		}
	}()
	region := ""
	if info.RegionId != nil {
		region = *info.RegionId
	}
	running, err = c.isInstanceRunning(ctx, conv.ToSharedTypeV2(info.InstanceType), region, *info.InstanceId)
	if err != nil {
		log.Error(ctx, "rds api: isInstanceRunning  err %s", err.Error())
		return false, err
	}
	return
}

func (c *CronActor) EnableAutoCreateIndex(ctx types.Context, agg *TriggerAgg, info *model.InstanceInfo, res *IndexAdviceRes) (enable bool, err error) {
	defer func() {
		if !enable {
			log.Info(ctx, "instance not EnableAutoCreateIndex %s", *info.InstanceId)
			agg.NotEnableAutoCreateCnt++
		}
	}()
	var config *model.ListSlowQueryAdviceConfigResp
	config, err = c.configService.ListSlowQueryAdviceTask(
		ctx, &shared.ListSlowQueryAdviceTaskReq{
			InstanceId: *info.InstanceId,
		},
	)
	if err != nil {
		log.Error(ctx, "configService: QuerySlowQueryAdviceTask err %s", err.Error())
		return
	}
	if config == nil || len(config.Configs) <= 0 {
		return
	}
	if config.Configs[0].Config.EnableAutoCreateIndex == nil || !*config.Configs[0].Config.EnableAutoCreateIndex {
		return
	}
	cnf := config.Configs[0].Config
	res.PlanTimeType = cnf.GetPlanTimeType()
	PlanExecTimeStamp := time.Now()
	switch res.PlanTimeType {
	case model.PlanTimeType_MaintenanceWindow:
		var resp *datasource.DescribeDBInstanceDetailResp
		resp, err = c.ds.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
			Type:       conv.ToSharedTypeV2(info.InstanceType),
			InstanceId: *info.InstanceId,
		})
		if err != nil {
			log.Error(ctx, "ds: DescribeDBInstanceDetail err %s", err.Error())
			return
		}
		if resp.DescribeMaintenanceWindow == nil || resp.DescribeMaintenanceWindow.MaintenanceTime == "" {
			log.Error(ctx, "ds: DescribeDBInstanceDetail not found exec time instanceId %s", *info.InstanceId)
			err = errors.New("not found exec time")
			return
		}
		PlanExecTimeStamp, _, err = ParseTimeRange(resp.DescribeMaintenanceWindow.MaintenanceTime, 8)
		if err != nil {
			log.Error(ctx, "ds: DescribeDBInstanceDetail parse time range error %v %s", err, resp.DescribeMaintenanceWindow)
			err = errors.New(fmt.Sprintf("parse time range error %v %s", err, *cnf.AutoCreateIndexTimeStr))
			return
		}
		switch resp.DescribeMaintenanceWindow.DayKind {
		case datasource.DayKind_Week:
			weekday := PlanExecTimeStamp.Weekday()
			daysToAdd := (time.Friday - weekday + 7) % 7
			PlanExecTimeStamp = PlanExecTimeStamp.AddDate(0, 0, int(daysToAdd))
		case datasource.DayKind_Month:
			return false, errors.New("month not support")
		default:
			return false, errors.New("day kind not support")
		}
	case model.PlanTimeType_Now:
		// just now
	case model.PlanTimeType_TimeRange:
		// 时间范围，需要解析时间字符串
		if cnf.AutoCreateIndexTimeStr == nil || *cnf.AutoCreateIndexTimeStr == "" {
			err = errors.New("not found exec time")
			log.Error(ctx, "cnf.AutoCreateIndexTimeStr is empty instanceId %s", *info.InstanceId)
			return
		}
		PlanExecTimeStamp, _, err = ParseTimeRange(*cnf.AutoCreateIndexTimeStr, 0)
		if err != nil {
			err = errors.New(fmt.Sprintf("parse time range error %v %s", err, *cnf.AutoCreateIndexTimeStr))
			log.Error(ctx, "parse time range error %v %s", err, *cnf.AutoCreateIndexTimeStr)
			return
		}
	default:
		err = errors.New("not found exec time")
		return
	}
	res.PlanExecTimeStamp = PlanExecTimeStamp
	res.TenantId = config.Configs[0].GetTenantID()
	enable = true
	return
}

func (c *CronActor) getCloudLink(info *model.InstanceInfo) string {
	return fmt.Sprintf("https://cloud.bytedance.net/dbw/observation?dsType=ByteRDS&instanceId=%s&dbName=%s&tab=slowLog&x-resource-account=public&x-bc-region-id=bytedance&x-bc-vregion=China-North",
		*info.InstanceId, *info.InstanceId)
}

func (c *CronActor) getCloudOpsLink(info *model.InstanceInfo) string {
	return fmt.Sprintf("https://cloud.bytedance.net/dbw/observation?dsType=ByteRDS&instanceId=%s&dbName=%s&tab=autoOps&x-resource-account=public&x-bc-region-id=bytedance&x-bc-vregion=China-North",
		*info.InstanceId, *info.InstanceId)
}

func (c *CronActor) Promote(ctx types.Context, agg *TriggerAgg, info *model.InstanceInfo, res *IndexAdviceRes) error {
	agg.PromoteCnt++
	if !c.c3.GetNamespace(ctx, consts.C3ApplicationNamespace).PromoteSwitch {
		log.Info(ctx, "PromoteSwitch is false, no need to promote instance %s", *info.InstanceId)
		return nil
	}
	if !dbw_utils.IsByteCloud() {
		// 火山目前没有触达方式
		log.Info(ctx, "Is Not ByteCloud, no need to promote instance %s", *info.InstanceId)
		return nil
	}
	var users []string
	if c.c3.GetNamespace(ctx, consts.C3ApplicationNamespace).PromoteInnerOnly {
		users = append(users, "mahangyu.tb")
	} else {
		users = append(users, "mahangyu.tb")
		users = append(users, info.Owners...)
	}
	content := c.buildNotifyMsg(res)
	res.Content = pretty.Sprint(content)
	return c.larkService.NotifyUsersRichText(ctx, content, users...)
}

func (c *CronActor) buildNotifyMsg(res *IndexAdviceRes) *lark.PostContent {
	title := "Dbw 数据库工作台 索引优化建议"
	builder := lark.NewPostBuilder()
	builder.Title(title)

	// 数据库信息部分
	{
		builder.TextTag("📋 数据库信息: ", 1, true)
		builder.TextTag(fmt.Sprintf("您负责的数据库 %s 存在性能提升显著的索引缺失\n\n", res.advices[0].DB), 1, false)
	}
	// 索引建议部分
	{
		builder.TextTag("💡 优化建议: ", 1, true)
		builder.TextTag("创建以下索引可显著提升查询性能\n", 1, false)
		builder.TextTag(fmt.Sprintf("%s\n\n", res.advices[0].Advice), 1, false)
	}
	// 已经创建了工单
	if len(res.ApproveLinks) > 0 {
		builder.TextTag("✅ 因该数据库已开启自动创建索引且探测到空间充足，优化DDL已自动提交，请前往推进审批\n", 1, false)
		builder.LinkTag(" 前往工单\n\n", res.ApproveLinks[0])
	} else {
		if !res.EnableCreateIndex {
			builder.LinkTag("⚠️ 如需开启 or 取消自动创建索引，请前往DBW自动运维\n\n", res.AutoLink)
		} else if !res.CreateIndexSwitch {
			builder.TextTag("⚠️ 当前暂未开启创建\n\n", 1, false)
		} else {
			builder.TextTag("⚠️ 空间不足，请先解决空间问题，再推进索引创建\n\n", 1, false)
		}
	}
	// 性能影响部分
	{
		builder.TextTag("⏱️ 性能数据: \n", 1, true)
		builder.TextTag(fmt.Sprintf("当前查询耗时: %.2fs \n", res.advices[0].Agg.QueryTimeStats.Max), 1, false)
		builder.TextTag(fmt.Sprintf("占总慢查询时间比例: %.2f%% \n", res.advices[0].Agg.QueryTimeRatio), 1, false)
		builder.TextTag(fmt.Sprintf("预计提升效果: %.2f倍 \n", res.advices[0].SpeedUp), 1, false)
		builder.TextTag(fmt.Sprintf("总慢查询耗时削减: %.2f%% \n\n", res.advices[0].Benefit), 1, false)
	}
	// 慢查询详情部分
	{
		builder.TextTag("🔍 慢查询详情: \n", 1, true)
		builder.TextTag(res.advices[0].SQLModule, 1, false)
		builder.LinkTag("✅ 更多慢日志诊断建议请前往DBW查看\n\n", res.DBWLink)
	}
	// 操作建议部分
	{
		builder.TextTag("⚠️ 影响说明: \n", 1, true)
		builder.TextTag(" 当前建议已通过索引排他检查，即新建后不影响其他索引使用，理论上不会对现有业务造成影响，建议在低峰期执行\n\n", 1, false)
	}
	// 咨询链接部分
	//{
	//	builder.TextTag("💬 咨询支持: ", 1, true)
	//	builder.LinkTag("点击加入DBW用户反馈群", "https://applink.larkoffice.com/client/chat/chatter/add_by_link?link_token=8f3v927d-f2be-441e-83d0-2ee9e5783dcd")
	//}

	return builder.Render()
}

func (c *CronActor) preDealDDL(ctx context.Context, ddl string) (string, error) {
	p := parser.New()
	stmts, _, err := p.Parse(ddl, "utf8", "")
	if err != nil {
		log.Warn(ctx, fmt.Sprintf("SQL statement %v error or unsupported, reason: %v", ddl, err))
		return "", err
	}
	if len(stmts) == 0 {
		log.Warn(ctx, fmt.Sprintf("SQL statement %v error or unsupported, reason: empty stmts", ddl))
		return "", err
	}
	// 内场ndb不能出现db，但是内场本身就是一个库就只有一个db，sql中不会出现db
	// 火山会出现，但是能出现
	return ddl, nil
}

// ParseTimeRange 解析时间范围字符串，返回明天对应时间的时间戳
func ParseTimeRange(timeRange string, utcdiff int) (start time.Time, end time.Time, err error) {
	// 分割时间范围字符串
	parts := strings.Split(timeRange, "-")
	if len(parts) != 2 {
		err = fmt.Errorf("invalid time range format")
		return
	}

	// 去除时区标识 'Z' 并解析小时和分钟
	startTimeStr := strings.TrimSuffix(parts[0], "Z")
	endTimeStr := strings.TrimSuffix(parts[1], "Z")

	var startHour, startMin int
	startHour, startMin, err = parseTime(startTimeStr)
	if err != nil {
		return
	}
	var endHour, endMin int
	endHour, endMin, err = parseTime(endTimeStr)
	if err != nil {
		return
	}

	// 获取明天的日期
	tomorrow := time.Now().AddDate(0, 0, 1)

	startTime := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), (startHour+utcdiff)%24, startMin, 0, 0, time.Now().Location())
	endTime := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), (endHour+utcdiff)%24, endMin, 0, 0, time.Now().Location())

	return startTime, endTime, nil
}

// parseTime 解析时间字符串为小时和分钟
func parseTime(timeStr string) (int, int, error) {
	parts := strings.Split(timeStr, ":")
	if len(parts) != 2 {
		return 0, 0, fmt.Errorf("invalid time format")
	}

	hour, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, 0, err
	}

	min, err := strconv.Atoi(parts[1])
	if err != nil {
		return 0, 0, err
	}

	return hour, min, nil
}

func (c *CronActor) CreateIndex(ctx types.Context, agg *TriggerAgg, IndexAdviceRes *IndexAdviceRes) error {
	IndexAdviceRes.CreateIndexSwitch = c.c3.GetNamespace(ctx, consts.C3ApplicationNamespace).CreateIndexSwitch
	if !c.c3.GetNamespace(ctx, consts.C3ApplicationNamespace).CreateIndexSwitch {
		log.Info(ctx, "CreateIndexSwitch is false, no need to create index")
		return nil
	}
	// 提前处理下ddl，不能出现db
	ddl, _ := c.preDealDDL(ctx, IndexAdviceRes.advices[0].Advice)
	if IndexAdviceRes.info.InstanceType == model.InstanceType_ByteRDS {
		ticketId, link, err := c.createIndexForInner(ctx, IndexAdviceRes, ddl, IndexAdviceRes.PlanExecTimeStamp)
		if err != nil {
			log.Error(ctx, "createIndexForInner err %s", err.Error())
			return err
		}
		IndexAdviceRes.TicketPlatFormType = model.TicketPlatFormType_BpmWorkflow
		IndexAdviceRes.ApproveLinks = append(IndexAdviceRes.ApproveLinks, link)
		IndexAdviceRes.TicketId = ticketId
		return nil
	}
	return c.createIndexForVol(ctx, agg, IndexAdviceRes)
}

func (c *CronActor) createIndexForInner(ctx types.Context, IndexAdviceRes *IndexAdviceRes, ddl string, PlanExecTimeStamp time.Time) (string, string, error) {
	// 1、生成一个ds
	ds := common.GenerateDatasource(ctx, c.c3, c.cnf, IndexAdviceRes.info.GetInstanceId(),
		IndexAdviceRes.advices[0].DB, conv.ToSharedTypeV2(IndexAdviceRes.info.InstanceType).String(), IndexAdviceRes.info.RegionId)
	// 2.创建工单
	if len(IndexAdviceRes.info.Owners) <= 0 {
		return "", "", errors.New(fmt.Sprintf("no owners in instance %s", IndexAdviceRes.info.GetInstanceId()))
	}
	resp, err := c.ds.CreateDDLTicket(ctx, &datasource.CreateDDLTicketReq{
		Source:            ds,
		BackGround:        "DBW自动创建索引，有疑问可联系mahangyu.tb",
		SQL:               ddl,
		PlanExecTimeStamp: PlanExecTimeStamp,
		Creator:           IndexAdviceRes.info.Owners[0],
	})
	if err != nil {
		log.Warn(ctx, fmt.Sprintf("create index for inner failed, err:%s", err.Error()))
		return "", "", err
	}
	return resp.TktId, resp.Link, nil
}
func checkMySQLFormat(ctx context.Context, sql string) error {
	p := parser.New()
	stmts, _, err := p.Parse(sql, "utf8", "")
	if err != nil {
		log.Warn(ctx, fmt.Sprintf("SQL statement %v error or unsupported, reason: %v", sql, err))
		return err
	}
	if len(stmts) == 0 {
		log.Warn(ctx, fmt.Sprintf("SQL statement %v error or unsupported, reason: empty stmts", sql))
		return err
	}
	return nil
}

func (c *CronActor) createIndexForVol(ctx types.Context, agg *TriggerAgg, IndexAdviceRes *IndexAdviceRes) (err error) {
	// 确保实例中注入dbw_boot账号
	c.userMgmtSvc.AddDBWBotAccount(ctx, IndexAdviceRes.info.GetAccountId())
	if err = c.createTicketForVol(ctx, agg, IndexAdviceRes); err != nil {
		return
	}
	var pass bool
	if pass, err = c.preCheckPass(ctx, agg, IndexAdviceRes); err != nil || !pass {
		return
	}
	if err = c.submitTicket(ctx, agg, IndexAdviceRes); err != nil {
		return
	}
	return
}

func (c *CronActor) createTicketForVol(ctx context.Context, agg *TriggerAgg, IndexAdviceRes *IndexAdviceRes) (err error) {
	var (
		isInstanceAvailable bool
		ticketId            int64
		approvalFlowId      int64
		approvalTemplateId  int64
	)
	defer func() {
		if !isInstanceAvailable {
			agg.SafeCntlCnt++
		}
	}()

	IndexAdviceRes.TicketPlatFormType = model.TicketPlatFormType_DbwTicket
	if err = checkMySQLFormat(ctx, IndexAdviceRes.advices[0].Advice); err != nil {
		return err
	}
	if isInstanceAvailable, err = c.ticketService.IsInstanceAvailable(ctx, IndexAdviceRes.TenantId, *IndexAdviceRes.info.InstanceId); err != nil || !isInstanceAvailable {
		return
	}
	if ticketId, err = c.idSvc.NextID(ctx); err != nil {
		log.Warn(ctx, fmt.Sprintf("创建工单失败, 生成工单ID失败，err:%s", err.Error()))
		return
	}
	if approvalFlowId, approvalTemplateId, err = c.createApprovalFlow(ctx, &model.CreateTicketReq{
		InstanceType: IndexAdviceRes.info.InstanceType,
		InstanceId:   *IndexAdviceRes.info.InstanceId,
	}, *IndexAdviceRes.info.AccountId); err != nil {
		log.Warn(ctx, "createApprovalFlow error: %s", err.Error())
		return consts.ErrorWithParam(model.ErrorCode_SystemError, "create approval flow error")
	}
	//ds := &model.DataSource{
	//	Type:       conv.ToModelType(conv.ToSharedTypeV2(IndexAdviceRes.info.InstanceType)),
	//	InstanceId: IndexAdviceRes.info.InstanceId,
	//	LinkType:   model.LinkType_Volc,
	//}
	//err = c.userMgmtSvc.GetDBAccount(ctx, ds)
	//if err != nil {
	//	log.Error(ctx, "get db account error: %s", err.Error())
	//	return err
	//}
	var dbwBotAccount string
	if dbwBotAccount, err = c.userMgmtSvc.GetDBDbwBotAccount(ctx, IndexAdviceRes.info.GetAccountId()); err != nil {
		return
	}
	TicketExecuteType := model.ExecuteType_Auto
	if IndexAdviceRes.PlanTimeType != model.PlanTimeType_Now {
		TicketExecuteType = model.ExecuteType_Cron
	}
	_, err = c.ticketService.CreateTicket(ctx, &model.CreateTicketReq{
		BatchConfig: &model.BatchConfig{
			SleepTimeMs:        thrift.Int32Ptr(500),
			IsEnableDelayCheck: thrift.BoolPtr(false),
			BatchSize:          thrift.Int32Ptr(1000),
		},
		//CreateUser: ds.GetDbaUid(),
		CreateUser:        dbwBotAccount,
		DatabaseName:      IndexAdviceRes.advices[0].DB,
		InstanceId:        IndexAdviceRes.info.GetInstanceId(),
		InstanceType:      IndexAdviceRes.info.InstanceType,
		SqlText:           thrift.StringPtr(IndexAdviceRes.advices[0].Advice),
		TicketExecuteType: model.ExecuteTypePtr(TicketExecuteType),
		TicketType:        model.TicketType_FreeLockStructChange, // online ddl
		ExecStartTime:     thrift.Int32Ptr(int32(IndexAdviceRes.PlanExecTimeStamp.Unix())),
		ExecEndTime:       thrift.Int32Ptr(0),
		Title:             thrift.StringPtr(BackgroundStr),
	}, approvalFlowId, approvalTemplateId, 0, ticketId, IndexAdviceRes.TenantId, dbwBotAccount)
	if err != nil {
		log.Error(ctx, "createTicket error: %s", err.Error())
		return err
	}
	IndexAdviceRes.TicketId = fmt.Sprintf("%d", ticketId)
	return
}

func (c *CronActor) preCheckPass(ctx context.Context, agg *TriggerAgg, IndexAdviceRes *IndexAdviceRes) (bool, error) {
	time.Sleep(5 * time.Second)
	detail, err := c.ticketService.DescribePreCheckDetail(
		ctx, &model.DescribePreCheckDetailReq{TicketId: IndexAdviceRes.TicketId},
	)
	if err != nil {
		return false, err
	}
	if !detail.AllPass {
		log.Info(ctx, "precheck not pass, detail: %s", pretty.Sprint(detail))
		agg.PreCheckFilterCnt++
		return false, nil
	}
	return true, nil
}

func (c *CronActor) submitTicket(ctx context.Context, agg *TriggerAgg, IndexAdviceRes *IndexAdviceRes) error {
	_, err := c.ticketService.SubmitTicket(
		ctx, &model.SubmitTicketReq{
			TicketId: IndexAdviceRes.TicketId,
			IsUnlock: true,
		},
	)
	return err
}

func (c *CronActor) createApprovalFlow(ctx context.Context, req *model.CreateTicketReq, tenantId string) (int64, int64, error) {
	instanceInfo, err := c.approvalFlowService.GetInstanceInfo(ctx, req.InstanceId, req.InstanceType.String(), tenantId)
	if err != nil {
		log.Warn(ctx, "GetInstanceInfo error: %s", err.Error())
		return 0, 0, err
	}
	// 获取工单的审批模板id ticketApprovalTemplateId
	flowConfig, err := c.approvalFlowService.GetApprovalFlowConfig(ctx, instanceInfo.ApprovalFlowConfigId)
	if err != nil {
		log.Warn(ctx, "ListApprovalFlowConfig error: %s", err.Error())
		return 0, 0, err
	}
	if flowConfig == nil {
		log.Warn(ctx, "there is no flowConfig configId:%d", instanceInfo.ApprovalFlowConfigId)
		return 0, 0, fmt.Errorf(fmt.Sprintf("there is no flowConfig configId:%d", instanceInfo.ApprovalFlowConfigId))
	}
	ticketApprovalTemplateId := c.getTicketApprovalTemplateID(flowConfig)
	approvalFlowId, err := c.approvalFlowService.CreatApprovalFlowRecord(ctx, fmt.Sprintf("%d", ticketApprovalTemplateId), req.InstanceId)
	if err != nil {
		log.Warn(ctx, "CreatApprovalFlowRecord error: %s", err.Error())
		return 0, 0, err
	}
	return approvalFlowId, ticketApprovalTemplateId, nil
}

func (h *CronActor) getTicketApprovalTemplateID(flowConfig *entity.ApprovalFlowConfig) int64 {
	for _, flowScenes := range flowConfig.FlowScenes {
		if flowScenes.ScenesType == entity.TicketFlowType {
			return flowScenes.TemplateId
		}
	}
	return 0
}

type IndexAdviceRes struct {
	info               *model.InstanceInfo
	advices            []*model.Advice
	BenefitSum         float64
	DBWLink            string
	AutoLink           string
	ApproveLinks       []string
	SpaceInfo          string
	PlanExecTimeStamp  time.Time
	TenantId           string
	PlanTimeType       model.PlanTimeType
	SQLDate            int64
	TicketPlatFormType model.TicketPlatFormType
	TicketId           string
	Content            string
	EnableCreateIndex  bool
	CreateIndexSwitch  bool
}

func (c *CronActor) checkSpacePass(ctx types.Context, agg *TriggerAgg, regionId string, info *model.InstanceInfo, advice *model.Advice) (pass bool, spaceInfo string, err error) {
	defer func() {
		if !pass {
			log.Info(ctx, "checkSpacePass not pass, advice: %s instance: %s", pretty.Sprint(advice), info.InstanceId)
			agg.SpaceFilterCnt++
		}
		log.Info(ctx, "checkSpace instance id %s, pass: %v, spaceInfo: %s", *info.InstanceId, pass, spaceInfo)
	}()
	if info.InstanceType == model.InstanceType_VeDBMySQL {
		// VeDB 理论空间无上限，不存在总空间的概念
		pass = true
		return
	}
	describeTableSpace := shared.DescribeTableSpace{
		Product:    int64(info.InstanceType),
		Database:   advice.DB,
		InstanceId: *info.InstanceId,
		RegionId:   regionId,
		TableName:  advice.TableName,
		PageNumber: 1,
		PageSize:   10,
		OrderItem:  "TableSpace",
		OrderRule:  "DESC",
	}
	var resp *shared.DescribeTableSpaceResp
	resp, err = c.sqlSpaceAnalysisService.DescribeDataBaseTableSpace(ctx, &describeTableSpace)
	if err != nil || resp == nil {
		log.Warn(ctx, "DescribeTableSpace-enter2-failed %v resp:%v", err, resp)
		return
	}
	log.Info(ctx, "DescribeTableSpace-enter2-success %v", resp)
	spaceSum := resp.TableStats[0].TableSpace

	nodes, err := c.ds.ListInstanceNodes(ctx, &datasource.ListInstanceNodesReq{
		RegionId:   regionId,
		DSType:     conv.ToSharedTypeV2(info.InstanceType),
		InstanceId: *info.InstanceId,
	})
	if err != nil || nodes == nil {
		log.Warn(ctx, "ListInstanceNodes error:%v resp:%v", err, nodes)
		return
	}
	var master string
	for _, node := range nodes.Nodes {
		if node.NodeType == model.NodeType_Primary {
			master = node.NodeId
		}
	}
	if master == "" {
		log.Warn(ctx, "ListInstanceNodes error: master node not found")
		err = fmt.Errorf("master node not found")
		return
	}
	// 查当前空间使用大小
	var usedSize int64
	usedSize, err = c.ds.GetUsedSize(ctx, &datasource.GetDiskSizeReq{
		InstanceType: conv.ToSharedTypeV2(info.InstanceType),
		InstanceId:   *info.InstanceId,
		NodeIds:      []string{master},
	})
	if err != nil {
		log.Warn(ctx, "GetUsedSize error:%v", err)
		return
	}

	// 查总空间大小
	var storageSpace uint64
	storageSpace, err = c.ds.GetDiskTotalSize(ctx, &datasource.GetDiskSizeReq{
		InstanceType: conv.ToSharedTypeV2(info.InstanceType),
		InstanceId:   *info.InstanceId,
		NodeIds:      []string{master},
	})
	if err != nil {
		log.Warn(ctx, "GetDiskTotalSize error:%v", err)
		return
	}
	// StorageSpace 单位 GiB  1024*1024*1024 B
	// usedSize,totalSum 单位 B
	free := uint64(storageSpace) - uint64(usedSize)
	log.Info(ctx, "InstanceId:%s, spaceInfo, total:%d, tableTotal:%f, used:%d free:%d", *info.InstanceId, storageSpace, spaceSum, usedSize, free)
	if float64(free)-float64(spaceSum)*2 < 0 {
		// 剩余空间小于这次变更的表的两倍
		agg.SpaceNotTwoCnt++
		spaceInfo = fmt.Sprintf("剩余空间 %d 小于变更表 %s 空间 %f 的两倍", free, advice.TableName, spaceSum)
		return
	}
	if float64(free)/float64(storageSpace) < 0.2 {
		// 使用水位低于80%
		agg.SpaceTooHigh++
		spaceInfo = fmt.Sprintf("剩余空间比例 %f 低于80%%", float64(free)/float64(storageSpace))
		return
	}
	if free < 100 {
		// 剩余空间大于100g
		agg.LeftSpaceTooSmall++
		spaceInfo = fmt.Sprintf("剩余空间 %d 低于100g", free)
		return
	}
	spaceInfo = "当前磁盘空间安全，剩余空间小于这次变更的表的两倍，且使用水位低于80%,剩余空间大于100g"
	pass = true
	return
}

// countSemicolons 计算字符串中 ; 字符的个数
func countSemicolons(str string) int {
	count := 0
	for _, char := range str {
		if char == ';' {
			count++
		}
	}
	return count
}

// getIndexAdvice 推荐索引，目前只拿1条
func (c *CronActor) getIndexAdvice(ctx types.Context, agg *TriggerAgg, info *model.InstanceInfo, Res *IndexAdviceRes, sqlDate time.Time) (ddls []*model.Advice, benefitSum float64, err error) {
	defer func() {
		if len(ddls) >= 2 { // todo: config
			ddls = ddls[:1]
		}
		if len(ddls) <= 0 {
			agg.NoIndexAdviceCnt++
		}
	}()

	var impl tls.SlowAdviceClient
	impl, err = tls.GetSlowAdviceClientImpl(ctx, c.cnf, c.c3, info.InstanceType.String(), *info.InstanceId)
	if err != nil {
		log.Warn(ctx, "GetSlowClient error: %s", err)
		return
	}
	var history *model.SlowQueryAdviceTaskHistoryResp
	history, err = impl.SlowQueryAdviceTaskHistory(ctx, &shared.SlowQueryAdviceTaskHistoryReq{
		InstanceType:     conv.ToSharedTypeV2(info.InstanceType),
		InstanceId:       *info.InstanceId,
		SqlDateTimeStamp: sqlDate.Unix(),
		PageSize:         1,
		PageNumber:       1,
		StartTime:        time.Now().Add(-7 * 24 * time.Hour).Unix(),
		EndTime:          time.Now().Unix(),
	})
	if err != nil {
		log.Error(ctx, "slowAdviceResService: SlowQueryAdviceTaskHistory err %s", err.Error())
		return
	}
	if len(history.ResList) <= 0 {
		return
	}
	if history.ResList[0].AdviceIndexNum <= 0 {
		return
	}
	var advices *model.ListSlowQueryAdviceResp
	advices, err = c.slowAdviceResService.ListSlowQueryAdvice(
		ctx, &model.ListSlowQueryAdviceReq{
			InstanceId:   *info.InstanceId,
			InstanceType: info.InstanceType,
			RegionId:     info.GetRegionId(),
			AdviceType:   model.AdviceType_index_advice,
			PageNumber:   1,
			PageSize:     200, // get all advice
			SummaryId:    history.ResList[0].SummaryId,
			OrderBy:      model.ListSlowQueryAdviceOrderBy_Benefit,
			GroupBy:      model.ListSlowQueryAdviceGroupBy_Advice, // 按索引分组，以最少索引获得更高收益
		},
	)
	if err != nil {
		log.Error(ctx, "slowAdviceResService: ListSlowQueryAdvice err %s", err.Error())
		return
	}
	if advices == nil || len(advices.AdvicesByGroup) <= 0 {
		return
	}
	speedUpPass := false
	execPass := false
	exclusivityPass := false
	tooManyIndexPass := false

LOOP:
	for _, group := range advices.AdvicesByGroup {
		for _, advice := range group.Advices {
			benefitSum += advice.Benefit

			if advice.SpeedUp > 2 {
				speedUpPass = true
			} else {
				continue
			}

			if advice.Agg.ExecuteCount > 10 {
				execPass = true
			} else {
				continue
			}

			if countSemicolons(advice.Advice) <= 1 {
				tooManyIndexPass = true
			} else {
				log.Info(ctx, "tooManyIndex filter advice %s", advice.Advice)
				continue
			}

			if exclusivityPass, err = c.IndexExclusivityPass(
				ctx, info, advice.DB, advice.TableName, advice.Advice,
			); err != nil {
				log.Error(ctx, "IndexExclusivityPass err %s", err.Error())
				return
			} else if !exclusivityPass {
				continue
			}

			ddls = append(ddls, advice)
			if len(Res.advices) > 0 {
				if advice.Advice != Res.advices[0].Advice {
					break LOOP
				}
			}
			Res.advices = append(Res.advices, advice)
		}
	}
	if !speedUpPass {
		agg.SpeedUpFilterCnt++
		return
	}
	if !execPass {
		agg.ExeFilterCnt++
		return
	}
	if !tooManyIndexPass {
		agg.ManyIndexCnt++
		return
	}
	if !exclusivityPass {
		agg.ExclusivityFilterCnt++
		return
	}
	return
}

func getColumnsByDDl(ddl string) ([]string, error) {
	// 编译正则表达式，匹配反引号内的列名
	pattern := "\\`(\\w+)\\`"
	re, err := regexp.Compile(pattern)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("正则表达式编译错误: %v\n", err))
	}
	// 查找所有匹配项
	matches := re.FindAllStringSubmatch(ddl, -1)
	// 提取列名并存储到切片中
	var columns []string
	for _, match := range matches {
		if len(match) > 1 {
			columns = append(columns, match[1])
		}
	}
	return columns, nil
}

func getColumnsByIndexStat(columns string) []string {
	return strings.Split(columns, ",")
}

// IndexExclusivityPass 索引排他性检查
func (c *CronActor) IndexExclusivityPass(ctx types.Context, info *model.InstanceInfo, Db, Table string, ddl string) (pass bool, err error) {
	var index *shared.DescribeTableIndexResp
	defer func() {
		if !pass {
			log.Info(ctx, "IndexExclusivity Not Pass %s err %v index %s", *info.InstanceId, err, pretty.Sprint(index))
		}
	}()
	var tableIndex *shared.DescribeTableIndexResp
	tableIndex, err = c.sqlSpaceAnalysisService.DescribeTableIndex(
		ctx, &model.DescribeTableIndexReq{
			RegionId:     info.RegionId,
			InstanceType: info.InstanceType,
			InstanceId:   *info.InstanceId,
			Database:     Db,
			Table:        Table,
		},
	)
	if err != nil {
		log.Error(ctx, "DescribeTableIndex err %s", err.Error())
		return
	}
	log.Info(ctx, "Fetched Index %s", pretty.Sprint(tableIndex))
	for _, stat := range tableIndex.IndexStats {
		var cc []string
		cc, err = getColumnsByDDl(ddl)
		if err != nil {
			log.Error(ctx, "getColumnsByDDl err %s", err.Error())
			return
		}
		for _, c := range cc {
			if slices.Contains(getColumnsByIndexStat(stat.Columns), c) {
				log.Info(ctx, "IndexExclusivityCheck Not Pass ddl %s index columns %v", ddl, stat.Columns)
				return
			}
		}
	}
	pass = true
	return
}

func (c *CronActor) isInstanceRunning(ctx types.Context, typ shared.DataSourceType, RegionId, InstanceId string) (bool, error) {
	inst, err := c.ds.DescribeDBInstanceDetail(ctx, &datasource.DescribeDBInstanceDetailReq{
		InstanceId: InstanceId,
		RegionId:   RegionId,
		Type:       typ,
	})
	if err != nil {
		log.Warn(ctx, "inspection: rds api err %s", err.Error())
		return false, err
	}
	if inst != nil && inst.InstanceStatus == rdsModel_v2_new.InstanceStatus_Running.String() {
		return true, nil
	}
	log.Warn(ctx, "inspection: instance not found %s", InstanceId)
	return false, nil
}
