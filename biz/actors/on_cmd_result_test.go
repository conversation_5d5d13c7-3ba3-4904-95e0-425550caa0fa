package actors

import (
	"errors"
	"testing"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/dbw-mgr/biz/test/mocks"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type OnCmdResultTestSuite struct {
	suite.Suite
	ctrl *gomock.Controller
}

func (suite *OnCmdResultTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
}

func (suite *OnCmdResultTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

func TestOnCmdResultTestSuite(t *testing.T) {
	suite.Run(t, new(OnCmdResultTestSuite))
}

func (suite *OnCmdResultTestSuite) TestOnCommandCursorReady() {
	// Test case 1: GetCommandSetByCmdID fails
	suite.T().Run("GetCommandSetByCmdID fails", func(t *testing.T) {
		ctx := mocks.NewMockContext(suite.ctrl)
		sessionActor := &SessionActor{
			cmdRepo: mocks.NewMockCommandRepo(suite.ctrl),
		}
		ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

		msg := &shared.CommandResult{
			CommandId: "cmd1",
			Payload: []*shared.CommandResultChunk{
				{Header: []string{"col1", "col2"}},
			},
		}
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
			GetCommandSetByCmdID(ctx, "cmd1").
			Return(nil, errors.New("command set not found"))

		sessionActor.onCommandCursorReady(ctx, msg)
	})

	// Test case 2: Command not found in command set
	suite.T().Run("Command not found", func(t *testing.T) {
		ctx := mocks.NewMockContext(suite.ctrl)
		sessionActor := &SessionActor{
			cmdRepo: mocks.NewMockCommandRepo(suite.ctrl),
		}
		ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

		msg := &shared.CommandResult{
			CommandId: "cmd1",
			Payload: []*shared.CommandResultChunk{
				{Header: []string{"col1", "col2"}},
			},
		}
		cs := &entity.CommandSet{
			ID:       "cs1",
			Commands: []*entity.Command{},
		}
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
			GetCommandSetByCmdID(ctx, "cmd1").
			Return(cs, nil)

		sessionActor.onCommandCursorReady(ctx, msg)
	})

	// Test case 3: Command not in executing state
	suite.T().Run("Command not executing", func(t *testing.T) {
		ctx := mocks.NewMockContext(suite.ctrl)
		sessionActor := &SessionActor{
			cmdRepo: mocks.NewMockCommandRepo(suite.ctrl),
		}
		ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

		msg := &shared.CommandResult{
			CommandId: "cmd1",
			Payload: []*shared.CommandResultChunk{
				{Header: []string{"col1", "col2"}},
			},
		}
		cs := &entity.CommandSet{
			ID: "cs1",
			Commands: []*entity.Command{
				{ID: "cmd1", State: entity.CommandPending},
			},
		}
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
			GetCommandSetByCmdID(ctx, "cmd1").
			Return(cs, nil)

		sessionActor.onCommandCursorReady(ctx, msg)
	})

	// Test case 4: Successful case
	suite.T().Run("Successful case", func(t *testing.T) {
		ctx := mocks.NewMockContext(suite.ctrl)
		sessionActor := &SessionActor{
			cmdRepo: mocks.NewMockCommandRepo(suite.ctrl),
		}
		ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

		msg := &shared.CommandResult{
			CommandId: "cmd1",
			Payload: []*shared.CommandResultChunk{
				{Header: []string{"col1", "col2"}},
			},
		}
		cs := &entity.CommandSet{
			ID: "cs1",
			Commands: []*entity.Command{
				{ID: "cmd1", State: entity.CommandExecuting},
			},
		}
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
			GetCommandSetByCmdID(ctx, "cmd1").
			Return(cs, nil)
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
			SaveCommandSet(ctx, gomock.Any()).
			Return(nil)

		sessionActor.onCommandCursorReady(ctx, msg)
	})

	// Test case 5: Empty payload
	suite.T().Run("Empty payload", func(t *testing.T) {
		ctx := mocks.NewMockContext(suite.ctrl)
		sessionActor := &SessionActor{
			cmdRepo: mocks.NewMockCommandRepo(suite.ctrl),
		}
		ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()

		msgEmpty := &shared.CommandResult{
			CommandId: "cmd1",
			Payload:   []*shared.CommandResultChunk{},
		}
		cs := &entity.CommandSet{
			ID: "cs1",
			Commands: []*entity.Command{
				{ID: "cmd1", State: entity.CommandExecuting},
			},
		}
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
			GetCommandSetByCmdID(ctx, "cmd1").
			Return(cs, nil)
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
			SaveCommandSet(ctx, gomock.Any()).
			Return(nil)

		sessionActor.onCommandCursorReady(ctx, msgEmpty)
	})
}

func (suite *OnCmdResultTestSuite) TestOnCursorFinished() {
	// Test case 1: GetCommandSetByCmdID fails
	suite.T().Run("GetCommandSetByCmdID fails", func(t *testing.T) {
		ctx := mocks.NewMockContext(suite.ctrl)
		sessionActor := &SessionActor{
			cmdRepo: mocks.NewMockCommandRepo(suite.ctrl),
			state: &sessionState{
				UserID:   "user1",
				TenantID: "tenant1",
			},
		}
		ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()
		ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes()

		msg := &shared.FinishCursor{
			CommandId:    "cmd1",
			ConnectionId: "conn1",
			Count:        0,
		}
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
			GetCommandSetByCmdID(ctx, "cmd1").
			Return(nil, errors.New("command set not found"))

		sessionActor.onCursorFinished(ctx, msg)
	})

	// Test case 2: Command not found in command set
	suite.T().Run("Command not found", func(t *testing.T) {
		ctx := mocks.NewMockContext(suite.ctrl)
		sessionActor := &SessionActor{
			cmdRepo: mocks.NewMockCommandRepo(suite.ctrl),
			state: &sessionState{
				UserID:   "user1",
				TenantID: "tenant1",
			},
		}
		ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()
		ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes()

		msg := &shared.FinishCursor{
			CommandId:    "cmd1",
			ConnectionId: "conn1",
			Count:        0,
		}
		cs := &entity.CommandSet{
			ID:       "cs1",
			Commands: []*entity.Command{},
		}
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
			GetCommandSetByCmdID(ctx, "cmd1").
			Return(cs, nil)

		sessionActor.onCursorFinished(ctx, msg)
	})

	// Test case 3: Command not in terminated state
	suite.T().Run("Command not terminated", func(t *testing.T) {
		ctx := mocks.NewMockContext(suite.ctrl)
		sessionActor := &SessionActor{
			cmdRepo: mocks.NewMockCommandRepo(suite.ctrl),
			state: &sessionState{
				UserID:   "user1",
				TenantID: "tenant1",
			},
		}
		ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()
		ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes()

		msg := &shared.FinishCursor{
			CommandId:    "cmd1",
			ConnectionId: "conn1",
			Count:        0,
		}
		cs := &entity.CommandSet{
			ID: "cs1",
			Commands: []*entity.Command{
				{ID: "cmd1", State: entity.CommandExecuting},
			},
		}
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
			GetCommandSetByCmdID(ctx, "cmd1").
			Return(cs, nil)

		sessionActor.onCursorFinished(ctx, msg)
	})

	// Test case 4: No next command - all finished
	suite.T().Run("All commands finished", func(t *testing.T) {
		ctx := mocks.NewMockContext(suite.ctrl)
		sessionActor := &SessionActor{
			cmdRepo:              mocks.NewMockCommandRepo(suite.ctrl),
			OperateRecordService: mocks.NewMockOperateRecordService(suite.ctrl),
			state: &sessionState{
				UserID:     "user1",
				TenantID:   "tenant1",
				DataSource: &shared.DataSource{Db: "testdb"},
				Conn: []*Connection{
					{ID: "conn1", CurrentDB: "db1"},
				},
			},
		}
		ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()
		ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes()
		ctx.EXPECT().GetName().Return("session1").AnyTimes()
		ctx.EXPECT().SaveState().AnyTimes()

		msg := &shared.FinishCursor{
			CommandId:    "cmd1",
			ConnectionId: "conn1",
			Count:        0,
		}
		cs := &entity.CommandSet{
			ID: "cs1",
			Commands: []*entity.Command{
				{ID: "cmd1", State: entity.CommandTerminated},
			},
		}
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
			GetCommandSetByCmdID(ctx, "cmd1").
			Return(cs, nil)
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
			SaveCommandSet(ctx, gomock.Any()).
			Return(nil)
		sessionActor.OperateRecordService.(*mocks.MockOperateRecordService).EXPECT().
			CreateConsoleRecordBySQLExecute(ctx, "cmd1", model.ConsoleOperationStatus_SUCCESS, sessionActor.state.DataSource, "db1", int64(0))

		sessionActor.onCursorFinished(ctx, msg)

		// Wait for goroutine to complete
		time.Sleep(10 * time.Millisecond)
	})
}

func (suite *OnCmdResultTestSuite) TestOnCursorTimeout() {
	// Test case 1: GetCommandSetByCmdID fails
	suite.T().Run("GetCommandSetByCmdID fails", func(t *testing.T) {
		ctx := mocks.NewMockContext(suite.ctrl)
		sessionActor := &SessionActor{
			cmdRepo: mocks.NewMockCommandRepo(suite.ctrl),
			state: &sessionState{
				UserID:     "user1",
				TenantID:   "tenant1",
				DataSource: &shared.DataSource{Db: "defaultdb"},
				Conn:       []*Connection{},
			},
		}
		ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()
		ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes() // For tenant recovery
		ctx.EXPECT().GetName().Return("test").AnyTimes()

		msg := &shared.CursorTimeout{
			CommandId:    "cmd1",
			ConnectionId: "conn1",
			AffectedRows: 0,
		}
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
			GetCommandSetByCmdID(ctx, "cmd1").
			Return(nil, errors.New("command set not found"))

		sessionActor.onCursorTimeout(ctx, msg)
	})

	// Test case 2: CommandSet is finished, use default DbName
	suite.T().Run("CommandSet finished, default Db", func(t *testing.T) {
		ctx := mocks.NewMockContext(suite.ctrl)
		sessionActor := &SessionActor{
			cmdRepo:              mocks.NewMockCommandRepo(suite.ctrl),
			OperateRecordService: mocks.NewMockOperateRecordService(suite.ctrl),
			state: &sessionState{
				UserID:     "user1",
				TenantID:   "tenant1",
				DataSource: &shared.DataSource{Db: "defaultdb"},
				Conn:       []*Connection{},
			},
		}
		ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()
		ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes()
		ctx.EXPECT().GetName().Return("test").AnyTimes()

		msg := &shared.CursorTimeout{
			CommandId:    "cmd1",
			ConnectionId: "conn1",
			AffectedRows: 5,
		}
		cs := &entity.CommandSet{
			ID: "cs1",
			Commands: []*entity.Command{
				{ID: "cmd1", State: entity.CommandTerminated},
			},
		}
		cs.Progress = 100
		cs.EndTimeMS = time.Now().UnixMilli()
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
			GetCommandSetByCmdID(ctx, "cmd1").
			Return(cs, nil)
		// Mock IsFinished indirectly by state
		// No cancel called since finished

		sessionActor.OperateRecordService.(*mocks.MockOperateRecordService).EXPECT().
			CreateConsoleRecordBySQLExecute(ctx, "cmd1", model.ConsoleOperationStatus_SUCCESS, sessionActor.state.DataSource, "defaultdb", int64(5))

		sessionActor.onCursorTimeout(ctx, msg)

		time.Sleep(10 * time.Millisecond)
	})

	// Test case 3: CommandSet not finished, call cancel, use Conn DbName
	suite.T().Run("CommandSet not finished, Conn Db", func(t *testing.T) {
		ctx := mocks.NewMockContext(suite.ctrl)
		sessionActor := &SessionActor{
			cmdRepo:              mocks.NewMockCommandRepo(suite.ctrl),
			OperateRecordService: mocks.NewMockOperateRecordService(suite.ctrl),
			state: &sessionState{
				UserID:     "user1",
				TenantID:   "tenant1",
				DataSource: &shared.DataSource{Db: "defaultdb"},
				Conn: []*Connection{
					{ID: "conn1", CurrentDB: "specificdb"},
				},
			},
		}
		ctx.EXPECT().Value(gomock.Any()).Return(nil).AnyTimes()
		ctx.EXPECT().WithValue(gomock.Any(), gomock.Any()).AnyTimes()
		ctx.EXPECT().GetName().Return("test").AnyTimes()
		ctx.EXPECT().Respond(&shared.CommandAccepted{})

		msg := &shared.CursorTimeout{
			CommandId:    "cmd1",
			ConnectionId: "conn1",
			AffectedRows: 10,
		}
		cs := &entity.CommandSet{
			ID: "cs1",
			Commands: []*entity.Command{
				{ID: "cmd1", State: entity.CommandExecuting},
			},
		}
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().
			GetCommandSetByCmdID(ctx, "cmd1").
			Return(cs, nil)

		mockSources := mocks.NewMockDataSourceService(suite.ctrl)
		sessionActor.sources = mockSources
		mockSources.EXPECT().KillQuery(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

		mockActorCli := mocks.NewMockActorClient(suite.ctrl)
		sessionActor.actorCli = mockActorCli

		mockKindClient := mocks.NewMockKindClient(suite.ctrl)
		mockActorCli.EXPECT().KindOf(consts.ConnectionActorKind).Return(mockKindClient)
		mockKindClient.EXPECT().Send(gomock.Any(), "conn1", gomock.Any()).Return(nil)

		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().GetCommandSet(ctx, "cs1").Return(cs, nil)
		sessionActor.cmdRepo.(*mocks.MockCommandRepo).EXPECT().SaveCommandSet(ctx, gomock.Any()).Return(nil)

		sessionActor.OperateRecordService.(*mocks.MockOperateRecordService).EXPECT().
			CreateConsoleRecordBySQLExecute(ctx, "cmd1", model.ConsoleOperationStatus_SUCCESS, sessionActor.state.DataSource, "specificdb", int64(10))

		sessionActor.onCursorTimeout(ctx, msg)

		time.Sleep(10 * time.Millisecond)
	})
}
