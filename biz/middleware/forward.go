package middleware

import (
	sysCtx "context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	sysHttp "net/http"
	"net/url"
	"os"
	"strings"
	"time"

	gocache "github.com/patrickmn/go-cache"

	"code.byted.org/gopkg/context"
	"code.byted.org/gopkg/env"
	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/service/datasource/byterds"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	"code.byted.org/infcs/ds-lib/common/http"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"code.byted.org/infcs/ds-lib/framework/mgr"
	"code.byted.org/infcs/mgr/kitex_gen/infcs/mgr/framework"
	jobpkg "code.byted.org/infcs/mgr/pkg/job"
)

var insCache *gocache.Cache

func init() {
	insCache = gocache.New(gocache.NoExpiration, gocache.NoExpiration)
}

const (
	DBWVersion       = "2018-01-01"
	getBasicInfoPath = "/openapi/multi_cloud/v1/database/get_volc_db_basic_info/"
)

type instanceInfo struct {
	instanceId   string
	instanceType string
	tenantId     string
	region       string
}

func Forward(byterdsCli byterds.ByteRDSClient) mgr.MgrMiddleware {
	return func(next mgr.MgrHandler) mgr.MgrHandler {
		return func(ctx sysCtx.Context, job *jobpkg.Job) (err error) {
			if needForward(ctx, job) {
				err = forward(ctx, job, byterdsCli)
				if err != nil {
					return err
				}
				return nil
			}
			return next(ctx, job)
		}
	}
}

func needForward(ctx sysCtx.Context, job *jobpkg.Job) bool {
	var instanceType string
	var linkType model.LinkType
	action := job.Req.MgrReq.Ctx.Action
	if action == "AddRecentlyUsedDBInstance" {
		return false
	}
	req := job.Context.(*mgr.JobContext).Request
	if reqLinkType, ok := req.(LinkType); ok {
		linkType = reqLinkType.GetLinkType()
		if linkType == model.LinkType_Volc {
			return true
		}
	}
	if reqLinkType, ok := req.(Source); ok {
		linkType = reqLinkType.GetSource()
		if linkType == model.LinkType_Volc {
			return true
		}
	}
	if reqDsType, ok := req.(DSType); ok {
		instanceType = reqDsType.GetDSType().String()
		return isVolcInstance(instanceType)
	}
	if reqInstanceType, ok := req.(InstanceType); ok {
		instanceType = reqInstanceType.GetInstanceType().String()
		return isVolcInstance(instanceType)
	}
	if reqDsType, ok := req.(InstanceDsType); ok {
		instanceType = reqDsType.GetInstanceType().String()
		return isVolcInstance(instanceType)
	}

	if getter, ok := req.(DataSourceReq); ok {
		instanceType = getter.GetDataSource().Type.String()
		return isVolcInstance(instanceType)
	}
	if dtype, ok := fwctx.GetExtra(ctx)["Ds-Type"]; ok {
		instanceTypeModel, _ := model.InstanceTypeFromString(dtype)
		return isVolcInstance(instanceTypeModel.String())
	}
	return false
}

func isVolcInstance(instanceType string) bool {
	log.Info(context.Background(), "instanceType: %s", instanceType)
	return !strings.HasPrefix(instanceType, "Byte")
}

func forward(ctx sysCtx.Context, job *jobpkg.Job, byterdsCli byterds.ByteRDSClient) error {
	log.Info(ctx, "start forward request to volcengine action: %s, body %s", job.Req.MgrReq.Ctx.Action, string(job.Req.PrivateReq.ReqBytes))
	reqBodyBytes := job.Req.PrivateReq.ReqBytes
	var body map[string]interface{}
	err := json.Unmarshal(reqBodyBytes, &body)
	if err != nil {
		log.Error(ctx, "Unmarshal reqBodyBytes error: %+v", err)
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	instanceId, dsType := parseInstance(ctx, job)
	if instanceId == "" || dsType.String() == "<UNSET>" {
		log.Info(ctx, "instanceId or dbType is empty")
		return consts.ErrorOf(model.ErrorCode_InternalError)
	}
	log.Info(ctx, "instance is is %v,ds type is %v", instanceId, dsType)
	if isVolcInstance(dsType.String()) {
		var info *instanceInfo
		ins, found := insCache.Get(instanceId)
		if found {
			info = ins.(*instanceInfo)
		} else {
			basicInfo, err := getVolcRDSBasicInfo(ctx, byterdsCli, job, instanceId)
			if err != nil {
				log.Error(ctx, "getVolcRDSBasicInfo error: %+v", err)
				return consts.ErrorOf(model.ErrorCode_InternalError)
			}
			log.Info(ctx, "getVolcRDSBasicInfo success, basicInfo: %+v", basicInfo)
			info = &instanceInfo{
				instanceId: instanceId,
				region:     basicInfo.VolcRegion,
				tenantId:   basicInfo.VolcAccountId,
			}
			insCache.Set(instanceId, info, gocache.DefaultExpiration)
		}
		if _, ok := body["RegionId"]; ok {
			body["RegionId"] = info.region
		}
		headers := buildHeaders(ctx, instanceId, dsType.String(), info.tenantId, info.region)
		params := map[string]string{
			"Version":    DBWVersion,
			"Region":     info.region,
			"Action":     job.Req.MgrReq.Ctx.Action,
			"instanceId": instanceId,
			"dsType":     dsType.String(),
		}
		log.Info(ctx, "doRequest params: %+v,headers: %+v", params, headers)
		resp, err := doRequest(ctx, headers, body, params)
		if err != nil {
			log.Error(ctx, "doRequest error: %+v", err)
			return consts.ErrorOf(model.ErrorCode_InternalError)
		}
		if resp.ResponseMetadata.Error != nil {
			log.Error(ctx, "doRequest error: %+v, RequestId %s", resp.ResponseMetadata.Error, resp.ResponseMetadata.RequestId)
			errorCode, err := model.ErrorCodeFromString(resp.ResponseMetadata.Error.Code)
			if err != nil {
				log.Error(ctx, "ErrorCodeFromString error: %+v", err)
				var opts []StandardOption
				opts = append(opts, WithHTTPCode(sysHttp.StatusForbidden))
				opts = append(opts, WithMsg(resp.ResponseMetadata.Error.Message))
				return NewCustomStandardError(opts...)
			}
			return consts.ErrorOf(errorCode)
		}
		result := resp.Result
		out, err := json.Marshal(result)
		if err != nil {
			log.Error(ctx, "Marshal result error: %+v", err)
			return consts.ErrorOf(model.ErrorCode_InternalError)
		}
		job.Resp.PrivateResp = &framework.PrivateResp{}
		job.Resp.PrivateResp.RespBytes = out
		job.SetExit(jobpkg.ExitSuccess)
		log.Info(ctx, "forward success")
		return nil
	}
	log.Info(ctx, "need not forward")
	return nil
}

func getVolcRDSBasicInfo(ctx sysCtx.Context, cli byterds.ByteRDSClient, job *jobpkg.Job, instanceId string) (*BasicInfo, error) {
	var region string
	if job.Req.MgrReq.Ctx.Top != nil {
		region = job.Req.MgrReq.Ctx.Top.Region
	}
	params := map[string]string{
		"region":           region,
		"volc_instance_id": instanceId,
	}
	log.Info(ctx, "get volc rds basic info region is %v", region)
	resp := cli.Get(ctx, getBasicInfoPath, params, nil, region, true)
	if resp == nil || resp.StatusCode != 200 {
		log.Error(ctx, "Get volc rds basic info error")
		return nil, consts.ErrorOf(model.ErrorCode_InternalError)
	}
	info, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var basicInfoResp BasicInfoResp
	if err := json.Unmarshal(info, &basicInfoResp); err != nil {
		return nil, err
	}
	return basicInfoResp.Data, nil
}

type BasicInfoResp struct {
	Code int        `json:"code"`
	Msg  string     `json:"msg"`
	Data *BasicInfo `json:"data"`
}

type BasicInfo struct {
	DBName        string `json:"db_name"`
	VolcAccountId string `json:"volc_account_id"`
	VolcRegion    string `json:"volc_region"`
}

func buildHeaders(ctx sysCtx.Context, instanceId string, instanceType string, accountId string, region string) map[string]string {
	extra := fwctx.GetExtra(ctx)
	var (
		jwtStr       string
		byteCloudEnv string
	)
	if _, ok := extra["X-Jwt-Token"]; ok {
		jwtStr = extra["X-Jwt-Token"]
	}
	if _, ok := extra["X-Jwt-Token"]; ok {
		byteCloudEnv = extra["X-Top-Region"]
	}
	log.Info(ctx, "jwtStr from web is %v，byteCloudEnv from web is %v ", jwtStr, byteCloudEnv)
	var headers map[string]string
	if env.IsBoe() {
		headers = map[string]string{
			"x-bcgw-accountmeta": fmt.Sprintf("main_account_id=%v", accountId),
			"instanceId":         instanceId,
			"dSType":             instanceType,
			"x-jwt-token":        jwtStr,
			"x-jwt-dbw":          jwtStr,
			"x-bcgw-producttype": "dbw",
			//"volc-env":           "cn_stable",
			"x-tt-trace-id":          fwctx.GetExtra(ctx)["X-Tt-Trace-Id"],
			"x-tt-logid":             fwctx.GetExtra(ctx)["X-Tt-Logid"],
			"x-bcgw-volc-proxy-host": "volcengineapi-boe-stable.byted.org", // NOTE 测试用
			"x-bytecloud-env":        byteCloudEnv,
			//"X-Mgr-Service":          "yyz-mgr-test-headless.dbw.svc.control.org:8088", // NOTE 测试用
			"x-request-ticket-from": "multi_cloud", // 从多云来的
		}
	} else {
		headers = map[string]string{
			"x-bcgw-accountmeta":     fmt.Sprintf("main_account_id=%v", accountId),
			"InstanceId":             instanceId,
			"DSType":                 instanceType,
			"x-jwt-token":            jwtStr,
			"x-jwt-dbw":              jwtStr,
			"x-bcgw-producttype":     "dbw",
			"x-tt-trace-id":          fwctx.GetExtra(ctx)["X-Tt-Trace-Id"],
			"x-tt-logid":             fwctx.GetExtra(ctx)["X-Tt-Logid"],
			"x-bytecloud-env":        byteCloudEnv,
			"x-bcgw-volc-proxy-host": fmt.Sprintf("dbw.%v.volcengineapi.com", region),
			"x-request-ticket-from":  "multi_cloud", // 从多云来的
		}
	}
	return headers
}
func parseInstance(ctx sysCtx.Context, job *jobpkg.Job) (instanceId string, dsType model.DSType) {
	req := job.Context.(*mgr.JobContext).Request
	if req != nil {
		if reqInstanceId, ok := req.(InstanceId); ok {
			instanceId = reqInstanceId.GetInstanceId()
		}
		if reqInstanceId, ok := req.(InstanceID); ok {
			instanceId = reqInstanceId.GetInstanceID()
		}
		if reqInstanceId, ok := req.(FollowInstanceID); ok {
			instanceId = reqInstanceId.GetFollowInstanceID()
		}
		if reqInstanceId, ok := req.(InspectionInstanceInfo); ok {
			for _, instanceInfo := range reqInstanceId.GetInstanceInfo() {
				instanceId = instanceInfo.GetInstanceId()
			}
		}
		if reqInstanceId, ok := req.(MetricFilter); ok {
			for _, filter := range reqInstanceId.GetFilters() {
				instanceId = filter.InstanceId
			}
		}

		if reqInstanceType, ok := req.(InstanceType); ok {
			dsType, _ = model.DSTypeFromString(reqInstanceType.GetInstanceType().String())
		}
		if reqDsType, ok := req.(InstanceDsType); ok {
			dsType = reqDsType.GetInstanceType()
		}
		if reqDsType, ok := req.(DSType); ok {
			dsType = reqDsType.GetDSType()
		}
		if getter, ok := req.(DataSourceReq); ok {
			dsType = getter.GetDataSource().Type
			instanceId = *getter.GetDataSource().InstanceId
		}
		if id, ok := fwctx.GetExtra(ctx)["Instance-Id"]; ok {
			instanceId = id
		}
		if dtype, ok := fwctx.GetExtra(ctx)["Ds-Type"]; ok {
			dsType, _ = model.DSTypeFromString(dtype)
		}
	}
	return
}

type TopResponse struct {
	ResponseMetadata struct {
		RequestId string `json:"RequestId"`
		Action    string `json:"Action"`
		Version   string `json:"Version"`
		Service   string `json:"Service"`
		Region    string `json:"Region"`
		Error     *struct {
			Code    string `json:"Code"`
			Message string `json:"Message"`
		} `json:"Error"`
	}
	Result json.RawMessage `json:"Result"`
}

func doRequest(ctx context.Context, headers map[string]string, body map[string]interface{}, param map[string]string) (*TopResponse, error) {
	urls := url.Values{}
	for k, v := range param {
		urls.Set(k, v)
	}
	multiCloudHost := os.Getenv("MULTI_CLOUD_HOST")
	endpoint := fmt.Sprintf("http://%s?%s", multiCloudHost, urls.Encode())
	cli := http.NewClient()
	cli.SetHeaders(headers)
	reqBody, err := json.Marshal(body)
	if err != nil {
		log.Warn(ctx, "marshal request body fail %v", err)
		return nil, err
	}
	log.Info(ctx, "request volc endpoint: %s, body: %s", endpoint, string(reqBody))
	rsp := cli.SetTimeout(30*time.Second).Post(ctx, endpoint, reqBody)
	log.Info(ctx, "request volc rsp: %v", utils.Show(rsp))
	log.Info(ctx, "request volc rsp response is : %v", utils.Show(*rsp.Response))
	respBody, err := rsp.GetBody()
	if err != nil {
		log.Warn(ctx, "request volc fail %v, logId %s traceId %s", err, headers["X-Tt-Logid"], headers["X-Tt-Trace-Id"])
		return nil, err
	}
	log.Info(ctx, "request volc rsp body is : %v", utils.Show(respBody))
	var res TopResponse
	if err = json.Unmarshal(respBody, &res); err != nil {
		log.Warn(ctx, "unmarshal volc response fail %s", err)
		return nil, err
	}
	return &res, nil
}
